@echo off
title infapapp - Invoice Management System
cls

echo ==========================================
echo    Invoice Management System
echo ==========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if package.json exists
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Run this file from the project folder
    pause
    exit /b 1
)

:: Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

:: Clean build files
if exist ".next" rmdir /s /q ".next" >nul 2>&1

:: Start the development server
echo.
echo Starting server...
echo Open http://localhost:3000 in your browser
echo Press Ctrl+C to stop
echo.

npm run dev

pause
