'use client';

import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { PhotographyService } from '@/types';

interface PhotographyServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const PhotographyServiceComponent: React.FC<PhotographyServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as PhotographyService;
  const isEnabled = serviceData?.enabled || false;

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'photography');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة التصوير</label>
      </div>

      {isEnabled && (
        <div className="space-y-4 pr-6 border-r-4 border-purple-500">
          {/* مكان التصوير */}
          <div className="form-field">
            <label className="form-label">مكان التصوير</label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <Field
                  type="radio"
                  name={`${namePrefix}.location`}
                  value="indoor"
                  className="form-checkbox"
                />
                <span className="text-sm">داخلي</span>
              </label>
              <label className="flex items-center">
                <Field
                  type="radio"
                  name={`${namePrefix}.location`}
                  value="outdoor"
                  className="form-checkbox"
                />
                <span className="text-sm">خارجي</span>
              </label>
            </div>
            <ErrorMessage name={`${namePrefix}.location`} component="div" className="error-message" />
          </div>

          {/* مع موديل */}
          <div className="form-field">
            <label className="flex items-center">
              <Field
                type="checkbox"
                name={`${namePrefix}.withModel`}
                className="form-checkbox"
              />
              <span className="text-sm">مع موديل</span>
            </label>
          </div>

          {/* مع تعديل */}
          <div className="form-field">
            <label className="flex items-center">
              <Field
                type="checkbox"
                name={`${namePrefix}.withEditing`}
                className="form-checkbox"
              />
              <span className="text-sm">مع تعديل الصور</span>
            </label>
          </div>

          {/* التكلفة */}
          <div className="form-field">
            <label className="form-label">التكلفة (جنيه)</label>
            <Field
              name={`${namePrefix}.cost`}
              type="number"
              min="0"
              step="0.01"
              className="form-input"
              placeholder="أدخل التكلفة"
            />
            <ErrorMessage name={`${namePrefix}.cost`} component="div" className="error-message" />
          </div>

          {/* ملاحظات إضافية */}
          <div className="form-field">
            <label className="form-label">ملاحظات التصوير</label>
            <Field
              name={`${namePrefix}.notes`}
              as="textarea"
              rows="3"
              className="form-input"
              placeholder="تفاصيل إضافية عن جلسة التصوير"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PhotographyServiceComponent;
