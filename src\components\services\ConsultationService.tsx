'use client';

import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { ConsultationService } from '@/types';

interface ConsultationServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
  isPreSelected?: boolean;
}

const ConsultationServiceComponent: React.FC<ConsultationServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
  isPreSelected = false,
}) => {
  const serviceData = values[namePrefix] as ConsultationService;
  const isEnabled = serviceData?.enabled || false;

  // تفعيل الخدمة تلقائياً إذا كانت مختارة مسبقاً
  React.useEffect(() => {
    if (isPreSelected && !serviceData?.enabled) {
      setFieldValue(`${namePrefix}.enabled`, true);
      setFieldValue(`${namePrefix}.serviceType`, 'consultation');
    }
  }, [isPreSelected, namePrefix, setFieldValue, serviceData?.enabled]);

  const paymentMethods = [
    { value: 'cash', label: 'نقدي' },
    { value: 'card', label: 'بطاقة' },
    { value: 'transfer', label: 'تحويل' },
    { value: 'installment', label: 'تقسيط' },
  ];

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'consultation');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة الاستشارات</label>
      </div>

      {isEnabled && (
        <div className="space-y-4 pr-6 border-r-4 border-blue-500">
          {/* عدد الساعات */}
          <div className="form-field">
            <label className="form-label">عدد الساعات</label>
            <Field
              name={`${namePrefix}.hours`}
              type="number"
              min="0"
              step="0.5"
              className="form-input"
              placeholder="أدخل عدد الساعات"
            />
            <ErrorMessage name={`${namePrefix}.hours`} component="div" className="error-message" />
          </div>

          {/* موضوع الاستشارة */}
          <div className="form-field">
            <label className="form-label">موضوع الاستشارة</label>
            <Field
              name={`${namePrefix}.topic`}
              as="textarea"
              rows="3"
              className="form-input"
              placeholder="اكتب موضوع الاستشارة"
            />
            <ErrorMessage name={`${namePrefix}.topic`} component="div" className="error-message" />
          </div>

          {/* موعد الاستشارة */}
          <div className="form-field">
            <label className="form-label">موعد الاستشارة</label>
            <Field
              name={`${namePrefix}.dateTime`}
              type="datetime-local"
              className="form-input"
            />
            <ErrorMessage name={`${namePrefix}.dateTime`} component="div" className="error-message" />
          </div>

          {/* التكلفة */}
          <div className="form-field">
            <label className="form-label">التكلفة (جنيه)</label>
            <Field
              name={`${namePrefix}.cost`}
              type="number"
              min="0"
              step="0.01"
              className="form-input"
              placeholder="أدخل التكلفة"
            />
            <ErrorMessage name={`${namePrefix}.cost`} component="div" className="error-message" />
          </div>

          {/* طريقة الدفع */}
          <div className="form-field">
            <label className="form-label">طريقة الدفع</label>
            <Field as="select" name={`${namePrefix}.paymentMethod`} className="form-input">
              <option value="">اختر طريقة الدفع</option>
              {paymentMethods.map((method) => (
                <option key={method.value} value={method.value}>
                  {method.label}
                </option>
              ))}
            </Field>
            <ErrorMessage name={`${namePrefix}.paymentMethod`} component="div" className="error-message" />
          </div>

          {/* ملاحظات إضافية */}
          <div className="form-field">
            <label className="form-label">ملاحظات إضافية</label>
            <Field
              name={`${namePrefix}.notes`}
              as="textarea"
              rows="2"
              className="form-input"
              placeholder="أي ملاحظات إضافية"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ConsultationServiceComponent;
