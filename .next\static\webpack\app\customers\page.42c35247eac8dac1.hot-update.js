"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/components/CustomerList.tsx":
/*!*****************************************!*\
  !*** ./src/components/CustomerList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/**\n * مكون قائمة العملاء مع البحث والتصفية\n */ \nvar _s = $RefreshSig$();\n\n\nconst CustomerList = (param)=>{\n    let { onSelectCustomer, onEditCustomer, onDeleteCustomer, onCreateInvoice, selectable = false, showActions = true } = param;\n    _s();\n    const { customers, searchQuery, setSearchQuery, searchCustomers, isLoading, error, loadCustomers, clearError } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_2__.useCustomerStore)();\n    const [filteredCustomers, setFilteredCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"name\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    const [selectedCustomer, setSelectedCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadCustomers();\n    }, [\n        loadCustomers\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = searchQuery ? searchCustomers(searchQuery) : customers;\n        // ترتيب النتائج\n        filtered = [\n            ...filtered\n        ].sort((a, b)=>{\n            let aValue;\n            let bValue;\n            switch(sortBy){\n                case \"code\":\n                    aValue = a.clientCode || \"\";\n                    bValue = b.clientCode || \"\";\n                    break;\n                case \"date\":\n                    aValue = a.id || \"\";\n                    bValue = b.id || \"\";\n                    break;\n                default:\n                    aValue = a.name;\n                    bValue = b.name;\n            }\n            const comparison = aValue.localeCompare(bValue, \"ar\");\n            return sortOrder === \"asc\" ? comparison : -comparison;\n        });\n        setFilteredCustomers(filtered);\n    }, [\n        customers,\n        searchQuery,\n        sortBy,\n        sortOrder,\n        searchCustomers\n    ]);\n    const handleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortBy(field);\n            setSortOrder(\"asc\");\n        }\n    };\n    const handleSelectCustomer = (customer)=>{\n        if (selectable) {\n            setSelectedCustomer(customer.id || null);\n            onSelectCustomer === null || onSelectCustomer === void 0 ? void 0 : onSelectCustomer(customer);\n        }\n    };\n    const getContactMethodIcon = (method)=>{\n        switch(method){\n            case \"whatsapp\":\n                return \"\\uD83D\\uDCF1\";\n            case \"meta\":\n                return \"\\uD83D\\uDCAC\";\n            case \"meeting\":\n                return \"\\uD83E\\uDD1D\";\n            case \"phone\":\n                return \"\\uD83D\\uDCDE\";\n            default:\n                return \"\\uD83D\\uDCDE\";\n        }\n    };\n    const getCategoryColor = (category)=>{\n        switch(category){\n            case \"A\":\n                return \"bg-green-100 text-green-800\";\n            case \"B\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"C\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-gray-600\",\n                    children: \"جاري تحميل العملاء...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-500 ml-2\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearError,\n                        className: \"text-red-600 hover:text-red-800\",\n                        children: \"✕\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-gray-400\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            placeholder: \"البحث بالاسم، الهاتف، أو كود العميل...\",\n                                            className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"ترتيب بالاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"code\",\n                                                children: \"ترتيب بالكود\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"ترتيب بالتاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\"),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center gap-4 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"إجمالي العملاء: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-blue-600\",\n                                        children: customers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"النتائج المعروضة: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-green-600\",\n                                        children: filteredCustomers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 35\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            filteredCustomers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"لا توجد عملاء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: searchQuery ? \"لم يتم العثور على عملاء مطابقين لبحثك\" : \"لم يتم إضافة أي عملاء بعد\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                            onClick: ()=>handleSort(\"name\"),\n                                            children: [\n                                                \"اسم العميل\",\n                                                sortBy === \"name\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                            onClick: ()=>handleSort(\"code\"),\n                                            children: [\n                                                \"كود العميل\",\n                                                sortBy === \"code\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الهاتف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"التصنيف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"التواصل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الخدمات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: filteredCustomers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50 transition-colors \".concat(selectable && selectedCustomer === customer.id ? \"bg-blue-50\" : \"\", \" \").concat(selectable ? \"cursor-pointer\" : \"\"),\n                                        onClick: ()=>handleSelectCustomer(customer),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        customer.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: customer.businessName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                    children: customer.clientCode\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                children: customer.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getCategoryColor(customer.initialCategory)),\n                                                    children: [\n                                                        \"فئة \",\n                                                        customer.initialCategory\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1\",\n                                                            children: getContactMethodIcon(customer.contactMethod)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        customer.contactMethod\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        customer.services.slice(0, 2).map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                children: service\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 27\n                                                            }, undefined)),\n                                                        customer.services.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                            children: [\n                                                                \"+\",\n                                                                customer.services.length - 2\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        onCreateInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onCreateInvoice(customer);\n                                                            },\n                                                            className: \"bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                            title: \"إنشاء فاتورة جديدة لهذا العميل\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                \"فاتورة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        onEditCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onEditCustomer(customer);\n                                                            },\n                                                            className: \"text-blue-600 hover:text-blue-900\",\n                                                            children: \"تعديل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        onDeleteCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                if (confirm(\"هل أنت متأكد من حذف هذا العميل؟\")) {\n                                                                    onDeleteCustomer(customer.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 hover:text-red-900\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, customer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerList, \"KJZN3zosjEpNeIyk5VNwrHoEA8w=\", false, function() {\n    return [\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_2__.useCustomerStore\n    ];\n});\n_c = CustomerList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomerList);\nvar _c;\n$RefreshReg$(_c, \"CustomerList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CustomerList.tsx\n"));

/***/ })

});