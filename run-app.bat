@echo off
title Invoice Management System - infapapp
cls

echo.
echo ==========================================
echo     Invoice Management System
echo     infapapp - Arabic Invoice App
echo ==========================================
echo.

:: Check Node.js
echo [1/4] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed
    echo.
    echo Please install Node.js from:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
echo OK: Node.js installed - %NODE_VERSION%

:: Check npm
echo.
echo [2/4] Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
echo OK: npm available - %NPM_VERSION%

:: Check project files
echo.
echo [3/4] Checking project files...
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure to run this file from the correct project folder
    echo Current directory: %CD%
    pause
    exit /b 1
)
echo OK: package.json found

if not exist "src" (
    echo ERROR: src folder not found
    pause
    exit /b 1
)
echo OK: src folder found

:: Clean and install dependencies
echo.
echo [4/4] Setting up dependencies...

:: Remove old installation files to fix conflicts
if exist "package-lock.json" (
    echo Cleaning old package-lock.json...
    del "package-lock.json" >nul 2>&1
)

if exist "node_modules" (
    echo Cleaning old node_modules...
    rmdir /s /q "node_modules" >nul 2>&1
)

if exist ".next" (
    echo Cleaning old build files...
    rmdir /s /q ".next" >nul 2>&1
)

:: Clean npm cache
echo Cleaning npm cache...
npm cache clean --force >nul 2>&1

:: Install dependencies
echo.
echo Installing dependencies...
echo This may take a few minutes...
echo.

npm install --no-audit --no-fund --legacy-peer-deps
if errorlevel 1 (
    echo.
    echo ERROR: Failed to install dependencies
    echo.
    echo Try these solutions:
    echo 1. Check internet connection
    echo 2. Run Command Prompt as Administrator
    echo 3. Try: npm install --force
    echo.
    pause
    exit /b 1
)

echo OK: Dependencies installed successfully

:: Check port 3000
echo.
echo Checking port 3000...
netstat -an | find ":3000" >nul 2>&1
if not errorlevel 1 (
    echo WARNING: Port 3000 is in use
    echo Will try to use another port automatically
)

:: Start the server
echo.
echo ==========================================
echo Starting development server...
echo.
echo Important notes:
echo - Wait for "Ready - started server" message
echo - App will open at http://localhost:3000
echo - To stop server press Ctrl+C
echo.
echo ==========================================
echo.

:: Run npm dev with error handling
call npm run dev
set EXIT_CODE=%errorlevel%

echo.
if %EXIT_CODE% neq 0 (
    echo ERROR: Server failed to start
    echo.
    echo To help solve the problem:
    echo 1. Check error messages above
    echo 2. Make sure there are no code errors
    echo 3. Try: npm run build to check for errors
    echo.
) else (
    echo OK: Server closed successfully
)

echo Press any key to exit...
pause >nul
