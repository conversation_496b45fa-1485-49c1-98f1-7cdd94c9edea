@echo off
chcp 65001 >nul 2>&1
title تطبيق إدارة الفواتير - infapapp
cls

echo.
echo ==========================================
echo     تطبيق إدارة الفواتير - infapapp
echo     Invoice Management System
echo ==========================================
echo.

:: فحص Node.js
echo [1/4] فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
echo ✅ Node.js مثبت - %NODE_VERSION%

:: فحص npm
echo.
echo [2/4] فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
echo ✅ npm متوفر - %NPM_VERSION%

:: فحص ملفات المشروع
echo.
echo [3/4] فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من تشغيل الملف من مجلد المشروع الصحيح
    echo المجلد الحالي: %CD%
    pause
    exit /b 1
)
echo ✅ package.json موجود

if not exist "src" (
    echo ❌ مجلد src غير موجود
    pause
    exit /b 1
)
echo ✅ مجلد src موجود

:: تنظيف وتثبيت التبعيات
echo.
echo [4/4] إعداد التبعيات...

:: حذف ملفات التثبيت القديمة لحل مشاكل التضارب
if exist "package-lock.json" (
    echo تنظيف package-lock.json القديم...
    del "package-lock.json" >nul 2>&1
)

if exist "node_modules" (
    echo تنظيف node_modules القديم...
    rmdir /s /q "node_modules" >nul 2>&1
)

if exist ".next" (
    echo تنظيف ملفات البناء القديمة...
    rmdir /s /q ".next" >nul 2>&1
)

:: تنظيف npm cache
echo تنظيف npm cache...
npm cache clean --force >nul 2>&1

:: تثبيت التبعيات
echo.
echo 📦 تثبيت التبعيات الجديدة...
echo هذا قد يستغرق بضع دقائق...
echo.

npm install --no-audit --no-fund --legacy-peer-deps
if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت التبعيات
    echo.
    echo جرب الحلول التالية:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل Command Prompt كمدير
    echo 3. جرب: npm install --force
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح

:: فحص المنفذ 3000
echo.
echo فحص المنفذ 3000...
netstat -an | find ":3000" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ المنفذ 3000 مستخدم
    echo سيتم محاولة استخدام منفذ آخر تلقائياً
)

:: تشغيل الخادم
echo.
echo ==========================================
echo 🚀 تشغيل خادم التطوير...
echo.
echo ملاحظات مهمة:
echo - انتظر رسالة "Ready - started server"
echo - سيفتح التطبيق على http://localhost:3000
echo - لإيقاف الخادم اضغط Ctrl+C
echo.
echo ==========================================
echo.

:: تشغيل npm dev مع معالجة الأخطاء
call npm run dev
set EXIT_CODE=%errorlevel%

echo.
if %EXIT_CODE% neq 0 (
    echo ❌ خطأ في تشغيل الخادم
    echo.
    echo للمساعدة في حل المشكلة:
    echo 1. تحقق من رسائل الخطأ أعلاه
    echo 2. تأكد من عدم وجود أخطاء في الكود
    echo 3. جرب: npm run build للتحقق من الأخطاء
    echo.
) else (
    echo ✅ تم إغلاق الخادم بنجاح
)

echo اضغط أي مفتاح للخروج...
pause >nul
