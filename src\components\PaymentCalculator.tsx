'use client';

import React from 'react';

interface PaymentCalculatorProps {
  totalAmount: number;
  paidAmount: number;
  onPaidAmountChange: (amount: number) => void;
}

const PaymentCalculator: React.FC<PaymentCalculatorProps> = ({
  totalAmount,
  paidAmount,
  onPaidAmountChange,
}) => {
  const remainingAmount = totalAmount - paidAmount;
  const paymentPercentage = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

  const getPaymentStatus = () => {
    if (paidAmount === 0) {
      return { status: 'غير مدفوع', color: 'text-red-600 bg-red-50', icon: '❌' };
    } else if (remainingAmount > 0) {
      return { status: 'مدفوع جزئياً', color: 'text-yellow-600 bg-yellow-50', icon: '⚠️' };
    } else if (remainingAmount === 0) {
      return { status: 'مدفوع بالكامل', color: 'text-green-600 bg-green-50', icon: '✅' };
    } else {
      return { status: 'مدفوع زائد', color: 'text-blue-600 bg-blue-50', icon: '💰' };
    }
  };

  const paymentStatus = getPaymentStatus();

  const formatCurrency = (amount: number | string | undefined | null) => {
    // التحقق من صحة القيمة وتحويلها لرقم
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));

    // التحقق من أن النتيجة رقم صحيح
    if (isNaN(numericAmount)) {
      console.warn('⚠️ Invalid amount passed to formatCurrency:', amount);
      return '0.00 جنيه';
    }

    return `${numericAmount.toFixed(2)} جنيه`;
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">حساب المدفوعات</h3>
      
      {/* إجمالي المبلغ */}
      <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
        <span className="font-medium text-blue-800">إجمالي المبلغ:</span>
        <span className="text-xl font-bold text-blue-900">{formatCurrency(totalAmount)}</span>
      </div>

      {/* المبلغ المدفوع */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          المبلغ المدفوع:
        </label>
        <div className="flex items-center space-x-2 space-x-reverse">
          <input
            type="number"
            min="0"
            max={totalAmount * 2} // يمكن دفع أكثر من المطلوب
            step="0.01"
            value={paidAmount}
            onChange={(e) => onPaidAmountChange(Number(e.target.value) || 0)}
            className="form-input flex-1"
            placeholder="أدخل المبلغ المدفوع"
          />
          <span className="text-gray-500 text-sm">جنيه</span>
        </div>
      </div>

      {/* المبلغ المتبقي */}
      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
        <span className="font-medium text-gray-700">المبلغ المتبقي:</span>
        <span className={`text-xl font-bold ${remainingAmount > 0 ? 'text-red-600' : remainingAmount === 0 ? 'text-green-600' : 'text-blue-600'}`}>
          {formatCurrency(Math.abs(remainingAmount))}
          {remainingAmount < 0 && ' (زائد)'}
        </span>
      </div>

      {/* حالة الدفع */}
      <div className={`flex items-center justify-between p-3 rounded-lg ${paymentStatus.color}`}>
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className="text-lg">{paymentStatus.icon}</span>
          <span className="font-semibold">حالة الدفع:</span>
        </div>
        <span className="font-bold">{paymentStatus.status}</span>
      </div>

      {/* شريط التقدم */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-gray-600">
          <span>نسبة الدفع</span>
          <span>{paymentPercentage.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className={`h-3 rounded-full transition-all duration-300 ${
              paymentPercentage === 100 
                ? 'bg-green-500' 
                : paymentPercentage > 100 
                  ? 'bg-blue-500' 
                  : paymentPercentage > 0 
                    ? 'bg-yellow-500' 
                    : 'bg-red-500'
            }`}
            style={{ width: `${Math.min(paymentPercentage, 100)}%` }}
          ></div>
        </div>
      </div>

      {/* أزرار سريعة */}
      <div className="grid grid-cols-3 gap-2 pt-2">
        <button
          type="button"
          onClick={() => onPaidAmountChange(0)}
          className="text-xs bg-red-100 hover:bg-red-200 text-red-700 py-2 px-3 rounded transition-colors"
        >
          صفر
        </button>
        <button
          type="button"
          onClick={() => onPaidAmountChange(totalAmount / 2)}
          className="text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-700 py-2 px-3 rounded transition-colors"
        >
          نصف المبلغ
        </button>
        <button
          type="button"
          onClick={() => onPaidAmountChange(totalAmount)}
          className="text-xs bg-green-100 hover:bg-green-200 text-green-700 py-2 px-3 rounded transition-colors"
        >
          كامل المبلغ
        </button>
      </div>

      {/* ملاحظات الدفع */}
      {remainingAmount > 0 && (
        <div className="text-sm text-yellow-700 bg-yellow-50 p-3 rounded-lg">
          <strong>تنبيه:</strong> يتبقى دفع {formatCurrency(remainingAmount)} لإتمام الفاتورة
        </div>
      )}
      
      {remainingAmount < 0 && (
        <div className="text-sm text-blue-700 bg-blue-50 p-3 rounded-lg">
          <strong>ملاحظة:</strong> تم دفع مبلغ زائد قدره {formatCurrency(Math.abs(remainingAmount))}
        </div>
      )}
    </div>
  );
};

export default PaymentCalculator;
