'use client';

/**
 * صفحة إدارة العملاء الشاملة
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Client } from '@/types';
import { useCustomerStore } from '@/store/customerStore';
import CustomerList from '@/components/CustomerList';
import ClientForm from '@/components/ClientForm';
import CustomerImport from '@/components/CustomerImport';
import { getCustomerCodeStats } from '@/utils/customerCodeGenerator';

type ViewMode = 'list' | 'add' | 'edit' | 'stats' | 'import';

const CustomersPage: React.FC = () => {
  const router = useRouter();
  const {
    customers,
    currentCustomer,
    setCurrentCustomer,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getStats,
    isLoading,
    error,
    clearError,
  } = useCustomerStore();

  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [editingCustomer, setEditingCustomer] = useState<Client | null>(null);
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    const customerStats = getCustomerCodeStats();
    setStats(customerStats);
  }, [customers]);

  const handleAddCustomer = async (customerData: Omit<Client, 'id' | 'clientCode'>) => {
    const result = await addCustomer(customerData);
    if (result.success) {
      setViewMode('list');
      // تحديث الإحصائيات
      const newStats = getCustomerCodeStats();
      setStats(newStats);
    }
  };

  const handleEditCustomer = (customer: Client) => {
    setEditingCustomer(customer);
    setViewMode('edit');
  };

  const handleUpdateCustomer = async (customerData: Client) => {
    if (editingCustomer?.id) {
      const result = await updateCustomer(editingCustomer.id, customerData);
      if (result.success) {
        setViewMode('list');
        setEditingCustomer(null);
      }
    }
  };

  const handleDeleteCustomer = async (customerId: string) => {
    const result = await deleteCustomer(customerId);
    if (result.success) {
      // تحديث الإحصائيات
      const newStats = getCustomerCodeStats();
      setStats(newStats);
    }
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  const renderHeader = () => (
    <div className="bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="bg-blue-600 p-3 rounded-full shadow-md">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">إدارة العملاء</h1>
            <p className="text-gray-600 mt-1">
              {viewMode === 'list' && 'عرض وإدارة جميع العملاء'}
              {viewMode === 'add' && 'إضافة عميل جديد'}
              {viewMode === 'edit' && 'تعديل بيانات العميل'}
              {viewMode === 'stats' && 'إحصائيات العملاء'}
              {viewMode === 'import' && 'استيراد العملاء من ملف CSV'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={handleBackToHome}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd"/>
            </svg>
            العودة للرئيسية
          </button>
        </div>
      </div>
    </div>
  );

  const renderNavigation = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setViewMode('list')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            viewMode === 'list'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
          </svg>
          قائمة العملاء
        </button>
        
        <button
          onClick={() => setViewMode('add')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            viewMode === 'add'
              ? 'bg-green-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd"/>
          </svg>
          إضافة عميل
        </button>
        
        <button
          onClick={() => setViewMode('stats')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            viewMode === 'stats'
              ? 'bg-purple-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
          </svg>
          الإحصائيات
        </button>

        <button
          onClick={() => setViewMode('import')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            viewMode === 'import'
              ? 'bg-indigo-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
          </svg>
          استيراد العملاء
        </button>
      </div>
    </div>
  );

  const renderStats = () => (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">إجمالي العملاء</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalCustomers || 0}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">أكواد مُنشأة</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.generatedCodes || 0}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">أكواد مخصصة</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.customCodes || 0}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">الكود التالي</p>
              <p className="text-lg font-bold text-gray-900">{stats?.nextCode || 'p013621'}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* معلومات تفصيلية */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">معلومات النظام</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">إعدادات الأكواد</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• تنسيق الكود: p + رقم المعاملة + رقم العميل</li>
              <li>• رقم المعاملة الحالي: {stats?.lastTransactionNumber || 1}</li>
              <li>• آخر رقم عميل: {stats?.lastCustomerNumber || 3621}</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">إحصائيات إضافية</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• نسبة الأكواد المُنشأة: {stats?.totalCustomers > 0 ? Math.round((stats.generatedCodes / stats.totalCustomers) * 100) : 0}%</li>
              <li>• نسبة الأكواد المخصصة: {stats?.totalCustomers > 0 ? Math.round((stats.customCodes / stats.totalCustomers) * 100) : 0}%</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const handleImportComplete = (result: any) => {
    if (result.success) {
      // تحديث الإحصائيات
      const newStats = getCustomerCodeStats();
      setStats(newStats);
      // العودة لقائمة العملاء
      setTimeout(() => {
        setViewMode('list');
      }, 2000);
    }
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'add':
        return (
          <div className="max-w-4xl mx-auto">
            <ClientForm onSubmit={handleAddCustomer} />
          </div>
        );

      case 'edit':
        return editingCustomer ? (
          <div className="max-w-4xl mx-auto">
            <ClientForm
              onSubmit={handleUpdateCustomer}
              initialValues={editingCustomer}
            />
          </div>
        ) : null;

      case 'stats':
        return renderStats();

      case 'import':
        return (
          <div className="max-w-4xl mx-auto">
            <CustomerImport
              onImportComplete={handleImportComplete}
              onClose={() => setViewMode('list')}
            />
          </div>
        );

      default:
        return (
          <CustomerList
            onEditCustomer={handleEditCustomer}
            onDeleteCustomer={handleDeleteCustomer}
            showActions={true}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {renderHeader()}
        {renderNavigation()}
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-500 ml-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
                </svg>
                <span className="text-red-800">{error}</span>
              </div>
              <button
                onClick={clearError}
                className="text-red-600 hover:text-red-800"
              >
                ✕
              </button>
            </div>
          </div>
        )}
        
        {renderContent()}
      </div>
    </div>
  );
};

export default CustomersPage;
