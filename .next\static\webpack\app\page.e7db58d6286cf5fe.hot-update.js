"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeInvoiceStore: function() { return /* binding */ initializeInvoiceStore; },\n/* harmony export */   useInvoiceStore: function() { return /* binding */ useInvoiceStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// تحميل البيانات من النظام الموحد\nconst loadInvoicesFromStorage = async ()=>{\n    if (true) {\n        try {\n            const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n            if (!result.success) {\n                console.error(\"❌ Error loading invoices from unified storage:\", result.message);\n                return [];\n            }\n            console.log(\"✅ Loaded invoices from unified storage:\", result.data.length);\n            return result.data;\n        } catch (error) {\n            console.error(\"❌ Critical error loading invoices:\", error);\n            return [];\n        }\n    }\n    return [];\n};\n// إنشاء المتجر مع تهيئة فارغة أولاً\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: [],\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            var _service_models;\n                            (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            var _service_files;\n                            (_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            var _service_models1;\n                            (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            var _service_items;\n                            let productsSubtotal = 0;\n                            (_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            var _service_materials;\n                            (_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            var _service_samples;\n                            (_service_samples = service.samples) === null || _service_samples === void 0 ? void 0 : _service_samples.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: async ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || \"INV-\".concat(Date.now()),\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من النظام الموحد للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n                    currentStoredInvoices = result.success ? result.data : invoices;\n                    if (!result.success) {\n                        console.error(\"❌ Error reading current invoices from unified storage:\", result.message);\n                    }\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في النظام الموحد\n                try {\n                    const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, uniqueInvoices);\n                    if (saveResult.success) {\n                        console.log(\"✅ Invoice saved via unified storage. Total invoices:\", uniqueInvoices.length);\n                    } else {\n                        console.error(\"❌ Error saving invoice via unified storage:\", saveResult.message);\n                        // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                        if (true) {\n                            alert(\"تحذير: فشل في حفظ الفاتورة. \" + saveResult.message);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"❌ Critical error saving invoice:\", error);\n                    if (true) {\n                        alert(\"خطأ حرج: فشل في حفظ الفاتورة. يرجى المحاولة مرة أخرى.\");\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (true) {\n                    alert(\"تحذير: فشل في حذف الفاتورة. \" + saveResult.message);\n                }\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return (currentInvoice === null || currentInvoice === void 0 ? void 0 : currentInvoice.services.filter((s)=>s.serviceType === type)) || [];\n        }\n    }));\n// دالة تهيئة المتجر - يجب استدعاؤها عند بدء التطبيق\nconst initializeInvoiceStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 Initializing invoice store with unified storage...\");\n        const invoices = await loadInvoicesFromStorage();\n        useInvoiceStore.setState({\n            invoices\n        });\n        console.log(\"✅ Invoice store initialized successfully with\", invoices.length, \"invoices\");\n    } catch (error) {\n        console.error(\"❌ Failed to initialize invoice store:\", error);\n        // في حالة الفشل، استخدام البيانات الافتراضية\n        useInvoiceStore.setState({\n            invoices: []\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/invoiceStore.ts\n"));

/***/ })

});