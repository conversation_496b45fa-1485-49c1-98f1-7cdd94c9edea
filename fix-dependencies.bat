@echo off
title Fix Dependencies - infapapp
cls

echo.
echo ==========================================
echo      Fix Dependencies Issues
echo      infapapp - Invoice Management
echo ==========================================
echo.

echo This will fix common dependency issues
echo.

:: Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not installed
    echo Please install Node.js first from https://nodejs.org/
    pause
    exit /b 1
)

echo OK: Node.js available

:: Check package.json
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure to run this from the project folder
    pause
    exit /b 1
)

echo OK: package.json found

echo.
echo [1/6] Removing old installation files...

:: Remove node_modules
if exist "node_modules" (
    echo Removing node_modules folder...
    rmdir /s /q "node_modules" >nul 2>&1
    if exist "node_modules" (
        echo WARNING: node_modules not completely removed
        echo You may need to delete it manually
    ) else (
        echo OK: node_modules removed
    )
) else (
    echo OK: node_modules not present
)

:: Remove package-lock.json
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del "package-lock.json" >nul 2>&1
    echo OK: package-lock.json removed
) else (
    echo OK: package-lock.json not present
)

:: Remove yarn.lock if exists
if exist "yarn.lock" (
    echo Removing yarn.lock...
    del "yarn.lock" >nul 2>&1
    echo OK: yarn.lock removed
)

:: Remove .next
if exist ".next" (
    echo Removing .next folder...
    rmdir /s /q ".next" >nul 2>&1
    echo OK: .next removed
)

echo.
echo [2/6] Cleaning npm cache...
npm cache clean --force
if errorlevel 1 (
    echo WARNING: Failed to clean cache
) else (
    echo OK: npm cache cleaned
)

echo.
echo [3/6] Checking npm registry...
npm config get registry
echo OK: npm registry ready

echo.
echo [4/6] Updating npm...
npm install -g npm@latest >nul 2>&1
if errorlevel 1 (
    echo WARNING: npm not updated (may need admin rights)
) else (
    echo OK: npm updated
)

echo.
echo [5/6] Installing dependencies...
echo This may take several minutes...
echo.

:: Install with multiple options to solve problems
npm install --no-audit --no-fund --legacy-peer-deps --force
set INSTALL_RESULT=%errorlevel%

if %INSTALL_RESULT% neq 0 (
    echo.
    echo ERROR: First install failed, trying again...
    echo.

    :: Second attempt without --force
    npm install --no-audit --no-fund --legacy-peer-deps
    set INSTALL_RESULT=%errorlevel%

    if %INSTALL_RESULT% neq 0 (
        echo.
        echo ERROR: Second install failed, trying final attempt...
        echo.

        :: Final attempt with basic options
        npm install
        set INSTALL_RESULT=%errorlevel%
    )
)

if %INSTALL_RESULT% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies
    echo.
    echo Additional solutions:
    echo 1. Check internet connection
    echo 2. Run Command Prompt as Administrator
    echo 3. Check Firewall/Antivirus settings
    echo 4. Try using VPN if there's blocking
    echo.
    pause
    exit /b 1
) else (
    echo OK: Dependencies installed successfully
)

echo.
echo [6/6] Checking installation...
npm list --depth=0 >nul 2>&1
if errorlevel 1 (
    echo WARNING: Some dependency issues exist but can continue
) else (
    echo OK: All dependencies installed correctly
)

echo.
echo ==========================================
echo SUCCESS: Dependencies fixed successfully!
echo.
echo You can now run the app using:
echo - run-app.bat
echo or
echo - npm run dev
echo ==========================================
echo.

pause
