@echo off
chcp 65001 >nul 2>&1
title إصلاح مشاكل التبعيات - Fix Dependencies
cls

echo.
echo ==========================================
echo      إصلاح مشاكل التبعيات
echo      Fix Dependencies Issues
echo ==========================================
echo.

echo هذا الملف سيقوم بإصلاح مشاكل التبعيات الشائعة
echo.

:: التحقق من Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً من https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

:: التحقق من package.json
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من تشغيل الملف من مجلد المشروع
    pause
    exit /b 1
)

echo ✅ package.json موجود

echo.
echo [1/6] حذف ملفات التثبيت القديمة...

:: حذف node_modules
if exist "node_modules" (
    echo حذف مجلد node_modules...
    rmdir /s /q "node_modules" >nul 2>&1
    if exist "node_modules" (
        echo ⚠️ لم يتم حذف node_modules بالكامل
        echo قد تحتاج لحذفه يدوياً
    ) else (
        echo ✅ تم حذف node_modules
    )
) else (
    echo ✅ node_modules غير موجود
)

:: حذف package-lock.json
if exist "package-lock.json" (
    echo حذف package-lock.json...
    del "package-lock.json" >nul 2>&1
    echo ✅ تم حذف package-lock.json
) else (
    echo ✅ package-lock.json غير موجود
)

:: حذف yarn.lock إذا وجد
if exist "yarn.lock" (
    echo حذف yarn.lock...
    del "yarn.lock" >nul 2>&1
    echo ✅ تم حذف yarn.lock
)

:: حذف .next
if exist ".next" (
    echo حذف مجلد .next...
    rmdir /s /q ".next" >nul 2>&1
    echo ✅ تم حذف .next
)

echo.
echo [2/6] تنظيف npm cache...
npm cache clean --force
if errorlevel 1 (
    echo ⚠️ فشل في تنظيف cache
) else (
    echo ✅ تم تنظيف npm cache
)

echo.
echo [3/6] فحص npm registry...
npm config get registry
echo ✅ npm registry جاهز

echo.
echo [4/6] تحديث npm...
npm install -g npm@latest >nul 2>&1
if errorlevel 1 (
    echo ⚠️ لم يتم تحديث npm (قد تحتاج صلاحيات مدير)
) else (
    echo ✅ تم تحديث npm
)

echo.
echo [5/6] تثبيت التبعيات...
echo هذا قد يستغرق عدة دقائق...
echo.

:: تثبيت مع خيارات متعددة لحل المشاكل
npm install --no-audit --no-fund --legacy-peer-deps --force
set INSTALL_RESULT=%errorlevel%

if %INSTALL_RESULT% neq 0 (
    echo.
    echo ❌ فشل التثبيت الأول، جاري المحاولة مرة أخرى...
    echo.
    
    :: محاولة ثانية بدون --force
    npm install --no-audit --no-fund --legacy-peer-deps
    set INSTALL_RESULT=%errorlevel%
    
    if %INSTALL_RESULT% neq 0 (
        echo.
        echo ❌ فشل التثبيت الثاني، جاري المحاولة الأخيرة...
        echo.
        
        :: محاولة أخيرة بخيارات أساسية
        npm install
        set INSTALL_RESULT=%errorlevel%
    )
)

if %INSTALL_RESULT% neq 0 (
    echo.
    echo ❌ فشل في تثبيت التبعيات
    echo.
    echo حلول إضافية:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل Command Prompt كمدير
    echo 3. تحقق من إعدادات Firewall/Antivirus
    echo 4. جرب استخدام VPN إذا كان هناك حجب
    echo.
    pause
    exit /b 1
) else (
    echo ✅ تم تثبيت التبعيات بنجاح
)

echo.
echo [6/6] فحص التثبيت...
npm list --depth=0 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ توجد بعض المشاكل في التبعيات لكن يمكن المتابعة
) else (
    echo ✅ جميع التبعيات مثبتة بشكل صحيح
)

echo.
echo ==========================================
echo ✅ تم إصلاح مشاكل التبعيات بنجاح!
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo - run-app.bat
echo أو
echo - npm run dev
echo ==========================================
echo.

pause
