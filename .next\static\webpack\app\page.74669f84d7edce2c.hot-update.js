"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/imageExporter.ts":
/*!************************************!*\
  !*** ./src/utils/imageExporter.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportAsImage: function() { return /* binding */ exportAsImage; },\n/* harmony export */   exportPOSInvoiceAsImage: function() { return /* binding */ exportPOSInvoiceAsImage; },\n/* harmony export */   previewAsImage: function() { return /* binding */ previewAsImage; },\n/* harmony export */   sharePOSInvoiceAsImage: function() { return /* binding */ sharePOSInvoiceAsImage; }\n/* harmony export */ });\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * انتظار تحميل جميع الصور في العنصر\n * @param element العنصر المراد فحصه\n */ const waitForImages = (element)=>{\n    return new Promise((resolve)=>{\n        const images = element.querySelectorAll(\"img\");\n        if (images.length === 0) {\n            resolve();\n            return;\n        }\n        let loadedCount = 0;\n        const totalImages = images.length;\n        const checkComplete = ()=>{\n            loadedCount++;\n            if (loadedCount === totalImages) {\n                resolve();\n            }\n        };\n        images.forEach((img)=>{\n            if (img.complete) {\n                checkComplete();\n            } else {\n                img.onload = checkComplete;\n                img.onerror = checkComplete; // حتى لو فشل التحميل، نكمل\n            }\n        });\n    });\n};\n/**\n * تصدير عنصر HTML كصورة JPG\n * @param elementId معرف العنصر المراد تصديره\n * @param filename اسم الملف (اختياري)\n * @param options خيارات التصدير\n */ const exportAsImage = async (elementId, filename, options)=>{\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        // إعدادات html2canvas المحسنة للطابعات الحرارية مع دعم أفضل للصور\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: (options === null || options === void 0 ? void 0 : options.scale) || 2,\n            backgroundColor: (options === null || options === void 0 ? void 0 : options.backgroundColor) || \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            width: (options === null || options === void 0 ? void 0 : options.width) || element.scrollWidth,\n            height: (options === null || options === void 0 ? void 0 : options.height) || element.scrollHeight,\n            scrollX: 0,\n            scrollY: 0,\n            // تحسينات للنصوص العربية\n            letterRendering: true,\n            logging: false,\n            // تحسين جودة الخطوط والصور\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    // تطبيق أنماط إضافية للطباعة\n                    clonedElement.style.fontFamily = \"Arial, sans-serif\";\n                    clonedElement.style.fontSize = \"14px\";\n                    clonedElement.style.lineHeight = \"1.4\";\n                    // التأكد من أن جميع الصور مرئية ومحسنة\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block !important\";\n                        img.style.visibility = \"visible !important\";\n                        img.style.opacity = \"1 !important\";\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                        img.style.objectFit = \"contain\";\n                        img.style.imageRendering = \"crisp-edges\";\n                        img.style.filter = \"contrast(1.2) brightness(1.1)\";\n                        // التأكد من تحميل الصورة\n                        if (img.src && !img.complete) {\n                            img.crossOrigin = \"anonymous\";\n                        }\n                    });\n                }\n            }\n        });\n        // تحويل إلى صورة JPG\n        const imageData = canvas.toDataURL(\"image/jpeg\", (options === null || options === void 0 ? void 0 : options.quality) || 0.95);\n        // إنشاء رابط التحميل\n        const link = document.createElement(\"a\");\n        link.download = filename || \"فاتورة-POS-\".concat(Date.now(), \".jpg\");\n        link.href = imageData;\n        // تحميل الصورة\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        console.log(\"تم تصدير الصورة بنجاح\");\n    } catch (error) {\n        console.error(\"خطأ في تصدير الصورة:\", error);\n        throw error;\n    }\n};\n/**\n * تصدير فاتورة POS كصورة مع إعدادات محسنة\n * @param invoiceId معرف الفاتورة\n * @param invoiceNumber رقم الفاتورة\n */ const exportPOSInvoiceAsImage = async (invoiceId, invoiceNumber)=>{\n    const filename = \"فاتورة-POS-\".concat(invoiceNumber || invoiceId, \"-\").concat(new Date().toISOString().split(\"T\")[0], \".jpg\");\n    // انتظار قصير للتأكد من تحميل جميع العناصر\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n    await exportAsImage(\"pos-invoice-content\", filename, {\n        quality: 0.98,\n        scale: 4,\n        backgroundColor: \"#ffffff\",\n        width: 320\n    });\n};\n/**\n * مشاركة فاتورة POS كصورة (إذا كان المتصفح يدعم Web Share API)\n * @param elementId معرف العنصر\n * @param title عنوان المشاركة\n */ const sharePOSInvoiceAsImage = async function(elementId) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"فاتورة POS\";\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: 2,\n            backgroundColor: \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block\";\n                        img.style.visibility = \"visible\";\n                        img.style.opacity = \"1\";\n                    });\n                }\n            }\n        });\n        // تحويل إلى blob\n        canvas.toBlob(async (blob)=>{\n            if (!blob) {\n                throw new Error(\"فشل في إنشاء الصورة\");\n            }\n            if (navigator.share && navigator.canShare) {\n                const file = new File([\n                    blob\n                ], \"\".concat(title, \".jpg\"), {\n                    type: \"image/jpeg\"\n                });\n                if (navigator.canShare({\n                    files: [\n                        file\n                    ]\n                })) {\n                    await navigator.share({\n                        title: title,\n                        text: \"فاتورة من OKA GROUP\",\n                        files: [\n                            file\n                        ]\n                    });\n                    return;\n                }\n            }\n            // إذا لم يكن Web Share API متاحاً، استخدم التحميل العادي\n            const imageData = canvas.toDataURL(\"image/jpeg\", 0.95);\n            const link = document.createElement(\"a\");\n            link.download = \"\".concat(title, \".jpg\");\n            link.href = imageData;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }, \"image/jpeg\", 0.95);\n    } catch (error) {\n        console.error(\"خطأ في مشاركة الصورة:\", error);\n        throw error;\n    }\n};\n/**\n * معاينة الصورة قبل التصدير\n * @param elementId معرف العنصر\n * @returns Promise<string> رابط الصورة\n */ const previewAsImage = async (elementId)=>{\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: 2,\n            backgroundColor: \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block\";\n                        img.style.visibility = \"visible\";\n                        img.style.opacity = \"1\";\n                    });\n                }\n            }\n        });\n        return canvas.toDataURL(\"image/jpeg\", 0.95);\n    } catch (error) {\n        console.error(\"خطأ في معاينة الصورة:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageExporter.ts\n"));

/***/ })

});