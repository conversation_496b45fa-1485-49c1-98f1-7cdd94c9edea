'use client';

import React, { useState, useRef } from 'react';
import { useSettingsStore } from '@/store/settingsStore';

interface CompanySettingsModalProps {
  onClose: () => void;
}

const CompanySettingsModal: React.FC<CompanySettingsModalProps> = ({ onClose }) => {
  const { settings, updateSettings, uploadLogo, uploadStamp } = useSettingsStore();
  const [isUploading, setIsUploading] = useState(false);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const stampInputRef = useRef<HTMLInputElement>(null);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      try {
        await uploadLogo(file);
        alert('تم رفع شعار الشركة بنجاح!');
      } catch (error) {
        alert('فشل في رفع شعار الشركة: ' + (error as Error).message);
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleStampUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploading(true);
      try {
        await uploadStamp(file);
        alert('تم رفع ختم الشركة بنجاح!');
      } catch (error) {
        alert('فشل في رفع ختم الشركة: ' + (error as Error).message);
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleInputChange = (field: string, value: string) => {
    updateSettings({ [field]: value });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold flex items-center gap-3">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
              </svg>
              إعدادات الشركة
            </h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Left Column - Company Information */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-gray-800 border-b-2 border-blue-200 pb-2">معلومات الشركة</h3>
              
              {/* Company Name */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">اسم الشركة</label>
                <input
                  type="text"
                  value={settings.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="أدخل اسم الشركة"
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  value={settings.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>

              {/* Complaints Phone */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">هاتف الشكاوى والاقتراحات</label>
                <input
                  type="tel"
                  value={settings.complaintsPhone}
                  onChange={(e) => handleInputChange('complaintsPhone', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="أدخل رقم هاتف الشكاوى"
                />
              </div>

              {/* Address */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">عنوان المقر الإداري</label>
                <textarea
                  value={settings.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                  placeholder="أدخل عنوان المقر الإداري"
                />
              </div>

              {/* Slogan */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">شعار الشركة</label>
                <input
                  type="text"
                  value={settings.slogan}
                  onChange={(e) => handleInputChange('slogan', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="أدخل شعار الشركة"
                />
              </div>

              {/* Wish Message */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">رسالة التمنيات</label>
                <input
                  type="text"
                  value={settings.wishMessage}
                  onChange={(e) => handleInputChange('wishMessage', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="أدخل رسالة التمنيات"
                />
              </div>
            </div>

            {/* Right Column - Images */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-gray-800 border-b-2 border-blue-200 pb-2">الصور والشعارات</h3>
              
              {/* Company Logo */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">شعار الشركة</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  {settings.companyLogo ? (
                    <div className="space-y-4">
                      <img
                        src={settings.companyLogo}
                        alt="شعار الشركة"
                        className="w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                      />
                      <button
                        onClick={() => logoInputRef.current?.click()}
                        disabled={isUploading}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isUploading ? 'جاري الرفع...' : 'تغيير الشعار'}
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <svg className="w-16 h-16 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                      </svg>
                      <p className="text-gray-600">اضغط لرفع شعار الشركة</p>
                      <button
                        onClick={() => logoInputRef.current?.click()}
                        disabled={isUploading}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isUploading ? 'جاري الرفع...' : 'رفع الشعار'}
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={logoInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
              </div>

              {/* Company Stamp */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">ختم الشركة</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                  {settings.companyStamp ? (
                    <div className="space-y-4">
                      <img
                        src={settings.companyStamp}
                        alt="ختم الشركة"
                        className="w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                      />
                      <button
                        onClick={() => stampInputRef.current?.click()}
                        disabled={isUploading}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isUploading ? 'جاري الرفع...' : 'تغيير الختم'}
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <svg className="w-12 h-12 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                      </svg>
                      <p className="text-gray-600">اضغط لرفع ختم الشركة</p>
                      <button
                        onClick={() => stampInputRef.current?.click()}
                        disabled={isUploading}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isUploading ? 'جاري الرفع...' : 'رفع الختم'}
                      </button>
                    </div>
                  )}
                </div>
                <input
                  ref={stampInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleStampUpload}
                  className="hidden"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              سيتم حفظ الإعدادات تلقائياً عند التغيير
            </p>
            <button
              onClick={onClose}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanySettingsModal;
