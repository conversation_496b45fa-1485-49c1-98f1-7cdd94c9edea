'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ClientForm from '@/components/ClientForm';
import InvoiceForm from '@/components/InvoiceForm';
import InvoicePreview from '@/components/InvoicePreview';
import InvoicesList from '@/components/InvoicesList';
import { Client, Invoice } from '@/types';
import { useInvoiceStore } from '@/store/invoiceStore';
import { generateInvoicePDF } from '@/utils/pdfGenerator';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';


type Step = 'client' | 'invoice' | 'preview';
type Page = 'home' | 'invoices';

export default function Home() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<Page>('home');
  const [currentStep, setCurrentStep] = useState<Step>('client');
  const [currentClient, setCurrentClient] = useState<Client | null>(null);
  const [currentInvoice, setCurrentInvoice] = useState<Invoice | null>(null);

  const { saveInvoice } = useInvoiceStore();



  const handleClientSubmit = (client: Client) => {
    setCurrentClient(client);
    setCurrentStep('invoice');
  };

  const handleInvoiceSubmit = (invoice: Invoice) => {


    // إنشاء ID فريد مع ترقيم تسلسلي
    const generateSequentialId = () => {
      const now = new Date();
      const month = now.getMonth() + 1;
      const year = now.getFullYear();
      const monthYearKey = `${month}-${year}`;

      // قراءة العدادات المحفوظة باستخدام النظام الآمن
      const countersResult = secureGetItem<{ [key: string]: number }>('invoiceCounters', {});
      let counters: { [key: string]: number } = {};

      if (countersResult.success) {
        counters = countersResult.data;
      } else {
        console.error('❌ Error reading invoice counters:', countersResult.message);
        counters = {};
      }

      // الحصول على العداد الحالي للشهر والسنة
      const currentCounter = counters[monthYearKey] || 0;
      const newCounter = currentCounter + 1;

      // تحديث العداد
      counters[monthYearKey] = newCounter;

      // حفظ العدادات المحدثة باستخدام النظام الآمن
      const saveResult = secureSetItem('invoiceCounters', counters);
      if (!saveResult.success) {
        console.error('❌ Error saving invoice counters:', saveResult.message);
      }

      // إنشاء ID بالتنسيق المطلوب
      const sequentialNumber = String(newCounter).padStart(3, '0');
      const invoiceId = `INV-${month}-${year}-${sequentialNumber}`;


      return invoiceId;
    };

    const invoiceWithId = {
      ...invoice,
      id: invoice.id || generateSequentialId(),
      createdAt: invoice.createdAt || new Date().toISOString(),
    };



    // تعيين الفاتورة الحالية
    setCurrentInvoice(invoiceWithId);

    // حفظ الفاتورة في المتجر
    const store = useInvoiceStore.getState();
    store.setCurrentInvoice(invoiceWithId);

    // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)
    store.saveInvoice();

    // التحقق من النتيجة باستخدام النظام الآمن
    setTimeout(() => {
      const invoicesResult = secureGetItem<Invoice[]>('invoices', []);
      if (invoicesResult.success) {
        const savedInvoice = invoicesResult.data.find((inv: Invoice) => inv.id === invoiceWithId.id);
        if (!savedInvoice) {
          console.error('❌ Invoice not found in storage after save attempt');
        }
      } else {
        console.error('❌ Error verifying save:', invoicesResult.message);
      }
    }, 100);

    setCurrentStep('preview');
  };

  const handleEditInvoice = () => {
    setCurrentStep('invoice');
  };

  const handleExportPDF = async () => {
    if (currentInvoice) {
      try {
        await generateInvoicePDF(currentInvoice);
      } catch (error) {
        alert('حدث خطأ أثناء تصدير PDF: ' + (error as Error).message);
      }
    }
  };

  const handleStartOver = () => {
    console.log('🔄 بداية جديدة - إعادة تعيين النظام');
    setCurrentClient(null);
    setCurrentInvoice(null);
    setCurrentStep('client');
    setCurrentPage('home');
  };

  const handleManageInvoices = () => {
    console.log('📋 الانتقال لصفحة إدارة الفواتير');
    setCurrentPage('invoices');
  };

  const handleCreateInvoice = () => {
    console.log('➕ بدء إنشاء فاتورة جديدة');
    setCurrentPage('home');
    setCurrentStep('client');
    setCurrentClient(null);
    setCurrentInvoice(null);
  };

  const handleDataProtection = () => {
    console.log('🛡️ الانتقال لصفحة حماية البيانات');
    router.push('/data-protection');
  };

  const handleStatistics = () => {
    console.log('📊 الانتقال لصفحة الإحصائيات الشاملة');
    router.push('/statistics');
  };

  const handleCustomerManagement = () => {
    console.log('👥 الانتقال لصفحة إدارة العملاء');
    router.push('/customers');
  };

  const getStepTitle = (step: Step) => {
    switch (step) {
      case 'client':
        return 'إدخال بيانات العميل';
      case 'invoice':
        return 'إنشاء الفاتورة';
      case 'preview':
        return 'معاينة الفاتورة';
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                </svg>
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">نظام إدارة الفواتير</h1>
                <p className="text-blue-100 text-sm hidden sm:block">إدارة شاملة للفواتير والخدمات</p>
              </div>
            </div>
            <nav className="flex gap-2 sm:gap-3">
              <button
                onClick={handleCreateInvoice}
                className={`px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 ${
                  currentPage === 'home'
                    ? 'bg-white text-blue-600 shadow-md ring-2 ring-blue-200'
                    : 'bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md'
                }`}
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z"/>
                </svg>
                <span className="hidden sm:inline">إنشاء فاتورة</span>
                <span className="sm:hidden">إنشاء</span>
              </button>
              <button
                onClick={handleManageInvoices}
                className={`px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 ${
                  currentPage === 'invoices'
                    ? 'bg-white text-blue-600 shadow-md ring-2 ring-blue-200'
                    : 'bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md'
                }`}
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
                </svg>
                <span className="hidden sm:inline">إدارة الفواتير</span>
                <span className="sm:hidden">إدارة</span>
              </button>
              <button
                onClick={handleCustomerManagement}
                className="bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95"
                title="إدارة العملاء وأكواد العملاء"
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
                <span className="hidden sm:inline">إدارة العملاء</span>
                <span className="sm:hidden">👥</span>
              </button>
              <button
                onClick={handleDataProtection}
                className="bg-purple-500 hover:bg-purple-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95"
                title="لوحة تحكم حماية البيانات"
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                <span className="hidden sm:inline">حماية البيانات</span>
                <span className="sm:hidden">🛡️</span>
              </button>
              <button
                onClick={handleStatistics}
                className="bg-indigo-500 hover:bg-indigo-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95"
                title="الإحصائيات الشاملة والتقارير"
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                </svg>
                <span className="hidden sm:inline">الإحصائيات</span>
                <span className="sm:hidden">📊</span>
              </button>
              <button
                onClick={handleStartOver}
                className="bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95"
                type="button"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd"/>
                </svg>
                <span className="hidden sm:inline">بداية جديدة</span>
                <span className="sm:hidden">جديد</span>
              </button>

            </nav>
          </div>
        </div>
      </header>

      {/* Enhanced Progress Steps */}
      {currentPage === 'home' && (
        <div className="bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex justify-center">
              <div className="flex items-center space-x-4 sm:space-x-12 space-x-reverse">
                {/* Step 1: Client */}
                <div className={`flex items-center transition-all duration-500 ${
                  currentStep === 'client' ? 'text-blue-600 scale-105' :
                  currentStep === 'invoice' || currentStep === 'preview' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <div className="flex flex-col items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl ${
                      currentStep === 'client' ? 'bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse' :
                      currentStep === 'invoice' || currentStep === 'preview' ? 'bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200' : 'bg-gray-400'
                    }`}>
                      {currentStep === 'invoice' || currentStep === 'preview' ? (
                        <svg className="w-6 h-6 animate-bounceIn" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                        </svg>
                      )}
                    </div>
                    <span className="mt-3 font-semibold text-sm sm:text-base text-center">بيانات العميل</span>
                    {currentStep === 'client' && (
                      <div className="mt-1 text-xs text-blue-500 animate-pulse">جاري التعبئة...</div>
                    )}
                  </div>
                </div>

                {/* Progress Line 1 */}
                <div className="flex-1 max-w-24">
                  <div className={`h-1 rounded-full transition-all duration-700 ${
                    currentStep === 'invoice' || currentStep === 'preview' ? 'bg-gradient-to-l from-green-400 to-green-500' : 'bg-gray-300'
                  }`}>
                    {currentStep === 'invoice' && (
                      <div className="h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                </div>

                {/* Step 2: Invoice */}
                <div className={`flex items-center transition-all duration-500 ${
                  currentStep === 'invoice' ? 'text-blue-600 scale-105' :
                  currentStep === 'preview' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <div className="flex flex-col items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl ${
                      currentStep === 'invoice' ? 'bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse' :
                      currentStep === 'preview' ? 'bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200' : 'bg-gray-400'
                    }`}>
                      {currentStep === 'preview' ? (
                        <svg className="w-6 h-6 animate-bounceIn" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                        </svg>
                      )}
                    </div>
                    <span className="mt-3 font-semibold text-sm sm:text-base text-center">إنشاء الفاتورة</span>
                    {currentStep === 'invoice' && (
                      <div className="mt-1 text-xs text-blue-500 animate-pulse">جاري التعبئة...</div>
                    )}
                  </div>
                </div>

                {/* Progress Line 2 */}
                <div className="flex-1 max-w-24">
                  <div className={`h-1 rounded-full transition-all duration-700 ${
                    currentStep === 'preview' ? 'bg-gradient-to-l from-green-400 to-green-500' : 'bg-gray-300'
                  }`}>
                    {currentStep === 'preview' && (
                      <div className="h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                </div>

                {/* Step 3: Preview */}
                <div className={`flex items-center transition-all duration-500 ${
                  currentStep === 'preview' ? 'text-blue-600 scale-105' : 'text-gray-400'
                }`}>
                  <div className="flex flex-col items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl ${
                      currentStep === 'preview' ? 'bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse' : 'bg-gray-400'
                    }`}>
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <span className="mt-3 font-semibold text-sm sm:text-base text-center">معاينة وتصدير</span>
                    {currentStep === 'preview' && (
                      <div className="mt-1 text-xs text-blue-500 animate-pulse">جاري المعاينة...</div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Percentage */}
            <div className="mt-6 flex justify-center">
              <div className="bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-gray-600">
                    التقدم: {currentStep === 'client' ? '33%' : currentStep === 'invoice' ? '66%' : '100%'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          {currentPage === 'home' && (
            <>
              <div className="mb-8 sm:mb-10">
                <div className="bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300">
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`p-3 rounded-full shadow-md ${
                      currentStep === 'client' ? 'bg-blue-600' :
                      currentStep === 'invoice' ? 'bg-green-600' : 'bg-purple-600'
                    }`}>
                      {currentStep === 'client' && (
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                        </svg>
                      )}
                      {currentStep === 'invoice' && (
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                        </svg>
                      )}
                      {currentStep === 'preview' && (
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                        </svg>
                      )}
                    </div>
                    <div>
                      <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">{getStepTitle(currentStep)}</h2>
                      <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                        {currentStep === 'client' && 'أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة'}
                        {currentStep === 'invoice' && 'اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة'}
                        {currentStep === 'preview' && 'راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة'}
                      </p>
                    </div>
                  </div>

                  {/* Step Tips */}
                  <div className={`mt-4 p-4 rounded-lg border-l-4 ${
                    currentStep === 'client' ? 'bg-blue-50 border-blue-400' :
                    currentStep === 'invoice' ? 'bg-green-50 border-green-400' : 'bg-purple-50 border-purple-400'
                  }`}>
                    <div className="flex items-start gap-3">
                      <svg className={`w-5 h-5 mt-0.5 flex-shrink-0 ${
                        currentStep === 'client' ? 'text-blue-600' :
                        currentStep === 'invoice' ? 'text-green-600' : 'text-purple-600'
                      }`} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                      </svg>
                      <div className="text-sm">
                        {currentStep === 'client' && (
                          <div>
                            <p className="font-medium text-blue-800 mb-1">نصائح مفيدة:</p>
                            <ul className="text-blue-700 space-y-1">
                              <li>• تأكد من صحة رقم الهاتف للتواصل</li>
                              <li>• اختر الخدمات المطلوبة بدقة</li>
                              <li>• يمكن ترك الحقول الاختيارية فارغة</li>
                            </ul>
                          </div>
                        )}
                        {currentStep === 'invoice' && (
                          <div>
                            <p className="font-medium text-green-800 mb-1">نصائح مفيدة:</p>
                            <ul className="text-green-700 space-y-1">
                              <li>• أدخل تفاصيل كل خدمة بدقة</li>
                              <li>• تحقق من الأسعار والكميات</li>
                              <li>• يتم حساب المجموع تلقائياً</li>
                            </ul>
                          </div>
                        )}
                        {currentStep === 'preview' && (
                          <div>
                            <p className="font-medium text-purple-800 mb-1">نصائح مفيدة:</p>
                            <ul className="text-purple-700 space-y-1">
                              <li>• راجع جميع البيانات قبل التصدير</li>
                              <li>• يمكن طباعة الفاتورة مباشرة</li>
                              <li>• الفاتورة محفوظة تلقائياً</li>
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step Content with Enhanced Animations */}
              <div className="relative">
                {currentStep === 'client' && (
                  <div className="animate-slideInRight">
                    <ClientForm onSubmit={handleClientSubmit} />
                  </div>
                )}

                {currentStep === 'invoice' && currentClient && (
                  <div className="animate-slideInLeft">
                    <InvoiceForm
                      client={currentClient}
                      onSubmit={handleInvoiceSubmit}
                    />
                  </div>
                )}

                {currentStep === 'preview' && currentInvoice && (
                  <div className="animate-slideInUp">
                    <InvoicePreview
                      invoice={currentInvoice}
                      onEdit={handleEditInvoice}
                      onExportPDF={handleExportPDF}
                    />
                  </div>
                )}
              </div>
            </>
          )}

          {currentPage === 'invoices' && (
            <div className="animate-fadeIn">
              <InvoicesList />
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Company Info */}
            <div className="text-center md:text-right">
              <div className="flex items-center justify-center md:justify-start gap-3 mb-4">
                <div className="bg-blue-600 p-2 rounded-lg">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold">نظام إدارة الفواتير</h3>
              </div>
              <p className="text-gray-300 text-sm leading-relaxed">
                نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي
              </p>
            </div>

            {/* Services */}
            <div className="text-center md:text-right">
              <h4 className="text-lg font-semibold mb-4 text-blue-400">الخدمات المدعومة</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  الاستشارات والتصميم
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  الباترون والطباعة
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  التصنيع والعينات
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  الشحن والتسويق
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  التصوير والمنتجات
                </li>
              </ul>
            </div>

            {/* Features */}
            <div className="text-center md:text-right">
              <h4 className="text-lg font-semibold mb-4 text-green-400">المميزات</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  ترقيم تسلسلي للفواتير
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  تصدير PDF احترافي
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  حساب المدفوعات التلقائي
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  واجهة عربية متجاوبة
                </li>
                <li className="flex items-center justify-center md:justify-start gap-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  إدارة شاملة للعملاء
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-6 text-center">
            <p className="text-gray-400 text-sm">
              © 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.
            </p>
            <p className="text-gray-500 text-xs mt-2">
              تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
