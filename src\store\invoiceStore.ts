import { create } from 'zustand';
import { Client, Invoice, Service } from '@/types';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';

interface InvoiceStore {
  currentClient: Client | null;
  currentInvoice: Invoice | null;
  invoices: Invoice[];

  // Client actions
  setCurrentClient: (client: Client) => void;
  clearCurrentClient: () => void;

  // Invoice actions
  setCurrentInvoice: (invoice: Invoice) => void;
  addService: (service: Service) => void;
  removeService: (serviceId: string) => void;
  updateService: (serviceId: string, service: Service) => void;
  calculateTotal: () => void;
  saveInvoice: () => void;
  deleteInvoice: (invoiceId: string) => void;
  clearCurrentInvoice: () => void;

  // Utility functions
  getServicesByType: (type: string) => Service[];
}

// تحميل البيانات من التخزين الآمن
const loadInvoicesFromStorage = (): Invoice[] => {
  if (typeof window !== 'undefined') {
    const result = secureGetItem<Invoice[]>('invoices', []);
    if (!result.success) {
      console.error('❌ Error loading invoices:', result.message);
    }
    return result.data;
  }
  return [];
};

export const useInvoiceStore = create<InvoiceStore>((set, get) => ({
  currentClient: null,
  currentInvoice: null,
  invoices: loadInvoicesFromStorage(),

  setCurrentClient: (client: Client) => {
    set({ currentClient: client });
  },

  clearCurrentClient: () => {
    set({ currentClient: null });
  },

  setCurrentInvoice: (invoice: Invoice) => {
    set({ currentInvoice: invoice });
  },

  addService: (service: Service) => {
    const { currentInvoice } = get();
    if (currentInvoice) {
      const updatedServices = [...currentInvoice.services, service];
      const updatedInvoice = { ...currentInvoice, services: updatedServices };
      set({ currentInvoice: updatedInvoice });
      get().calculateTotal();
    }
  },

  removeService: (serviceId: string) => {
    const { currentInvoice } = get();
    if (currentInvoice) {
      const updatedServices = currentInvoice.services.filter(s => s.id !== serviceId);
      const updatedInvoice = { ...currentInvoice, services: updatedServices };
      set({ currentInvoice: updatedInvoice });
      get().calculateTotal();
    }
  },

  updateService: (serviceId: string, service: Service) => {
    const { currentInvoice } = get();
    if (currentInvoice) {
      const updatedServices = currentInvoice.services.map(s => 
        s.id === serviceId ? service : s
      );
      const updatedInvoice = { ...currentInvoice, services: updatedServices };
      set({ currentInvoice: updatedInvoice });
      get().calculateTotal();
    }
  },

  calculateTotal: () => {
    const { currentInvoice } = get();
    if (currentInvoice) {
      let total = 0;
      
      currentInvoice.services.forEach(service => {
        switch (service.serviceType) {
          case 'consultation':
            total += (service as any).cost || 0;
            break;
          case 'pattern':
            (service as any).models?.forEach((model: any) => {
              total += model.finalAmount || model.cost || 0;
            });
            break;
          case 'pattern_printing':
            (service as any).files?.forEach((file: any) => {
              total += file.cost || 0;
            });
            break;
          case 'manufacturing':
            (service as any).models?.forEach((model: any) => {
              total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
            });
            break;
          case 'shipping':
            total += (service as any).total || 0;
            break;
          case 'marketing':
            total += (service as any).cost || 0;
            break;
          case 'photography':
            total += (service as any).cost || 0;
            break;
          case 'products':
            let productsSubtotal = 0;
            (service as any).items?.forEach((item: any) => {
              productsSubtotal += item.total || 0;
            });
            // طرح الخصم إن وجد
            if ((service as any).discountAmount) {
              productsSubtotal -= (service as any).discountAmount || 0;
            }
            total += Math.max(0, productsSubtotal);
            break;
          case 'raw_purchases':
            (service as any).materials?.forEach((material: any) => {
              total += material.total || 0;
            });
            break;
          case 'samples':
            (service as any).samples?.forEach((sample: any) => {
              total += sample.cost || 0;
            });
            break;
        }
      });
      
      const updatedInvoice = { ...currentInvoice, total };
      set({ currentInvoice: updatedInvoice });
    }
  },

  saveInvoice: () => {
    const { currentInvoice, invoices } = get();
    if (currentInvoice) {
      console.log('🔄 Attempting to save invoice:', currentInvoice.id);

      const invoiceToSave = {
        ...currentInvoice,
        id: currentInvoice.id || `INV-${Date.now()}`,
        createdAt: currentInvoice.createdAt || new Date().toISOString(),
        // التأكد من وجود الحقول المطلوبة
        paidAmount: currentInvoice.paidAmount || 0,
        remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,
        paymentStatus: currentInvoice.paymentStatus || 'غير مدفوع',
        paymentMethod: currentInvoice.paymentMethod || '',
        requiresShipping: currentInvoice.requiresShipping || false,
        shippingAddress: currentInvoice.shippingAddress || '',
        notes: currentInvoice.notes || '',
      };

      // قراءة البيانات الحالية من localStorage للتأكد من التزامن
      let currentStoredInvoices: Invoice[] = [];
      try {
        const stored = localStorage.getItem('invoices');
        currentStoredInvoices = stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.error('❌ Error reading current invoices from localStorage:', error);
        currentStoredInvoices = invoices;
      }

      // التحقق من عدم وجود الفاتورة مسبقاً في localStorage
      const existingIndex = currentStoredInvoices.findIndex(inv => inv.id === invoiceToSave.id);
      let updatedInvoices;

      if (existingIndex >= 0) {
        // تحديث فاتورة موجودة
        updatedInvoices = [...currentStoredInvoices];
        updatedInvoices[existingIndex] = invoiceToSave;
        console.log('📝 Updated existing invoice:', invoiceToSave.id);
      } else {
        // إضافة فاتورة جديدة
        updatedInvoices = [...currentStoredInvoices, invoiceToSave];
        console.log('➕ Added new invoice:', invoiceToSave.id);
      }

      // إزالة أي تكرارات محتملة
      const uniqueInvoices = updatedInvoices.filter((invoice, index, self) =>
        index === self.findIndex(inv => inv.id === invoice.id)
      );

      if (uniqueInvoices.length !== updatedInvoices.length) {
        console.warn('⚠️ Removed duplicate invoices during save');
      }

      // تحديث المتجر
      set({ invoices: uniqueInvoices, currentInvoice: invoiceToSave });

      // حفظ في التخزين الآمن
      const saveResult = secureSetItem('invoices', uniqueInvoices);
      if (saveResult.success) {
        console.log('✅ Invoice saved securely. Total invoices:', uniqueInvoices.length);
      } else {
        console.error('❌ Error saving invoice:', saveResult.message);
        // في حالة فشل الحفظ، عرض تنبيه للمستخدم
        if (typeof window !== 'undefined') {
          alert('تحذير: فشل في حفظ الفاتورة. ' + saveResult.message);
        }
      }
    } else {
      console.warn('⚠️ No current invoice to save');
    }
  },

  deleteInvoice: (invoiceId: string) => {
    const { invoices } = get();
    console.log('🗑️ Attempting to delete invoice:', invoiceId);

    // البحث عن الفاتورة
    const invoiceToDelete = invoices.find(inv => inv.id === invoiceId);
    if (!invoiceToDelete) {
      console.warn('⚠️ Invoice not found for deletion:', invoiceId);
      return;
    }

    // إنشاء قائمة جديدة بدون الفاتورة المحذوفة
    const updatedInvoices = invoices.filter(inv => inv.id !== invoiceId);
    console.log('📝 Removing invoice from list. Remaining invoices:', updatedInvoices.length);

    // تحديث المتجر
    set({ invoices: updatedInvoices });

    // حفظ في التخزين الآمن
    const saveResult = secureSetItem('invoices', updatedInvoices);
    if (saveResult.success) {
      console.log('✅ Invoice deleted successfully');
      console.log('📊 Total invoices after deletion:', updatedInvoices.length);
    } else {
      console.error('❌ Error deleting invoice:', saveResult.message);
      // في حالة فشل الحذف، عرض تنبيه للمستخدم
      if (typeof window !== 'undefined') {
        alert('تحذير: فشل في حذف الفاتورة. ' + saveResult.message);
      }
    }
  },

  clearCurrentInvoice: () => {
    set({ currentInvoice: null });
  },

  getServicesByType: (type: string) => {
    const { currentInvoice } = get();
    return currentInvoice?.services.filter(s => s.serviceType === type) || [];
  },
}));
