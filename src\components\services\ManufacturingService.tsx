'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { ManufacturingService, ManufacturingModel } from '@/types';

interface ManufacturingServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const ManufacturingServiceComponent: React.FC<ManufacturingServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as ManufacturingService;
  const isEnabled = serviceData?.enabled || false;

  const defaultModel: ManufacturingModel = {
    patternSize: '',
    quantity: 1,
    patternPrice: 0,
    samplePrice: 0,
    manufacturingPrice: 0,
  };

  // حساب المجموع لكل نموذج
  const calculateModelTotal = (model: ManufacturingModel) => {
    return (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
  };

  // حساب المجموع الكلي لجميع النماذج
  const calculateGrandTotal = () => {
    if (!serviceData?.models) return 0;
    return serviceData.models.reduce((total, model) => {
      return total + calculateModelTotal(model);
    }, 0);
  };

  // تحديث المجموع عند تغيير الأسعار
  React.useEffect(() => {
    if (isEnabled && serviceData?.models) {
      const total = calculateGrandTotal();
      setFieldValue(`${namePrefix}.total`, total);
    }
  }, [serviceData?.models, isEnabled]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'manufacturing');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              setFieldValue(`${namePrefix}.models`, [defaultModel]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة التصنيع</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-red-500">
          <FieldArray name={`${namePrefix}.models`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">نماذج التصنيع</h3>
                  <button
                    type="button"
                    onClick={() => push(defaultModel)}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة نموذج جديد
                  </button>
                </div>

                {serviceData?.models?.map((model, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">نموذج التصنيع {index + 1}</h4>
                      {serviceData.models.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* مقاس الباترون */}
                      <div className="form-field">
                        <label className="form-label">مقاس الباترون</label>
                        <Field
                          name={`${namePrefix}.models.${index}.patternSize`}
                          type="text"
                          className="form-input"
                          placeholder="مثل: S, M, L أو 38, 40, 42"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.patternSize`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* الكمية */}
                      <div className="form-field">
                        <label className="form-label">الكمية المطلوبة</label>
                        <Field
                          name={`${namePrefix}.models.${index}.quantity`}
                          type="number"
                          min="1"
                          className="form-input"
                          placeholder="عدد القطع"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.quantity`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* سعر الباترون */}
                      <div className="form-field">
                        <label className="form-label">سعر الباترون (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.patternPrice`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="تكلفة الباترون"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.patternPrice`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* سعر العينة */}
                      <div className="form-field">
                        <label className="form-label">سعر العينة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.samplePrice`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="تكلفة العينة"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.samplePrice`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* سعر التصنيع */}
                      <div className="form-field">
                        <label className="form-label">سعر التصنيع (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.manufacturingPrice`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="تكلفة التصنيع"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.manufacturingPrice`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* المجموع */}
                      <div className="form-field">
                        <label className="form-label">المجموع (جنيه)</label>
                        <div className="form-input bg-green-50 font-semibold text-green-700">
                          {calculateModelTotal(model).toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          باترون + عينة + تصنيع
                        </div>
                      </div>
                    </div>

                    {/* تفاصيل إضافية */}
                    <div className="mt-4 space-y-3">
                      {/* نوع القماش */}
                      <div className="form-field">
                        <label className="form-label">نوع القماش</label>
                        <Field
                          name={`${namePrefix}.models.${index}.fabricType`}
                          type="text"
                          className="form-input"
                          placeholder="مثل: قطن، حرير، بوليستر"
                        />
                      </div>

                      {/* اللون */}
                      <div className="form-field">
                        <label className="form-label">اللون المطلوب</label>
                        <Field
                          name={`${namePrefix}.models.${index}.color`}
                          type="text"
                          className="form-input"
                          placeholder="اللون أو الألوان المطلوبة"
                        />
                      </div>

                      {/* تاريخ التسليم */}
                      <div className="form-field">
                        <label className="form-label">تاريخ التسليم المطلوب</label>
                        <Field
                          name={`${namePrefix}.models.${index}.deliveryDate`}
                          type="date"
                          className="form-input"
                        />
                      </div>

                      {/* ملاحظات خاصة */}
                      <div className="form-field">
                        <label className="form-label">ملاحظات خاصة</label>
                        <Field
                          name={`${namePrefix}.models.${index}.specialNotes`}
                          as="textarea"
                          rows="2"
                          className="form-input"
                          placeholder="أي ملاحظات خاصة بالتصنيع"
                        />
                      </div>

                      {/* خيارات إضافية */}
                      <div className="grid grid-cols-2 gap-4">
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.models.${index}.rushOrder`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">طلب مستعجل</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.models.${index}.qualityCheck`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">فحص جودة إضافي</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.models.${index}.packaging`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">تغليف خاص</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.models.${index}.labeling`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">وضع علامة تجارية</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>

          {/* المجموع الكلي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-green-700">المجموع الكلي لجميع النماذج:</span>
              <span className="text-2xl font-bold text-green-800">{calculateGrandTotal().toFixed(2)} جنيه</span>
            </div>
            <div className="text-sm text-green-600 mt-1">
              شامل جميع تكاليف الباترون والعينات والتصنيع
            </div>
          </div>

          {/* معلومات عامة للتصنيع */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات عامة</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* المصنع */}
              <div className="form-field">
                <label className="form-label">اسم المصنع</label>
                <Field
                  name={`${namePrefix}.factoryName`}
                  type="text"
                  className="form-input"
                  placeholder="اسم المصنع المسؤول"
                />
              </div>

              {/* مدير الإنتاج */}
              <div className="form-field">
                <label className="form-label">مدير الإنتاج</label>
                <Field
                  name={`${namePrefix}.productionManager`}
                  type="text"
                  className="form-input"
                  placeholder="اسم مدير الإنتاج"
                />
              </div>
            </div>

            {/* شروط خاصة */}
            <div className="form-field">
              <label className="form-label">شروط خاصة</label>
              <Field
                name={`${namePrefix}.specialConditions`}
                as="textarea"
                rows="3"
                className="form-input"
                placeholder="أي شروط خاصة بعملية التصنيع"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ManufacturingServiceComponent;
