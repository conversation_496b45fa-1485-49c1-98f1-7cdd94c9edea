"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ClientForm.tsx":
/*!***************************************!*\
  !*** ./src/components/ClientForm.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst availableServices = [\n    {\n        value: \"consultation\",\n        label: \"استشارات\"\n    },\n    {\n        value: \"pattern\",\n        label: \"باترون\"\n    },\n    {\n        value: \"pattern_printing\",\n        label: \"طباعة باترون\"\n    },\n    {\n        value: \"manufacturing\",\n        label: \"تصنيع\"\n    },\n    {\n        value: \"samples\",\n        label: \"عينات\"\n    },\n    {\n        value: \"shipping\",\n        label: \"شحن/تعبئة\"\n    },\n    {\n        value: \"marketing\",\n        label: \"تسويق\"\n    },\n    {\n        value: \"photography\",\n        label: \"تصوير\"\n    },\n    {\n        value: \"products\",\n        label: \"منتجات\"\n    },\n    {\n        value: \"raw_purchases\",\n        label: \"مشتريات خام\"\n    }\n];\nconst contactMethods = [\n    {\n        value: \"whatsapp\",\n        label: \"واتساب\"\n    },\n    {\n        value: \"meta\",\n        label: \"ميتا\"\n    },\n    {\n        value: \"meeting\",\n        label: \"مقابلة\"\n    },\n    {\n        value: \"phone\",\n        label: \"هاتف\"\n    }\n];\nconst categories = [\n    {\n        value: \"A\",\n        label: \"فئة A\"\n    },\n    {\n        value: \"B\",\n        label: \"فئة B\"\n    },\n    {\n        value: \"C\",\n        label: \"فئة C\"\n    }\n];\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_2__.object({\n    name: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم العميل مطلوب\"),\n    phone: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"رقم الموبايل مطلوب\"),\n    services: yup__WEBPACK_IMPORTED_MODULE_2__.array().min(1, \"يجب اختيار خدمة واحدة على الأقل\"),\n    contactMethod: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"وسيلة التواصل مطلوبة\"),\n    salesperson: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم السيلز مطلوب\"),\n    initialCategory: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"تصنيف العميل مطلوب\"),\n    clientCode: yup__WEBPACK_IMPORTED_MODULE_2__.string().test(\"valid-code\", \"تنسيق كود العميل غير صحيح\", function(value) {\n        if (!value) return true; // الكود اختياري\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerCode)(value);\n        return validation.isValid;\n    })\n}).test(\"unique-customer\", \"بيانات العميل\", function(values) {\n    var _this_options_context;\n    const { name, phone, clientCode } = values;\n    if (!name || !phone) return true; // سيتم التحقق من هذه الحقول بشكل منفصل\n    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerUniqueness)({\n        name,\n        phone,\n        clientCode\n    }, (_this_options_context = this.options.context) === null || _this_options_context === void 0 ? void 0 : _this_options_context.excludeId);\n    if (!validation.isValid) {\n        return this.createError({\n            path: \"name\",\n            message: validation.errors.join(\", \")\n        });\n    }\n    return true;\n});\nconst ClientForm = (param)=>{\n    let { onSubmit, initialValues } = param;\n    _s();\n    const setCurrentClient = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore)((state)=>state.setCurrentClient);\n    const { generateNewCustomerCode, validateCode, getSuggestions, suggestions, clearSuggestions, customers } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore)();\n    const [showCodeGenerator, setShowCodeGenerator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [codeStats, setCodeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationSuggestions, setValidationSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExistingCustomers, setShowExistingCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedExistingCustomer, setSelectedExistingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.getCustomerCodeStats)();\n        setCodeStats(stats);\n    }, []);\n    // التحقق من التكرار عند تغيير البيانات\n    const checkForDuplicates = (name, phone, clientCode)=>{\n        if (!name.trim() || !phone.trim()) {\n            setValidationWarnings([]);\n            setValidationSuggestions([]);\n            return;\n        }\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerUniqueness)({\n            name: name.trim(),\n            phone: phone.trim(),\n            clientCode\n        }, initialValues === null || initialValues === void 0 ? void 0 : initialValues.id);\n        setValidationWarnings(validation.warnings);\n        setValidationSuggestions(validation.suggestions || []);\n    };\n    // اختيار عميل موجود\n    const handleSelectExistingCustomer = (customer, setValues)=>{\n        setSelectedExistingCustomer(customer);\n        setValues({\n            ...customer,\n            services: customer.services || []\n        });\n        setShowExistingCustomers(false);\n        setValidationWarnings([]);\n        setValidationSuggestions([]);\n    };\n    const defaultValues = {\n        name: \"\",\n        phone: \"\",\n        services: [],\n        contactMethod: \"whatsapp\",\n        salesperson: \"\",\n        initialCategory: \"A\",\n        businessName: \"\",\n        province: \"\",\n        clientCode: \"\",\n        ...initialValues\n    };\n    const handleSubmit = (values)=>{\n        // إذا لم يتم تحديد كود العميل، إنشاء كود تلقائي\n        const finalValues = {\n            ...values,\n            clientCode: values.clientCode || generateNewCustomerCode()\n        };\n        setCurrentClient(finalValues);\n        onSubmit(finalValues);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"بيانات العميل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"أدخل المعلومات الأساسية للعميل لبدء إنشاء الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-blue-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"الخطوة 1 من 3 - معلومات العميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            (validationWarnings.length > 0 || validationSuggestions.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    validationWarnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-yellow-800 mb-2\",\n                                            children: \"تحذير - عميل مشابه موجود\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-yellow-700 space-y-1\",\n                                            children: validationWarnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• \",\n                                                        warning\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, undefined),\n                    validationSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-800 mb-2\",\n                                            children: \"اقتراحات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-700 space-y-1\",\n                                            children: validationSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• \",\n                                                        suggestion\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Formik, {\n                initialValues: defaultValues,\n                validationSchema: validationSchema,\n                onSubmit: handleSubmit,\n                children: (param)=>{\n                    let { values, setFieldValue, errors, touched, setValues } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-blue-100 to-indigo-100 px-6 py-4 border-b border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-blue-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"اختيار عميل موجود أو إضافة عميل جديد\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600 mt-1\",\n                                                children: \"يمكنك اختيار عميل من قاعدة البيانات أو إضافة عميل جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowExistingCustomers(!showExistingCustomers),\n                                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 hover:shadow-lg hover:scale-105 active:scale-95\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showExistingCustomers ? \"إخفاء العملاء الموجودين\" : \"اختيار من العملاء الموجودين\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedExistingCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-green-600\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-green-800 font-medium\",\n                                                                        children: selectedExistingCustomer.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-green-600 text-sm\",\n                                                                        children: [\n                                                                            selectedExistingCustomer.clientCode,\n                                                                            \" - \",\n                                                                            selectedExistingCustomer.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>{\n                                                                    setSelectedExistingCustomer(null);\n                                                                    setValues(defaultValues);\n                                                                },\n                                                                className: \"text-green-600 hover:text-green-800 p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showExistingCustomers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 bg-white rounded-lg border border-gray-200 max-h-96 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border-b border-gray-200 bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: [\n                                                                \"العملاء المسجلين (\",\n                                                                customers.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-200\",\n                                                        children: customers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 text-center text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 mx-auto mb-3 text-gray-300\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"لا يوجد عملاء مسجلين بعد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: \"ابدأ بإضافة عميل جديد أدناه\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 25\n                                                        }, undefined) : customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                                onClick: ()=>handleSelectExistingCustomer(customer, setValues),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-blue-600 font-medium text-sm\",\n                                                                                            children: customer.name.charAt(0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 293,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 292,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"font-medium text-gray-900\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 298,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.clientCode\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 300,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.phone\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 301,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.businessName\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 302,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 299,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        customer.services.length,\n                                                                                        \" خدمة\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: customer.salesperson\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, customer.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" اسم العميل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"name\",\n                                                                children: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        ...field,\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.name && touched.name ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"أدخل اسم العميل الكامل\",\n                                                                        onChange: (e)=>{\n                                                                            if (field === null || field === void 0 ? void 0 : field.onChange) {\n                                                                                field.onChange(e);\n                                                                            }\n                                                                            checkForDuplicates(e.target.value, values.phone, values.clientCode);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, undefined);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"name\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" رقم الموبايل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"phone\",\n                                                                children: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        ...field,\n                                                                        type: \"tel\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.phone && touched.phone ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"01xxxxxxxxx\",\n                                                                        onChange: (e)=>{\n                                                                            if (field === null || field === void 0 ? void 0 : field.onChange) {\n                                                                                field.onChange(e);\n                                                                            }\n                                                                            checkForDuplicates(values.name, e.target.value, values.clientCode);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, undefined);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"phone\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"الاسم التجاري\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 406,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"businessName\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"اسم الشركة أو المتجر (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"businessName\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"المحافظة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"province\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"المحافظة (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"province\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"كود العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { field, form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ...field,\n                                                                                type: \"text\",\n                                                                                className: \"form-input-enhanced pr-10\",\n                                                                                placeholder: \"مثال: \".concat((codeStats === null || codeStats === void 0 ? void 0 : codeStats.nextCode) || \"p013621\"),\n                                                                                onChange: (e)=>{\n                                                                                    if (field === null || field === void 0 ? void 0 : field.onChange) {\n                                                                                        field.onChange(e);\n                                                                                    }\n                                                                                    if (e.target.value) {\n                                                                                        getSuggestions(e.target.value);\n                                                                                    } else {\n                                                                                        clearSuggestions();\n                                                                                    }\n                                                                                },\n                                                                                onBlur: (e)=>{\n                                                                                    if (field === null || field === void 0 ? void 0 : field.onBlur) {\n                                                                                        field.onBlur(e);\n                                                                                    }\n                                                                                    clearSuggestions();\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                                        children: suggestions.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                                                onClick: ()=>{\n                                                                                    // يمكن إضافة منطق لملء البيانات من العميل المقترح\n                                                                                    clearSuggestions();\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-blue-600\",\n                                                                                                children: customer.clientCode\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 488,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 489,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 487,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: customer.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 491,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowCodeGenerator(!showCodeGenerator),\n                                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 505,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"إعدادات الكود\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    const newCode = generateNewCustomerCode();\n                                                                                    form.setFieldValue(\"clientCode\", newCode);\n                                                                                },\n                                                                                className: \"flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 522,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 521,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \"إنشاء كود جديد\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showCodeGenerator && codeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: \"إحصائيات أكواد العملاء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 533,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"إجمالي العملاء:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 536,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-blue-600 mr-2\",\n                                                                                        children: codeStats.totalCustomers\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 537,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"الكود التالي:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 540,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-green-600 mr-2\",\n                                                                                        children: codeStats.nextCode\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 541,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مُنشأة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-purple-600 mr-2\",\n                                                                                        children: codeStats.generatedCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 543,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مخصصة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 548,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-orange-600 mr-2\",\n                                                                                        children: codeStats.customCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 549,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 leading-relaxed\",\n                                                                        children: \"تنسيق الكود: p + رقم المعاملة (01) + رقم العميل (3621). إذا تُرك فارغاً، سيتم إنشاء كود تلقائياً.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"clientCode\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"الخدمات المطلوبة\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-sm\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"اختر الخدمات التي يحتاجها العميل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: availableServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"service-card group cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                type: \"checkbox\",\n                                                                name: \"services\",\n                                                                value: service.value,\n                                                                className: \"sr-only\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"service-card-content\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"service-icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"service-label\",\n                                                                        children: service.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.value, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                name: \"services\",\n                                                component: \"div\",\n                                                className: \"error-message-enhanced mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-purple-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"التواصل والمبيعات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" وسيلة التواصل المفضلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3\",\n                                                        children: contactMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"contact-method-card group cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        type: \"radio\",\n                                                                        name: \"contactMethod\",\n                                                                        value: method.value,\n                                                                        className: \"sr-only\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"contact-method-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"contact-icon\",\n                                                                                children: [\n                                                                                    method.value === \"whatsapp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 637,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 636,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meta\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 641,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meeting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 647,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 646,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"phone\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 652,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 651,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"contact-label\",\n                                                                                children: method.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 656,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, method.value, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"contactMethod\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" اسم السيلز\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 673,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"salesperson\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.salesperson && touched.salesperson ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"اسم مندوب المبيعات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"salesperson\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" تصنيف العميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        as: \"select\",\n                                                                        name: \"initialCategory\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.initialCategory && touched.initialCategory ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: category.value,\n                                                                                children: category.label\n                                                                            }, category.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"initialCategory\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center items-center gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary-enhanced group w-full sm:w-auto\",\n                                        disabled: Object.keys(errors).length > 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"حفظ بيانات العميل والمتابعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"البيانات محفوظة محلياً وآمنة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientForm, \"DyazIHoIqx5zDl+vKaC04U3ySPg=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore\n    ];\n});\n_c = ClientForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientForm);\nvar _c;\n$RefreshReg$(_c, \"ClientForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClientForm.tsx\n"));

/***/ })

});