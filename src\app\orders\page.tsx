'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import CustomerOrdersTracker from '@/components/CustomerOrdersTracker';
import { exportToCSV, exportToExcel, printOrdersReport } from '@/utils/exportUtils';
import { useInvoiceStore } from '@/store/invoiceStore';

const OrdersPage: React.FC = () => {
  const router = useRouter();
  const { invoices } = useInvoiceStore();
  const [showExportOptions, setShowExportOptions] = useState(false);

  const handleBackToHome = () => {
    router.push('/');
  };

  // تحويل الفواتير إلى بيانات تتبع الطلبات للتصدير
  const getOrdersData = () => {
    return invoices.map((invoice) => {
      const patternService = invoice.services.find(s => s.serviceType === 'pattern');
      const manufacturingService = invoice.services.find(s => s.serviceType === 'manufacturing');

      let sizesCount = 0;
      if (patternService && 'models' in patternService) {
        sizesCount = patternService.models?.length || 0;
      } else if (manufacturingService && 'items' in manufacturingService) {
        sizesCount = manufacturingService.items?.length || 0;
      }

      let modelName = 'غير محدد';
      if (patternService && 'models' in patternService && patternService.models?.[0]) {
        modelName = patternService.models[0].modelName || 'غير محدد';
      } else if (manufacturingService && 'items' in manufacturingService && manufacturingService.items?.[0]) {
        modelName = manufacturingService.items[0].itemName || 'غير محدد';
      }

      const printingService = invoice.services.find(s => s.serviceType === 'pattern_printing');
      const printingCost = printingService && 'cost' in printingService ? printingService.cost : 0;

      return {
        id: `order-${invoice.id}`,
        invoiceId: invoice.id || '',
        date: invoice.createdAt,
        customerName: invoice.client.name,
        customerCode: invoice.client.clientCode || 'غير محدد',
        phone: invoice.client.phone,
        model: modelName,
        sizesCount,
        lastUpdate: invoice.createdAt,
        deposit: invoice.paidAmount,
        remaining: invoice.remainingAmount,
        printingCost,
        chat: invoice.client.contactMethod === 'whatsapp' ? 'واتساب' :
              invoice.client.contactMethod === 'meta' ? 'ميتا' :
              invoice.client.contactMethod === 'phone' ? 'هاتف' : 'مقابلة',
        salesperson: invoice.client.salesperson,
        status: invoice.paymentStatus === 'مدفوع بالكامل' ? 'تم التنفيذ' :
                invoice.paymentStatus === 'مدفوع جزئياً' ? 'تحت التشغيل' : 'في الانتظار',
        services: invoice.services.map(s => s.serviceType),
        totalAmount: invoice.total,
        notes: invoice.notes
      };
    });
  };

  const handleExportData = async (format: 'excel' | 'pdf' | 'csv' | 'statistics') => {
    try {
      const ordersData = getOrdersData();
      let result;

      switch (format) {
        case 'csv':
          result = exportOrdersToCSV(ordersData);
          break;
        case 'excel':
          result = exportOrdersToExcel(ordersData);
          break;
        case 'pdf':
          result = printOrdersReport(ordersData);
          break;
        case 'statistics':
          result = exportOrdersStatistics(ordersData);
          break;
        default:
          throw new Error('تنسيق غير مدعوم');
      }

      if (result.success) {
        alert('✅ ' + result.message);
      } else {
        alert('❌ ' + result.message);
      }
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('❌ حدث خطأ أثناء التصدير');
    }

    setShowExportOptions(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* شريط التنقل العلوي */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* الجانب الأيمن - العنوان والتنقل */}
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToHome}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd"/>
                </svg>
                <span>العودة للرئيسية</span>
              </button>
              
              <div className="h-6 w-px bg-gray-300"></div>
              
              <div className="flex items-center gap-2">
                <svg className="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
                </svg>
                <h1 className="text-xl font-bold text-gray-900">إدارة طلبات العملاء</h1>
              </div>
            </div>

            {/* الجانب الأيسر - أزرار الإجراءات */}
            <div className="flex items-center gap-3">
              {/* زر التصدير */}
              <div className="relative">
                <button
                  onClick={() => setShowExportOptions(!showExportOptions)}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                  تصدير البيانات
                </button>

                {/* قائمة خيارات التصدير */}
                {showExportOptions && (
                  <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <div className="py-1">
                      <button
                        onClick={() => handleExportData('excel')}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"/>
                        </svg>
                        تصدير Excel
                      </button>
                      <button
                        onClick={() => handleExportData('csv')}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                        </svg>
                        تصدير CSV
                      </button>
                      <button
                        onClick={() => handleExportData('pdf')}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 5a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
                        </svg>
                        طباعة التقرير
                      </button>
                      <div className="border-t border-gray-200 my-1"></div>
                      <button
                        onClick={() => handleExportData('statistics')}
                        className="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                        </svg>
                        تصدير الإحصائيات
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* زر إنشاء طلب جديد */}
              <button
                onClick={handleBackToHome}
                className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd"/>
                </svg>
                طلب جديد
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                    <path fillRule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div className="mr-4">
                <div className="text-sm font-medium text-gray-500">إجمالي الطلبات</div>
                <div className="text-2xl font-bold text-gray-900">--</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div className="mr-4">
                <div className="text-sm font-medium text-gray-500">قيد التنفيذ</div>
                <div className="text-2xl font-bold text-gray-900">--</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div className="mr-4">
                <div className="text-sm font-medium text-gray-500">مكتملة</div>
                <div className="text-2xl font-bold text-gray-900">--</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
              <div className="mr-4">
                <div className="text-sm font-medium text-gray-500">إجمالي المبيعات</div>
                <div className="text-2xl font-bold text-gray-900">-- جنيه</div>
              </div>
            </div>
          </div>
        </div>

        {/* لوحة تتبع الطلبات */}
        <CustomerOrdersTracker />
      </div>

      {/* إغلاق قائمة التصدير عند النقر خارجها */}
      {showExportOptions && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowExportOptions(false)}
        />
      )}
    </div>
  );
};

export default OrdersPage;
