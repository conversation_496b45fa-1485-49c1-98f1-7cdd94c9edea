<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار - infapapp</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }
        
        .button-test {
            margin: 15px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .button-test h3 {
            margin: 0 0 10px 0;
            color: #555;
            font-size: 18px;
        }
        
        .button-test p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🧪 اختبار أزرار infapapp</h1>
        
        <div class="instructions">
            <strong>تعليمات الاختبار:</strong><br>
            1. اضغط على كل زر أدناه<br>
            2. تحقق من ظهور رسالة النجاح<br>
            3. افتح وحدة التحكم (F12) لرؤية رسائل console.log<br>
            4. إذا لم تعمل الأزرار، فهناك مشكلة في JavaScript
        </div>
        
        <div class="button-test">
            <h3>1. زر إنشاء فاتورة</h3>
            <p>يجب أن يقوم بإعادة تعيين النموذج وبدء عملية إنشاء فاتورة جديدة</p>
            <button class="test-button" onclick="testCreateInvoice()">اختبار إنشاء فاتورة</button>
            <div id="create-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="button-test">
            <h3>2. زر إدارة الفواتير</h3>
            <p>يجب أن ينتقل إلى صفحة إدارة الفواتير</p>
            <button class="test-button" onclick="testManageInvoices()">اختبار إدارة الفواتير</button>
            <div id="manage-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="button-test">
            <h3>3. زر حماية البيانات</h3>
            <p>يجب أن ينتقل إلى صفحة حماية البيانات</p>
            <button class="test-button" onclick="testDataProtection()">اختبار حماية البيانات</button>
            <div id="protection-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="button-test">
            <h3>4. زر بداية جديدة</h3>
            <p>يجب أن يقوم بإعادة تعيين جميع البيانات والعودة للبداية</p>
            <button class="test-button" onclick="testStartOver()">اختبار بداية جديدة</button>
            <div id="startover-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="button-test">
            <h3>5. اختبار شامل</h3>
            <p>اختبار جميع الأزرار معاً</p>
            <button class="test-button" onclick="testAllButtons()" style="background: #2196F3;">اختبار شامل</button>
            <div id="all-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.style.display = 'block';
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                element.style.display = 'none';
            }, 3000);
        }
        
        function testCreateInvoice() {
            console.log('🧪 اختبار زر إنشاء فاتورة');
            console.log('➕ بدء إنشاء فاتورة جديدة');
            showStatus('create-status', '✅ زر إنشاء فاتورة يعمل بشكل صحيح!');
        }
        
        function testManageInvoices() {
            console.log('🧪 اختبار زر إدارة الفواتير');
            console.log('📋 الانتقال لصفحة إدارة الفواتير');
            showStatus('manage-status', '✅ زر إدارة الفواتير يعمل بشكل صحيح!');
        }
        
        function testDataProtection() {
            console.log('🧪 اختبار زر حماية البيانات');
            console.log('🛡️ الانتقال لصفحة حماية البيانات');
            showStatus('protection-status', '✅ زر حماية البيانات يعمل بشكل صحيح!');
        }
        
        function testStartOver() {
            console.log('🧪 اختبار زر بداية جديدة');
            console.log('🔄 بداية جديدة - إعادة تعيين النظام');
            showStatus('startover-status', '✅ زر بداية جديدة يعمل بشكل صحيح!');
        }
        
        function testAllButtons() {
            console.log('🧪 بدء الاختبار الشامل لجميع الأزرار');
            
            testCreateInvoice();
            setTimeout(() => testManageInvoices(), 500);
            setTimeout(() => testDataProtection(), 1000);
            setTimeout(() => testStartOver(), 1500);
            
            setTimeout(() => {
                showStatus('all-status', '🎉 جميع الأزرار تعمل بشكل صحيح!');
                console.log('✅ انتهى الاختبار الشامل - جميع الأزرار تعمل!');
            }, 2000);
        }
        
        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة اختبار الأزرار');
        console.log('📝 استخدم الأزرار أعلاه لاختبار وظائف التطبيق');
    </script>
</body>
</html>
