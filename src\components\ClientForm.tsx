'use client';

import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Client } from '@/types';
import { useInvoiceStore } from '@/store/invoiceStore';
import { useCustomerStore } from '@/store/customerStore';
import {
  validateCustomerCode,
  getCustomerCodeStats,
  validateCustomerUniqueness
} from '@/utils/customerCodeGenerator';

const availableServices = [
  { value: 'consultation', label: 'استشارات' },
  { value: 'pattern', label: 'باترون' },
  { value: 'pattern_printing', label: 'طباعة باترون' },
  { value: 'manufacturing', label: 'تصنيع' },
  { value: 'samples', label: 'عينات' },
  { value: 'shipping', label: 'شحن/تعبئة' },
  { value: 'marketing', label: 'تسويق' },
  { value: 'photography', label: 'تصوير' },
  { value: 'products', label: 'منتجات' },
  { value: 'raw_purchases', label: 'مشتريات خام' },
];

const contactMethods = [
  { value: 'whatsapp', label: 'واتساب' },
  { value: 'meta', label: 'ميتا' },
  { value: 'meeting', label: 'مقابلة' },
  { value: 'phone', label: 'هاتف' },
];

const categories = [
  { value: 'A', label: 'فئة A' },
  { value: 'B', label: 'فئة B' },
  { value: 'C', label: 'فئة C' },
];

const validationSchema = Yup.object({
  name: Yup.string().required('اسم العميل مطلوب'),
  phone: Yup.string().required('رقم الموبايل مطلوب'),
  services: Yup.array().min(1, 'يجب اختيار خدمة واحدة على الأقل'),
  contactMethod: Yup.string().required('وسيلة التواصل مطلوبة'),
  salesperson: Yup.string().required('اسم السيلز مطلوب'),
  initialCategory: Yup.string().required('تصنيف العميل مطلوب'),
  clientCode: Yup.string()
    .test('valid-code', 'تنسيق كود العميل غير صحيح', function(value) {
      if (!value) return true; // الكود اختياري
      const validation = validateCustomerCode(value);
      return validation.isValid;
    }),
}).test('unique-customer', 'بيانات العميل', function(values) {
  const { name, phone, clientCode } = values;
  if (!name || !phone) return true; // سيتم التحقق من هذه الحقول بشكل منفصل

  const validation = validateCustomerUniqueness(
    { name, phone, clientCode },
    this.options.context?.excludeId
  );

  if (!validation.isValid) {
    return this.createError({
      path: 'name', // أو يمكن استخدام path عام
      message: validation.errors.join(', ')
    });
  }

  return true;
});

interface ClientFormProps {
  onSubmit: (client: Client) => void;
  initialValues?: Partial<Client>;
}

const ClientForm: React.FC<ClientFormProps> = ({ onSubmit, initialValues }) => {
  const setCurrentClient = useInvoiceStore(state => state.setCurrentClient);
  const { generateNewCustomerCode, validateCode, getSuggestions, suggestions, clearSuggestions, customers } = useCustomerStore();
  const [showCodeGenerator, setShowCodeGenerator] = useState(false);
  const [codeStats, setCodeStats] = useState<any>(null);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);
  const [validationSuggestions, setValidationSuggestions] = useState<string[]>([]);
  const [showExistingCustomers, setShowExistingCustomers] = useState(false);
  const [selectedExistingCustomer, setSelectedExistingCustomer] = useState<Client | null>(null);

  useEffect(() => {
    const stats = getCustomerCodeStats();
    setCodeStats(stats);
  }, []);

  // التحقق من التكرار عند تغيير البيانات
  const checkForDuplicates = (name: string, phone: string, clientCode?: string) => {
    if (!name.trim() || !phone.trim()) {
      setValidationWarnings([]);
      setValidationSuggestions([]);
      return;
    }

    const validation = validateCustomerUniqueness(
      { name: name.trim(), phone: phone.trim(), clientCode },
      initialValues?.id
    );

    setValidationWarnings(validation.warnings);
    setValidationSuggestions(validation.suggestions || []);
  };

  // اختيار عميل موجود
  const handleSelectExistingCustomer = (customer: Client, setValues: any) => {
    setSelectedExistingCustomer(customer);
    setValues({
      ...customer,
      services: customer.services || []
    });
    setShowExistingCustomers(false);
    setValidationWarnings([]);
    setValidationSuggestions([]);
  };

  const defaultValues: Client = {
    name: '',
    phone: '',
    services: [],
    contactMethod: 'whatsapp',
    salesperson: '',
    initialCategory: 'A',
    businessName: '',
    province: '',
    clientCode: '',
    ...initialValues,
  };

  const handleSubmit = (values: Client) => {
    // إذا لم يتم تحديد كود العميل، إنشاء كود تلقائي
    const finalValues = {
      ...values,
      clientCode: values.clientCode || generateNewCustomerCode(),
    };

    setCurrentClient(finalValues);
    onSubmit(finalValues);
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm">
        <div className="flex items-center gap-4 mb-4">
          <div className="bg-blue-600 p-3 rounded-full shadow-md">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">بيانات العميل</h2>
            <p className="text-gray-600 text-sm">أدخل المعلومات الأساسية للعميل لبدء إنشاء الفاتورة</p>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center gap-2 text-sm text-blue-600">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <span>الخطوة 1 من 3 - معلومات العميل</span>
        </div>
      </div>

      {/* تحذيرات واقتراحات التكرار */}
      {(validationWarnings.length > 0 || validationSuggestions.length > 0) && (
        <div className="space-y-3">
          {validationWarnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                </svg>
                <div>
                  <h4 className="font-medium text-yellow-800 mb-2">تحذير - عميل مشابه موجود</h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {validationWarnings.map((warning, index) => (
                      <li key={index}>• {warning}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {validationSuggestions.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                </svg>
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">اقتراحات</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {validationSuggestions.map((suggestion, index) => (
                      <li key={index}>• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <Formik
        initialValues={defaultValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, errors, touched, setValues }) => (
          <Form className="space-y-8">
            {/* قسم اختيار عميل موجود */}
            <div className="bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-200 overflow-hidden">
              <div className="bg-gradient-to-l from-blue-100 to-indigo-100 px-6 py-4 border-b border-blue-200">
                <h3 className="text-lg font-semibold text-blue-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                  </svg>
                  اختيار عميل موجود أو إضافة عميل جديد
                </h3>
                <p className="text-sm text-blue-600 mt-1">يمكنك اختيار عميل من قاعدة البيانات أو إضافة عميل جديد</p>
              </div>

              <div className="p-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    type="button"
                    onClick={() => setShowExistingCustomers(!showExistingCustomers)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 hover:shadow-lg hover:scale-105 active:scale-95"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    {showExistingCustomers ? 'إخفاء العملاء الموجودين' : 'اختيار من العملاء الموجودين'}
                  </button>

                  {selectedExistingCustomer && (
                    <div className="flex-1 bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-3">
                      <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                      </svg>
                      <div>
                        <p className="text-green-800 font-medium">{selectedExistingCustomer.name}</p>
                        <p className="text-green-600 text-sm">{selectedExistingCustomer.clientCode} - {selectedExistingCustomer.phone}</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          setSelectedExistingCustomer(null);
                          setValues(defaultValues);
                        }}
                        className="text-green-600 hover:text-green-800 p-1"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                        </svg>
                      </button>
                    </div>
                  )}
                </div>

                {/* قائمة العملاء الموجودين */}
                {showExistingCustomers && (
                  <div className="mt-6 bg-white rounded-lg border border-gray-200 max-h-96 overflow-y-auto">
                    <div className="p-4 border-b border-gray-200 bg-gray-50">
                      <h4 className="font-medium text-gray-800">العملاء المسجلين ({customers.length})</h4>
                    </div>
                    <div className="divide-y divide-gray-200">
                      {customers.length === 0 ? (
                        <div className="p-6 text-center text-gray-500">
                          <svg className="w-12 h-12 mx-auto mb-3 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                          </svg>
                          <p>لا يوجد عملاء مسجلين بعد</p>
                          <p className="text-sm mt-1">ابدأ بإضافة عميل جديد أدناه</p>
                        </div>
                      ) : (
                        customers.map((customer) => (
                          <div
                            key={customer.id}
                            className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => handleSelectExistingCustomer(customer, setValues)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span className="text-blue-600 font-medium text-sm">
                                      {customer.name.charAt(0)}
                                    </span>
                                  </div>
                                  <div>
                                    <h5 className="font-medium text-gray-900">{customer.name}</h5>
                                    <div className="flex items-center gap-4 text-sm text-gray-500">
                                      <span>{customer.clientCode}</span>
                                      <span>{customer.phone}</span>
                                      <span>{customer.businessName}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm text-gray-500">
                                  {customer.services.length} خدمة
                                </div>
                                <div className="text-xs text-gray-400">
                                  {customer.salesperson}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Basic Information Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd"/>
                  </svg>
                  المعلومات الأساسية
                </h3>
              </div>

              <div className="p-6 space-y-6">
                {/* اسم العميل */}
                <div className="form-field group">
                  <label className="form-label-enhanced">
                    <span className="text-red-500">*</span> اسم العميل
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <Field name="name">
                      {({ field }: any) => (
                        <input
                          {...field}
                          type="text"
                          className={`form-input-enhanced pr-10 ${errors.name && touched.name ? 'border-red-300 focus:ring-red-500' : ''}`}
                          placeholder="أدخل اسم العميل الكامل"
                          onChange={(e) => {
                            field.onChange(e);
                            checkForDuplicates(e.target.value, values.phone, values.clientCode);
                          }}
                        />
                      )}
                    </Field>
                  </div>
                  <ErrorMessage name="name" component="div" className="error-message-enhanced" />
                </div>

                {/* رقم الموبايل */}
                <div className="form-field group">
                  <label className="form-label-enhanced">
                    <span className="text-red-500">*</span> رقم الموبايل
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                      </svg>
                    </div>
                    <Field name="phone">
                      {({ field }: any) => (
                        <input
                          {...field}
                          type="tel"
                          className={`form-input-enhanced pr-10 ${errors.phone && touched.phone ? 'border-red-300 focus:ring-red-500' : ''}`}
                          placeholder="01xxxxxxxxx"
                          onChange={(e) => {
                            field.onChange(e);
                            checkForDuplicates(values.name, e.target.value, values.clientCode);
                          }}
                        />
                      )}
                    </Field>
                  </div>
                  <ErrorMessage name="phone" component="div" className="error-message-enhanced" />
                </div>

                {/* الاسم التجاري والمحافظة في صف واحد */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-field group">
                    <label className="form-label-enhanced">الاسم التجاري</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <Field
                        name="businessName"
                        type="text"
                        className="form-input-enhanced pr-10"
                        placeholder="اسم الشركة أو المتجر (اختياري)"
                      />
                    </div>
                    <ErrorMessage name="businessName" component="div" className="error-message-enhanced" />
                  </div>

                  <div className="form-field group">
                    <label className="form-label-enhanced">المحافظة</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <Field
                        name="province"
                        type="text"
                        className="form-input-enhanced pr-10"
                        placeholder="المحافظة (اختياري)"
                      />
                    </div>
                    <ErrorMessage name="province" component="div" className="error-message-enhanced" />
                  </div>
                </div>

                {/* كود العميل */}
                <div className="form-field group">
                  <label className="form-label-enhanced">كود العميل</label>
                  <div className="space-y-3">
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <Field name="clientCode">
                        {({ field, form }: any) => (
                          <input
                            {...field}
                            type="text"
                            className="form-input-enhanced pr-10"
                            placeholder={`مثال: ${codeStats?.nextCode || 'p013621'}`}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.value) {
                                getSuggestions(e.target.value);
                              } else {
                                clearSuggestions();
                              }
                            }}
                            onBlur={() => {
                              field.onBlur();
                              clearSuggestions();
                            }}
                          />
                        )}
                      </Field>

                      {/* اقتراحات الأكواد */}
                      {suggestions.length > 0 && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                          {suggestions.map((customer, index) => (
                            <div
                              key={customer.id}
                              className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                              onClick={() => {
                                // يمكن إضافة منطق لملء البيانات من العميل المقترح
                                clearSuggestions();
                              }}
                            >
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-blue-600">{customer.clientCode}</span>
                                <span className="text-sm text-gray-600">{customer.name}</span>
                              </div>
                              <div className="text-xs text-gray-500">{customer.phone}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* أزرار إدارة الكود */}
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={() => setShowCodeGenerator(!showCodeGenerator)}
                        className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
                        </svg>
                        إعدادات الكود
                      </button>

                      <Field name="clientCode">
                        {({ form }: any) => (
                          <button
                            type="button"
                            onClick={() => {
                              const newCode = generateNewCustomerCode();
                              form.setFieldValue('clientCode', newCode);
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd"/>
                            </svg>
                            إنشاء كود جديد
                          </button>
                        )}
                      </Field>
                    </div>

                    {/* معلومات الكود */}
                    {showCodeGenerator && codeStats && (
                      <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                        <h4 className="font-medium text-gray-800">إحصائيات أكواد العملاء</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">إجمالي العملاء:</span>
                            <span className="font-medium text-blue-600 mr-2">{codeStats.totalCustomers}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">الكود التالي:</span>
                            <span className="font-medium text-green-600 mr-2">{codeStats.nextCode}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">أكواد مُنشأة:</span>
                            <span className="font-medium text-purple-600 mr-2">{codeStats.generatedCodes}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">أكواد مخصصة:</span>
                            <span className="font-medium text-orange-600 mr-2">{codeStats.customCodes}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-start gap-2">
                      <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                      </svg>
                      <p className="text-xs text-blue-600 leading-relaxed">
                        تنسيق الكود: p + رقم المعاملة (01) + رقم العميل (3621). إذا تُرك فارغاً، سيتم إنشاء كود تلقائياً.
                      </p>
                    </div>
                  </div>
                  <ErrorMessage name="clientCode" component="div" className="error-message-enhanced" />
                </div>
              </div>
            </div>

            {/* Services Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  الخدمات المطلوبة
                  <span className="text-red-500 text-sm">*</span>
                </h3>
                <p className="text-sm text-gray-600 mt-1">اختر الخدمات التي يحتاجها العميل</p>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableServices.map((service) => (
                    <label key={service.value} className="service-card group cursor-pointer">
                      <Field
                        type="checkbox"
                        name="services"
                        value={service.value}
                        className="sr-only"
                      />
                      <div className="service-card-content">
                        <div className="service-icon">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                          </svg>
                        </div>
                        <span className="service-label">{service.label}</span>
                      </div>
                    </label>
                  ))}
                </div>
                <ErrorMessage name="services" component="div" className="error-message-enhanced mt-4" />
              </div>
            </div>

            {/* Contact & Sales Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  التواصل والمبيعات
                </h3>
              </div>

              <div className="p-6 space-y-6">
                {/* وسيلة التواصل */}
                <div className="form-field">
                  <label className="form-label-enhanced">
                    <span className="text-red-500">*</span> وسيلة التواصل المفضلة
                  </label>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3">
                    {contactMethods.map((method) => (
                      <label key={method.value} className="contact-method-card group cursor-pointer">
                        <Field
                          type="radio"
                          name="contactMethod"
                          value={method.value}
                          className="sr-only"
                        />
                        <div className="contact-method-content">
                          <div className="contact-icon">
                            {method.value === 'whatsapp' && (
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                              </svg>
                            )}
                            {method.value === 'meta' && (
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                              </svg>
                            )}
                            {method.value === 'meeting' && (
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                              </svg>
                            )}
                            {method.value === 'phone' && (
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                              </svg>
                            )}
                          </div>
                          <span className="contact-label">{method.label}</span>
                        </div>
                      </label>
                    ))}
                  </div>
                  <ErrorMessage name="contactMethod" component="div" className="error-message-enhanced" />
                </div>

                {/* اسم السيلز وتصنيف العميل */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-field group">
                    <label className="form-label-enhanced">
                      <span className="text-red-500">*</span> اسم السيلز
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <Field
                        name="salesperson"
                        type="text"
                        className={`form-input-enhanced pr-10 ${errors.salesperson && touched.salesperson ? 'border-red-300 focus:ring-red-500' : ''}`}
                        placeholder="اسم مندوب المبيعات"
                      />
                    </div>
                    <ErrorMessage name="salesperson" component="div" className="error-message-enhanced" />
                  </div>

                  <div className="form-field group">
                    <label className="form-label-enhanced">
                      <span className="text-red-500">*</span> تصنيف العميل
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <Field
                        as="select"
                        name="initialCategory"
                        className={`form-input-enhanced pr-10 ${errors.initialCategory && touched.initialCategory ? 'border-red-300 focus:ring-red-500' : ''}`}
                      >
                        {categories.map((category) => (
                          <option key={category.value} value={category.value}>
                            {category.label}
                          </option>
                        ))}
                      </Field>
                    </div>
                    <ErrorMessage name="initialCategory" component="div" className="error-message-enhanced" />
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 pt-6">
              <button
                type="submit"
                className="btn-primary-enhanced group w-full sm:w-auto"
                disabled={Object.keys(errors).length > 0}
              >
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
                حفظ بيانات العميل والمتابعة
              </button>

              <div className="flex items-center gap-2 text-sm text-gray-500">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd"/>
                </svg>
                <span>البيانات محفوظة محلياً وآمنة</span>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default ClientForm;
