'use client';

/**
 * صفحة تفاصيل العميل الشاملة
 */

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Client, Invoice } from '@/types';
import { useCustomerStore } from '@/store/customerStore';
import { useInvoiceStore } from '@/store/invoiceStore';
import ClientForm from '@/components/ClientForm';

type ViewMode = 'details' | 'edit';

const CustomerDetailsPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const customerId = params.id as string;

  const {
    customers,
    updateCustomer,
    deleteCustomer,
    isLoading,
    error,
  } = useCustomerStore();

  const { invoices, setCurrentClient } = useInvoiceStore();

  const [customer, setCustomer] = useState<Client | null>(null);
  const [customerInvoices, setCustomerInvoices] = useState<Invoice[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('details');
  const [stats, setStats] = useState({
    totalInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    remainingAmount: 0,
    lastInvoiceDate: '',
    averageInvoiceValue: 0,
  });

  useEffect(() => {
    // البحث عن العميل
    const foundCustomer = customers.find(c => c.id === customerId);
    if (foundCustomer) {
      setCustomer(foundCustomer);
      
      // البحث عن فواتير العميل
      const relatedInvoices = invoices.filter(invoice => 
        invoice.client.phone === foundCustomer.phone || 
        (foundCustomer.clientCode && invoice.client.clientCode === foundCustomer.clientCode)
      );
      
      setCustomerInvoices(relatedInvoices);
      
      // حساب الإحصائيات
      calculateStats(relatedInvoices);
    }
  }, [customerId, customers, invoices]);

  const calculateStats = (invoices: Invoice[]) => {
    if (invoices.length === 0) {
      setStats({
        totalInvoices: 0,
        totalAmount: 0,
        paidAmount: 0,
        remainingAmount: 0,
        lastInvoiceDate: '',
        averageInvoiceValue: 0,
      });
      return;
    }

    const totalAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);
    const paidAmount = invoices.reduce((sum, inv) => sum + inv.paidAmount, 0);
    const remainingAmount = totalAmount - paidAmount;
    
    // ترتيب الفواتير حسب التاريخ للحصول على آخر فاتورة
    const sortedInvoices = [...invoices].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    const lastInvoiceDate = sortedInvoices[0]?.createdAt || '';
    const averageInvoiceValue = totalAmount / invoices.length;

    setStats({
      totalInvoices: invoices.length,
      totalAmount,
      paidAmount,
      remainingAmount,
      lastInvoiceDate,
      averageInvoiceValue,
    });
  };

  const handleUpdateCustomer = async (customerData: Client) => {
    if (customer?.id) {
      const result = await updateCustomer(customer.id, customerData);
      if (result.success) {
        setCustomer(customerData);
        setViewMode('details');
      }
    }
  };

  const handleDeleteCustomer = async () => {
    if (customer?.id && confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع بياناته.')) {
      const result = await deleteCustomer(customer.id);
      if (result.success) {
        router.push('/customers');
      }
    }
  };

  const handleCreateInvoice = () => {
    if (customer) {
      setCurrentClient(customer);
      router.push('/');
    }
  };

  const handleBackToCustomers = () => {
    router.push('/customers');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} جنيه`;
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'مدفوع بالكامل':
        return 'text-green-600 bg-green-100';
      case 'مدفوع جزئياً':
        return 'text-yellow-600 bg-yellow-100';
      case 'غير مدفوع':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getContactMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      whatsapp: 'واتساب',
      meta: 'ميتا',
      meeting: 'مقابلة',
      phone: 'هاتف',
    };
    return methods[method] || method;
  };

  const getServiceNames = (services: string[]) => {
    const serviceMap: { [key: string]: string } = {
      'consultation': 'استشارات',
      'pattern': 'باترون',
      'manufacturing': 'تصنيع',
      'shipping': 'شحن',
      'marketing': 'تسويق',
      'photography': 'تصوير',
      'products': 'منتجات',
      'raw_purchases': 'مواد خام',
      'pattern_printing': 'طباعة باترون',
      'samples': 'عينات',
    };
    
    return services.map(service => serviceMap[service] || service).join(', ');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات العميل...</p>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 p-4 rounded-lg">
            <h2 className="text-xl font-bold text-red-800 mb-2">العميل غير موجود</h2>
            <p className="text-red-600 mb-4">لم يتم العثور على العميل المطلوب</p>
            <button
              onClick={handleBackToCustomers}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              العودة لقائمة العملاء
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (viewMode === 'edit') {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-800">تعديل بيانات العميل</h1>
              <button
                onClick={() => setViewMode('details')}
                className="text-gray-600 hover:text-gray-800"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
              </button>
            </div>
            <ClientForm
              onSubmit={handleUpdateCustomer}
              initialValues={customer}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToCustomers}
                className="text-gray-600 hover:text-gray-800"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">{customer.name}</h1>
                <p className="text-gray-600">تفاصيل العميل الشاملة</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={handleCreateInvoice}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd"/>
                </svg>
                إنشاء فاتورة
              </button>
              <button
                onClick={() => setViewMode('edit')}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                </svg>
                تعديل
              </button>
              <button
                onClick={handleDeleteCustomer}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd"/>
                  <path fillRule="evenodd" d="M10 5a2 2 0 00-2 2v6a2 2 0 004 0V7a2 2 0 00-2-2z" clipRule="evenodd"/>
                </svg>
                حذف
              </button>
            </div>
          </div>
        </div>

        {/* Customer Info & Stats Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Customer Basic Info */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">معلومات العميل الأساسية</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">الاسم</label>
                <p className="text-lg font-semibold text-gray-800">{customer.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">رقم الهاتف</label>
                <p className="text-lg font-semibold text-gray-800">{customer.phone}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">كود العميل</label>
                <p className="text-lg font-mono font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  {customer.clientCode || 'غير محدد'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">وسيلة التواصل</label>
                <p className="text-lg font-semibold text-gray-800">{getContactMethodName(customer.contactMethod)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">اسم المصنع/البراند</label>
                <p className="text-lg font-semibold text-gray-800">{customer.businessName || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">المحافظة</label>
                <p className="text-lg font-semibold text-gray-800">{customer.province || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">اسم السيلز</label>
                <p className="text-lg font-semibold text-gray-800">{customer.salesperson}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">التصنيف الأولي</label>
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                  customer.initialCategory === 'A' ? 'bg-green-100 text-green-800' :
                  customer.initialCategory === 'B' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {customer.initialCategory}
                </span>
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-600 mb-2">الخدمات المطلوبة</label>
              <div className="flex flex-wrap gap-2">
                {customer.services.map((service, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                  >
                    {getServiceNames([service])}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Financial Stats */}
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">الإحصائيات المالية</h2>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-600 font-medium">إجمالي الفواتير</p>
                <p className="text-2xl font-bold text-blue-800">{stats.totalInvoices}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-600 font-medium">إجمالي المبلغ</p>
                <p className="text-2xl font-bold text-green-800">{formatCurrency(stats.totalAmount)}</p>
              </div>
              <div className="bg-emerald-50 p-4 rounded-lg">
                <p className="text-sm text-emerald-600 font-medium">المبلغ المدفوع</p>
                <p className="text-2xl font-bold text-emerald-800">{formatCurrency(stats.paidAmount)}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <p className="text-sm text-red-600 font-medium">المبلغ المتبقي</p>
                <p className="text-2xl font-bold text-red-800">{formatCurrency(stats.remainingAmount)}</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <p className="text-sm text-purple-600 font-medium">متوسط قيمة الفاتورة</p>
                <p className="text-2xl font-bold text-purple-800">{formatCurrency(stats.averageInvoiceValue)}</p>
              </div>
              {stats.lastInvoiceDate && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600 font-medium">آخر فاتورة</p>
                  <p className="text-lg font-bold text-gray-800">{formatDate(stats.lastInvoiceDate)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Invoices History */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-800">تاريخ الفواتير</h2>
            <div className="text-sm text-gray-600">
              {customerInvoices.length} فاتورة
            </div>
          </div>

          {customerInvoices.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd"/>
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">لا توجد فواتير</h3>
              <p className="text-gray-600 mb-4">لم يتم إنشاء أي فواتير لهذا العميل بعد</p>
              <button
                onClick={handleCreateInvoice}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                إنشاء أول فاتورة
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">رقم الفاتورة</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">التاريخ</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الخدمات</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">المبلغ الإجمالي</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">المدفوع</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">المتبقي</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">حالة الدفع</th>
                    <th className="text-right py-3 px-4 font-semibold text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {customerInvoices.map((invoice, index) => (
                    <tr key={invoice.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                          {invoice.id?.slice(-8) || `INV-${index + 1}`}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-700">
                        {formatDate(invoice.createdAt)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex flex-wrap gap-1">
                          {invoice.services.slice(0, 2).map((service, idx) => (
                            <span
                              key={idx}
                              className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                            >
                              {getServiceNames([service.serviceType])}
                            </span>
                          ))}
                          {invoice.services.length > 2 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                              +{invoice.services.length - 2}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4 font-semibold text-gray-800">
                        {formatCurrency(invoice.total)}
                      </td>
                      <td className="py-3 px-4 font-semibold text-green-600">
                        {formatCurrency(invoice.paidAmount)}
                      </td>
                      <td className="py-3 px-4 font-semibold text-red-600">
                        {formatCurrency(invoice.remainingAmount)}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getPaymentStatusColor(invoice.paymentStatus)}`}>
                          {invoice.paymentStatus}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => router.push(`/?invoice=${invoice.id}`)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          عرض
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerDetailsPage;
