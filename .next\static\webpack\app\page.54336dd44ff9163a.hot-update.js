"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/services/SamplesService.tsx":
/*!****************************************************!*\
  !*** ./src/components/services/SamplesService.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SamplesServiceComponent = (param)=>{\n    let { namePrefix, values, setFieldValue } = param;\n    _s();\n    const serviceData = values[namePrefix];\n    const isEnabled = (serviceData === null || serviceData === void 0 ? void 0 : serviceData.enabled) || false;\n    const defaultSample = {\n        sampleName: \"\",\n        quantity: 1,\n        cost: 0,\n        fabricType: \"\",\n        color: \"\",\n        size: \"\",\n        deliveryDate: \"\",\n        notes: \"\",\n        sampleType: \"\",\n        urgencyLevel: \"normal\"\n    };\n    const sampleTypes = [\n        {\n            value: \"fabric\",\n            label: \"عينة قماش\"\n        },\n        {\n            value: \"garment\",\n            label: \"عينة ملبس\"\n        },\n        {\n            value: \"accessory\",\n            label: \"عينة إكسسوار\"\n        },\n        {\n            value: \"pattern\",\n            label: \"عينة باترون\"\n        },\n        {\n            value: \"color\",\n            label: \"عينة لون\"\n        },\n        {\n            value: \"texture\",\n            label: \"عينة ملمس\"\n        }\n    ];\n    const urgencyLevels = [\n        {\n            value: \"normal\",\n            label: \"عادي\"\n        },\n        {\n            value: \"urgent\",\n            label: \"مستعجل\"\n        },\n        {\n            value: \"very_urgent\",\n            label: \"مستعجل جداً\"\n        }\n    ];\n    // حساب المجموع الكلي\n    const calculateGrandTotal = ()=>{\n        if (!(serviceData === null || serviceData === void 0 ? void 0 : serviceData.samples)) return 0;\n        return serviceData.samples.reduce((total, sample)=>{\n            return total + (Number(sample.cost) || 0);\n        }, 0);\n    };\n    // تحديث المجموع الكلي\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (isEnabled) {\n            const total = calculateGrandTotal();\n            setFieldValue(\"\".concat(namePrefix, \".grandTotal\"), total);\n        }\n    }, [\n        serviceData === null || serviceData === void 0 ? void 0 : serviceData.samples,\n        isEnabled\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"form-section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                        type: \"checkbox\",\n                        name: \"\".concat(namePrefix, \".enabled\"),\n                        className: \"form-checkbox\",\n                        onChange: (e)=>{\n                            setFieldValue(\"\".concat(namePrefix, \".enabled\"), e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"\".concat(namePrefix, \".serviceType\"), \"samples\");\n                                setFieldValue(\"\".concat(namePrefix, \".id\"), Date.now().toString());\n                                setFieldValue(\"\".concat(namePrefix, \".samples\"), [\n                                    defaultSample\n                                ]);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"خدمة العينات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            isEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 pr-6 border-r-4 border-teal-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.FieldArray, {\n                        name: \"\".concat(namePrefix, \".samples\"),\n                        children: (param)=>{\n                            let { push, remove } = param;\n                            var _serviceData_samples;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-semibold\",\n                                                children: \"قائمة العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>push(defaultSample),\n                                                className: \"btn-primary text-sm px-3 py-1\",\n                                                children: \"إضافة عينة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    serviceData === null || serviceData === void 0 ? void 0 : (_serviceData_samples = serviceData.samples) === null || _serviceData_samples === void 0 ? void 0 : _serviceData_samples.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg mb-4 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"العينة \",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        serviceData.samples.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>remove(index),\n                                                            className: \"text-red-500 hover:text-red-700 text-sm\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"اسم العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleName\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"اسم العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 119,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.ErrorMessage, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleName\"),\n                                                                    component: \"div\",\n                                                                    className: \"error-message\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"نوع العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    as: \"select\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleType\"),\n                                                                    className: \"form-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر نوع العينة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                            lineNumber: 136,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        sampleTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: type.value,\n                                                                                children: type.label\n                                                                            }, type.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                                lineNumber: 138,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".quantity\"),\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"عدد العينات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"التكلفة (جنيه)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".cost\"),\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"تكلفة العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"نوع القماش\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".fabricType\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"نوع القماش\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"اللون\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".color\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"اللون المطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"المقاس\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".size\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"المقاس\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"مستوى الأولوية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    as: \"select\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".urgency\"),\n                                                                    className: \"form-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر مستوى الأولوية\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        urgencyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: level.value,\n                                                                                children: level.label\n                                                                            }, level.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                                lineNumber: 209,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"تاريخ التسليم المطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".deliveryDate\"),\n                                                                    type: \"date\",\n                                                                    className: \"form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"وصف العينة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".description\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"وصف تفصيلي للعينة المطلوبة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"متطلبات خاصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".specialRequirements\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"أي متطلبات خاصة للعينة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".withMeasurements\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مع القياسات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".withPattern\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مع الباترون\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".qualityCheck\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"فحص جودة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".returnRequired\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مطلوب الإرجاع\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".notes\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"أي ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"المجموع الكلي لجميع العينات:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: [\n                                            calculateGrandTotal().toFixed(2),\n                                            \" جنيه\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-green-600 mt-1\",\n                                children: \"شامل جميع العينات المطلوبة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-3\",\n                                children: \"معلومات عامة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"form-label\",\n                                                children: \"الغرض من العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                as: \"select\",\n                                                name: \"\".concat(namePrefix, \".purpose\"),\n                                                className: \"form-input\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر الغرض\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"client_approval\",\n                                                        children: \"موافقة العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"quality_test\",\n                                                        children: \"اختبار جودة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"color_matching\",\n                                                        children: \"مطابقة لون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"size_fitting\",\n                                                        children: \"اختبار مقاس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"production_reference\",\n                                                        children: \"مرجع إنتاج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"market_research\",\n                                                        children: \"بحث سوق\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"form-label\",\n                                                children: \"مسؤول العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                name: \"\".concat(namePrefix, \".sampleManager\"),\n                                                type: \"text\",\n                                                className: \"form-input\",\n                                                placeholder: \"اسم المسؤول عن العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-field\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"form-label\",\n                                        children: \"شروط خاصة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                        name: \"\".concat(namePrefix, \".specialConditions\"),\n                                        as: \"textarea\",\n                                        rows: \"3\",\n                                        className: \"form-input\",\n                                        placeholder: \"أي شروط خاصة بالعينات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SamplesServiceComponent, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = SamplesServiceComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SamplesServiceComponent);\nvar _c;\n$RefreshReg$(_c, \"SamplesServiceComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/services/SamplesService.tsx\n"));

/***/ })

});