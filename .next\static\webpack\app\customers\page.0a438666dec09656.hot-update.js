"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerStore: function() { return /* binding */ useCustomerStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n/**\n * تحميل العملاء من التخزين الآمن\n */ const loadCustomersFromStorage = ()=>{\n    const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    return [];\n};\n/**\n * حفظ العملاء في التخزين الآمن\n */ const saveCustomersToStorage = (customers)=>{\n    const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customers\", customers);\n    return result.success;\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return \"customer-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)((set, get)=>({\n        customers: loadCustomersFromStorage(),\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        // إضافة عملاء متعددين (للاستيراد)\n        addMultipleCustomers: async (customersData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                const newCustomers = [];\n                const errors = [];\n                for (const customerData of customersData){\n                    try {\n                        let clientCode = customerData.clientCode;\n                        // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد\n                        if (!clientCode) {\n                            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.generateCustomerCode)();\n                            clientCode = codeData.code;\n                        }\n                        // التحقق من عدم تكرار الكود\n                        const existingCustomer = [\n                            ...customers,\n                            ...newCustomers\n                        ].find((c)=>c.clientCode === clientCode);\n                        if (existingCustomer) {\n                            errors.push(\"كود العميل \".concat(clientCode, \" مستخدم بالفعل للعميل \").concat(customerData.name));\n                            continue;\n                        }\n                        const newCustomer = {\n                            ...customerData,\n                            id: generateCustomerId(),\n                            clientCode\n                        };\n                        newCustomers.push(newCustomer);\n                    } catch (error) {\n                        errors.push(\"خطأ في إضافة العميل \".concat(customerData.name, \": \").concat(error instanceof Error ? error.message : \"خطأ غير معروف\"));\n                    }\n                }\n                if (newCustomers.length > 0) {\n                    const updatedCustomers = [\n                        ...customers,\n                        ...newCustomers\n                    ];\n                    // حفظ في التخزين\n                    const saved = saveCustomersToStorage(updatedCustomers);\n                    if (!saved) {\n                        throw new Error(\"فشل في حفظ بيانات العملاء\");\n                    }\n                    set({\n                        customers: updatedCustomers,\n                        isLoading: false\n                    });\n                    return {\n                        success: true,\n                        imported: newCustomers.length,\n                        errors,\n                        customers: newCustomers\n                    };\n                } else {\n                    set({\n                        isLoading: false\n                    });\n                    return {\n                        success: false,\n                        error: \"لم يتم إضافة أي عميل\",\n                        imported: 0,\n                        errors\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage,\n                    imported: 0,\n                    errors: []\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                var _get_currentCustomer;\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: ((_get_currentCustomer = get().currentCustomer) === null || _get_currentCustomer === void 0 ? void 0 : _get_currentCustomer.id) === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const searchTerm = query.toLowerCase().trim();\n            // البحث الأساسي المحسن\n            const basicResults = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm) || customer.phone.includes(query) || customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm) || customer.businessName && customer.businessName.toLowerCase().includes(searchTerm) || customer.province && customer.province.toLowerCase().includes(searchTerm) || customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm) || customer.services.some((service)=>service.toLowerCase().includes(searchTerm)));\n            // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج\n            if (basicResults.length === 0 && searchTerm.length > 2) {\n                const fuzzyResults = customers.filter((customer)=>{\n                    const name = customer.name.toLowerCase();\n                    const words = searchTerm.split(\" \");\n                    return words.some((word)=>name.includes(word) && word.length > 1);\n                });\n                return fuzzyResults;\n            }\n            // ترتيب النتائج حسب الأولوية\n            return basicResults.sort((a, b)=>{\n                var _a_clientCode, _b_clientCode;\n                // أولوية للمطابقة الدقيقة في بداية الاسم\n                const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);\n                const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);\n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                // أولوية للمطابقة في كود العميل\n                const aCodeMatch = (_a_clientCode = a.clientCode) === null || _a_clientCode === void 0 ? void 0 : _a_clientCode.toLowerCase().includes(searchTerm);\n                const bCodeMatch = (_b_clientCode = b.clientCode) === null || _b_clientCode === void 0 ? void 0 : _b_clientCode.toLowerCase().includes(searchTerm);\n                if (aCodeMatch && !bCodeMatch) return -1;\n                if (!aCodeMatch && bCodeMatch) return 1;\n                // ترتيب أبجدي كافتراضي\n                return a.name.localeCompare(b.name, \"ar\");\n            });\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/customerStore.ts\n"));

/***/ })

});