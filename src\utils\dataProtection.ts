/**
 * نظام حماية البيانات والمراقبة التلقائية
 * يوفر مراقبة مستمرة ونسخ احتياطية تلقائية
 */

import { createBackup, checkStorageHealth, restoreFromBackup, STORAGE_CONFIG } from './secureStorage';

// حالة النظام
interface SystemStatus {
  isMonitoring: boolean;
  lastBackup: string | null;
  lastHealthCheck: string | null;
  alertsCount: number;
  autoBackupEnabled: boolean;
}

// نوع التنبيهات
interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

class DataProtectionSystem {
  private status: SystemStatus = {
    isMonitoring: false,
    lastBackup: null,
    lastHealthCheck: null,
    alertsCount: 0,
    autoBackupEnabled: true,
  };

  private alerts: Alert[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private backupInterval: NodeJS.Timeout | null = null;

  /**
   * بدء نظام الحماية
   */
  public start(): void {
    if (this.status.isMonitoring) {
      console.log('🛡️ Data protection system already running');
      return;
    }

    console.log('🚀 Starting data protection system...');
    
    // بدء المراقبة
    this.startMonitoring();
    
    // بدء النسخ الاحتياطي التلقائي
    if (this.status.autoBackupEnabled) {
      this.startAutoBackup();
    }

    // فحص فوري للحالة
    this.performHealthCheck();

    this.status.isMonitoring = true;
    console.log('✅ Data protection system started successfully');
  }

  /**
   * إيقاف نظام الحماية
   */
  public stop(): void {
    if (!this.status.isMonitoring) {
      return;
    }

    console.log('🛑 Stopping data protection system...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }

    this.status.isMonitoring = false;
    console.log('✅ Data protection system stopped');
  }

  /**
   * بدء المراقبة المستمرة
   */
  private startMonitoring(): void {
    // فحص كل دقيقة
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck();
    }, 60 * 1000);
  }

  /**
   * بدء النسخ الاحتياطي التلقائي
   */
  private startAutoBackup(): void {
    // نسخ احتياطي كل 5 دقائق
    this.backupInterval = setInterval(() => {
      this.performAutoBackup();
    }, STORAGE_CONFIG.BACKUP_INTERVAL);
  }

  /**
   * تنفيذ فحص الحالة
   */
  private performHealthCheck(): void {
    try {
      const health = checkStorageHealth();
      this.status.lastHealthCheck = new Date().toISOString();

      // معالجة المشاكل المكتشفة
      if (!health.isHealthy) {
        health.issues.forEach(issue => {
          this.addAlert('warning', `مشكلة في النظام: ${issue}`);
        });

        // إجراءات تلقائية للمشاكل الحرجة
        if (health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {
          this.handleStorageOverflow();
        }
      }

      // تنبيه إذا لم تكن هناك نسخ احتياطية
      if (health.backupsCount === 0) {
        this.addAlert('error', 'لا توجد نسخ احتياطية - سيتم إنشاء نسخة فورية');
        this.performAutoBackup();
      }

      console.log('🔍 Health check completed:', {
        healthy: health.isHealthy,
        issues: health.issues.length,
        storageUsed: Math.round(health.storageUsed / 1024) + 'KB',
        backups: health.backupsCount,
      });

    } catch (error) {
      this.addAlert('error', 'فشل في فحص حالة النظام');
      console.error('❌ Health check failed:', error);
    }
  }

  /**
   * تنفيذ نسخ احتياطي تلقائي
   */
  private performAutoBackup(): void {
    try {
      // فحص إذا كانت هناك تغييرات تستدعي النسخ الاحتياطي
      if (this.shouldCreateBackup()) {
        const result = createBackup();
        
        if (result.success) {
          this.status.lastBackup = new Date().toISOString();
          console.log('💾 Auto backup completed successfully');
        } else {
          this.addAlert('error', `فشل النسخ الاحتياطي التلقائي: ${result.message}`);
        }
      }
    } catch (error) {
      this.addAlert('error', 'خطأ في النسخ الاحتياطي التلقائي');
      console.error('❌ Auto backup failed:', error);
    }
  }

  /**
   * فحص إذا كان النسخ الاحتياطي مطلوب
   */
  private shouldCreateBackup(): boolean {
    // إنشاء نسخة احتياطية إذا:
    // 1. لا توجد نسخة سابقة
    // 2. مر أكثر من 5 دقائق على آخر نسخة
    // 3. تم تعديل البيانات مؤخراً

    if (!this.status.lastBackup) {
      return true;
    }

    const lastBackupTime = new Date(this.status.lastBackup).getTime();
    const now = Date.now();
    const timeDiff = now - lastBackupTime;

    return timeDiff > STORAGE_CONFIG.BACKUP_INTERVAL;
  }

  /**
   * معالجة امتلاء التخزين
   */
  private handleStorageOverflow(): void {
    try {
      console.log('⚠️ Storage overflow detected, attempting cleanup...');

      // تنظيف النسخ الاحتياطية القديمة أولاً
      const backupKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('backup-'))
        .sort((a, b) => {
          const timeA = parseInt(a.replace('backup-', ''));
          const timeB = parseInt(b.replace('backup-', ''));
          return timeA - timeB; // ترتيب تصاعدي (الأقدم أولاً)
        });

      // حذف النصف الأقدم من النسخ الاحتياطية
      const toDelete = backupKeys.slice(0, Math.floor(backupKeys.length / 2));
      toDelete.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ Removed old backup due to storage overflow:', key);
      });

      // تنظيف البيانات المؤقتة
      const tempKeys = Object.keys(localStorage).filter(key => 
        key.startsWith('temp-') || 
        key.startsWith('cache-') ||
        key.startsWith('draft-')
      );
      
      tempKeys.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ Removed temporary data:', key);
      });

      this.addAlert('info', 'تم تنظيف التخزين تلقائياً لتوفير مساحة');

    } catch (error) {
      this.addAlert('error', 'فشل في تنظيف التخزين');
      console.error('❌ Storage cleanup failed:', error);
    }
  }

  /**
   * إضافة تنبيه
   */
  private addAlert(type: Alert['type'], message: string): void {
    const alert: Alert = {
      id: Date.now().toString(),
      type,
      message,
      timestamp: new Date().toISOString(),
      resolved: false,
    };

    this.alerts.unshift(alert);
    this.status.alertsCount++;

    // الاحتفاظ بآخر 50 تنبيه فقط
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(0, 50);
    }

    console.log(`🚨 Alert [${type.toUpperCase()}]: ${message}`);
  }

  /**
   * الحصول على حالة النظام
   */
  public getStatus(): SystemStatus & { alerts: Alert[] } {
    return {
      ...this.status,
      alerts: this.alerts.filter(alert => !alert.resolved),
    };
  }

  /**
   * حل تنبيه
   */
  public resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.status.alertsCount = Math.max(0, this.status.alertsCount - 1);
    }
  }

  /**
   * تفعيل/إلغاء النسخ الاحتياطي التلقائي
   */
  public toggleAutoBackup(enabled: boolean): void {
    this.status.autoBackupEnabled = enabled;
    
    if (enabled && this.status.isMonitoring) {
      this.startAutoBackup();
    } else if (this.backupInterval) {
      clearInterval(this.backupInterval);
      this.backupInterval = null;
    }
  }

  /**
   * إنشاء نسخة احتياطية يدوية
   */
  public createManualBackup(): { success: boolean; message: string } {
    const result = createBackup();
    if (result.success) {
      this.status.lastBackup = new Date().toISOString();
      this.addAlert('info', 'تم إنشاء نسخة احتياطية يدوية بنجاح');
    }
    return result;
  }

  /**
   * استرداد من النسخة الاحتياطية
   */
  public restoreData(backupId?: string): { success: boolean; message: string } {
    const result = restoreFromBackup(backupId);
    if (result.success) {
      this.addAlert('info', 'تم استرداد البيانات بنجاح');
      // إعادة تحميل الصفحة لتطبيق البيانات المستردة
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    return result;
  }

  /**
   * تصدير البيانات
   */
  public exportData(): { success: boolean; data?: string; message: string } {
    try {
      const data = {
        invoices: JSON.parse(localStorage.getItem('invoices') || '[]'),
        settings: JSON.parse(localStorage.getItem('companySettings') || '{}'),
        counters: JSON.parse(localStorage.getItem('invoiceCounters') || '{}'),
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };

      const exportData = JSON.stringify(data, null, 2);
      
      return {
        success: true,
        data: exportData,
        message: 'تم تصدير البيانات بنجاح',
      };
    } catch (error) {
      return {
        success: false,
        message: 'فشل في تصدير البيانات: ' + (error as Error).message,
      };
    }
  }

  /**
   * استيراد البيانات
   */
  public importData(importData: string): { success: boolean; message: string } {
    try {
      const data = JSON.parse(importData);
      
      // التحقق من صحة البيانات
      if (!data.invoices || !Array.isArray(data.invoices)) {
        throw new Error('بيانات الفواتير غير صحيحة');
      }

      // إنشاء نسخة احتياطية قبل الاستيراد
      const backupResult = createBackup();
      if (!backupResult.success) {
        throw new Error('فشل في إنشاء نسخة احتياطية قبل الاستيراد');
      }

      // استيراد البيانات
      localStorage.setItem('invoices', JSON.stringify(data.invoices));
      if (data.settings) {
        localStorage.setItem('companySettings', JSON.stringify(data.settings));
      }
      if (data.counters) {
        localStorage.setItem('invoiceCounters', JSON.stringify(data.counters));
      }

      this.addAlert('info', 'تم استيراد البيانات بنجاح');
      
      return {
        success: true,
        message: 'تم استيراد البيانات بنجاح',
      };
    } catch (error) {
      return {
        success: false,
        message: 'فشل في استيراد البيانات: ' + (error as Error).message,
      };
    }
  }
}

// إنشاء مثيل واحد من النظام
export const dataProtection = new DataProtectionSystem();

// بدء النظام تلقائياً عند تحميل الصفحة
if (typeof window !== 'undefined') {
  // انتظار تحميل الصفحة كاملة
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => dataProtection.start(), 1000);
    });
  } else {
    setTimeout(() => dataProtection.start(), 1000);
  }

  // إيقاف النظام عند إغلاق الصفحة
  window.addEventListener('beforeunload', () => {
    dataProtection.stop();
  });
}
