'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { SamplesService, SampleItem } from '@/types';

interface SamplesServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
  isPreSelected?: boolean;
}

const SamplesServiceComponent: React.FC<SamplesServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
  isPreSelected = false,
}) => {
  const serviceData = values[namePrefix] as SamplesService;
  const isEnabled = serviceData?.enabled || false;

  // تفعيل الخدمة تلقائياً إذا كانت مختارة مسبقاً
  React.useEffect(() => {
    if (isPreSelected && !serviceData?.enabled) {
      setFieldValue(`${namePrefix}.enabled`, true);
      setFieldValue(`${namePrefix}.serviceType`, 'samples');
    }
  }, [isPreSelected, namePrefix, setFieldValue, serviceData?.enabled]);

  const defaultSample: SampleItem = {
    sampleName: '',
    quantity: 1,
    cost: 0,
    fabricType: '',
    color: '',
    size: '',
    deliveryDate: '',
    notes: '',
    sampleType: '',
    urgencyLevel: 'normal',
  };

  const sampleTypes = [
    { value: 'fabric', label: 'عينة قماش' },
    { value: 'garment', label: 'عينة ملبس' },
    { value: 'accessory', label: 'عينة إكسسوار' },
    { value: 'pattern', label: 'عينة باترون' },
    { value: 'color', label: 'عينة لون' },
    { value: 'texture', label: 'عينة ملمس' },
  ];

  const urgencyLevels = [
    { value: 'normal', label: 'عادي' },
    { value: 'urgent', label: 'مستعجل' },
    { value: 'very_urgent', label: 'مستعجل جداً' },
  ];

  // حساب المجموع الكلي
  const calculateGrandTotal = () => {
    if (!serviceData?.samples) return 0;
    return serviceData.samples.reduce((total: number, sample: any) => {
      return total + (Number(sample.cost) || 0);
    }, 0);
  };

  // تحديث المجموع الكلي
  React.useEffect(() => {
    if (isEnabled) {
      const total = calculateGrandTotal();
      setFieldValue(`${namePrefix}.grandTotal`, total);
    }
  }, [serviceData?.samples, isEnabled]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'samples');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              setFieldValue(`${namePrefix}.samples`, [defaultSample]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة العينات</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-teal-500">
          <FieldArray name={`${namePrefix}.samples`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">قائمة العينات</h3>
                  <button
                    type="button"
                    onClick={() => push(defaultSample)}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة عينة جديدة
                  </button>
                </div>

                {serviceData?.samples?.map((sample: any, index: number) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">العينة {index + 1}</h4>
                      {serviceData.samples.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* اسم العينة */}
                      <div className="form-field">
                        <label className="form-label">اسم العينة</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.sampleName`}
                          type="text"
                          className="form-input"
                          placeholder="اسم العينة"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.samples.${index}.sampleName`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* نوع العينة */}
                      <div className="form-field">
                        <label className="form-label">نوع العينة</label>
                        <Field as="select" name={`${namePrefix}.samples.${index}.sampleType`} className="form-input">
                          <option value="">اختر نوع العينة</option>
                          {sampleTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </Field>
                      </div>

                      {/* الكمية */}
                      <div className="form-field">
                        <label className="form-label">الكمية</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.quantity`}
                          type="number"
                          min="1"
                          className="form-input"
                          placeholder="عدد العينات"
                        />
                      </div>

                      {/* التكلفة */}
                      <div className="form-field">
                        <label className="form-label">التكلفة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.cost`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="تكلفة العينة"
                        />
                      </div>

                      {/* نوع القماش */}
                      <div className="form-field">
                        <label className="form-label">نوع القماش</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.fabricType`}
                          type="text"
                          className="form-input"
                          placeholder="نوع القماش"
                        />
                      </div>

                      {/* اللون */}
                      <div className="form-field">
                        <label className="form-label">اللون</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.color`}
                          type="text"
                          className="form-input"
                          placeholder="اللون المطلوب"
                        />
                      </div>

                      {/* المقاس */}
                      <div className="form-field">
                        <label className="form-label">المقاس</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.size`}
                          type="text"
                          className="form-input"
                          placeholder="المقاس"
                        />
                      </div>

                      {/* مستوى الأولوية */}
                      <div className="form-field">
                        <label className="form-label">مستوى الأولوية</label>
                        <Field as="select" name={`${namePrefix}.samples.${index}.urgency`} className="form-input">
                          <option value="">اختر مستوى الأولوية</option>
                          {urgencyLevels.map((level) => (
                            <option key={level.value} value={level.value}>
                              {level.label}
                            </option>
                          ))}
                        </Field>
                      </div>

                      {/* تاريخ التسليم */}
                      <div className="form-field">
                        <label className="form-label">تاريخ التسليم المطلوب</label>
                        <Field
                          name={`${namePrefix}.samples.${index}.deliveryDate`}
                          type="date"
                          className="form-input"
                        />
                      </div>
                    </div>

                    {/* وصف العينة */}
                    <div className="form-field mt-4">
                      <label className="form-label">وصف العينة</label>
                      <Field
                        name={`${namePrefix}.samples.${index}.description`}
                        as="textarea"
                        rows="2"
                        className="form-input"
                        placeholder="وصف تفصيلي للعينة المطلوبة"
                      />
                    </div>

                    {/* متطلبات خاصة */}
                    <div className="form-field">
                      <label className="form-label">متطلبات خاصة</label>
                      <Field
                        name={`${namePrefix}.samples.${index}.specialRequirements`}
                        as="textarea"
                        rows="2"
                        className="form-input"
                        placeholder="أي متطلبات خاصة للعينة"
                      />
                    </div>

                    {/* خيارات إضافية */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      <label className="flex items-center">
                        <Field
                          type="checkbox"
                          name={`${namePrefix}.samples.${index}.withMeasurements`}
                          className="form-checkbox"
                        />
                        <span className="text-sm">مع القياسات</span>
                      </label>
                      
                      <label className="flex items-center">
                        <Field
                          type="checkbox"
                          name={`${namePrefix}.samples.${index}.withPattern`}
                          className="form-checkbox"
                        />
                        <span className="text-sm">مع الباترون</span>
                      </label>
                      
                      <label className="flex items-center">
                        <Field
                          type="checkbox"
                          name={`${namePrefix}.samples.${index}.qualityCheck`}
                          className="form-checkbox"
                        />
                        <span className="text-sm">فحص جودة</span>
                      </label>
                      
                      <label className="flex items-center">
                        <Field
                          type="checkbox"
                          name={`${namePrefix}.samples.${index}.returnRequired`}
                          className="form-checkbox"
                        />
                        <span className="text-sm">مطلوب الإرجاع</span>
                      </label>
                    </div>

                    {/* ملاحظات */}
                    <div className="form-field mt-4">
                      <label className="form-label">ملاحظات إضافية</label>
                      <Field
                        name={`${namePrefix}.samples.${index}.notes`}
                        as="textarea"
                        rows="2"
                        className="form-input"
                        placeholder="أي ملاحظات إضافية"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>

          {/* المجموع الكلي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-green-700">المجموع الكلي لجميع العينات:</span>
              <span className="text-2xl font-bold text-green-800">{calculateGrandTotal().toFixed(2)} جنيه</span>
            </div>
            <div className="text-sm text-green-600 mt-1">
              شامل جميع العينات المطلوبة
            </div>
          </div>

          {/* معلومات عامة */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات عامة</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* الغرض من العينات */}
              <div className="form-field">
                <label className="form-label">الغرض من العينات</label>
                <Field as="select" name={`${namePrefix}.purpose`} className="form-input">
                  <option value="">اختر الغرض</option>
                  <option value="client_approval">موافقة العميل</option>
                  <option value="quality_test">اختبار جودة</option>
                  <option value="color_matching">مطابقة لون</option>
                  <option value="size_fitting">اختبار مقاس</option>
                  <option value="production_reference">مرجع إنتاج</option>
                  <option value="market_research">بحث سوق</option>
                </Field>
              </div>

              {/* مسؤول العينات */}
              <div className="form-field">
                <label className="form-label">مسؤول العينات</label>
                <Field
                  name={`${namePrefix}.sampleManager`}
                  type="text"
                  className="form-input"
                  placeholder="اسم المسؤول عن العينات"
                />
              </div>
            </div>

            {/* شروط خاصة */}
            <div className="form-field">
              <label className="form-label">شروط خاصة</label>
              <Field
                name={`${namePrefix}.specialConditions`}
                as="textarea"
                rows="3"
                className="form-input"
                placeholder="أي شروط خاصة بالعينات"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SamplesServiceComponent;
