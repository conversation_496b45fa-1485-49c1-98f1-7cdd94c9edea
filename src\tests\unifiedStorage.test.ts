/**
 * اختبارات النظام الموحد للتخزين
 * يختبر جميع وظائف UnifiedStorageManager
 */

import { UnifiedStorageManager } from '../utils/unifiedStorage';
import { STORAGE_KEYS } from '../utils/storageKeys';

// Mock localStorage for testing
const localStorageMock = (() => {
  let store: { [key: string]: string } = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    },
    hasOwnProperty: (key: string) => key in store
  };
})();

// Replace global localStorage with mock
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('UnifiedStorageManager', () => {
  
  beforeEach(() => {
    // تنظيف التخزين قبل كل اختبار
    localStorageMock.clear();
  });

  describe('setItem', () => {
    test('يجب أن يحفظ البيانات بنجاح', async () => {
      const testData = { name: 'اختبار', value: 123 };
      
      const result = await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, testData);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(testData);
      expect(result.message).toContain('نجاح');
      expect(result.timestamp).toBeDefined();
    });

    test('يجب أن يتعامل مع البيانات الكبيرة', async () => {
      const largeData = {
        items: Array(1000).fill({ id: 1, name: 'عنصر كبير', description: 'وصف طويل جداً'.repeat(100) })
      };
      
      const result = await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, largeData);
      
      expect(result.success).toBe(true);
    });

    test('يجب أن يحذر من المفاتيح غير المعروفة', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      await UnifiedStorageManager.setItem('unknown-key', { test: true });
      
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('unknown storage key'));
      consoleSpy.mockRestore();
    });
  });

  describe('getItem', () => {
    test('يجب أن يقرأ البيانات المحفوظة', async () => {
      const testData = { company: 'شركة الاختبار' };
      
      // حفظ البيانات أولاً
      await UnifiedStorageManager.setItem(STORAGE_KEYS.COMPANY_SETTINGS, testData);
      
      // قراءة البيانات
      const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(testData);
    });

    test('يجب أن يرجع القيمة الافتراضية عند عدم وجود بيانات', async () => {
      const defaultValue = { default: true };
      
      const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, defaultValue);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(defaultValue);
    });

    test('يجب أن يتعامل مع البيانات التالفة', async () => {
      // إدراج بيانات تالفة مباشرة
      localStorageMock.setItem(STORAGE_KEYS.CUSTOMERS, 'invalid-json{');
      
      const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
      
      expect(result.success).toBe(false);
      expect(result.data).toEqual([]);
    });
  });

  describe('mergeDuplicateKeys', () => {
    test('يجب أن يدمج إعدادات الشركة من مفاتيح متعددة', async () => {
      // إعداد بيانات مكررة
      const companySettings = { name: 'الشركة الأساسية', phone: '123' };
      const legacySettings = { address: 'العنوان القديم', phone: '456' };
      const companyInfo = { email: '<EMAIL>', website: 'www.company.com' };
      
      localStorageMock.setItem(STORAGE_KEYS.COMPANY_SETTINGS, JSON.stringify(companySettings));
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS, JSON.stringify(legacySettings));
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_INFO, JSON.stringify(companyInfo));
      
      const result = await UnifiedStorageManager.mergeDuplicateKeys();
      
      expect(result.success).toBe(true);
      expect(result.merged).toContain('companySettings');
      expect(result.duplicatesRemoved).toBeGreaterThan(0);
      
      // التحقق من دمج البيانات
      const mergedResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      expect(mergedResult.data).toMatchObject({
        name: 'الشركة الأساسية',
        phone: '456', // القيمة الأحدث
        address: 'العنوان القديم',
        email: '<EMAIL>',
        website: 'www.company.com'
      });
      
      // التحقق من حذف المفاتيح القديمة
      expect(localStorageMock.getItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS)).toBeNull();
      expect(localStorageMock.getItem(STORAGE_KEYS.LEGACY_COMPANY_INFO)).toBeNull();
    });

    test('يجب أن ينظف المفاتيح غير المرغوب فيها', async () => {
      // إضافة مفاتيح للتنظيف
      localStorageMock.setItem('test-invoices', '[]');
      localStorageMock.setItem('demo-data', '{}');
      localStorageMock.setItem('temp-invoice', '{}');
      
      const result = await UnifiedStorageManager.mergeDuplicateKeys();
      
      expect(result.success).toBe(true);
      expect(result.duplicatesRemoved).toBe(3);
      
      // التحقق من حذف المفاتيح
      expect(localStorageMock.getItem('test-invoices')).toBeNull();
      expect(localStorageMock.getItem('demo-data')).toBeNull();
      expect(localStorageMock.getItem('temp-invoice')).toBeNull();
    });

    test('يجب أن يتعامل مع البيانات التالفة أثناء الدمج', async () => {
      // إضافة بيانات تالفة
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS, 'invalid-json{');
      
      const result = await UnifiedStorageManager.mergeDuplicateKeys();
      
      expect(result.success).toBe(true); // يجب أن يستمر رغم الأخطاء
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('خطأ في تحليل');
    });
  });

  describe('checkUnifiedSystemHealth', () => {
    test('يجب أن يفحص حالة النظام بنجاح', async () => {
      // إعداد بيانات أساسية
      await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, []);
      await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, []);
      await UnifiedStorageManager.setItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      
      const health = await UnifiedStorageManager.checkUnifiedSystemHealth();
      
      expect(health.isHealthy).toBe(true);
      expect(health.keyStats.coreData).toBe(3);
      expect(health.keyStats.legacy).toBe(0);
    });

    test('يجب أن يكتشف المفاتيح المفقودة', async () => {
      // عدم إضافة أي بيانات أساسية
      
      const health = await UnifiedStorageManager.checkUnifiedSystemHealth();
      
      expect(health.isHealthy).toBe(false);
      expect(health.issues.some(issue => issue.includes('مفاتيح أساسية مفقودة'))).toBe(true);
      expect(health.recommendations.some(rec => rec.includes('تهيئة البيانات الأساسية'))).toBe(true);
    });

    test('يجب أن يكتشف المفاتيح القديمة', async () => {
      // إضافة مفاتيح قديمة
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS, '{}');
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_INFO, '{}');
      
      const health = await UnifiedStorageManager.checkUnifiedSystemHealth();
      
      expect(health.keyStats.legacy).toBe(2);
      expect(health.issues.some(issue => issue.includes('مفاتيح قديمة موجودة'))).toBe(true);
      expect(health.recommendations.some(rec => rec.includes('دمج البيانات'))).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    test('سيناريو كامل: حفظ، قراءة، دمج، فحص', async () => {
      // 1. حفظ بيانات جديدة
      const invoices = [{ id: 1, total: 100 }];
      const customers = [{ id: 1, name: 'عميل اختبار' }];
      
      await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, invoices);
      await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, customers);
      
      // 2. إضافة بيانات قديمة مكررة
      localStorageMock.setItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS, JSON.stringify({ name: 'شركة قديمة' }));
      
      // 3. دمج البيانات
      const mergeResult = await UnifiedStorageManager.mergeDuplicateKeys();
      expect(mergeResult.success).toBe(true);
      
      // 4. قراءة البيانات المدموجة
      const invoicesResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
      const customersResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
      const settingsResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      
      expect(invoicesResult.data).toEqual(invoices);
      expect(customersResult.data).toEqual(customers);
      expect(settingsResult.data).toMatchObject({ name: 'شركة قديمة' });
      
      // 5. فحص حالة النظام
      const health = await UnifiedStorageManager.checkUnifiedSystemHealth();
      expect(health.isHealthy).toBe(true);
      expect(health.keyStats.legacy).toBe(0); // تم حذف المفاتيح القديمة
    });
  });
});
