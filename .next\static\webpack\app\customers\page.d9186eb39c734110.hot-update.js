"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerStore: function() { return /* binding */ useCustomerStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n\n\n/**\n * تحميل العملاء من النظام الموحد\n */ const loadCustomersFromStorage = async ()=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.CUSTOMERS, []);\n        if (result.success) {\n            console.log(\"✅ تم تحميل العملاء من النظام الموحد:\", result.data.length);\n            return result.data;\n        } else {\n            console.error(\"❌ فشل في تحميل العملاء:\", result.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في تحميل العملاء:\", error);\n        return [];\n    }\n};\n/**\n * حفظ العملاء في التخزين الآمن\n */ const saveCustomersToStorage = (customers)=>{\n    const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customers\", customers);\n    return result.success;\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return \"customer-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_4__.create)((set, get)=>({\n        customers: loadCustomersFromStorage(),\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        // إضافة عملاء متعددين (للاستيراد)\n        addMultipleCustomers: async (customersData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                const newCustomers = [];\n                const errors = [];\n                for (const customerData of customersData){\n                    try {\n                        let clientCode = customerData.clientCode;\n                        // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد\n                        if (!clientCode) {\n                            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.generateCustomerCode)();\n                            clientCode = codeData.code;\n                        }\n                        // التحقق من عدم تكرار الكود\n                        const existingCustomer = [\n                            ...customers,\n                            ...newCustomers\n                        ].find((c)=>c.clientCode === clientCode);\n                        if (existingCustomer) {\n                            errors.push(\"كود العميل \".concat(clientCode, \" مستخدم بالفعل للعميل \").concat(customerData.name));\n                            continue;\n                        }\n                        const newCustomer = {\n                            ...customerData,\n                            id: generateCustomerId(),\n                            clientCode\n                        };\n                        newCustomers.push(newCustomer);\n                    } catch (error) {\n                        errors.push(\"خطأ في إضافة العميل \".concat(customerData.name, \": \").concat(error instanceof Error ? error.message : \"خطأ غير معروف\"));\n                    }\n                }\n                if (newCustomers.length > 0) {\n                    const updatedCustomers = [\n                        ...customers,\n                        ...newCustomers\n                    ];\n                    // حفظ في التخزين\n                    const saved = saveCustomersToStorage(updatedCustomers);\n                    if (!saved) {\n                        throw new Error(\"فشل في حفظ بيانات العملاء\");\n                    }\n                    set({\n                        customers: updatedCustomers,\n                        isLoading: false\n                    });\n                    return {\n                        success: true,\n                        imported: newCustomers.length,\n                        errors,\n                        customers: newCustomers\n                    };\n                } else {\n                    set({\n                        isLoading: false\n                    });\n                    return {\n                        success: false,\n                        error: \"لم يتم إضافة أي عميل\",\n                        imported: 0,\n                        errors\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage,\n                    imported: 0,\n                    errors: []\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                var _get_currentCustomer;\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: ((_get_currentCustomer = get().currentCustomer) === null || _get_currentCustomer === void 0 ? void 0 : _get_currentCustomer.id) === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const searchTerm = query.toLowerCase().trim();\n            // البحث الأساسي المحسن\n            const basicResults = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm) || customer.phone.includes(query) || customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm) || customer.businessName && customer.businessName.toLowerCase().includes(searchTerm) || customer.province && customer.province.toLowerCase().includes(searchTerm) || customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm) || customer.services.some((service)=>service.toLowerCase().includes(searchTerm)));\n            // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج\n            if (basicResults.length === 0 && searchTerm.length > 2) {\n                const fuzzyResults = customers.filter((customer)=>{\n                    const name = customer.name.toLowerCase();\n                    const words = searchTerm.split(\" \");\n                    return words.some((word)=>name.includes(word) && word.length > 1);\n                });\n                return fuzzyResults;\n            }\n            // ترتيب النتائج حسب الأولوية\n            return basicResults.sort((a, b)=>{\n                var _a_clientCode, _b_clientCode;\n                // أولوية للمطابقة الدقيقة في بداية الاسم\n                const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);\n                const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);\n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                // أولوية للمطابقة في كود العميل\n                const aCodeMatch = (_a_clientCode = a.clientCode) === null || _a_clientCode === void 0 ? void 0 : _a_clientCode.toLowerCase().includes(searchTerm);\n                const bCodeMatch = (_b_clientCode = b.clientCode) === null || _b_clientCode === void 0 ? void 0 : _b_clientCode.toLowerCase().includes(searchTerm);\n                if (aCodeMatch && !bCodeMatch) return -1;\n                if (!aCodeMatch && bCodeMatch) return 1;\n                // ترتيب أبجدي كافتراضي\n                return a.name.localeCompare(b.name, \"ar\");\n            });\n        },\n        // الاقتراحات السريعة للبحث\n        getQuickSuggestions: function(query) {\n            let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n            const { customers } = get();\n            if (!query.trim()) return [];\n            const searchTerm = query.toLowerCase().trim();\n            const suggestions = customers.filter((customer)=>customer.name.toLowerCase().startsWith(searchTerm) || customer.phone.startsWith(query) || customer.clientCode && customer.clientCode.toLowerCase().startsWith(searchTerm)).slice(0, limit).map((customer)=>{\n                var _customer_clientCode, _customer_clientCode1;\n                return {\n                    id: customer.id,\n                    name: customer.name,\n                    phone: customer.phone,\n                    clientCode: customer.clientCode,\n                    businessName: customer.businessName,\n                    type: ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().startsWith(searchTerm)) ? \"code\" : customer.phone.startsWith(query) ? \"phone\" : \"name\",\n                    matchText: ((_customer_clientCode1 = customer.clientCode) === null || _customer_clientCode1 === void 0 ? void 0 : _customer_clientCode1.toLowerCase().startsWith(searchTerm)) ? customer.clientCode : customer.phone.startsWith(query) ? customer.phone : customer.name\n                };\n            });\n            return suggestions;\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_3__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/customerStore.ts\n"));

/***/ })

});