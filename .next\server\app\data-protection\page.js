/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/data-protection/page";
exports.ids = ["app/data-protection/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdata-protection%2Fpage&page=%2Fdata-protection%2Fpage&appPaths=%2Fdata-protection%2Fpage&pagePath=private-next-app-dir%2Fdata-protection%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdata-protection%2Fpage&page=%2Fdata-protection%2Fpage&appPaths=%2Fdata-protection%2Fpage&pagePath=private-next-app-dir%2Fdata-protection%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'data-protection',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/data-protection/page.tsx */ \"(rsc)/./src/app/data-protection/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\data-protection\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\data-protection\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/data-protection/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/data-protection/page\",\n        pathname: \"/data-protection\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZkYXRhLXByb3RlY3Rpb24lMkZwYWdlJnBhZ2U9JTJGZGF0YS1wcm90ZWN0aW9uJTJGcGFnZSZhcHBQYXRocz0lMkZkYXRhLXByb3RlY3Rpb24lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGF0YS1wcm90ZWN0aW9uJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q0NBRCUyMFVTRVIlMjBYNjQlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDMjMxJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNDQUQlMjBVU0VSJTIwWDY0JTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1QzIzMSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsZ0xBQW1JO0FBQzFKO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUFvSDtBQUM3SSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvPzYzMDYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnZGF0YS1wcm90ZWN0aW9uJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxzcmNcXFxcYXBwXFxcXGRhdGEtcHJvdGVjdGlvblxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcZGF0YS1wcm90ZWN0aW9uXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxzcmNcXFxcYXBwXFxcXGRhdGEtcHJvdGVjdGlvblxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2RhdGEtcHJvdGVjdGlvbi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2RhdGEtcHJvdGVjdGlvbi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXRhLXByb3RlY3Rpb25cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdata-protection%2Fpage&page=%2Fdata-protection%2Fpage&appPaths=%2Fdata-protection%2Fpage&pagePath=private-next-app-dir%2Fdata-protection%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0NBRCUyMFVTRVIlMjBYNjQlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDMjMxJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0NBRCUyMFVTRVIlMjBYNjQlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDMjMxJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTJKO0FBQzNKLDBPQUErSjtBQUMvSix3T0FBOEo7QUFDOUosa1BBQW1LO0FBQ25LLHNRQUE2SztBQUM3SyIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmFwYXBwLz81NWM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/DataProtectionDashboard.tsx */ \"(ssr)/./src/components/DataProtectionDashboard.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNzcmMlNUNjb21wb25lbnRzJTVDRGF0YVByb3RlY3Rpb25EYXNoYm9hcmQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmFwYXBwLz8zYTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxEYXRhUHJvdGVjdGlvbkRhc2hib2FyZC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/DataProtectionDashboard.tsx":
/*!****************************************************!*\
  !*** ./src/components/DataProtectionDashboard.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/dataProtection */ \"(ssr)/./src/utils/dataProtection.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst DataProtectionDashboard = ()=>{\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [storageHealth, setStorageHealth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث البيانات\n    const refreshData = ()=>{\n        const currentStatus = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.getStatus();\n        const health = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.checkStorageHealth)();\n        setStatus(currentStatus);\n        setStorageHealth(health);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshData();\n        // تحديث كل 30 ثانية\n        const interval = setInterval(refreshData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // إنشاء نسخة احتياطية يدوية\n    const handleManualBackup = async ()=>{\n        setIsLoading(true);\n        try {\n            const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.createManualBackup();\n            if (result.success) {\n                alert(\"✅ تم إنشاء النسخة الاحتياطية بنجاح\");\n            } else {\n                alert(\"❌ فشل في إنشاء النسخة الاحتياطية: \" + result.message);\n            }\n            refreshData();\n        } catch (error) {\n            alert(\"❌ خطأ غير متوقع\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // استرداد البيانات\n    const handleRestore = async ()=>{\n        if (!confirm(\"هل أنت متأكد من استرداد البيانات من النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.\")) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.restoreData();\n            if (result.success) {\n                alert(\"✅ تم استرداد البيانات بنجاح. سيتم إعادة تحميل الصفحة.\");\n            } else {\n                alert(\"❌ فشل في استرداد البيانات: \" + result.message);\n            }\n        } catch (error) {\n            alert(\"❌ خطأ غير متوقع\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // تصدير البيانات\n    const handleExport = ()=>{\n        const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.exportData();\n        if (result.success && result.data) {\n            const blob = new Blob([\n                result.data\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = `infapapp-backup-${new Date().toISOString().split(\"T\")[0]}.json`;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n            alert(\"✅ تم تصدير البيانات بنجاح\");\n        } else {\n            alert(\"❌ فشل في تصدير البيانات: \" + result.message);\n        }\n    };\n    // استيراد البيانات\n    const handleImport = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".json\";\n        input.onchange = (e)=>{\n            const file = e.target.files?.[0];\n            if (file) {\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    try {\n                        const data = e.target?.result;\n                        const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.importData(data);\n                        if (result.success) {\n                            alert(\"✅ تم استيراد البيانات بنجاح\");\n                            refreshData();\n                        } else {\n                            alert(\"❌ فشل في استيراد البيانات: \" + result.message);\n                        }\n                    } catch (error) {\n                        alert(\"❌ ملف غير صحيح\");\n                    }\n                };\n                reader.readAsText(file);\n            }\n        };\n        input.click();\n    };\n    // تبديل النسخ الاحتياطي التلقائي\n    const toggleAutoBackup = ()=>{\n        const newState = !status?.autoBackupEnabled;\n        _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.toggleAutoBackup(newState);\n        refreshData();\n    };\n    // حل التنبيه\n    const resolveAlert = (alertId)=>{\n        _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.resolveAlert(alertId);\n        refreshData();\n    };\n    if (!status || !storageHealth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 bg-white rounded-lg shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"جاري تحميل بيانات النظام...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\uD83D\\uDEE1️ لوحة تحكم حماية البيانات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"opacity-90\",\n                        children: \"مراقبة وحماية بيانات التطبيق مع نسخ احتياطية تلقائية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-green-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"حالة المراقبة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-green-600\",\n                                        children: status.isMonitoring ? \"\\uD83D\\uDFE2 نشط\" : \"\\uD83D\\uDD34 متوقف\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-blue-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"النسخ الاحتياطي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-blue-600\",\n                                        children: status.autoBackupEnabled ? \"\\uD83D\\uDFE2 تلقائي\" : \"\\uD83D\\uDFE1 يدوي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-purple-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"التنبيهات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-purple-600\",\n                                        children: [\n                                            status.alertsCount,\n                                            \" تنبيه\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-orange-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"حالة التخزين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-orange-600\",\n                                        children: storageHealth.isHealthy ? \"\\uD83D\\uDFE2 سليم\" : \"\\uD83D\\uDFE1 يحتاج انتباه\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"\\uD83D\\uDCCA معلومات التخزين\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"المساحة المستخدمة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            Math.round(storageHealth.storageUsed / 1024),\n                                            \" KB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2 mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full\",\n                                            style: {\n                                                width: `${storageHealth.storageUsed / storageHealth.storageLimit * 100}%`\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"النسخ الاحتياطية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            storageHealth.backupsCount,\n                                            \" نسخة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"آخر نسخة احتياطية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: status.lastBackup ? new Date(status.lastBackup).toLocaleString(\"ar-EG\") : \"لا توجد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"⚙️ أدوات التحكم\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleManualBackup,\n                                disabled: isLoading,\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                                children: \"\\uD83D\\uDCBE نسخة احتياطية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRestore,\n                                disabled: isLoading,\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                children: \"\\uD83D\\uDD04 استرداد البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                className: \"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\",\n                                children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleImport,\n                                className: \"bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700\",\n                                children: \"\\uD83D\\uDCE5 استيراد البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: status.autoBackupEnabled,\n                                    onChange: toggleAutoBackup,\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"تفعيل النسخ الاحتياطي التلقائي (كل 5 دقائق)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined),\n            status.alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"\\uD83D\\uDEA8 التنبيهات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: status.alerts.map((alert1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-3 rounded-lg border-r-4 ${alert1.type === \"error\" ? \"bg-red-50 border-red-500\" : alert1.type === \"warning\" ? \"bg-yellow-50 border-yellow-500\" : \"bg-blue-50 border-blue-500\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: alert1.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: new Date(alert1.timestamp).toLocaleString(\"ar-EG\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>resolveAlert(alert1.id),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, alert1.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined),\n            storageHealth.issues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"⚠️ مشاكل التخزين\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: storageHealth.issues.map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-yellow-50 border-r-4 border-yellow-500 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-yellow-800\",\n                                    children: issue\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined),\n                    storageHealth.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold mb-2\",\n                                children: \"\\uD83D\\uDCA1 التوصيات:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside space-y-1\",\n                                children: storageHealth.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-700\",\n                                        children: rec\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataProtectionDashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DataProtectionDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/dataProtection.ts":
/*!*************************************!*\
  !*** ./src/utils/dataProtection.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataProtection: () => (/* binding */ dataProtection)\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/**\n * نظام حماية البيانات والمراقبة التلقائية\n * يوفر مراقبة مستمرة ونسخ احتياطية تلقائية\n */ \nclass DataProtectionSystem {\n    /**\n   * بدء نظام الحماية\n   */ start() {\n        if (this.status.isMonitoring) {\n            console.log(\"\\uD83D\\uDEE1️ Data protection system already running\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDE80 Starting data protection system...\");\n        // بدء المراقبة\n        this.startMonitoring();\n        // بدء النسخ الاحتياطي التلقائي\n        if (this.status.autoBackupEnabled) {\n            this.startAutoBackup();\n        }\n        // فحص فوري للحالة\n        this.performHealthCheck();\n        this.status.isMonitoring = true;\n        console.log(\"✅ Data protection system started successfully\");\n    }\n    /**\n   * إيقاف نظام الحماية\n   */ stop() {\n        if (!this.status.isMonitoring) {\n            return;\n        }\n        console.log(\"\\uD83D\\uDED1 Stopping data protection system...\");\n        if (this.monitoringInterval) {\n            clearInterval(this.monitoringInterval);\n            this.monitoringInterval = null;\n        }\n        if (this.backupInterval) {\n            clearInterval(this.backupInterval);\n            this.backupInterval = null;\n        }\n        this.status.isMonitoring = false;\n        console.log(\"✅ Data protection system stopped\");\n    }\n    /**\n   * بدء المراقبة المستمرة\n   */ startMonitoring() {\n        // فحص كل دقيقة\n        this.monitoringInterval = setInterval(()=>{\n            this.performHealthCheck();\n        }, 60 * 1000);\n    }\n    /**\n   * بدء النسخ الاحتياطي التلقائي\n   */ startAutoBackup() {\n        // نسخ احتياطي كل 5 دقائق\n        this.backupInterval = setInterval(()=>{\n            this.performAutoBackup();\n        }, _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.BACKUP_INTERVAL);\n    }\n    /**\n   * تنفيذ فحص الحالة\n   */ performHealthCheck() {\n        try {\n            const health = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.checkStorageHealth)();\n            this.status.lastHealthCheck = new Date().toISOString();\n            // معالجة المشاكل المكتشفة\n            if (!health.isHealthy) {\n                health.issues.forEach((issue)=>{\n                    this.addAlert(\"warning\", `مشكلة في النظام: ${issue}`);\n                });\n                // إجراءات تلقائية للمشاكل الحرجة\n                if (health.storageUsed > _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n                    this.handleStorageOverflow();\n                }\n            }\n            // تنبيه إذا لم تكن هناك نسخ احتياطية\n            if (health.backupsCount === 0) {\n                this.addAlert(\"error\", \"لا توجد نسخ احتياطية - سيتم إنشاء نسخة فورية\");\n                this.performAutoBackup();\n            }\n            console.log(\"\\uD83D\\uDD0D Health check completed:\", {\n                healthy: health.isHealthy,\n                issues: health.issues.length,\n                storageUsed: Math.round(health.storageUsed / 1024) + \"KB\",\n                backups: health.backupsCount\n            });\n        } catch (error) {\n            this.addAlert(\"error\", \"فشل في فحص حالة النظام\");\n            console.error(\"❌ Health check failed:\", error);\n        }\n    }\n    /**\n   * تنفيذ نسخ احتياطي تلقائي\n   */ performAutoBackup() {\n        try {\n            // فحص إذا كانت هناك تغييرات تستدعي النسخ الاحتياطي\n            if (this.shouldCreateBackup()) {\n                const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n                if (result.success) {\n                    this.status.lastBackup = new Date().toISOString();\n                    console.log(\"\\uD83D\\uDCBE Auto backup completed successfully\");\n                } else {\n                    this.addAlert(\"error\", `فشل النسخ الاحتياطي التلقائي: ${result.message}`);\n                }\n            }\n        } catch (error) {\n            this.addAlert(\"error\", \"خطأ في النسخ الاحتياطي التلقائي\");\n            console.error(\"❌ Auto backup failed:\", error);\n        }\n    }\n    /**\n   * فحص إذا كان النسخ الاحتياطي مطلوب\n   */ shouldCreateBackup() {\n        // إنشاء نسخة احتياطية إذا:\n        // 1. لا توجد نسخة سابقة\n        // 2. مر أكثر من 5 دقائق على آخر نسخة\n        // 3. تم تعديل البيانات مؤخراً\n        if (!this.status.lastBackup) {\n            return true;\n        }\n        const lastBackupTime = new Date(this.status.lastBackup).getTime();\n        const now = Date.now();\n        const timeDiff = now - lastBackupTime;\n        return timeDiff > _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.BACKUP_INTERVAL;\n    }\n    /**\n   * معالجة امتلاء التخزين\n   */ handleStorageOverflow() {\n        try {\n            console.log(\"⚠️ Storage overflow detected, attempting cleanup...\");\n            // تنظيف النسخ الاحتياطية القديمة أولاً\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeA - timeB; // ترتيب تصاعدي (الأقدم أولاً)\n            });\n            // حذف النصف الأقدم من النسخ الاحتياطية\n            const toDelete = backupKeys.slice(0, Math.floor(backupKeys.length / 2));\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup due to storage overflow:\", key);\n            });\n            // تنظيف البيانات المؤقتة\n            const tempKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"temp-\") || key.startsWith(\"cache-\") || key.startsWith(\"draft-\"));\n            tempKeys.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed temporary data:\", key);\n            });\n            this.addAlert(\"info\", \"تم تنظيف التخزين تلقائياً لتوفير مساحة\");\n        } catch (error) {\n            this.addAlert(\"error\", \"فشل في تنظيف التخزين\");\n            console.error(\"❌ Storage cleanup failed:\", error);\n        }\n    }\n    /**\n   * إضافة تنبيه\n   */ addAlert(type, message) {\n        const alert = {\n            id: Date.now().toString(),\n            type,\n            message,\n            timestamp: new Date().toISOString(),\n            resolved: false\n        };\n        this.alerts.unshift(alert);\n        this.status.alertsCount++;\n        // الاحتفاظ بآخر 50 تنبيه فقط\n        if (this.alerts.length > 50) {\n            this.alerts = this.alerts.slice(0, 50);\n        }\n        console.log(`🚨 Alert [${type.toUpperCase()}]: ${message}`);\n    }\n    /**\n   * الحصول على حالة النظام\n   */ getStatus() {\n        return {\n            ...this.status,\n            alerts: this.alerts.filter((alert)=>!alert.resolved)\n        };\n    }\n    /**\n   * حل تنبيه\n   */ resolveAlert(alertId) {\n        const alert = this.alerts.find((a)=>a.id === alertId);\n        if (alert) {\n            alert.resolved = true;\n            this.status.alertsCount = Math.max(0, this.status.alertsCount - 1);\n        }\n    }\n    /**\n   * تفعيل/إلغاء النسخ الاحتياطي التلقائي\n   */ toggleAutoBackup(enabled) {\n        this.status.autoBackupEnabled = enabled;\n        if (enabled && this.status.isMonitoring) {\n            this.startAutoBackup();\n        } else if (this.backupInterval) {\n            clearInterval(this.backupInterval);\n            this.backupInterval = null;\n        }\n    }\n    /**\n   * إنشاء نسخة احتياطية يدوية\n   */ createManualBackup() {\n        const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n        if (result.success) {\n            this.status.lastBackup = new Date().toISOString();\n            this.addAlert(\"info\", \"تم إنشاء نسخة احتياطية يدوية بنجاح\");\n        }\n        return result;\n    }\n    /**\n   * استرداد من النسخة الاحتياطية\n   */ restoreData(backupId) {\n        const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.restoreFromBackup)(backupId);\n        if (result.success) {\n            this.addAlert(\"info\", \"تم استرداد البيانات بنجاح\");\n            // إعادة تحميل الصفحة لتطبيق البيانات المستردة\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1000);\n        }\n        return result;\n    }\n    /**\n   * تصدير البيانات\n   */ exportData() {\n        try {\n            const data = {\n                invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n                settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n                counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\"),\n                exportDate: new Date().toISOString(),\n                version: \"1.0.0\"\n            };\n            const exportData = JSON.stringify(data, null, 2);\n            return {\n                success: true,\n                data: exportData,\n                message: \"تم تصدير البيانات بنجاح\"\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: \"فشل في تصدير البيانات: \" + error.message\n            };\n        }\n    }\n    /**\n   * استيراد البيانات\n   */ importData(importData) {\n        try {\n            const data = JSON.parse(importData);\n            // التحقق من صحة البيانات\n            if (!data.invoices || !Array.isArray(data.invoices)) {\n                throw new Error(\"بيانات الفواتير غير صحيحة\");\n            }\n            // إنشاء نسخة احتياطية قبل الاستيراد\n            const backupResult = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n            if (!backupResult.success) {\n                throw new Error(\"فشل في إنشاء نسخة احتياطية قبل الاستيراد\");\n            }\n            // استيراد البيانات\n            localStorage.setItem(\"invoices\", JSON.stringify(data.invoices));\n            if (data.settings) {\n                localStorage.setItem(\"companySettings\", JSON.stringify(data.settings));\n            }\n            if (data.counters) {\n                localStorage.setItem(\"invoiceCounters\", JSON.stringify(data.counters));\n            }\n            this.addAlert(\"info\", \"تم استيراد البيانات بنجاح\");\n            return {\n                success: true,\n                message: \"تم استيراد البيانات بنجاح\"\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: \"فشل في استيراد البيانات: \" + error.message\n            };\n        }\n    }\n    constructor(){\n        this.status = {\n            isMonitoring: false,\n            lastBackup: null,\n            lastHealthCheck: null,\n            alertsCount: 0,\n            autoBackupEnabled: true\n        };\n        this.alerts = [];\n        this.monitoringInterval = null;\n        this.backupInterval = null;\n    }\n}\n// إنشاء مثيل واحد من النظام\nconst dataProtection = new DataProtectionSystem();\n// بدء النظام تلقائياً عند تحميل الصفحة\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/dataProtection.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: () => (/* binding */ STORAGE_CONFIG),\n/* harmony export */   checkStorageHealth: () => (/* binding */ checkStorageHealth),\n/* harmony export */   createBackup: () => (/* binding */ createBackup),\n/* harmony export */   restoreFromBackup: () => (/* binding */ restoreFromBackup),\n/* harmony export */   secureGetItem: () => (/* binding */ secureGetItem),\n/* harmony export */   secureSetItem: () => (/* binding */ secureSetItem)\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = `backup-${Date.now()}`;\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: `تم استرداد البيانات بنجاح من النسخة: ${backup.timestamp}`\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/secureStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"af7364717194\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzkyZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZjczNjQ3MTcxOTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/data-protection/page.tsx":
/*!******************************************!*\
  !*** ./src/app/data-protection/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DataProtectionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_DataProtectionDashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/DataProtectionDashboard */ \"(rsc)/./src/components/DataProtectionDashboard.tsx\");\n\n\nfunction DataProtectionPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DataProtectionDashboard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\data-protection\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\data-protection\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2RhdGEtcHJvdGVjdGlvbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEyRTtBQUU1RCxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0gsMkVBQXVCQTs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmFwYXBwLy4vc3JjL2FwcC9kYXRhLXByb3RlY3Rpb24vcGFnZS50c3g/NDNlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRGF0YVByb3RlY3Rpb25EYXNoYm9hcmQgZnJvbSAnQC9jb21wb25lbnRzL0RhdGFQcm90ZWN0aW9uRGFzaGJvYXJkJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGF0YVByb3RlY3Rpb25QYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgcC02XCI+XG4gICAgICA8RGF0YVByb3RlY3Rpb25EYXNoYm9hcmQgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJEYXRhUHJvdGVjdGlvbkRhc2hib2FyZCIsIkRhdGFQcm90ZWN0aW9uUGFnZSIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/data-protection/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الفواتير - Invoice Management System\",\n    description: \"نظام شامل لإدارة فواتير الخدمات مع دعم كامل للغة العربية\",\n    keywords: \"فواتير, إدارة, خدمات, استشارات, باترون, تصنيع\",\n    authors: [\n        {\n            name: \"Invoice Management System\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().className)} font-arabic`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/DataProtectionDashboard.tsx":
/*!****************************************************!*\
  !*** ./src/components/DataProtectionDashboard.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\231\src\components\DataProtectionDashboard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdata-protection%2Fpage&page=%2Fdata-protection%2Fpage&appPaths=%2Fdata-protection%2Fpage&pagePath=private-next-app-dir%2Fdata-protection%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();