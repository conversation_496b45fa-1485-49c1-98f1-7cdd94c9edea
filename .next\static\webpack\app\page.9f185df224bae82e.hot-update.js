"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/POSInvoice.tsx":
/*!***************************************!*\
  !*** ./src/components/POSInvoice.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/imageExporter */ \"(app-pages-browser)/./src/utils/imageExporter.ts\");\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/settingsStore */ \"(app-pages-browser)/./src/store/settingsStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst POSInvoice = (param)=>{\n    let { invoice, onClose } = param;\n    _s();\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSharing, setIsSharing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { settings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_4__.useSettingsStore)();\n    // دالة تصدير الفاتورة كصورة\n    const handleExportAsImage = async ()=>{\n        try {\n            setIsExporting(true);\n            await (0,_utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__.exportPOSInvoiceAsImage)(invoice.id || \"unknown\", invoice.id);\n            alert(\"تم تصدير الفاتورة كصورة بنجاح!\");\n        } catch (error) {\n            console.error(\"خطأ في تصدير الصورة:\", error);\n            alert(\"حدث خطأ أثناء تصدير الصورة. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // دالة مشاركة الفاتورة كصورة\n    const handleShareAsImage = async ()=>{\n        try {\n            setIsSharing(true);\n            await (0,_utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__.sharePOSInvoiceAsImage)(\"pos-invoice-content\", \"فاتورة-\".concat(invoice.id || \"unknown\"));\n        } catch (error) {\n            console.error(\"خطأ في مشاركة الصورة:\", error);\n            alert(\"حدث خطأ أثناء مشاركة الصورة. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setIsSharing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"ar-EG\", {\n            style: \"currency\",\n            currency: \"EGP\",\n            minimumFractionDigits: 2\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\",\n            samples: \"عينات\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getServiceDetails = (service)=>{\n        let details = \"\";\n        let quantity = \"\";\n        let unitPrice = 0;\n        let serviceTotal = 0;\n        switch(service.serviceType){\n            case \"consultation\":\n                serviceTotal = service.cost || 0;\n                details = \"\".concat(service.topic || \"استشارة\", \" - \").concat(service.hours || 0, \" ساعة\");\n                quantity = \"\".concat(service.hours || 0, \" ساعة\");\n                unitPrice = service.cost || 0;\n                break;\n            case \"pattern\":\n                var _service_models, _service_models1, _service_models2, _service_models3;\n                serviceTotal = ((_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                details = \"\".concat(((_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_service_models2 = service.models) === null || _service_models2 === void 0 ? void 0 : _service_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_service_models3 = service.models) === null || _service_models3 === void 0 ? void 0 : _service_models3.length) || 1);\n                break;\n            case \"pattern_printing\":\n                var _service_files, _service_files1, _service_files2;\n                serviceTotal = ((_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                details = \"طباعة \".concat(((_service_files1 = service.files) === null || _service_files1 === void 0 ? void 0 : _service_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_service_files2 = service.files) === null || _service_files2 === void 0 ? void 0 : _service_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n                break;\n            case \"manufacturing\":\n                var _service_models4, _service_models5, _service_models6, _service_models7;\n                serviceTotal = ((_service_models4 = service.models) === null || _service_models4 === void 0 ? void 0 : _service_models4.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                details = \"تصنيع \".concat(((_service_models5 = service.models) === null || _service_models5 === void 0 ? void 0 : _service_models5.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_service_models6 = service.models) === null || _service_models6 === void 0 ? void 0 : _service_models6.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_models7 = service.models) === null || _service_models7 === void 0 ? void 0 : _service_models7.length) || 1);\n                break;\n            case \"shipping\":\n                serviceTotal = service.total || 0;\n                details = \"شحن \".concat(service.item || \"منتج\");\n                quantity = \"\".concat(service.quantity || 1);\n                unitPrice = service.unitPrice || 0;\n                break;\n            case \"products\":\n                var _service_items, _service_items1, _service_items2, _service_items3;\n                let productsSubtotal = ((_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                if (service.discountAmount) {\n                    productsSubtotal -= service.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                details = \"\".concat(((_service_items1 = service.items) === null || _service_items1 === void 0 ? void 0 : _service_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_service_items2 = service.items) === null || _service_items2 === void 0 ? void 0 : _service_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_items3 = service.items) === null || _service_items3 === void 0 ? void 0 : _service_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                break;\n            default:\n                serviceTotal = service.cost || service.total || 0;\n                details = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n        }\n        return {\n            details,\n            quantity,\n            unitPrice,\n            serviceTotal\n        };\n    };\n    const handlePrint = ()=>{\n        // إنشاء نافذة طباعة منفصلة\n        const printWindow = window.open(\"\", \"_blank\", \"width=300,height=600\");\n        if (printWindow) {\n            var _document_querySelector;\n            // الحصول على محتوى الفاتورة مع تحديث الشعار\n            const posContent = (_document_querySelector = document.querySelector(\".pos-invoice-content\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.innerHTML;\n            if (posContent) {\n                printWindow.document.write('\\n          <!DOCTYPE html>\\n          <html dir=\"rtl\" lang=\"ar\">\\n          <head>\\n            <meta charset=\"UTF-8\">\\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n            <title>فاتورة POS - '.concat(invoice.id, \"</title>\\n            <style>\\n              * {\\n                margin: 0;\\n                padding: 0;\\n                box-sizing: border-box;\\n              }\\n\\n              body {\\n                font-family: 'Courier New', monospace;\\n                font-size: 10px;\\n                line-height: 1.2;\\n                color: black;\\n                background: white;\\n                width: 80mm;\\n                margin: 0 auto;\\n                padding: 2mm;\\n              }\\n\\n              @page {\\n                size: 80mm auto;\\n                margin: 2mm;\\n              }\\n\\n              @media print {\\n                body {\\n                  width: 80mm !important;\\n                  font-size: 9px !important;\\n                }\\n              }\\n\\n              .text-center { text-align: center; }\\n              .text-sm { font-size: 9px; }\\n              .text-xs { font-size: 8px; }\\n              .text-lg { font-size: 12px; }\\n              .font-bold { font-weight: bold; }\\n              .font-semibold { font-weight: 600; }\\n              .mb-1 { margin-bottom: 2px; }\\n              .mb-2 { margin-bottom: 4px; }\\n              .mb-3 { margin-bottom: 6px; }\\n              .pb-3 { padding-bottom: 6px; }\\n              .pt-3 { padding-top: 6px; }\\n              .mr-3 { margin-right: 6px; }\\n              .mt-2 { margin-top: 4px; }\\n\\n              .border-b { border-bottom: 1px solid #ccc; }\\n              .border-dashed { border-style: dashed; }\\n              .border-gray-400 { border-color: #999; }\\n\\n              .flex { display: flex; }\\n              .justify-between { justify-content: space-between; }\\n\\n              /* Thermal Printer Colors - Black and White Only */\\n              .text-green-600 { color: #000; font-weight: bold; }\\n              .text-orange-600 { color: #000; font-weight: bold; }\\n              .text-red-600 { color: #000; font-weight: bold; }\\n              .text-gray-600 { color: #666; }\\n              .text-gray-700 { color: #333; }\\n\\n              /* Enhanced Logo Styles for Print - Thermal Printer Optimized */\\n              .inline-block { display: inline-block; }\\n              .bg-gradient-to-br { background: #000; } /* Black background for thermal printing */\\n              .rounded-xl { border-radius: 8px; }\\n              .p-3 { padding: 6px; }\\n              .p-1 { padding: 2px; }\\n              .w-20 { width: 40px; }\\n              .h-20 { height: 40px; }\\n              .w-full { width: 100%; }\\n              .h-full { height: 100%; }\\n              .mx-auto { margin-left: auto; margin-right: auto; }\\n              .flex { display: flex; }\\n              .items-center { align-items: center; }\\n              .justify-center { justify-content: center; }\\n              .text-white { color: white; }\\n              .bg-white { background-color: white; }\\n              .font-extrabold { font-weight: 800; }\\n              .font-semibold { font-weight: 600; }\\n              .leading-tight { line-height: 1.1; }\\n              .tracking-wide { letter-spacing: 0.025em; }\\n              .tracking-widest { letter-spacing: 0.1em; }\\n              .opacity-90 { opacity: 0.9; }\\n              .space-y-1 > * + * { margin-top: 2px; }\\n              .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }\\n              .border { border: 1px solid #e5e7eb; }\\n              .border-gray-200 { border-color: #e5e7eb; }\\n              .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }\\n              .relative { position: relative; }\\n              .absolute { position: absolute; }\\n              .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n              .top-1 { top: 2px; }\\n              .right-1 { right: 2px; }\\n              .bottom-2 { bottom: 4px; }\\n              .left-2 { left: 4px; }\\n              .w-3 { width: 6px; }\\n              .h-3 { height: 6px; }\\n              .w-2 { width: 4px; }\\n              .h-2 { height: 4px; }\\n              .w-8 { width: 16px; }\\n              .h-0.5 { height: 1px; }\\n              .mt-1 { margin-top: 2px; }\\n              .rounded-full { border-radius: 50%; }\\n              .z-10 { z-index: 10; }\\n              .overflow-hidden { overflow: hidden; }\\n\\n              /* Image Styles for Uploaded Logo */\\n              .object-contain {\\n                object-fit: contain;\\n                image-rendering: -webkit-optimize-contrast;\\n                image-rendering: crisp-edges;\\n              }\\n\\n              /* Enhanced Layout Styles for Thermal Print - Black and White */\\n              .bg-gray-50 { background-color: #f8f8f8; }\\n              .bg-blue-50 { background-color: #f0f0f0; }\\n              .text-blue-600 { color: #000; font-weight: bold; }\\n              .text-blue-800 { color: #000; font-weight: bold; }\\n              .text-green-600 { color: #000; font-weight: bold; }\\n              .text-green-800 { color: #000; font-weight: bold; }\\n              .text-orange-800 { color: #000; font-weight: bold; }\\n              .text-red-800 { color: #000; font-weight: bold; }\\n              .bg-green-100 { background-color: #f0f0f0; }\\n              .bg-orange-100 { background-color: #f0f0f0; }\\n              .bg-red-100 { background-color: #f0f0f0; }\\n              .grid { display: grid; }\\n              .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\\n              .gap-2 { gap: 8px; }\\n              .items-center { align-items: center; }\\n              .border-b { border-bottom: 1px solid #e5e7eb; }\\n              .pb-1 { padding-bottom: 2px; }\\n              .font-medium { font-weight: 500; }\\n              .space-y-2 > * + * { margin-top: 8px; }\\n              .px-2 { padding-left: 4px; padding-right: 4px; }\\n              .py-1 { padding-top: 2px; padding-bottom: 2px; }\\n              .px-4 { padding-left: 8px; padding-right: 8px; }\\n              .py-2 { padding-top: 4px; padding-bottom: 4px; }\\n              .rounded-full { border-radius: 9999px; }\\n              .border-2 { border-width: 2px; }\\n              .border-t-2 { border-top-width: 2px; }\\n              .pt-4 { padding-top: 8px; }\\n              .mb-4 { margin-bottom: 8px; }\\n              .mb-3 { margin-bottom: 6px; }\\n              .mb-2 { margin-bottom: 4px; }\\n              .mb-1 { margin-bottom: 2px; }\\n              .mt-1 { margin-top: 2px; }\\n              .mt-2 { margin-top: 4px; }\\n              .mr-2 { margin-right: 4px; }\\n              .mr-3 { margin-right: 6px; }\\n              .tracking-wide { letter-spacing: 0.025em; }\\n              .leading-relaxed { line-height: 1.625; }\\n            </style>\\n          </head>\\n          <body>\\n            \").concat(posContent, \"\\n          </body>\\n          </html>\\n        \"));\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        printWindow.close();\n                    }, 250);\n                };\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-34d022bd924bccec\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-34d022bd924bccec\" + \" \" + \"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"p-4 border-b border-gray-200 print:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xl font-bold\",\n                                        children: \"فاتورة POS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex gap-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrint,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"طباعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExportAsImage,\n                                        disabled: isExporting,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isExporting ? \"جاري التصدير...\" : \"تصدير كصورة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShareAsImage,\n                                        disabled: isSharing,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isSharing ? \"جاري المشاركة...\" : \"مشاركة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"pos-invoice-content\",\n                        style: {\n                            fontFamily: \"Arial, sans-serif\",\n                            fontSize: \"13px\",\n                            lineHeight: \"1.5\",\n                            maxWidth: \"320px\",\n                            margin: \"0 auto\",\n                            color: \"#000\"\n                        },\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"pos-invoice-content p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-b border-dashed border-gray-400 pb-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"inline-block bg-white rounded-xl p-2 mb-2 border border-gray-400\",\n                                            children: settings.companyLogo ? /* Uploaded Company Logo */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-16 h-16 mx-auto rounded-lg overflow-hidden border border-gray-400 bg-white flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: settings.companyLogo,\n                                                    alt: \"شعار الشركة\",\n                                                    style: {\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\",\n                                                        imageRendering: \"crisp-edges\",\n                                                        filter: \"contrast(1.2) brightness(1.1)\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined) : /* Fallback CSS Logo - Thermal Optimized */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-16 h-16 mx-auto bg-black rounded-lg flex items-center justify-center border border-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-white font-bold text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-lg leading-tight font-extrabold tracking-wide\",\n                                                            children: \"OKA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs leading-tight font-semibold tracking-widest\",\n                                                            children: \"GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"w-6 h-0.5 bg-white mx-auto mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            color: \"#000\"\n                                        },\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xl font-bold mb-1 tracking-wide\",\n                                        children: settings.companyName || \"OKA GROUP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#333\"\n                                        },\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-semibold mb-2\",\n                                        children: settings.description || \"مجموعة أوكا للخدمات المتكاملة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mt-3 space-y-1 text-xs leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCE7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.email || \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCDE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.complaintsPhone || \"0114954118\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.address || \"73 ش 6 اكتوبر الجراش جسر السويس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-4 text-sm bg-white p-3 rounded-lg border border-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"grid grid-cols-1 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"رقم الفاتورة:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                    children: [\n                                                        \"#\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"التاريخ:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"العميل:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"الهاتف:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t-2 border-dashed border-gray-400 pt-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center font-bold mb-3 text-sm bg-white py-2 rounded-lg border border-gray-400\",\n                                        children: \"\\uD83D\\uDCCB تفاصيل الطلب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    invoice.services.map((service, index)=>{\n                                        const serviceInfo = getServiceDetails(service);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-3 p-2 bg-white rounded-lg border border-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center font-bold text-sm mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#000\"\n                                                            },\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: [\n                                                                index + 1,\n                                                                \". \",\n                                                                getServiceName(service.serviceType)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#000\",\n                                                                fontWeight: \"bold\"\n                                                            },\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: formatCurrency(serviceInfo.serviceTotal)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs mr-3 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                            children: serviceInfo.details\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-34d022bd924bccec\",\n                                                                    children: [\n                                                                        \"الكمية: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-semibold\",\n                                                                            children: serviceInfo.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 37\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-34d022bd924bccec\",\n                                                                    children: [\n                                                                        \"السعر: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-semibold\",\n                                                                            children: formatCurrency(Number(serviceInfo.quantity) > 0 ? serviceInfo.serviceTotal / Number(serviceInfo.quantity) : serviceInfo.serviceTotal)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 36\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, service.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t-2 border-dashed border-gray-400 pt-4 mb-4 bg-white p-3 rounded-lg border border-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المجموع الكلي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\",\n                                                        fontWeight: \"bold\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المبلغ المدفوع:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\",\n                                                        fontWeight: \"bold\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: formatCurrency(invoice.paidAmount || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المبلغ المتبقي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\",\n                                                        fontWeight: \"bold\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: formatCurrency(invoice.total - (invoice.paidAmount || 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t border-dashed border-gray-400 pt-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"inline-block bg-white border-2 rounded-lg px-4 py-2 shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-bold\",\n                                        children: [\n                                            \"حالة الدفع:\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"mr-2 px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-orange-100 text-orange-800\" : \"bg-red-100 text-red-800\"),\n                                                children: invoice.paymentStatus\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t-2 border-dashed border-gray-400 pt-4 text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"bg-blue-50 p-3 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold text-blue-800 mb-1\",\n                                            children: \"\\uD83D\\uDE4F شكراً لتعاملكم معنا\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-blue-600 mb-2\",\n                                            children: \"Thank you for your business\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t border-blue-200 pt-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-600\",\n                                                    children: [\n                                                        \"تاريخ الطباعة: \",\n                                                        new Date().toLocaleDateString(\"ar-EG\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                                    children: [\n                                                        \"Printed: \",\n                                                        new Date().toLocaleString(\"en-US\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"p-4 border-t border-gray-200 print:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePrint,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"\\uD83D\\uDDA8️ طباعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"إغلاق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"34d022bd924bccec\",\n                children: '@media print{*.jsx-34d022bd924bccec{-webkit-print-color-adjust:exact!important;color-adjust:exact!important}body.jsx-34d022bd924bccec{margin:0!important;padding:0!important;background:white!important}.pos-print-content.jsx-34d022bd924bccec{width:80mm!important;max-width:80mm!important;font-family:\"Courier New\",monospace!important;font-size:10px!important;line-height:1.2!important;color:black!important;background:white!important;margin:0!important;padding:2mm!important}.pos-invoice-content.jsx-34d022bd924bccec{width:100%!important;max-width:100%!important;font-size:10px!important;line-height:1.2!important;padding:0!important;margin:0!important}.pos-invoice-content.jsx-34d022bd924bccec .inline-block.jsx-34d022bd924bccec{display:inline-block!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gray-100.jsx-34d022bd924bccec{background-color:#f3f4f6!important}.pos-invoice-content.jsx-34d022bd924bccec .rounded-lg.jsx-34d022bd924bccec{-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important}.pos-invoice-content.jsx-34d022bd924bccec .w-16.jsx-34d022bd924bccec{width:32px!important;height:32px!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gradient-to-br.jsx-34d022bd924bccec{background:-webkit-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-moz-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-o-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:linear-gradient(135deg,#2563eb,#1d4ed8)!important}.pos-invoice-content.jsx-34d022bd924bccec .text-white.jsx-34d022bd924bccec{color:white!important}.pos-invoice-content.jsx-34d022bd924bccec .font-semibold.jsx-34d022bd924bccec{font-weight:600!important}@page{size:80mm auto;margin:2mm}.print\\\\\\\\.jsx-34d022bd924bccec:hidden{display:none!important}}.pos-invoice-content.jsx-34d022bd924bccec{font-family:\"Courier New\",monospace;font-size:12px;line-height:1.4;color:#333}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, undefined);\n};\n_s(POSInvoice, \"M+qfwJl7bPrrTj6eCKG63WmFpYU=\", false, function() {\n    return [\n        _store_settingsStore__WEBPACK_IMPORTED_MODULE_4__.useSettingsStore\n    ];\n});\n_c = POSInvoice;\n/* harmony default export */ __webpack_exports__[\"default\"] = (POSInvoice);\nvar _c;\n$RefreshReg$(_c, \"POSInvoice\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/POSInvoice.tsx\n"));

/***/ })

});