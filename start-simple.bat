@echo off
title تشغيل تطبيق إدارة الفواتير
cls

echo ========================================
echo    تطبيق إدارة الفواتير - infapapp
echo ========================================
echo.

:: التحقق من Node.js
echo فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js مثبت

:: التحقق من npm
echo فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: npm غير متوفر
    pause
    exit /b 1
)
echo ✓ npm متوفر

:: التحقق من ملفات المشروع
echo فحص ملفات المشروع...
if not exist "package.json" (
    echo خطأ: ملف package.json غير موجود
    echo تأكد من تشغيل الملف من مجلد المشروع
    pause
    exit /b 1
)
echo ✓ ملف package.json موجود

:: تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if errorlevel 1 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✓ تم تثبيت التبعيات
) else (
    echo ✓ التبعيات موجودة
)

:: تنظيف ملفات البناء القديمة
if exist ".next" (
    echo تنظيف ملفات البناء القديمة...
    rmdir /s /q .next >nul 2>&1
)

echo.
echo تشغيل خادم التطوير...
echo سيتم فتح التطبيق على http://localhost:3000
echo لإيقاف الخادم اضغط Ctrl+C
echo.
echo ========================================

:: تشغيل الخادم
npm run dev

echo.
echo تم إغلاق الخادم
pause
