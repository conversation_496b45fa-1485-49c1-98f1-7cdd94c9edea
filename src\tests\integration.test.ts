/**
 * اختبارات التكامل الشاملة للنظام الموحد
 */

import { UnifiedStorageManager } from '@/utils/unifiedStorage';
import { STORAGE_KEYS } from '@/utils/storageKeys';
import { initializeApp, checkAppHealth } from '@/utils/appInitializer';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    clear: () => { store = {}; },
    length: Object.keys(store).length,
    key: (index: number) => Object.keys(store)[index] || null
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock console methods
const originalConsole = { ...console };
beforeEach(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  localStorageMock.clear();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

describe('🔄 اختبارات التكامل الشاملة', () => {
  
  describe('📊 اختبار النظام الموحد الأساسي', () => {
    
    test('✅ يجب أن يحفظ ويقرأ البيانات بنجاح', async () => {
      const testData = { name: 'اختبار', value: 123 };
      
      // حفظ البيانات
      const saveResult = await UnifiedStorageManager.setItem('test-key', testData);
      expect(saveResult.success).toBe(true);
      expect(saveResult.data).toEqual(testData);
      
      // قراءة البيانات
      const loadResult = await UnifiedStorageManager.getItem('test-key', {});
      expect(loadResult.success).toBe(true);
      expect(loadResult.data).toEqual(testData);
    });
    
    test('🔄 يجب أن يدمج البيانات المكررة', async () => {
      // إنشاء بيانات مكررة
      localStorageMock.setItem('companySettings', JSON.stringify({ name: 'الشركة الأولى' }));
      localStorageMock.setItem('company-settings', JSON.stringify({ email: '<EMAIL>' }));
      localStorageMock.setItem('companyInfo', JSON.stringify({ phone: '*********' }));
      
      // تشغيل الدمج
      const mergeResult = await UnifiedStorageManager.mergeDuplicateKeys();
      
      expect(mergeResult.totalMerged).toBeGreaterThan(0);
      expect(mergeResult.mergedKeys).toContain('companySettings');
      
      // التحقق من الدمج
      const mergedResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      expect(mergedResult.success).toBe(true);
      expect(mergedResult.data).toMatchObject({
        name: 'الشركة الأولى',
        email: '<EMAIL>',
        phone: '*********'
      });
    });
    
    test('🏥 يجب أن يفحص صحة النظام', async () => {
      // إضافة بعض البيانات
      await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, []);
      await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, []);
      
      const healthCheck = await UnifiedStorageManager.checkUnifiedSystemHealth();
      
      expect(healthCheck.isHealthy).toBe(true);
      expect(healthCheck.totalKeys).toBeGreaterThanOrEqual(2);
      expect(healthCheck.issues).toHaveLength(0);
    });
  });
  
  describe('🚀 اختبار تهيئة التطبيق', () => {
    
    test('✅ يجب أن يهيئ التطبيق بنجاح', async () => {
      // إضافة بيانات أولية
      localStorageMock.setItem('invoices', JSON.stringify([]));
      localStorageMock.setItem('customers', JSON.stringify([]));
      localStorageMock.setItem('companySettings', JSON.stringify({ companyName: 'OKA Group' }));
      
      const result = await initializeApp();
      
      expect(result.success).toBe(true);
      expect(result.details.unifiedStorage).toBe(true);
      expect(result.details.invoiceStore).toBe(true);
      expect(result.details.customerStore).toBe(true);
      expect(result.details.settingsStore).toBe(true);
    });
    
    test('🏥 يجب أن يفحص صحة التطبيق', async () => {
      // إضافة بيانات للفحص
      await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, []);
      
      const healthResult = await checkAppHealth();
      
      expect(healthResult.isHealthy).toBeDefined();
      expect(healthResult.issues).toBeDefined();
      expect(healthResult.recommendations).toBeDefined();
      expect(healthResult.details).toBeDefined();
    });
  });
  
  describe('📋 اختبار سيناريوهات حقيقية', () => {
    
    test('🔄 سيناريو كامل: إضافة فاتورة وعميل', async () => {
      // 1. تهيئة التطبيق
      const initResult = await initializeApp();
      expect(initResult.success).toBe(true);
      
      // 2. إضافة عميل
      const customerData = {
        name: 'عميل تجريبي',
        email: '<EMAIL>',
        phone: '*********'
      };
      
      const customerResult = await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, [customerData]);
      expect(customerResult.success).toBe(true);
      
      // 3. إضافة فاتورة
      const invoiceData = {
        id: 'invoice-1',
        customerName: 'عميل تجريبي',
        total: 1000,
        date: new Date().toISOString()
      };
      
      const invoiceResult = await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, [invoiceData]);
      expect(invoiceResult.success).toBe(true);
      
      // 4. التحقق من البيانات
      const savedCustomers = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
      const savedInvoices = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
      
      expect(savedCustomers.data).toHaveLength(1);
      expect(savedInvoices.data).toHaveLength(1);
      expect(savedCustomers.data[0].name).toBe('عميل تجريبي');
      expect(savedInvoices.data[0].customerName).toBe('عميل تجريبي');
    });
    
    test('🔧 سيناريو استرداد من الأخطاء', async () => {
      // محاكاة خطأ في localStorage
      const originalGetItem = localStorageMock.getItem;
      localStorageMock.getItem = jest.fn(() => {
        throw new Error('Storage error');
      });
      
      // محاولة قراءة البيانات
      const result = await UnifiedStorageManager.getItem('test-key', { default: true });
      
      // يجب أن يعيد القيمة الافتراضية
      expect(result.success).toBe(false);
      expect(result.data).toEqual({ default: true });
      
      // استعادة الدالة الأصلية
      localStorageMock.getItem = originalGetItem;
    });
    
    test('📊 سيناريو ترقية البيانات القديمة', async () => {
      // إضافة بيانات بالتنسيق القديم
      localStorageMock.setItem('invoices', JSON.stringify([
        { id: '1', customer: 'عميل قديم', amount: 500 }
      ]));
      localStorageMock.setItem('company-settings', JSON.stringify({
        name: 'الشركة القديمة'
      }));
      
      // تشغيل الترقية
      await initializeApp();
      
      // التحقق من الترقية
      const upgradedInvoices = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
      const upgradedSettings = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      
      expect(upgradedInvoices.success).toBe(true);
      expect(upgradedSettings.success).toBe(true);
      expect(upgradedSettings.data.name).toBe('الشركة القديمة');
    });
  });
  
  describe('⚡ اختبار الأداء', () => {
    
    test('🚀 يجب أن يكون سريعاً في العمليات الأساسية', async () => {
      const startTime = Date.now();
      
      // تشغيل عدة عمليات
      await Promise.all([
        UnifiedStorageManager.setItem('test1', { data: 'test1' }),
        UnifiedStorageManager.setItem('test2', { data: 'test2' }),
        UnifiedStorageManager.setItem('test3', { data: 'test3' })
      ]);
      
      await Promise.all([
        UnifiedStorageManager.getItem('test1', {}),
        UnifiedStorageManager.getItem('test2', {}),
        UnifiedStorageManager.getItem('test3', {})
      ]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // يجب أن يكمل في أقل من ثانية واحدة
      expect(duration).toBeLessThan(1000);
    });
  });
  
  describe('🔒 اختبار الأمان', () => {
    
    test('🛡️ يجب أن يتعامل مع البيانات الحساسة بأمان', async () => {
      const sensitiveData = {
        password: 'secret123',
        apiKey: 'api-key-secret',
        personalInfo: 'معلومات شخصية'
      };
      
      // حفظ البيانات الحساسة
      const saveResult = await UnifiedStorageManager.setItem('sensitive-data', sensitiveData);
      expect(saveResult.success).toBe(true);
      
      // التحقق من التشفير (البيانات لا تظهر كما هي في localStorage)
      const rawData = localStorageMock.getItem('sensitive-data');
      expect(rawData).toBeDefined();
      // البيانات يجب أن تكون مشفرة أو محمية
      expect(rawData).not.toContain('secret123');
      
      // قراءة البيانات المحمية
      const loadResult = await UnifiedStorageManager.getItem('sensitive-data', {});
      expect(loadResult.success).toBe(true);
      expect(loadResult.data).toEqual(sensitiveData);
    });
  });
});
