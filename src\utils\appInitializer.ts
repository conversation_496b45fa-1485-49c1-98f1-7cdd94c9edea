/**
 * مهيئ التطبيق - يقوم بتهيئة جميع المتاجر والأنظمة
 * يجب استدعاؤه عند بدء التطبيق
 */

import { initializeInvoiceStore } from '@/store/invoiceStore';
import { UnifiedStorageManager } from './unifiedStorage';

/**
 * تهيئة شاملة للتطبيق
 */
export const initializeApp = async (): Promise<{
  success: boolean;
  message: string;
  details: any;
}> => {
  try {
    console.log('🚀 بدء تهيئة التطبيق...');
    
    const initResults = {
      unifiedStorage: false,
      invoiceStore: false,
      systemHealth: null as any
    };
    
    // 1. فحص وتهيئة النظام الموحد
    try {
      console.log('🔧 فحص النظام الموحد...');
      const healthCheck = await UnifiedStorageManager.checkUnifiedSystemHealth();
      initResults.systemHealth = healthCheck;
      
      if (!healthCheck.isHealthy) {
        console.log('⚠️ النظام يحتاج لإصلاحات، بدء عملية الدمج...');
        const mergeResult = await UnifiedStorageManager.mergeDuplicateKeys();
        console.log('🔄 نتيجة الدمج:', mergeResult);
      }
      
      initResults.unifiedStorage = true;
      console.log('✅ النظام الموحد جاهز');
      
    } catch (error) {
      console.error('❌ فشل في تهيئة النظام الموحد:', error);
      initResults.unifiedStorage = false;
    }
    
    // 2. تهيئة متجر الفواتير
    try {
      console.log('📋 تهيئة متجر الفواتير...');
      await initializeInvoiceStore();
      initResults.invoiceStore = true;
      console.log('✅ متجر الفواتير جاهز');
      
    } catch (error) {
      console.error('❌ فشل في تهيئة متجر الفواتير:', error);
      initResults.invoiceStore = false;
    }
    
    // 3. تقييم النتائج
    const allSuccess = initResults.unifiedStorage && initResults.invoiceStore;
    
    if (allSuccess) {
      console.log('🎉 تم تهيئة التطبيق بنجاح!');
      return {
        success: true,
        message: 'تم تهيئة التطبيق بنجاح',
        details: initResults
      };
    } else {
      console.warn('⚠️ تم تهيئة التطبيق مع بعض المشاكل');
      return {
        success: false,
        message: 'تم تهيئة التطبيق مع بعض المشاكل',
        details: initResults
      };
    }
    
  } catch (error) {
    console.error('💥 فشل حرج في تهيئة التطبيق:', error);
    return {
      success: false,
      message: `فشل في تهيئة التطبيق: ${(error as Error).message}`,
      details: { error: error }
    };
  }
};

/**
 * فحص حالة التطبيق
 */
export const checkAppHealth = async (): Promise<{
  isHealthy: boolean;
  issues: string[];
  recommendations: string[];
  details: any;
}> => {
  try {
    // فحص النظام الموحد
    const systemHealth = await UnifiedStorageManager.checkUnifiedSystemHealth();
    
    // فحص المتاجر
    const storeChecks = {
      invoiceStore: typeof window !== 'undefined' && 
                   window.localStorage.getItem('invoices') !== null
    };
    
    const allIssues = [...systemHealth.issues];
    const allRecommendations = [...systemHealth.recommendations];
    
    if (!storeChecks.invoiceStore) {
      allIssues.push('متجر الفواتير غير مهيأ');
      allRecommendations.push('تشغيل تهيئة التطبيق');
    }
    
    return {
      isHealthy: systemHealth.isHealthy && storeChecks.invoiceStore,
      issues: allIssues,
      recommendations: allRecommendations,
      details: {
        systemHealth,
        storeChecks
      }
    };
    
  } catch (error) {
    return {
      isHealthy: false,
      issues: [`خطأ في فحص التطبيق: ${(error as Error).message}`],
      recommendations: ['إعادة تشغيل التطبيق'],
      details: { error }
    };
  }
};

/**
 * إعادة تهيئة التطبيق (في حالة المشاكل)
 */
export const reinitializeApp = async (): Promise<{
  success: boolean;
  message: string;
}> => {
  try {
    console.log('🔄 إعادة تهيئة التطبيق...');
    
    // مسح الحالة الحالية
    if (typeof window !== 'undefined') {
      // إعادة تحميل المتاجر
      const { useInvoiceStore } = await import('@/store/invoiceStore');
      useInvoiceStore.setState({
        invoices: [],
        currentInvoice: null,
        currentClient: null
      });
    }
    
    // تهيئة جديدة
    const result = await initializeApp();
    
    if (result.success) {
      console.log('✅ تمت إعادة التهيئة بنجاح');
      return {
        success: true,
        message: 'تمت إعادة تهيئة التطبيق بنجاح'
      };
    } else {
      console.error('❌ فشلت إعادة التهيئة');
      return {
        success: false,
        message: 'فشلت إعادة تهيئة التطبيق'
      };
    }
    
  } catch (error) {
    console.error('💥 خطأ حرج في إعادة التهيئة:', error);
    return {
      success: false,
      message: `فشل في إعادة التهيئة: ${(error as Error).message}`
    };
  }
};
