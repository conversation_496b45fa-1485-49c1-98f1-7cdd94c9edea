"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ClientForm.tsx":
/*!***************************************!*\
  !*** ./src/components/ClientForm.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst availableServices = [\n    {\n        value: \"consultation\",\n        label: \"استشارات\"\n    },\n    {\n        value: \"pattern\",\n        label: \"باترون\"\n    },\n    {\n        value: \"pattern_printing\",\n        label: \"طباعة باترون\"\n    },\n    {\n        value: \"manufacturing\",\n        label: \"تصنيع\"\n    },\n    {\n        value: \"samples\",\n        label: \"عينات\"\n    },\n    {\n        value: \"shipping\",\n        label: \"شحن/تعبئة\"\n    },\n    {\n        value: \"marketing\",\n        label: \"تسويق\"\n    },\n    {\n        value: \"photography\",\n        label: \"تصوير\"\n    },\n    {\n        value: \"products\",\n        label: \"منتجات\"\n    },\n    {\n        value: \"raw_purchases\",\n        label: \"مشتريات خام\"\n    }\n];\nconst contactMethods = [\n    {\n        value: \"whatsapp\",\n        label: \"واتساب\"\n    },\n    {\n        value: \"meta\",\n        label: \"ميتا\"\n    },\n    {\n        value: \"meeting\",\n        label: \"مقابلة\"\n    },\n    {\n        value: \"phone\",\n        label: \"هاتف\"\n    }\n];\nconst categories = [\n    {\n        value: \"A\",\n        label: \"فئة A\"\n    },\n    {\n        value: \"B\",\n        label: \"فئة B\"\n    },\n    {\n        value: \"C\",\n        label: \"فئة C\"\n    }\n];\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_2__.object({\n    name: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم العميل مطلوب\"),\n    phone: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"رقم الموبايل مطلوب\"),\n    services: yup__WEBPACK_IMPORTED_MODULE_2__.array().min(1, \"يجب اختيار خدمة واحدة على الأقل\"),\n    contactMethod: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"وسيلة التواصل مطلوبة\"),\n    salesperson: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم السيلز مطلوب\"),\n    initialCategory: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"تصنيف العميل مطلوب\"),\n    clientCode: yup__WEBPACK_IMPORTED_MODULE_2__.string().test(\"valid-code\", \"تنسيق كود العميل غير صحيح\", function(value) {\n        if (!value) return true; // الكود اختياري\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerCode)(value);\n        return validation.isValid;\n    })\n}).test(\"unique-customer\", \"بيانات العميل\", function(values) {\n    var _this_options_context;\n    const { name, phone, clientCode } = values;\n    if (!name || !phone) return true; // سيتم التحقق من هذه الحقول بشكل منفصل\n    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerUniqueness)({\n        name,\n        phone,\n        clientCode\n    }, (_this_options_context = this.options.context) === null || _this_options_context === void 0 ? void 0 : _this_options_context.excludeId);\n    if (!validation.isValid) {\n        return this.createError({\n            path: \"name\",\n            message: validation.errors.join(\", \")\n        });\n    }\n    return true;\n});\nconst ClientForm = (param)=>{\n    let { onSubmit, initialValues } = param;\n    _s();\n    const setCurrentClient = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore)((state)=>state.setCurrentClient);\n    const { generateNewCustomerCode, validateCode, getSuggestions, suggestions, clearSuggestions, customers } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore)();\n    const [showCodeGenerator, setShowCodeGenerator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [codeStats, setCodeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationSuggestions, setValidationSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExistingCustomers, setShowExistingCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedExistingCustomer, setSelectedExistingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.getCustomerCodeStats)();\n        setCodeStats(stats);\n    }, []);\n    // التحقق من التكرار عند تغيير البيانات\n    const checkForDuplicates = (name, phone, clientCode)=>{\n        if (!name.trim() || !phone.trim()) {\n            setValidationWarnings([]);\n            setValidationSuggestions([]);\n            return;\n        }\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerUniqueness)({\n            name: name.trim(),\n            phone: phone.trim(),\n            clientCode\n        }, initialValues === null || initialValues === void 0 ? void 0 : initialValues.id);\n        setValidationWarnings(validation.warnings);\n        setValidationSuggestions(validation.suggestions || []);\n    };\n    // اختيار عميل موجود\n    const handleSelectExistingCustomer = (customer, setValues)=>{\n        setSelectedExistingCustomer(customer);\n        setValues({\n            ...customer,\n            services: customer.services || []\n        });\n        setShowExistingCustomers(false);\n        setValidationWarnings([]);\n        setValidationSuggestions([]);\n    };\n    const defaultValues = {\n        name: \"\",\n        phone: \"\",\n        services: [],\n        contactMethod: \"whatsapp\",\n        salesperson: \"\",\n        initialCategory: \"A\",\n        businessName: \"\",\n        province: \"\",\n        clientCode: \"\",\n        ...initialValues\n    };\n    const handleSubmit = (values)=>{\n        // إذا لم يتم تحديد كود العميل، إنشاء كود تلقائي\n        const finalValues = {\n            ...values,\n            clientCode: values.clientCode || generateNewCustomerCode()\n        };\n        setCurrentClient(finalValues);\n        onSubmit(finalValues);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"بيانات العميل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"أدخل المعلومات الأساسية للعميل لبدء إنشاء الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-blue-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"الخطوة 1 من 3 - معلومات العميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            (validationWarnings.length > 0 || validationSuggestions.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    validationWarnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-yellow-800 mb-2\",\n                                            children: \"تحذير - عميل مشابه موجود\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-yellow-700 space-y-1\",\n                                            children: validationWarnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• \",\n                                                        warning\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, undefined),\n                    validationSuggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-800 mb-2\",\n                                            children: \"اقتراحات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-700 space-y-1\",\n                                            children: validationSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• \",\n                                                        suggestion\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Formik, {\n                initialValues: defaultValues,\n                validationSchema: validationSchema,\n                onSubmit: handleSubmit,\n                children: (param)=>{\n                    let { values, setFieldValue, errors, touched, setValues } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-blue-100 to-indigo-100 px-6 py-4 border-b border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-blue-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"اختيار عميل موجود أو إضافة عميل جديد\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600 mt-1\",\n                                                children: \"يمكنك اختيار عميل من قاعدة البيانات أو إضافة عميل جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowExistingCustomers(!showExistingCustomers),\n                                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 hover:shadow-lg hover:scale-105 active:scale-95\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showExistingCustomers ? \"إخفاء العملاء الموجودين\" : \"اختيار من العملاء الموجودين\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    selectedExistingCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-green-600\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-green-800 font-medium\",\n                                                                        children: selectedExistingCustomer.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-green-600 text-sm\",\n                                                                        children: [\n                                                                            selectedExistingCustomer.clientCode,\n                                                                            \" - \",\n                                                                            selectedExistingCustomer.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>{\n                                                                    setSelectedExistingCustomer(null);\n                                                                    setValues(defaultValues);\n                                                                },\n                                                                className: \"text-green-600 hover:text-green-800 p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showExistingCustomers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 bg-white rounded-lg border border-gray-200 max-h-96 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border-b border-gray-200 bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-800\",\n                                                            children: [\n                                                                \"العملاء المسجلين (\",\n                                                                customers.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"divide-y divide-gray-200\",\n                                                        children: customers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 text-center text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 mx-auto mb-3 text-gray-300\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"لا يوجد عملاء مسجلين بعد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: \"ابدأ بإضافة عميل جديد أدناه\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 25\n                                                        }, undefined) : customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-4 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                                onClick: ()=>handleSelectExistingCustomer(customer, setValues),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-blue-600 font-medium text-sm\",\n                                                                                            children: customer.name.charAt(0)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 293,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 292,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"font-medium text-gray-900\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 298,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.clientCode\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 300,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.phone\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 301,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: customer.businessName\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                        lineNumber: 302,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 299,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        customer.services.length,\n                                                                                        \" خدمة\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: customer.salesperson\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, customer.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" اسم العميل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"name\",\n                                                                children: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        ...field,\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.name && touched.name ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"أدخل اسم العميل الكامل\",\n                                                                        onChange: (e)=>{\n                                                                            if (field === null || field === void 0 ? void 0 : field.onChange) {\n                                                                                field.onChange(e);\n                                                                            }\n                                                                            checkForDuplicates(e.target.value, values.phone, values.clientCode);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, undefined);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"name\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" رقم الموبايل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"phone\",\n                                                                children: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        ...field,\n                                                                        type: \"tel\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.phone && touched.phone ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"01xxxxxxxxx\",\n                                                                        onChange: (e)=>{\n                                                                            field.onChange(e);\n                                                                            checkForDuplicates(values.name, e.target.value, values.clientCode);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, undefined);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"phone\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"الاسم التجاري\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"businessName\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"اسم الشركة أو المتجر (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"businessName\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"المحافظة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 422,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"province\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"المحافظة (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"province\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"كود العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 443,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { field, form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ...field,\n                                                                                type: \"text\",\n                                                                                className: \"form-input-enhanced pr-10\",\n                                                                                placeholder: \"مثال: \".concat((codeStats === null || codeStats === void 0 ? void 0 : codeStats.nextCode) || \"p013621\"),\n                                                                                onChange: (e)=>{\n                                                                                    if (field === null || field === void 0 ? void 0 : field.onChange) {\n                                                                                        field.onChange(e);\n                                                                                    }\n                                                                                    if (e.target.value) {\n                                                                                        getSuggestions(e.target.value);\n                                                                                    } else {\n                                                                                        clearSuggestions();\n                                                                                    }\n                                                                                },\n                                                                                onBlur: (e)=>{\n                                                                                    if (field === null || field === void 0 ? void 0 : field.onBlur) {\n                                                                                        field.onBlur(e);\n                                                                                    }\n                                                                                    clearSuggestions();\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                                        children: suggestions.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                                                onClick: ()=>{\n                                                                                    // يمكن إضافة منطق لملء البيانات من العميل المقترح\n                                                                                    clearSuggestions();\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-blue-600\",\n                                                                                                children: customer.clientCode\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 486,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 487,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 485,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: customer.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 489,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowCodeGenerator(!showCodeGenerator),\n                                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"إعدادات الكود\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    const newCode = generateNewCustomerCode();\n                                                                                    form.setFieldValue(\"clientCode\", newCode);\n                                                                                },\n                                                                                className: \"flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 520,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 519,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \"إنشاء كود جديد\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 511,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showCodeGenerator && codeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: \"إحصائيات أكواد العملاء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"إجمالي العملاء:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 534,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-blue-600 mr-2\",\n                                                                                        children: codeStats.totalCustomers\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 533,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"الكود التالي:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 538,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-green-600 mr-2\",\n                                                                                        children: codeStats.nextCode\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 539,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مُنشأة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 542,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-purple-600 mr-2\",\n                                                                                        children: codeStats.generatedCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 543,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مخصصة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 546,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-orange-600 mr-2\",\n                                                                                        children: codeStats.customCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 545,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 leading-relaxed\",\n                                                                        children: \"تنسيق الكود: p + رقم المعاملة (01) + رقم العميل (3621). إذا تُرك فارغاً، سيتم إنشاء كود تلقائياً.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"clientCode\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"الخدمات المطلوبة\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-sm\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"اختر الخدمات التي يحتاجها العميل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: availableServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"service-card group cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                type: \"checkbox\",\n                                                                name: \"services\",\n                                                                value: service.value,\n                                                                className: \"sr-only\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"service-card-content\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"service-icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"service-label\",\n                                                                        children: service.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.value, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                name: \"services\",\n                                                component: \"div\",\n                                                className: \"error-message-enhanced mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-purple-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"التواصل والمبيعات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" وسيلة التواصل المفضلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3\",\n                                                        children: contactMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"contact-method-card group cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        type: \"radio\",\n                                                                        name: \"contactMethod\",\n                                                                        value: method.value,\n                                                                        className: \"sr-only\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"contact-method-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"contact-icon\",\n                                                                                children: [\n                                                                                    method.value === \"whatsapp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 635,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meta\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 639,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meeting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 645,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 644,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"phone\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 650,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 649,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"contact-label\",\n                                                                                children: method.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, method.value, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"contactMethod\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" اسم السيلز\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"salesperson\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.salesperson && touched.salesperson ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"اسم مندوب المبيعات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"salesperson\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" تصنيف العميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 691,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        as: \"select\",\n                                                                        name: \"initialCategory\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.initialCategory && touched.initialCategory ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: category.value,\n                                                                                children: category.label\n                                                                            }, category.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 700,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"initialCategory\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center items-center gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary-enhanced group w-full sm:w-auto\",\n                                        disabled: Object.keys(errors).length > 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"حفظ بيانات العميل والمتابعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"البيانات محفوظة محلياً وآمنة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientForm, \"DyazIHoIqx5zDl+vKaC04U3ySPg=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore\n    ];\n});\n_c = ClientForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientForm);\nvar _c;\n$RefreshReg$(_c, \"ClientForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClientForm.tsx\n"));

/***/ })

});