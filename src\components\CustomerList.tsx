/**
 * مكون قائمة العملاء مع البحث والتصفية
 */

import React, { useState, useEffect } from 'react';
import { Client } from '@/types';
import { useCustomerStore } from '@/store/customerStore';

interface CustomerListProps {
  onSelectCustomer?: (customer: Client) => void;
  onEditCustomer?: (customer: Client) => void;
  onDeleteCustomer?: (customerId: string) => void;
  onCreateInvoice?: (customer: Client) => void;
  onViewDetails?: (customer: Client) => void;
  selectable?: boolean;
  showActions?: boolean;
}

const CustomerList: React.FC<CustomerListProps> = ({
  onSelectCustomer,
  onEditCustomer,
  onDeleteCustomer,
  onCreateInvoice,
  onViewDetails,
  selectable = false,
  showActions = true,
}) => {
  const {
    customers,
    searchQuery,
    setSearchQuery,
    searchCustomers,
    isLoading,
    error,
    loadCustomers,
    clearError,
  } = useCustomerStore();

  const [filteredCustomers, setFilteredCustomers] = useState<Client[]>([]);
  const [sortBy, setSortBy] = useState<'name' | 'code' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);

  useEffect(() => {
    loadCustomers();
  }, [loadCustomers]);

  useEffect(() => {
    let filtered = searchQuery ? searchCustomers(searchQuery) : customers;
    
    // ترتيب النتائج
    filtered = [...filtered].sort((a, b) => {
      let aValue: string;
      let bValue: string;
      
      switch (sortBy) {
        case 'code':
          aValue = a.clientCode || '';
          bValue = b.clientCode || '';
          break;
        case 'date':
          aValue = a.id || '';
          bValue = b.id || '';
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }
      
      const comparison = aValue.localeCompare(bValue, 'ar');
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    setFilteredCustomers(filtered);
  }, [customers, searchQuery, sortBy, sortOrder, searchCustomers]);

  const handleSort = (field: 'name' | 'code' | 'date') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectCustomer = (customer: Client) => {
    if (selectable) {
      setSelectedCustomer(customer.id || null);
      onSelectCustomer?.(customer);
    }
  };

  const getContactMethodIcon = (method: string) => {
    switch (method) {
      case 'whatsapp':
        return '📱';
      case 'meta':
        return '💬';
      case 'meeting':
        return '🤝';
      case 'phone':
        return '📞';
      default:
        return '📞';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B':
        return 'bg-yellow-100 text-yellow-800';
      case 'C':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="mr-3 text-gray-600">جاري تحميل العملاء...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-500 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
            </svg>
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={clearError}
            className="text-red-600 hover:text-red-800"
          >
            ✕
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* شريط البحث والتصفية */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* البحث */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
                </svg>
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="البحث بالاسم، الهاتف، أو كود العميل..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          {/* خيارات الترتيب */}
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'code' | 'date')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="name">ترتيب بالاسم</option>
              <option value="code">ترتيب بالكود</option>
              <option value="date">ترتيب بالتاريخ</option>
            </select>
            
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>
        
        {/* إحصائيات سريعة */}
        <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
          <span>إجمالي العملاء: <strong className="text-blue-600">{customers.length}</strong></span>
          <span>النتائج المعروضة: <strong className="text-green-600">{filteredCustomers.length}</strong></span>
        </div>
      </div>

      {/* قائمة العملاء */}
      {filteredCustomers.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عملاء</h3>
          <p className="text-gray-500">
            {searchQuery ? 'لم يتم العثور على عملاء مطابقين لبحثك' : 'لم يتم إضافة أي عملاء بعد'}
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th 
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('name')}
                  >
                    اسم العميل
                    {sortBy === 'name' && (
                      <span className="mr-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th 
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('code')}
                  >
                    كود العميل
                    {sortBy === 'code' && (
                      <span className="mr-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    رقم الهاتف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التصنيف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التواصل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الخدمات
                  </th>
                  {showActions && (
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCustomers.map((customer) => (
                  <tr
                    key={customer.id}
                    className={`hover:bg-gray-50 transition-colors ${
                      selectable && selectedCustomer === customer.id ? 'bg-blue-50' : ''
                    } ${selectable ? 'cursor-pointer' : ''}`}
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                        {customer.businessName && (
                          <div className="text-sm text-gray-500">{customer.businessName}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {customer.clientCode}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {customer.phone}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(customer.initialCategory)}`}>
                        فئة {customer.initialCategory}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="flex items-center">
                        <span className="ml-1">{getContactMethodIcon(customer.contactMethod)}</span>
                        {customer.contactMethod}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {customer.services.slice(0, 2).map((service, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {service}
                          </span>
                        ))}
                        {customer.services.length > 2 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            +{customer.services.length - 2}
                          </span>
                        )}
                      </div>
                    </td>
                    {showActions && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          {onViewDetails && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onViewDetails(customer);
                              }}
                              className="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded-md text-xs font-medium transition-colors flex items-center gap-1"
                              title="عرض تفاصيل العميل الشاملة"
                            >
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                              </svg>
                              تفاصيل
                            </button>
                          )}
                          {onCreateInvoice && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onCreateInvoice(customer);
                              }}
                              className="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-md text-xs font-medium transition-colors flex items-center gap-1"
                              title="إنشاء فاتورة جديدة لهذا العميل"
                            >
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                              </svg>
                              فاتورة
                            </button>
                          )}
                          {onEditCustomer && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onEditCustomer(customer);
                              }}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              تعديل
                            </button>
                          )}
                          {onDeleteCustomer && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                                  onDeleteCustomer(customer.id!);
                                }
                              }}
                              className="text-red-600 hover:text-red-900"
                            >
                              حذف
                            </button>
                          )}
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerList;
