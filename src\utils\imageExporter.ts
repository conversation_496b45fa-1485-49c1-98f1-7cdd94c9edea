import html2canvas from 'html2canvas';

/**
 * انتظار تحميل جميع الصور في العنصر
 * @param element العنصر المراد فحصه
 */
const waitForImages = (element: HTMLElement): Promise<void> => {
  return new Promise((resolve) => {
    const images = element.querySelectorAll('img');
    if (images.length === 0) {
      resolve();
      return;
    }

    let loadedCount = 0;
    const totalImages = images.length;

    const checkComplete = () => {
      loadedCount++;
      if (loadedCount === totalImages) {
        resolve();
      }
    };

    images.forEach((img) => {
      if (img.complete) {
        checkComplete();
      } else {
        img.onload = checkComplete;
        img.onerror = checkComplete; // حتى لو فشل التحميل، نكمل
      }
    });
  });
};

/**
 * تصدير عنصر HTML كصورة JPG
 * @param elementId معرف العنصر المراد تصديره
 * @param filename اسم الملف (اختياري)
 * @param options خيارات التصدير
 */
export const exportAsImage = async (
  elementId: string,
  filename?: string,
  options?: {
    quality?: number;
    scale?: number;
    backgroundColor?: string;
    width?: number;
    height?: number;
  }
): Promise<void> => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // انتظار تحميل جميع الصور
    await waitForImages(element);

    // إعدادات html2canvas المحسنة للطابعات الحرارية مع دعم أفضل للصور
    const canvas = await html2canvas(element, {
      scale: options?.scale || 2, // جودة عالية
      backgroundColor: options?.backgroundColor || '#ffffff',
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: false, // تحسين عرض الصور
      imageTimeout: 15000, // مهلة أطول لتحميل الصور
      width: options?.width || element.scrollWidth,
      height: options?.height || element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      // تحسينات للنصوص العربية
      letterRendering: true,
      logging: false,
      // تحسين جودة الخطوط والصور
      onclone: (clonedDoc) => {
        const clonedElement = clonedDoc.getElementById(elementId);
        if (clonedElement) {
          // تطبيق أنماط إضافية للطباعة
          clonedElement.style.fontFamily = 'Arial, sans-serif';
          clonedElement.style.fontSize = '14px';
          clonedElement.style.lineHeight = '1.4';

          // التأكد من أن جميع الصور مرئية
          const images = clonedElement.querySelectorAll('img');
          images.forEach((img) => {
            img.style.display = 'block';
            img.style.visibility = 'visible';
            img.style.opacity = '1';
          });
        }
      }
    });

    // تحويل إلى صورة JPG
    const imageData = canvas.toDataURL('image/jpeg', options?.quality || 0.95);
    
    // إنشاء رابط التحميل
    const link = document.createElement('a');
    link.download = filename || `فاتورة-POS-${Date.now()}.jpg`;
    link.href = imageData;
    
    // تحميل الصورة
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('تم تصدير الصورة بنجاح');
  } catch (error) {
    console.error('خطأ في تصدير الصورة:', error);
    throw error;
  }
};

/**
 * تصدير فاتورة POS كصورة مع إعدادات محسنة
 * @param invoiceId معرف الفاتورة
 * @param invoiceNumber رقم الفاتورة
 */
export const exportPOSInvoiceAsImage = async (
  invoiceId: string,
  invoiceNumber?: string
): Promise<void> => {
  const filename = `فاتورة-POS-${invoiceNumber || invoiceId}-${new Date().toISOString().split('T')[0]}.jpg`;
  
  await exportAsImage('pos-invoice-content', filename, {
    quality: 0.95,
    scale: 3, // جودة عالية جداً
    backgroundColor: '#ffffff',
    width: 320, // عرض مناسب للطابعات الحرارية 80mm
  });
};

/**
 * مشاركة فاتورة POS كصورة (إذا كان المتصفح يدعم Web Share API)
 * @param elementId معرف العنصر
 * @param title عنوان المشاركة
 */
export const sharePOSInvoiceAsImage = async (
  elementId: string,
  title: string = 'فاتورة POS'
): Promise<void> => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // انتظار تحميل جميع الصور
    await waitForImages(element);

    const canvas = await html2canvas(element, {
      scale: 2,
      backgroundColor: '#ffffff',
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: false,
      imageTimeout: 15000,
      onclone: (clonedDoc) => {
        const clonedElement = clonedDoc.getElementById(elementId);
        if (clonedElement) {
          const images = clonedElement.querySelectorAll('img');
          images.forEach((img) => {
            img.style.display = 'block';
            img.style.visibility = 'visible';
            img.style.opacity = '1';
          });
        }
      }
    });

    // تحويل إلى blob
    canvas.toBlob(async (blob) => {
      if (!blob) {
        throw new Error('فشل في إنشاء الصورة');
      }

      if (navigator.share && navigator.canShare) {
        const file = new File([blob], `${title}.jpg`, { type: 'image/jpeg' });
        
        if (navigator.canShare({ files: [file] })) {
          await navigator.share({
            title: title,
            text: 'فاتورة من OKA GROUP',
            files: [file]
          });
          return;
        }
      }
      
      // إذا لم يكن Web Share API متاحاً، استخدم التحميل العادي
      const imageData = canvas.toDataURL('image/jpeg', 0.95);
      const link = document.createElement('a');
      link.download = `${title}.jpg`;
      link.href = imageData;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    }, 'image/jpeg', 0.95);
    
  } catch (error) {
    console.error('خطأ في مشاركة الصورة:', error);
    throw error;
  }
};

/**
 * معاينة الصورة قبل التصدير
 * @param elementId معرف العنصر
 * @returns Promise<string> رابط الصورة
 */
export const previewAsImage = async (elementId: string): Promise<string> => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // انتظار تحميل جميع الصور
    await waitForImages(element);

    const canvas = await html2canvas(element, {
      scale: 2,
      backgroundColor: '#ffffff',
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: false,
      imageTimeout: 15000,
      onclone: (clonedDoc) => {
        const clonedElement = clonedDoc.getElementById(elementId);
        if (clonedElement) {
          const images = clonedElement.querySelectorAll('img');
          images.forEach((img) => {
            img.style.display = 'block';
            img.style.visibility = 'visible';
            img.style.opacity = '1';
          });
        }
      }
    });

    return canvas.toDataURL('image/jpeg', 0.95);
  } catch (error) {
    console.error('خطأ في معاينة الصورة:', error);
    throw error;
  }
};
