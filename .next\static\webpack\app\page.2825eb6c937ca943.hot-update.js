"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicesList.tsx":
/*!*****************************************!*\
  !*** ./src/components/InvoicesList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst InvoicesList = ()=>{\n    _s();\n    const { invoices } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date_desc\");\n    const [filteredInvoices, setFilteredInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previewInvoice, setPreviewInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [posInvoice, setPosInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // إعادة تحميل البيانات من التخزين الآمن عند تحميل المكون\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInvoices = ()=>{\n            // استخدام النظام الآمن لتحميل البيانات\n            const result = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState().invoices;\n            // التحقق من عدم وجود تكرار في البيانات\n            const uniqueInvoices = result.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n            // تحديث الحالة المحلية\n            setFilteredInvoices(uniqueInvoices);\n        };\n        // تحميل فوري\n        loadInvoices();\n        // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري\n        const handleStorageChange = (e)=>{\n            if (e.key === \"invoices\") {\n                loadInvoices();\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = [\n            ...invoices\n        ];\n        // البحث\n        if (searchTerm) {\n            filtered = filtered.filter((invoice)=>{\n                var _invoice_id;\n                return invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.client.phone.includes(searchTerm) || ((_invoice_id = invoice.id) === null || _invoice_id === void 0 ? void 0 : _invoice_id.includes(searchTerm));\n            });\n        }\n        // الفلترة\n        if (filterBy !== \"all\") {\n            filtered = filtered.filter((invoice)=>{\n                switch(filterBy){\n                    case \"consultation\":\n                        return invoice.services.some((s)=>s.serviceType === \"consultation\");\n                    case \"pattern\":\n                        return invoice.services.some((s)=>s.serviceType === \"pattern\");\n                    case \"photography\":\n                        return invoice.services.some((s)=>s.serviceType === \"photography\");\n                    case \"shipping\":\n                        return invoice.services.some((s)=>s.serviceType === \"shipping\");\n                    case \"high_value\":\n                        return invoice.total > 1000;\n                    case \"recent\":\n                        const oneWeekAgo = new Date();\n                        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n                        return new Date(invoice.createdAt) > oneWeekAgo;\n                    case \"paid_full\":\n                        return invoice.paymentStatus === \"مدفوع بالكامل\";\n                    case \"paid_partial\":\n                        return invoice.paymentStatus === \"مدفوع جزئياً\";\n                    case \"unpaid\":\n                        return invoice.paymentStatus === \"غير مدفوع\" || !invoice.paymentStatus;\n                    default:\n                        return true;\n                }\n            });\n        }\n        // الترتيب\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"date_desc\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"date_asc\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"amount_desc\":\n                    return b.total - a.total;\n                case \"amount_asc\":\n                    return a.total - b.total;\n                case \"client_name\":\n                    return a.client.name.localeCompare(b.client.name, \"ar\");\n                default:\n                    return 0;\n            }\n        });\n        setFilteredInvoices(filtered);\n    }, [\n        invoices,\n        searchTerm,\n        filterBy,\n        sortBy\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            \"consultation\": \"استشارات\",\n            \"pattern\": \"باترون\",\n            \"pattern_printing\": \"طباعة باترون\",\n            \"manufacturing\": \"تصنيع\",\n            \"samples\": \"عينات\",\n            \"shipping\": \"شحن\",\n            \"marketing\": \"تسويق\",\n            \"photography\": \"تصوير\",\n            \"products\": \"منتجات\",\n            \"raw_purchases\": \"مشتريات خام\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getServiceNames = (services)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return services.map((s)=>serviceNames[s.serviceType] || s.serviceType).join(\"، \");\n    };\n    // دالة عرض رسائل التأكيد المحسنة\n    const showSuccessMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 3000);\n    };\n    const showErrorMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 4000);\n    };\n    const handleExportPDF = async (invoice)=>{\n        try {\n            // فتح المعاينة أولاً للحصول على نفس التنسيق\n            setPreviewInvoice(invoice);\n            // انتظار قصير للتأكد من عرض المعاينة\n            setTimeout(async ()=>{\n                try {\n                    await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n                    showSuccessMessage(\"تم تصدير PDF للفاتورة \".concat(invoice.id, \" بنجاح! \\uD83C\\uDF89\"));\n                    // إغلاق المعاينة بعد التصدير بثانية واحدة\n                    setTimeout(()=>{\n                        setPreviewInvoice(null);\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"❌ Error exporting PDF:\", error);\n                    showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n                    setPreviewInvoice(null);\n                }\n            }, 500);\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تصدير PDF.\");\n        }\n    };\n    const handlePreviewInvoice = (invoice)=>{\n        setPreviewInvoice(invoice);\n    };\n    const handlePOSInvoice = (invoice)=>{\n        setPosInvoice(invoice);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewInvoice(null);\n    };\n    const handleEditFromPreview = ()=>{\n        // For now, just close the preview\n        // In a full implementation, this would navigate to edit mode\n        setPreviewInvoice(null);\n        alert(\"وظيفة التعديل ستكون متاحة قريباً\");\n    };\n    const handleExportFromPreview = async ()=>{\n        if (previewInvoice) {\n            await handleExportPDF(previewInvoice);\n        }\n    };\n    // دالة حذف الفاتورة مع تأكيد\n    const handleDeleteInvoice = (invoice)=>{\n        const confirmMessage = \"هل أنت متأكد من حذف هذه الفاتورة؟\\n\\nالفاتورة: \".concat(invoice.id, \"\\nالعميل: \").concat(invoice.client.name, \"\\nالمبلغ: \").concat(formatCurrency(invoice.total), \"\\n\\n⚠️ هذا الإجراء لا يمكن التراجع عنه!\");\n        if (window.confirm(confirmMessage)) {\n            const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState();\n            store.deleteInvoice(invoice.id);\n            if (previewInvoice && previewInvoice.id === invoice.id) {\n                setPreviewInvoice(null);\n            }\n            setTimeout(()=>{\n                const updatedInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                setFilteredInvoices(updatedInvoices);\n            }, 100);\n            showSuccessMessage(\"تم حذف الفاتورة \".concat(invoice.id, \" بنجاح! \\uD83D\\uDDD1️\"));\n        }\n    };\n    const getTotalStats = ()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const count = filteredInvoices.length;\n        const avgAmount = count > 0 ? total / count : 0;\n        // حساب المبالغ المدفوعة والمتبقية\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const totalRemaining = total - totalPaid;\n        // حساب عدد الفواتير حسب حالة الدفع\n        const paidFullCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const paidPartialCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        // حساب نسبة التحصيل\n        const collectionRate = total > 0 ? totalPaid / total * 100 : 0;\n        return {\n            total,\n            count,\n            avgAmount,\n            totalPaid,\n            totalRemaining,\n            paidFullCount,\n            paidPartialCount,\n            unpaidCount,\n            collectionRate\n        };\n    };\n    const stats = getTotalStats();\n    // حساب إحصائيات اليوم الحالي مع مقارنة بالأمس\n    const getTodayStats = ()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const todayStr = today.toDateString();\n        const yesterdayStr = yesterday.toDateString();\n        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === todayStr);\n        const yesterdayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === yesterdayStr);\n        const todayTotal = todayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const todayPaid = todayInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const todayRemaining = todayTotal - todayPaid;\n        const yesterdayTotal = yesterdayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const yesterdayCount = yesterdayInvoices.length;\n        const todayPaidFullCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const todayPaidPartialCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const todayUnpaidCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const todayCollectionRate = todayTotal > 0 ? todayPaid / todayTotal * 100 : 0;\n        // حساب التغيير مقارنة بالأمس\n        const countChange = todayInvoices.length - yesterdayCount;\n        const revenueChange = todayTotal - yesterdayTotal;\n        const countChangePercent = yesterdayCount > 0 ? countChange / yesterdayCount * 100 : 0;\n        const revenueChangePercent = yesterdayTotal > 0 ? revenueChange / yesterdayTotal * 100 : 0;\n        return {\n            count: todayInvoices.length,\n            total: todayTotal,\n            paid: todayPaid,\n            remaining: todayRemaining,\n            paidFullCount: todayPaidFullCount,\n            paidPartialCount: todayPaidPartialCount,\n            unpaidCount: todayUnpaidCount,\n            collectionRate: todayCollectionRate,\n            countChange,\n            revenueChange,\n            countChangePercent,\n            revenueChangePercent,\n            yesterdayCount,\n            yesterdayTotal\n        };\n    };\n    const todayStats = getTodayStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"إدارة الفواتير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-indigo-500 to-purple-600 text-white rounded-xl p-6 mb-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"\\uD83D\\uDCCA إحصائيات اليوم الحالي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-sm\",\n                                            children: new Date().toLocaleDateString(\"ar-EG\", {\n                                                weekday: \"long\",\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: todayStats.count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"فواتير اليوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.countChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.countChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.countChange > 0 ? \"↗️\" : \"↘️\",\n                                                    Math.abs(todayStats.countChange),\n                                                    \" عن الأمس\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"إجمالي المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.revenueChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.revenueChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.revenueChange > 0 ? \"↗️\" : \"↘️\",\n                                                    todayStats.revenueChangePercent.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.paid)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"المبلغ المحصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: [\n                                                    \"متبقي: \",\n                                                    formatCurrency(todayStats.remaining)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    todayStats.collectionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"نسبة التحصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: todayStats.count > 0 ? \"متوسط: \".concat(formatCurrency(todayStats.total / todayStats.count)) : \"لا توجد فواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-indigo-400 border-opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-green-200\",\n                                                            children: todayStats.paidFullCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"مدفوع بالكامل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-yellow-200\",\n                                                            children: todayStats.paidPartialCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"مدفوع جزئياً\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-red-200\",\n                                                            children: todayStats.unpaidCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"غير مدفوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-indigo-100 mb-2\",\n                                                    children: \"\\uD83C\\uDFC6 أهم الخدمات اليوم:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: (()=>{\n                                                        const today = new Date().toDateString();\n                                                        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === today);\n                                                        const serviceCount = new Map();\n                                                        todayInvoices.forEach((invoice)=>{\n                                                            invoice.services.forEach((service)=>{\n                                                                const serviceName = getServiceName(service.serviceType);\n                                                                serviceCount.set(serviceName, (serviceCount.get(serviceName) || 0) + 1);\n                                                            });\n                                                        });\n                                                        const topServices = Array.from(serviceCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 3);\n                                                        if (topServices.length === 0) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-indigo-200\",\n                                                                children: \"لا توجد خدمات اليوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 30\n                                                            }, undefined);\n                                                        }\n                                                        return topServices.map((param, index)=>{\n                                                            let [serviceName, count] = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-indigo-200\",\n                                                                        children: [\n                                                                            index + 1,\n                                                                            \". \",\n                                                                            serviceName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-semibold\",\n                                                                        children: count\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, serviceName, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        });\n                                                    })()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-700\",\n                                        children: \"إجمالي الفواتير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: stats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg border-r-4 border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"إجمالي المبلغ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatCurrency(stats.total)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 p-4 rounded-lg border-r-4 border-emerald-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-emerald-700\",\n                                        children: \"المبلغ المدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-emerald-800\",\n                                        children: formatCurrency(stats.totalPaid)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg border-r-4 border-red-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-red-700\",\n                                        children: \"المبلغ المتبقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-800\",\n                                        children: formatCurrency(stats.totalRemaining)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-teal-50 p-4 rounded-lg border-r-4 border-teal-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-teal-700\",\n                                        children: \"مدفوع بالكامل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-teal-800\",\n                                        children: stats.paidFullCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-teal-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-orange-700\",\n                                        children: \"مدفوع جزئياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: stats.paidPartialCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg border-r-4 border-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-700\",\n                                        children: \"غير مدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: stats.unpaidCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-700\",\n                                        children: \"نسبة التحصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: [\n                                            stats.collectionRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: [\n                                            \"متوسط الفاتورة: \",\n                                            formatCurrency(stats.avgAmount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البحث\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث بالاسم، الهاتف، أو رقم الفاتورة\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"فلترة حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterBy,\n                                            onChange: (e)=>setFilterBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"consultation\",\n                                                    children: \"استشارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pattern\",\n                                                    children: \"باترون\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"photography\",\n                                                    children: \"تصوير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shipping\",\n                                                    children: \"شحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high_value\",\n                                                    children: \"قيمة عالية (+1000 جنيه)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"recent\",\n                                                    children: \"الأسبوع الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_full\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_partial\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"unpaid\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ترتيب حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_desc\",\n                                                    children: \"التاريخ (الأحدث أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_asc\",\n                                                    children: \"التاريخ (الأقدم أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_desc\",\n                                                    children: \"المبلغ (الأعلى أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_asc\",\n                                                    children: \"المبلغ (الأقل أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"client_name\",\n                                                    children: \"اسم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, undefined),\n            filteredInvoices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-lg mb-4\",\n                        children: invoices.length === 0 ? \"لا توجد فواتير محفوظة\" : \"لا توجد نتائج للبحث\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 11\n                    }, undefined),\n                    invoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"قم بإنشاء فاتورة جديدة لتظهر هنا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 614,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: [\n                                                        \"فاتورة #\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"السيلز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.salesperson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الخدمات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getServiceNames(invoice.services)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"طريقة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.paymentMethod\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"حالة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: invoice.paymentStatus || \"غير محدد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المدفوع / المتبقي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatCurrency(invoice.paidAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" / \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                            children: formatCurrency(invoice.remainingAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-2 rounded text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-700 font-medium\",\n                                                children: \"\\uD83D\\uDE9A يتطلب شحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleExportPDF(invoice),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDCC4 PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePreviewInvoice(invoice),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDC41️ معاينة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(invoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83E\\uDDFE POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteInvoice(invoice),\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2\",\n                                        children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, invoice.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 623,\n                columnNumber: 9\n            }, undefined),\n            previewInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"معاينة الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClosePreview,\n                                    className: \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    invoice: previewInvoice,\n                                    onEdit: handleEditFromPreview,\n                                    onExportPDF: handleExportFromPreview\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(previewInvoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83E\\uDDFE فاتورة POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteInvoice(previewInvoice),\n                                                className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                    lineNumber: 734,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 733,\n                columnNumber: 9\n            }, undefined),\n            posInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                invoice: posInvoice,\n                onClose: ()=>setPosInvoice(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 775,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicesList, \"vNSHrZ6bGj05wXDBkzjUPPk++oo=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore\n    ];\n});\n_c = InvoicesList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicesList);\nvar _c;\n$RefreshReg$(_c, \"InvoicesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicesList.tsx\n"));

/***/ })

});