"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/components/ClientForm.tsx":
/*!***************************************!*\
  !*** ./src/components/ClientForm.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst availableServices = [\n    {\n        value: \"consultation\",\n        label: \"استشارات\"\n    },\n    {\n        value: \"pattern\",\n        label: \"باترون\"\n    },\n    {\n        value: \"pattern_printing\",\n        label: \"طباعة باترون\"\n    },\n    {\n        value: \"manufacturing\",\n        label: \"تصنيع\"\n    },\n    {\n        value: \"samples\",\n        label: \"عينات\"\n    },\n    {\n        value: \"shipping\",\n        label: \"شحن/تعبئة\"\n    },\n    {\n        value: \"marketing\",\n        label: \"تسويق\"\n    },\n    {\n        value: \"photography\",\n        label: \"تصوير\"\n    },\n    {\n        value: \"products\",\n        label: \"منتجات\"\n    },\n    {\n        value: \"raw_purchases\",\n        label: \"مشتريات خام\"\n    }\n];\nconst contactMethods = [\n    {\n        value: \"whatsapp\",\n        label: \"واتساب\"\n    },\n    {\n        value: \"meta\",\n        label: \"ميتا\"\n    },\n    {\n        value: \"meeting\",\n        label: \"مقابلة\"\n    },\n    {\n        value: \"phone\",\n        label: \"هاتف\"\n    }\n];\nconst categories = [\n    {\n        value: \"A\",\n        label: \"فئة A\"\n    },\n    {\n        value: \"B\",\n        label: \"فئة B\"\n    },\n    {\n        value: \"C\",\n        label: \"فئة C\"\n    }\n];\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_2__.object({\n    name: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم العميل مطلوب\"),\n    phone: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"رقم الموبايل مطلوب\"),\n    services: yup__WEBPACK_IMPORTED_MODULE_2__.array().min(1, \"يجب اختيار خدمة واحدة على الأقل\"),\n    contactMethod: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"وسيلة التواصل مطلوبة\"),\n    salesperson: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم السيلز مطلوب\"),\n    initialCategory: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"تصنيف العميل مطلوب\"),\n    clientCode: yup__WEBPACK_IMPORTED_MODULE_2__.string().test(\"valid-code\", \"تنسيق كود العميل غير صحيح\", function(value) {\n        if (!value) return true; // الكود اختياري\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerCode)(value);\n        return validation.isValid;\n    })\n});\nconst ClientForm = (param)=>{\n    let { onSubmit, initialValues } = param;\n    _s();\n    const setCurrentClient = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore)((state)=>state.setCurrentClient);\n    const { generateNewCustomerCode, validateCode, getSuggestions, suggestions, clearSuggestions } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore)();\n    const [showCodeGenerator, setShowCodeGenerator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [codeStats, setCodeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.getCustomerCodeStats)();\n        setCodeStats(stats);\n    }, []);\n    const defaultValues = {\n        name: \"\",\n        phone: \"\",\n        services: [],\n        contactMethod: \"whatsapp\",\n        salesperson: \"\",\n        initialCategory: \"A\",\n        businessName: \"\",\n        province: \"\",\n        clientCode: \"\",\n        ...initialValues\n    };\n    const handleSubmit = (values)=>{\n        // إذا لم يتم تحديد كود العميل، إنشاء كود تلقائي\n        const finalValues = {\n            ...values,\n            clientCode: values.clientCode || generateNewCustomerCode()\n        };\n        setCurrentClient(finalValues);\n        onSubmit(finalValues);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"بيانات العميل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"أدخل المعلومات الأساسية للعميل لبدء إنشاء الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-blue-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"الخطوة 1 من 3 - معلومات العميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Formik, {\n                initialValues: defaultValues,\n                validationSchema: validationSchema,\n                onSubmit: handleSubmit,\n                children: (param)=>{\n                    let { values, setFieldValue, errors, touched } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" اسم العميل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"name\",\n                                                                type: \"text\",\n                                                                className: \"form-input-enhanced pr-10 \".concat(errors.name && touched.name ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                placeholder: \"أدخل اسم العميل الكامل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"name\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" رقم الموبايل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"phone\",\n                                                                type: \"tel\",\n                                                                className: \"form-input-enhanced pr-10 \".concat(errors.phone && touched.phone ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                placeholder: \"01xxxxxxxxx\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"phone\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"الاسم التجاري\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 183,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"businessName\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"اسم الشركة أو المتجر (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"businessName\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"المحافظة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"province\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"المحافظة (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"province\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"كود العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 222,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { field, form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ...field,\n                                                                                type: \"text\",\n                                                                                className: \"form-input-enhanced pr-10\",\n                                                                                placeholder: \"مثال: \".concat((codeStats === null || codeStats === void 0 ? void 0 : codeStats.nextCode) || \"p013621\"),\n                                                                                onChange: (e)=>{\n                                                                                    field.onChange(e);\n                                                                                    if (e.target.value) {\n                                                                                        getSuggestions(e.target.value);\n                                                                                    } else {\n                                                                                        clearSuggestions();\n                                                                                    }\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    field.onBlur();\n                                                                                    clearSuggestions();\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                                        children: suggestions.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                                                onClick: ()=>{\n                                                                                    // يمكن إضافة منطق لملء البيانات من العميل المقترح\n                                                                                    clearSuggestions();\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-blue-600\",\n                                                                                                children: customer.clientCode\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 261,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 262,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: customer.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowCodeGenerator(!showCodeGenerator),\n                                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"إعدادات الكود\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    const newCode = generateNewCustomerCode();\n                                                                                    form.setFieldValue(\"clientCode\", newCode);\n                                                                                },\n                                                                                className: \"flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 295,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \"إنشاء كود جديد\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showCodeGenerator && codeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: \"إحصائيات أكواد العملاء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"إجمالي العملاء:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 309,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-blue-600 mr-2\",\n                                                                                        children: codeStats.totalCustomers\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 310,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"الكود التالي:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 313,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-green-600 mr-2\",\n                                                                                        children: codeStats.nextCode\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 314,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مُنشأة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-purple-600 mr-2\",\n                                                                                        children: codeStats.generatedCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 318,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مخصصة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-orange-600 mr-2\",\n                                                                                        children: codeStats.customCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 322,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 leading-relaxed\",\n                                                                        children: \"تنسيق الكود: p + رقم المعاملة (01) + رقم العميل (3621). إذا تُرك فارغاً، سيتم إنشاء كود تلقائياً.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"clientCode\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"الخدمات المطلوبة\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-sm\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"اختر الخدمات التي يحتاجها العميل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: availableServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"service-card group cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                type: \"checkbox\",\n                                                                name: \"services\",\n                                                                value: service.value,\n                                                                className: \"sr-only\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"service-card-content\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"service-icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"service-label\",\n                                                                        children: service.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.value, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                name: \"services\",\n                                                component: \"div\",\n                                                className: \"error-message-enhanced mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-purple-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"التواصل والمبيعات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" وسيلة التواصل المفضلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3\",\n                                                        children: contactMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"contact-method-card group cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        type: \"radio\",\n                                                                        name: \"contactMethod\",\n                                                                        value: method.value,\n                                                                        className: \"sr-only\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"contact-method-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"contact-icon\",\n                                                                                children: [\n                                                                                    method.value === \"whatsapp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 410,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 409,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meta\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 414,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meeting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 419,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"phone\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 425,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 424,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"contact-label\",\n                                                                                children: method.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, method.value, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"contactMethod\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" اسم السيلز\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"salesperson\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.salesperson && touched.salesperson ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"اسم مندوب المبيعات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"salesperson\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" تصنيف العميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        as: \"select\",\n                                                                        name: \"initialCategory\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.initialCategory && touched.initialCategory ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: category.value,\n                                                                                children: category.label\n                                                                            }, category.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"initialCategory\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center items-center gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary-enhanced group w-full sm:w-auto\",\n                                        disabled: Object.keys(errors).length > 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"حفظ بيانات العميل والمتابعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"البيانات محفوظة محلياً وآمنة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientForm, \"DruD6AUCRPknWrISIeafELjmiC0=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore\n    ];\n});\n_c = ClientForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientForm);\nvar _c;\n$RefreshReg$(_c, \"ClientForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClientForm.tsx\n"));

/***/ })

});