"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerStore: function() { return /* binding */ useCustomerStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n\n/**\n * تحميل العملاء من النظام الموحد\n */ const loadCustomersFromStorage = async ()=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, []);\n        if (result.success) {\n            console.log(\"✅ تم تحميل العملاء من النظام الموحد:\", result.data.length);\n            return result.data;\n        } else {\n            console.error(\"❌ فشل في تحميل العملاء:\", result.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في تحميل العملاء:\", error);\n        return [];\n    }\n};\n/**\n * حفظ العملاء في النظام الموحد\n */ const saveCustomersToStorage = async (customers)=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, customers);\n        if (result.success) {\n            console.log(\"✅ تم حفظ العملاء في النظام الموحد:\", customers.length);\n            return true;\n        } else {\n            console.error(\"❌ فشل في حفظ العملاء:\", result.message);\n            return false;\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في حفظ العملاء:\", error);\n        return false;\n    }\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return \"customer-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        customers: loadCustomersFromStorage(),\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        // إضافة عملاء متعددين (للاستيراد)\n        addMultipleCustomers: async (customersData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                const newCustomers = [];\n                const errors = [];\n                for (const customerData of customersData){\n                    try {\n                        let clientCode = customerData.clientCode;\n                        // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد\n                        if (!clientCode) {\n                            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                            clientCode = codeData.code;\n                        }\n                        // التحقق من عدم تكرار الكود\n                        const existingCustomer = [\n                            ...customers,\n                            ...newCustomers\n                        ].find((c)=>c.clientCode === clientCode);\n                        if (existingCustomer) {\n                            errors.push(\"كود العميل \".concat(clientCode, \" مستخدم بالفعل للعميل \").concat(customerData.name));\n                            continue;\n                        }\n                        const newCustomer = {\n                            ...customerData,\n                            id: generateCustomerId(),\n                            clientCode\n                        };\n                        newCustomers.push(newCustomer);\n                    } catch (error) {\n                        errors.push(\"خطأ في إضافة العميل \".concat(customerData.name, \": \").concat(error instanceof Error ? error.message : \"خطأ غير معروف\"));\n                    }\n                }\n                if (newCustomers.length > 0) {\n                    const updatedCustomers = [\n                        ...customers,\n                        ...newCustomers\n                    ];\n                    // حفظ في التخزين\n                    const saved = saveCustomersToStorage(updatedCustomers);\n                    if (!saved) {\n                        throw new Error(\"فشل في حفظ بيانات العملاء\");\n                    }\n                    set({\n                        customers: updatedCustomers,\n                        isLoading: false\n                    });\n                    return {\n                        success: true,\n                        imported: newCustomers.length,\n                        errors,\n                        customers: newCustomers\n                    };\n                } else {\n                    set({\n                        isLoading: false\n                    });\n                    return {\n                        success: false,\n                        error: \"لم يتم إضافة أي عميل\",\n                        imported: 0,\n                        errors\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage,\n                    imported: 0,\n                    errors: []\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                var _get_currentCustomer;\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: ((_get_currentCustomer = get().currentCustomer) === null || _get_currentCustomer === void 0 ? void 0 : _get_currentCustomer.id) === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const searchTerm = query.toLowerCase().trim();\n            // البحث الأساسي المحسن\n            const basicResults = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm) || customer.phone.includes(query) || customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm) || customer.businessName && customer.businessName.toLowerCase().includes(searchTerm) || customer.province && customer.province.toLowerCase().includes(searchTerm) || customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm) || customer.services.some((service)=>service.toLowerCase().includes(searchTerm)));\n            // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج\n            if (basicResults.length === 0 && searchTerm.length > 2) {\n                const fuzzyResults = customers.filter((customer)=>{\n                    const name = customer.name.toLowerCase();\n                    const words = searchTerm.split(\" \");\n                    return words.some((word)=>name.includes(word) && word.length > 1);\n                });\n                return fuzzyResults;\n            }\n            // ترتيب النتائج حسب الأولوية\n            return basicResults.sort((a, b)=>{\n                var _a_clientCode, _b_clientCode;\n                // أولوية للمطابقة الدقيقة في بداية الاسم\n                const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);\n                const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);\n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                // أولوية للمطابقة في كود العميل\n                const aCodeMatch = (_a_clientCode = a.clientCode) === null || _a_clientCode === void 0 ? void 0 : _a_clientCode.toLowerCase().includes(searchTerm);\n                const bCodeMatch = (_b_clientCode = b.clientCode) === null || _b_clientCode === void 0 ? void 0 : _b_clientCode.toLowerCase().includes(searchTerm);\n                if (aCodeMatch && !bCodeMatch) return -1;\n                if (!aCodeMatch && bCodeMatch) return 1;\n                // ترتيب أبجدي كافتراضي\n                return a.name.localeCompare(b.name, \"ar\");\n            });\n        },\n        // الاقتراحات السريعة للبحث\n        getQuickSuggestions: function(query) {\n            let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n            const { customers } = get();\n            if (!query.trim()) return [];\n            const searchTerm = query.toLowerCase().trim();\n            const suggestions = customers.filter((customer)=>customer.name.toLowerCase().startsWith(searchTerm) || customer.phone.startsWith(query) || customer.clientCode && customer.clientCode.toLowerCase().startsWith(searchTerm)).slice(0, limit).map((customer)=>{\n                var _customer_clientCode, _customer_clientCode1;\n                return {\n                    id: customer.id,\n                    name: customer.name,\n                    phone: customer.phone,\n                    clientCode: customer.clientCode,\n                    businessName: customer.businessName,\n                    type: ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().startsWith(searchTerm)) ? \"code\" : customer.phone.startsWith(query) ? \"phone\" : \"name\",\n                    matchText: ((_customer_clientCode1 = customer.clientCode) === null || _customer_clientCode1 === void 0 ? void 0 : _customer_clientCode1.toLowerCase().startsWith(searchTerm)) ? customer.clientCode : customer.phone.startsWith(query) ? customer.phone : customer.name\n                };\n            });\n            return suggestions;\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/customerStore.ts\n"));

/***/ })

});