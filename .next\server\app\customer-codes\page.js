/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/customer-codes/page";
exports.ids = ["app/customer-codes/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'customer-codes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/customer-codes/page.tsx */ \"(rsc)/./src/app/customer-codes/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/customer-codes/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/customer-codes/page\",\n        pathname: \"/customer-codes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/customer-codes/page.tsx */ \"(ssr)/./src/app/customer-codes/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNzcmMlNUNhcHAlNUNjdXN0b21lci1jb2RlcyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmFwYXBwLz85ZGI1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxzcmNcXFxcYXBwXFxcXGN1c3RvbWVyLWNvZGVzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/customer-codes/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/customer-codes/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CustomerCodeManager */ \"(ssr)/./src/components/CustomerCodeManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CustomerCodesPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة أكواد العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة وتتبع أكواد العملاء والعدادات الخاصة بالنظام\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/customers\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"إدارة العملاء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"الصفحة الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-blue-50 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-blue-800 mb-4\",\n                            children: \"معلومات مهمة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"تنسيق أكواد العملاء:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• التنسيق: p + رقم المعاملة (2 رقم) + رقم العميل (4 أرقام)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• مثال: p013621 (معاملة 01، عميل 3621)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• الأكواد تتزايد تلقائياً مع كل عميل جديد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"إدارة العدادات:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• يمكن تعديل العدادات يدوياً عند الحاجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• رقم المعاملة يزيد عند بدء معاملة جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• إعادة التعيين تحذف جميع العدادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-yellow-100 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"تنبيه:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" تأكد من عمل نسخة احتياطية قبل إجراء أي تعديلات على العدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerCodesPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/customer-codes/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CustomerCodeManager.tsx":
/*!************************************************!*\
  !*** ./src/components/CustomerCodeManager.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(ssr)/./src/utils/customerCodeGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CustomerCodeManager = ()=>{\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editCounters, setEditCounters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        lastTransactionNumber: 1,\n        lastCustomerNumber: 0,\n        totalCustomers: 0\n    });\n    const [searchCode, setSearchCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResult, setSearchResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadStats = ()=>{\n        try {\n            const codeStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n            setStats(codeStats);\n            // تحميل العدادات الحالية\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureGetItem)(\"customerCounters\");\n            if (result.success && result.data) {\n                setEditCounters(result.data);\n            }\n        } catch (error) {\n            console.error(\"خطأ في تحميل إحصائيات الأكواد:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStats();\n    }, []);\n    const handleGenerateNewCode = ()=>{\n        try {\n            const newCode = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            console.log(\"تم إنشاء كود جديد:\", newCode);\n            loadStats(); // إعادة تحميل الإحصائيات\n        } catch (error) {\n            console.error(\"خطأ في إنشاء كود جديد:\", error);\n            alert(\"حدث خطأ في إنشاء كود جديد\");\n        }\n    };\n    const handleIncrementTransaction = ()=>{\n        try {\n            (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.incrementTransactionNumber)();\n            loadStats();\n            alert(\"تم الانتقال للمعاملة التالية بنجاح\");\n        } catch (error) {\n            console.error(\"خطأ في تحديث رقم المعاملة:\", error);\n            alert(\"حدث خطأ في تحديث رقم المعاملة\");\n        }\n    };\n    const handleSaveCounters = ()=>{\n        try {\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureSetItem)(\"customerCounters\", editCounters);\n            if (result.success) {\n                loadStats();\n                setShowEditModal(false);\n                alert(\"تم حفظ العدادات بنجاح\");\n            } else {\n                throw new Error(result.error);\n            }\n        } catch (error) {\n            console.error(\"خطأ في حفظ العدادات:\", error);\n            alert(\"حدث خطأ في حفظ العدادات\");\n        }\n    };\n    const handleResetCounters = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع العدادات؟ هذا الإجراء لا يمكن التراجع عنه.\")) {\n            try {\n                (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.resetCustomerCounters)();\n                loadStats();\n                alert(\"تم إعادة تعيين العدادات بنجاح\");\n            } catch (error) {\n                console.error(\"خطأ في إعادة تعيين العدادات:\", error);\n                alert(\"حدث خطأ في إعادة تعيين العدادات\");\n            }\n        }\n    };\n    const handleSearchCode = ()=>{\n        if (!searchCode.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        try {\n            const customer = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(searchCode.trim());\n            setSearchResult(customer);\n        } catch (error) {\n            console.error(\"خطأ في البحث:\", error);\n            setSearchResult(null);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"إدارة أكواد العملاء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowEditModal(true),\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"تعديل العدادات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadStats,\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"تحديث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-800 mb-2\",\n                                children: \"إجمالي العملاء\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-blue-600\",\n                                children: stats.totalCustomers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-2\",\n                                children: \"الأكواد المولدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: stats.generatedCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                                children: \"الأكواد المخصصة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-yellow-600\",\n                                children: stats.customCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-purple-800 mb-2\",\n                                children: \"آخر كود\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl font-bold text-purple-600\",\n                                children: stats.lastGeneratedCode || \"لا يوجد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"رقم المعاملة الحالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastTransactionNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"آخر رقم عميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastCustomerNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"الكود التالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.nextCode\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-3\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-700 mb-3\",\n                                children: \"إنشاء كود عميل جديد للاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGenerateNewCode,\n                                className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-orange-800 mb-3\",\n                                children: \"الانتقال للمعاملة التالية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-orange-700 mb-3\",\n                                children: \"زيادة رقم المعاملة للانتقال لمعاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIncrementTransaction,\n                                className: \"w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                                children: \"معاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-blue-800 mb-3\",\n                        children: \"البحث عن كود عميل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchCode,\n                                onChange: (e)=>setSearchCode(e.target.value),\n                                placeholder: \"أدخل كود العميل (مثل: p013621)\",\n                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSearchCode,\n                                className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-800\",\n                                children: \"نتيجة البحث:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الاسم:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الهاتف:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.phone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الكود:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.clientCode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined),\n                    searchCode && !searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: \"لم يتم العثور على عميل بهذا الكود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-red-800 mb-3\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"تحذير:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            \" هذا الإجراء سيعيد تعيين جميع العدادات إلى القيم الافتراضية\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleResetCounters,\n                        className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            showEditModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"تعديل العدادات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"رقم المعاملة الحالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastTransactionNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastTransactionNumber: parseInt(e.target.value) || 1\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"آخر رقم عميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastCustomerNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastCustomerNumber: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"إجمالي العملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.totalCustomers,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    totalCustomers: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveCounters,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"حفظ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowEditModal(false),\n                                    className: \"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerCodeManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CustomerCodeManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/customerCodeGenerator.ts":
/*!********************************************!*\
  !*** ./src/utils/customerCodeGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCodeDuplication: () => (/* binding */ checkCodeDuplication),\n/* harmony export */   checkCustomerDataDuplication: () => (/* binding */ checkCustomerDataDuplication),\n/* harmony export */   findCustomerByCode: () => (/* binding */ findCustomerByCode),\n/* harmony export */   generateCustomerCode: () => (/* binding */ generateCustomerCode),\n/* harmony export */   getCustomerCodeStats: () => (/* binding */ getCustomerCodeStats),\n/* harmony export */   getCustomerCodeSuggestions: () => (/* binding */ getCustomerCodeSuggestions),\n/* harmony export */   incrementTransactionNumber: () => (/* binding */ incrementTransactionNumber),\n/* harmony export */   isCustomerCodeUnique: () => (/* binding */ isCustomerCodeUnique),\n/* harmony export */   parseCustomerCode: () => (/* binding */ parseCustomerCode),\n/* harmony export */   resetCustomerCounters: () => (/* binding */ resetCustomerCounters),\n/* harmony export */   validateCustomerCode: () => (/* binding */ validateCustomerCode),\n/* harmony export */   validateCustomerUniqueness: () => (/* binding */ validateCustomerUniqueness)\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/**\n * نظام إنشاء وإدارة أكواد العملاء\n * التنسيق: p013621 (p01 = رقم المعاملة، 3621 = الرقم المسلسل للعميل)\n */ \n// إعدادات نظام الأكواد\nconst CODE_CONFIG = {\n    PREFIX: \"p\",\n    TRANSACTION_DIGITS: 2,\n    CUSTOMER_DIGITS: 4,\n    STARTING_TRANSACTION: 1,\n    STARTING_CUSTOMER: 3621\n};\n/**\n * تحميل العدادات من التخزين الآمن\n */ const loadCounters = ()=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customerCounters\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    // القيم الافتراضية\n    return {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n};\n/**\n * حفظ العدادات في التخزين الآمن\n */ const saveCounters = (counters)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customerCounters\", counters);\n    if (!result.success) {\n        console.error(\"فشل في حفظ عدادات العملاء:\", result.error);\n        throw new Error(\"فشل في حفظ عدادات العملاء\");\n    }\n};\n/**\n * تنسيق رقم بعدد أرقام محدد\n */ const formatNumber = (num, digits)=>{\n    return num.toString().padStart(digits, \"0\");\n};\n/**\n * إنشاء كود عميل جديد\n */ const generateCustomerCode = ()=>{\n    const counters = loadCounters();\n    // زيادة العدادات\n    counters.lastCustomerNumber += 1;\n    counters.totalCustomers += 1;\n    // إنشاء الكود\n    const transactionPart = formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS);\n    const customerPart = formatNumber(counters.lastCustomerNumber, CODE_CONFIG.CUSTOMER_DIGITS);\n    const code = `${CODE_CONFIG.PREFIX}${transactionPart}${customerPart}`;\n    // حفظ العدادات المحدثة\n    saveCounters(counters);\n    return {\n        code,\n        transactionNumber: counters.lastTransactionNumber,\n        customerNumber: counters.lastCustomerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: false\n    };\n};\n/**\n * التحقق من صحة كود العميل\n */ const validateCustomerCode = (code)=>{\n    if (!code || typeof code !== \"string\") {\n        return {\n            isValid: false,\n            error: \"الكود مطلوب\"\n        };\n    }\n    // التحقق من البادئة\n    if (!code.startsWith(CODE_CONFIG.PREFIX)) {\n        return {\n            isValid: false,\n            error: `يجب أن يبدأ الكود بـ \"${CODE_CONFIG.PREFIX}\"`\n        };\n    }\n    // التحقق من الطول\n    const expectedLength = 1 + CODE_CONFIG.TRANSACTION_DIGITS + CODE_CONFIG.CUSTOMER_DIGITS;\n    if (code.length !== expectedLength) {\n        return {\n            isValid: false,\n            error: `يجب أن يكون طول الكود ${expectedLength} أحرف`\n        };\n    }\n    // التحقق من الأرقام\n    const numberPart = code.slice(1);\n    if (!/^\\d+$/.test(numberPart)) {\n        return {\n            isValid: false,\n            error: \"يجب أن يحتوي الكود على أرقام فقط بعد البادئة\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * تحليل كود العميل\n */ const parseCustomerCode = (code)=>{\n    const validation = validateCustomerCode(code);\n    if (!validation.isValid) {\n        return null;\n    }\n    const numberPart = code.slice(1);\n    const transactionNumber = parseInt(numberPart.slice(0, CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    const customerNumber = parseInt(numberPart.slice(CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    return {\n        code,\n        transactionNumber,\n        customerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: true\n    };\n};\n/**\n * التحقق من تفرد كود العميل\n */ const isCustomerCodeUnique = (code, excludeClientId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return true; // إذا لم توجد عملاء، فالكود فريد\n    }\n    const customers = result.data;\n    return !customers.some((customer)=>customer.clientCode === code && customer.id !== excludeClientId);\n};\n/**\n * الحصول على إحصائيات أكواد العملاء\n */ const getCustomerCodeStats = ()=>{\n    const counters = loadCounters();\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    const customers = result.success && result.data ? result.data : [];\n    const customCodes = customers.filter((c)=>c.clientCode && !c.clientCode.match(/^p\\d+$/));\n    const generatedCodes = customers.filter((c)=>c.clientCode && c.clientCode.match(/^p\\d+$/));\n    return {\n        totalCustomers: customers.length,\n        generatedCodes: generatedCodes.length,\n        customCodes: customCodes.length,\n        nextCode: `${CODE_CONFIG.PREFIX}${formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS)}${formatNumber(counters.lastCustomerNumber + 1, CODE_CONFIG.CUSTOMER_DIGITS)}`,\n        lastTransactionNumber: counters.lastTransactionNumber,\n        lastCustomerNumber: counters.lastCustomerNumber\n    };\n};\n/**\n * إعادة تعيين العدادات (للاستخدام في حالات الطوارئ)\n */ const resetCustomerCounters = ()=>{\n    const defaultCounters = {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n    saveCounters(defaultCounters);\n};\n/**\n * تحديث رقم المعاملة (للانتقال لمعاملة جديدة)\n */ const incrementTransactionNumber = ()=>{\n    const counters = loadCounters();\n    counters.lastTransactionNumber += 1;\n    saveCounters(counters);\n};\n/**\n * البحث عن عميل بالكود\n */ const findCustomerByCode = (code)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return null;\n    }\n    return result.data.find((customer)=>customer.clientCode === code) || null;\n};\n/**\n * اقتراحات أكواد العملاء (للبحث التلقائي)\n */ const getCustomerCodeSuggestions = (query, limit = 10)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return [];\n    }\n    const customers = result.data;\n    const lowerQuery = query.toLowerCase();\n    return customers.filter((customer)=>customer.clientCode?.toLowerCase().includes(lowerQuery) || customer.name.toLowerCase().includes(lowerQuery) || customer.phone.includes(query)).slice(0, limit);\n};\n/**\n * التحقق من تكرار كود العميل\n */ const checkCodeDuplication = (code, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const existingCustomer = customers.find((customer)=>customer.clientCode === code && customer.id !== excludeId);\n    return {\n        isDuplicate: !!existingCustomer,\n        existingCustomer\n    };\n};\n/**\n * التحقق من تكرار بيانات العميل (الاسم + الهاتف)\n */ const checkCustomerDataDuplication = (name, phone, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const trimmedName = name.trim().toLowerCase();\n    const trimmedPhone = phone.trim();\n    // البحث عن تطابق في الاسم والهاتف\n    const nameMatch = customers.find((customer)=>customer.name.trim().toLowerCase() === trimmedName && customer.id !== excludeId);\n    const phoneMatch = customers.find((customer)=>customer.phone.trim() === trimmedPhone && customer.id !== excludeId);\n    if (nameMatch && phoneMatch && nameMatch.id === phoneMatch.id) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"both\",\n            existingCustomer: nameMatch\n        };\n    } else if (nameMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"name\",\n            existingCustomer: nameMatch\n        };\n    } else if (phoneMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"phone\",\n            existingCustomer: phoneMatch\n        };\n    }\n    return {\n        isDuplicate: false\n    };\n};\n/**\n * التحقق الشامل من التكرار قبل إضافة/تحديث العميل\n */ const validateCustomerUniqueness = (customerData, excludeId)=>{\n    const errors = [];\n    const warnings = [];\n    const suggestions = [];\n    // التحقق من كود العميل إذا تم تمريره\n    if (customerData.clientCode) {\n        const codeValidation = validateCustomerCode(customerData.clientCode);\n        if (!codeValidation.isValid) {\n            errors.push(codeValidation.error);\n        } else {\n            const codeDuplication = checkCodeDuplication(customerData.clientCode, excludeId);\n            if (codeDuplication.isDuplicate) {\n                errors.push(`كود العميل \"${customerData.clientCode}\" مستخدم بالفعل للعميل: ${codeDuplication.existingCustomer?.name}`);\n                suggestions.push(\"يمكنك ترك حقل الكود فارغاً لإنشاء كود جديد تلقائياً\");\n            }\n        }\n    }\n    // التحقق من تكرار بيانات العميل\n    const dataDuplication = checkCustomerDataDuplication(customerData.name, customerData.phone, excludeId);\n    if (dataDuplication.isDuplicate) {\n        const existing = dataDuplication.existingCustomer;\n        switch(dataDuplication.duplicateType){\n            case \"both\":\n                errors.push(`عميل بنفس الاسم ورقم الهاتف موجود بالفعل: ${existing.name} (${existing.clientCode})`);\n                break;\n            case \"name\":\n                warnings.push(`يوجد عميل بنفس الاسم: ${existing.name} (${existing.clientCode}) - هاتف: ${existing.phone}`);\n                suggestions.push(\"تأكد من أن هذا عميل مختلف أم نفس العميل\");\n                break;\n            case \"phone\":\n                warnings.push(`يوجد عميل بنفس رقم الهاتف: ${existing.name} (${existing.clientCode})`);\n                suggestions.push(\"تأكد من صحة رقم الهاتف أو أن هذا عميل مختلف\");\n                break;\n        }\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings,\n        suggestions: suggestions.length > 0 ? suggestions : undefined\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/customerCodeGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: () => (/* binding */ STORAGE_CONFIG),\n/* harmony export */   checkStorageHealth: () => (/* binding */ checkStorageHealth),\n/* harmony export */   createBackup: () => (/* binding */ createBackup),\n/* harmony export */   restoreFromBackup: () => (/* binding */ restoreFromBackup),\n/* harmony export */   secureGetItem: () => (/* binding */ secureGetItem),\n/* harmony export */   secureSetItem: () => (/* binding */ secureSetItem)\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = `backup-${Date.now()}`;\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: `تم استرداد البيانات بنجاح من النسخة: ${backup.timestamp}`\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc2VjdXJlU3RvcmFnZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FJRCxpQkFBaUI7QUFDakIsTUFBTUEsaUJBQWlCO0lBQ3JCQyxrQkFBa0IsSUFBSSxPQUFPO0lBQzdCQyxpQkFBaUIsSUFBSSxLQUFLO0lBQzFCQyxhQUFhO0lBQ2JDLGdCQUFnQjtBQUNsQjtBQXlCQTs7Q0FFQyxHQUNELE1BQU1DLGNBQWMsQ0FBQ0M7SUFDbkIsSUFBSTtRQUNGLHNDQUFzQztRQUN0QyxNQUFNQyxVQUFVQyxLQUFLRjtRQUNyQixNQUFNRyxZQUFZRixRQUFRRyxLQUFLLENBQUMsSUFBSUMsT0FBTyxHQUFHQyxJQUFJLENBQUM7UUFDbkQsT0FBT0osS0FBS0MsWUFBWVQsZUFBZUksY0FBYztJQUN2RCxFQUFFLE9BQU9TLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7UUFDdEMsT0FBT1AsTUFBTSwwQ0FBMEM7SUFDekQ7QUFDRjtBQUVBOztDQUVDLEdBQ0QsTUFBTVMsY0FBYyxDQUFDQztJQUNuQixJQUFJO1FBQ0YsTUFBTUMsVUFBVUMsS0FBS0Y7UUFDckIsTUFBTUcsYUFBYUYsUUFBUUcsT0FBTyxDQUFDcEIsZUFBZUksY0FBYyxFQUFFO1FBQ2xFLE1BQU1pQixjQUFjRixXQUFXVCxLQUFLLENBQUMsSUFBSUMsT0FBTyxHQUFHQyxJQUFJLENBQUM7UUFDeEQsT0FBT00sS0FBS0c7SUFDZCxFQUFFLE9BQU9SLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7UUFDdEMsT0FBT0csZUFBZSxzQ0FBc0M7SUFDOUQ7QUFDRjtBQUVBOztDQUVDLEdBQ0QsTUFBTU0sb0JBQW9CLENBQUNoQjtJQUN6QixJQUFJaUIsT0FBTztJQUNYLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJbEIsS0FBS21CLE1BQU0sRUFBRUQsSUFBSztRQUNwQyxNQUFNRSxPQUFPcEIsS0FBS3FCLFVBQVUsQ0FBQ0g7UUFDN0JELE9BQU8sQ0FBRUEsUUFBUSxLQUFLQSxPQUFRRztRQUM5QkgsT0FBT0EsT0FBT0EsTUFBTSwwQkFBMEI7SUFDaEQ7SUFDQSxPQUFPSyxLQUFLQyxHQUFHLENBQUNOLE1BQU1PLFFBQVEsQ0FBQztBQUNqQztBQUVBOztDQUVDLEdBQ00sTUFBTUMscUJBQXFCO0lBQ2hDLE1BQU1DLFNBQXdCO1FBQzVCQyxXQUFXO1FBQ1hDLFFBQVEsRUFBRTtRQUNWQyxpQkFBaUIsRUFBRTtRQUNuQkMsYUFBYTtRQUNiQyxjQUFjckMsZUFBZUMsZ0JBQWdCO1FBQzdDcUMsY0FBYztJQUNoQjtJQUVBLElBQUk7UUFDRiw0QkFBNEI7UUFDNUIsSUFBSUMsWUFBWTtRQUNoQixJQUFLLElBQUlDLE9BQU9DLGFBQWM7WUFDNUIsSUFBSUEsYUFBYUMsY0FBYyxDQUFDRixNQUFNO2dCQUNwQ0QsYUFBYUUsWUFBWSxDQUFDRCxJQUFJLENBQUNmLE1BQU07WUFDdkM7UUFDRjtRQUNBTyxPQUFPSSxXQUFXLEdBQUdHO1FBRXJCLGtCQUFrQjtRQUNsQixJQUFJQSxZQUFZdkMsZUFBZUMsZ0JBQWdCLEdBQUcsS0FBSztZQUNyRCtCLE9BQU9DLFNBQVMsR0FBRztZQUNuQkQsT0FBT0UsTUFBTSxDQUFDUyxJQUFJLENBQUM7WUFDbkJYLE9BQU9HLGVBQWUsQ0FBQ1EsSUFBSSxDQUFDO1FBQzlCO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1DLGFBQWFDLE9BQU9DLElBQUksQ0FBQ0wsY0FBY00sTUFBTSxDQUFDUCxDQUFBQSxNQUFPQSxJQUFJUSxVQUFVLENBQUM7UUFDMUVoQixPQUFPTSxZQUFZLEdBQUdNLFdBQVduQixNQUFNO1FBRXZDLElBQUlPLE9BQU9NLFlBQVksS0FBSyxHQUFHO1lBQzdCTixPQUFPRSxNQUFNLENBQUNTLElBQUksQ0FBQztZQUNuQlgsT0FBT0csZUFBZSxDQUFDUSxJQUFJLENBQUM7UUFDOUI7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTU0sZUFBZVIsYUFBYVMsT0FBTyxDQUFDO1FBQzFDLElBQUksQ0FBQ0QsY0FBYztZQUNqQmpCLE9BQU9FLE1BQU0sQ0FBQ1MsSUFBSSxDQUFDO1FBQ3JCLE9BQU87WUFDTCxJQUFJO2dCQUNGLE1BQU1RLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0o7Z0JBQzVCLElBQUksQ0FBQ0ssTUFBTUMsT0FBTyxDQUFDSixXQUFXO29CQUM1Qm5CLE9BQU9DLFNBQVMsR0FBRztvQkFDbkJELE9BQU9FLE1BQU0sQ0FBQ1MsSUFBSSxDQUFDO29CQUNuQlgsT0FBT0csZUFBZSxDQUFDUSxJQUFJLENBQUM7Z0JBQzlCO1lBQ0YsRUFBRSxPQUFPOUIsT0FBTztnQkFDZG1CLE9BQU9DLFNBQVMsR0FBRztnQkFDbkJELE9BQU9FLE1BQU0sQ0FBQ1MsSUFBSSxDQUFDO2dCQUNuQlgsT0FBT0csZUFBZSxDQUFDUSxJQUFJLENBQUM7WUFDOUI7UUFDRjtJQUVGLEVBQUUsT0FBTzlCLE9BQU87UUFDZG1CLE9BQU9DLFNBQVMsR0FBRztRQUNuQkQsT0FBT0UsTUFBTSxDQUFDUyxJQUFJLENBQUM7UUFDbkJYLE9BQU9HLGVBQWUsQ0FBQ1EsSUFBSSxDQUFDO0lBQzlCO0lBRUEsT0FBT1g7QUFDVCxFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNd0IsZUFBZTtJQUMxQixJQUFJO1FBQ0YsTUFBTUMsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO1FBQ3hDLE1BQU1DLFdBQVcsQ0FBQyxPQUFPLEVBQUVGLEtBQUtHLEdBQUcsR0FBRyxDQUFDO1FBRXZDLGVBQWU7UUFDZixNQUFNdkQsT0FBb0I7WUFDeEI2QyxVQUFVQyxLQUFLQyxLQUFLLENBQUNaLGFBQWFTLE9BQU8sQ0FBQyxlQUFlO1lBQ3pEWSxVQUFVVixLQUFLQyxLQUFLLENBQUNaLGFBQWFTLE9BQU8sQ0FBQyxzQkFBc0I7WUFDaEVhLFVBQVVYLEtBQUtDLEtBQUssQ0FBQ1osYUFBYVMsT0FBTyxDQUFDLHNCQUFzQjtRQUNsRTtRQUVBLDBCQUEwQjtRQUMxQixNQUFNYyxTQUFxQjtZQUN6QjFEO1lBQ0FtRDtZQUNBUSxTQUFTO1lBQ1RDLFVBQVU1QyxrQkFBa0I4QixLQUFLZSxTQUFTLENBQUM3RDtRQUM3QztRQUVBLDBCQUEwQjtRQUMxQixNQUFNOEQsa0JBQWtCL0QsWUFBWStDLEtBQUtlLFNBQVMsQ0FBQ0g7UUFFbkQsd0JBQXdCO1FBQ3hCdkIsYUFBYTRCLE9BQU8sQ0FBQ1QsVUFBVVE7UUFFL0Isc0JBQXNCO1FBQ3RCRTtRQUVBeEQsUUFBUXlELEdBQUcsQ0FBQyxrQ0FBa0NYO1FBQzlDLE9BQU87WUFDTFksU0FBUztZQUNUQyxTQUFTO1lBQ1RiO1FBQ0Y7SUFFRixFQUFFLE9BQU8vQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU87WUFDTDJELFNBQVM7WUFDVEMsU0FBUyxxQ0FBcUMsTUFBaUJBLE9BQU87UUFDeEU7SUFDRjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNELE1BQU1ILG9CQUFvQjtJQUN4QixJQUFJO1FBQ0YsTUFBTTFCLGFBQWFDLE9BQU9DLElBQUksQ0FBQ0wsY0FDNUJNLE1BQU0sQ0FBQ1AsQ0FBQUEsTUFBT0EsSUFBSVEsVUFBVSxDQUFDLFlBQzdCMEIsSUFBSSxDQUFDLENBQUNDLEdBQUdDO1lBQ1IsTUFBTUMsUUFBUUMsU0FBU0gsRUFBRXZELE9BQU8sQ0FBQyxXQUFXO1lBQzVDLE1BQU0yRCxRQUFRRCxTQUFTRixFQUFFeEQsT0FBTyxDQUFDLFdBQVc7WUFDNUMsT0FBTzJELFFBQVFGLE9BQU8sOEJBQThCO1FBQ3REO1FBRUYsb0JBQW9CO1FBQ3BCLElBQUlqQyxXQUFXbkIsTUFBTSxHQUFHekIsZUFBZUcsV0FBVyxFQUFFO1lBQ2xELE1BQU02RSxXQUFXcEMsV0FBV3FDLEtBQUssQ0FBQ2pGLGVBQWVHLFdBQVc7WUFDNUQ2RSxTQUFTRSxPQUFPLENBQUMxQyxDQUFBQTtnQkFDZkMsYUFBYTBDLFVBQVUsQ0FBQzNDO2dCQUN4QjFCLFFBQVF5RCxHQUFHLENBQUMscUNBQTJCL0I7WUFDekM7UUFDRjtJQUNGLEVBQUUsT0FBTzNCLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLG9DQUFvQ0E7SUFDcEQ7QUFDRjtBQUVBOztDQUVDLEdBQ00sTUFBTXVFLG9CQUFvQixDQUFDeEI7SUFDaEMsSUFBSTtRQUNGLElBQUl5QixpQkFBaUJ6QjtRQUVyQix5Q0FBeUM7UUFDekMsSUFBSSxDQUFDeUIsZ0JBQWdCO1lBQ25CLE1BQU16QyxhQUFhQyxPQUFPQyxJQUFJLENBQUNMLGNBQzVCTSxNQUFNLENBQUNQLENBQUFBLE1BQU9BLElBQUlRLFVBQVUsQ0FBQyxZQUM3QjBCLElBQUksQ0FBQyxDQUFDQyxHQUFHQztnQkFDUixNQUFNQyxRQUFRQyxTQUFTSCxFQUFFdkQsT0FBTyxDQUFDLFdBQVc7Z0JBQzVDLE1BQU0yRCxRQUFRRCxTQUFTRixFQUFFeEQsT0FBTyxDQUFDLFdBQVc7Z0JBQzVDLE9BQU8yRCxRQUFRRjtZQUNqQjtZQUVGLElBQUlqQyxXQUFXbkIsTUFBTSxLQUFLLEdBQUc7Z0JBQzNCLE9BQU87b0JBQ0wrQyxTQUFTO29CQUNUQyxTQUFTO2dCQUNYO1lBQ0Y7WUFFQVksaUJBQWlCekMsVUFBVSxDQUFDLEVBQUU7UUFDaEM7UUFFQSwwQkFBMEI7UUFDMUIsTUFBTXdCLGtCQUFrQjNCLGFBQWFTLE9BQU8sQ0FBQ21DO1FBQzdDLElBQUksQ0FBQ2pCLGlCQUFpQjtZQUNwQixPQUFPO2dCQUNMSSxTQUFTO2dCQUNUQyxTQUFTO1lBQ1g7UUFDRjtRQUVBLGFBQWE7UUFDYixNQUFNYSxnQkFBZ0J2RSxZQUFZcUQ7UUFDbEMsTUFBTUosU0FBcUJaLEtBQUtDLEtBQUssQ0FBQ2lDO1FBRXRDLDJCQUEyQjtRQUMzQixNQUFNQyxrQkFBa0JqRSxrQkFBa0I4QixLQUFLZSxTQUFTLENBQUNILE9BQU8xRCxJQUFJO1FBQ3BFLElBQUlpRixvQkFBb0J2QixPQUFPRSxRQUFRLEVBQUU7WUFDdkMsT0FBTztnQkFDTE0sU0FBUztnQkFDVEMsU0FBUztZQUNYO1FBQ0Y7UUFFQSxtQkFBbUI7UUFDbkJoQyxhQUFhNEIsT0FBTyxDQUFDLFlBQVlqQixLQUFLZSxTQUFTLENBQUNILE9BQU8xRCxJQUFJLENBQUM2QyxRQUFRO1FBQ3BFVixhQUFhNEIsT0FBTyxDQUFDLG1CQUFtQmpCLEtBQUtlLFNBQVMsQ0FBQ0gsT0FBTzFELElBQUksQ0FBQ3dELFFBQVE7UUFDM0VyQixhQUFhNEIsT0FBTyxDQUFDLG1CQUFtQmpCLEtBQUtlLFNBQVMsQ0FBQ0gsT0FBTzFELElBQUksQ0FBQ3lELFFBQVE7UUFFM0VqRCxRQUFReUQsR0FBRyxDQUFDLHNDQUFzQ2M7UUFDbEQsT0FBTztZQUNMYixTQUFTO1lBQ1RDLFNBQVMsQ0FBQyxxQ0FBcUMsRUFBRVQsT0FBT1AsU0FBUyxDQUFDLENBQUM7UUFDckU7SUFFRixFQUFFLE9BQU81QyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQkFBcUJBO1FBQ25DLE9BQU87WUFDTDJELFNBQVM7WUFDVEMsU0FBUyw4QkFBOEIsTUFBaUJBLE9BQU87UUFDakU7SUFDRjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1lLGdCQUFnQixDQUFDaEQsS0FBYWxDO0lBQ3pDLElBQUk7UUFDRix5QkFBeUI7UUFDekIsTUFBTTBCLFNBQVNEO1FBQ2YsSUFBSSxDQUFDQyxPQUFPQyxTQUFTLElBQUlELE9BQU9JLFdBQVcsR0FBR3BDLGVBQWVDLGdCQUFnQixHQUFHLEtBQUs7WUFDbkYsT0FBTztnQkFDTHVFLFNBQVM7Z0JBQ1RDLFNBQVM7WUFDWDtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCLE1BQU1nQixXQUFXckMsS0FBS2UsU0FBUyxDQUFDN0Q7UUFFaEMsbUJBQW1CO1FBQ25CLElBQUltRixTQUFTaEUsTUFBTSxHQUFHekIsZUFBZUMsZ0JBQWdCLEdBQUcsS0FBSztZQUMzRGEsUUFBUTRFLElBQUksQ0FBQyw4QkFBOEI5RCxLQUFLK0QsS0FBSyxDQUFDRixTQUFTaEUsTUFBTSxHQUFHLE9BQU87UUFDakY7UUFFQSxRQUFRO1FBQ1JnQixhQUFhNEIsT0FBTyxDQUFDN0IsS0FBS2lEO1FBRTFCLGtCQUFrQjtRQUNsQixNQUFNRyxRQUFRbkQsYUFBYVMsT0FBTyxDQUFDVjtRQUNuQyxJQUFJb0QsVUFBVUgsVUFBVTtZQUN0QixNQUFNLElBQUlJLE1BQU07UUFDbEI7UUFFQSw4Q0FBOEM7UUFDOUMsSUFBSXJELFFBQVEsY0FBY0EsUUFBUSxtQkFBbUI7WUFDbkRzRCxXQUFXLElBQU10QyxnQkFBZ0I7UUFDbkM7UUFFQSxPQUFPO1lBQ0xnQixTQUFTO1lBQ1RDLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBTzVELE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBTztZQUNMMkQsU0FBUztZQUNUQyxTQUFTLG1CQUFtQixNQUFpQkEsT0FBTztRQUN0RDtJQUNGO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTXNCLGdCQUFnQixDQUFJdkQsS0FBYXdEO0lBQzVDLElBQUk7UUFDRixNQUFNQyxTQUFTeEQsYUFBYVMsT0FBTyxDQUFDVjtRQUVwQyxJQUFJLENBQUN5RCxRQUFRO1lBQ1gsT0FBTztnQkFDTHpCLFNBQVM7Z0JBQ1RsRSxNQUFNMEY7Z0JBQ052QixTQUFTO1lBQ1g7UUFDRjtRQUVBLE1BQU15QixTQUFTOUMsS0FBS0MsS0FBSyxDQUFDNEM7UUFFMUIseUJBQXlCO1FBQ3pCLElBQUksT0FBT0MsV0FBVyxPQUFPRixjQUFjO1lBQ3pDbEYsUUFBUTRFLElBQUksQ0FBQyxrQ0FBa0NsRDtZQUMvQyxPQUFPO2dCQUNMZ0MsU0FBUztnQkFDVGxFLE1BQU0wRjtnQkFDTnZCLFNBQVM7WUFDWDtRQUNGO1FBRUEsT0FBTztZQUNMRCxTQUFTO1lBQ1RsRSxNQUFNNEY7UUFDUjtJQUVGLEVBQUUsT0FBT3JGLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFFdkMsd0NBQXdDO1FBQ3hDLElBQUkyQixRQUFRLFlBQVk7WUFDdEIsTUFBTTJELGdCQUFnQmY7WUFDdEIsSUFBSWUsY0FBYzNCLE9BQU8sRUFBRTtnQkFDekIsK0JBQStCO2dCQUMvQixJQUFJO29CQUNGLE1BQU15QixTQUFTeEQsYUFBYVMsT0FBTyxDQUFDVjtvQkFDcEMsSUFBSXlELFFBQVE7d0JBQ1YsT0FBTzs0QkFDTHpCLFNBQVM7NEJBQ1RsRSxNQUFNOEMsS0FBS0MsS0FBSyxDQUFDNEM7NEJBQ2pCeEIsU0FBUzt3QkFDWDtvQkFDRjtnQkFDRixFQUFFLE9BQU8yQixZQUFZO29CQUNuQnRGLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUN1RjtnQkFDakQ7WUFDRjtRQUNGO1FBRUEsT0FBTztZQUNMNUIsU0FBUztZQUNUbEUsTUFBTTBGO1lBQ052QixTQUFTLDRCQUE0QixNQUFpQkEsT0FBTztRQUMvRDtJQUNGO0FBQ0YsRUFBRTtBQUVGLG9DQUFvQztBQUNWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvLi9zcmMvdXRpbHMvc2VjdXJlU3RvcmFnZS50cz8zOWE1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog2YbYuNin2YUg2KXYr9in2LHYqSDYp9mE2KrYrtiy2YrZhiDYp9mE2KLZhdmGINmI2KfZhNmF2K3Ys9mGXG4gKiDZitmI2YHYsSDYrdmF2KfZitipINi02KfZhdmE2Kkg2YTZhNio2YrYp9mG2KfYqiDZhdi5INmG2LPYriDYp9it2KrZitin2LfZitipINiq2YTZgtin2KbZitipXG4gKi9cblxuaW1wb3J0IHsgSW52b2ljZSwgQ2xpZW50IH0gZnJvbSAnQC90eXBlcyc7XG5cbi8vINil2LnYr9in2K/Yp9iqINin2YTZhti42KfZhVxuY29uc3QgU1RPUkFHRV9DT05GSUcgPSB7XG4gIE1BWF9TVE9SQUdFX1NJWkU6IDQgKiAxMDI0ICogMTAyNCwgLy8gNE1CICjYo9mC2YQg2YXZhiDYrdivIGxvY2FsU3RvcmFnZSlcbiAgQkFDS1VQX0lOVEVSVkFMOiA1ICogNjAgKiAxMDAwLCAvLyA1INiv2YLYp9im2YJcbiAgTUFYX0JBQ0tVUFM6IDEwLCAvLyDYudiv2K8g2KfZhNmG2LPYriDYp9mE2KfYrdiq2YrYp9i32YrYqSDYp9mE2YXYrdmB2YjYuNipXG4gIEVOQ1JZUFRJT05fS0VZOiAnaW5mYXBhcHAtc2VjdXJlLWtleS0yMDI0JywgLy8g2YXZgdiq2KfYrSDYp9mE2KrYtNmB2YrYsSDYp9mE2KPYs9in2LPZilxufTtcblxuLy8g2KPZhtmI2KfYuSDYp9mE2KjZitin2YbYp9iqXG5pbnRlcmZhY2UgU3RvcmFnZURhdGEge1xuICBpbnZvaWNlczogSW52b2ljZVtdO1xuICBzZXR0aW5nczogYW55O1xuICBjb3VudGVyczogeyBba2V5OiBzdHJpbmddOiBudW1iZXIgfTtcbn1cblxuaW50ZXJmYWNlIEJhY2t1cERhdGEge1xuICBkYXRhOiBTdG9yYWdlRGF0YTtcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIHZlcnNpb246IHN0cmluZztcbiAgY2hlY2tzdW06IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFN0b3JhZ2VIZWFsdGgge1xuICBpc0hlYWx0aHk6IGJvb2xlYW47XG4gIGlzc3Vlczogc3RyaW5nW107XG4gIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW107XG4gIHN0b3JhZ2VVc2VkOiBudW1iZXI7XG4gIHN0b3JhZ2VMaW1pdDogbnVtYmVyO1xuICBiYWNrdXBzQ291bnQ6IG51bWJlcjtcbn1cblxuLyoqXG4gKiDYqti02YHZitixINij2LPYp9iz2Yog2YTZhNio2YrYp9mG2KfYqiDYp9mE2K3Ys9in2LPYqVxuICovXG5jb25zdCBlbmNyeXB0RGF0YSA9IChkYXRhOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICB0cnkge1xuICAgIC8vINiq2LTZgdmK2LEg2KjYs9mK2Lcg2KjYp9iz2KrYrtiv2KfZhSBCYXNlNjQg2YXYuSDYqti02YjZiti0XG4gICAgY29uc3QgZW5jb2RlZCA9IGJ0b2EoZGF0YSk7XG4gICAgY29uc3Qgc2NyYW1ibGVkID0gZW5jb2RlZC5zcGxpdCgnJykucmV2ZXJzZSgpLmpvaW4oJycpO1xuICAgIHJldHVybiBidG9hKHNjcmFtYmxlZCArIFNUT1JBR0VfQ09ORklHLkVOQ1JZUFRJT05fS0VZKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgRW5jcnlwdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHJldHVybiBkYXRhOyAvLyDYpdix2KzYp9i5INin2YTYqNmK2KfZhtin2Kog2KjYr9mI2YYg2KrYtNmB2YrYsSDZgdmKINit2KfZhNipINin2YTZgdi02YRcbiAgfVxufTtcblxuLyoqXG4gKiDZgdmDINiq2LTZgdmK2LEg2KfZhNio2YrYp9mG2KfYqlxuICovXG5jb25zdCBkZWNyeXB0RGF0YSA9IChlbmNyeXB0ZWREYXRhOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGRlY29kZWQgPSBhdG9iKGVuY3J5cHRlZERhdGEpO1xuICAgIGNvbnN0IHdpdGhvdXRLZXkgPSBkZWNvZGVkLnJlcGxhY2UoU1RPUkFHRV9DT05GSUcuRU5DUllQVElPTl9LRVksICcnKTtcbiAgICBjb25zdCB1bnNjcmFtYmxlZCA9IHdpdGhvdXRLZXkuc3BsaXQoJycpLnJldmVyc2UoKS5qb2luKCcnKTtcbiAgICByZXR1cm4gYXRvYih1bnNjcmFtYmxlZCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIERlY3J5cHRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICByZXR1cm4gZW5jcnlwdGVkRGF0YTsgLy8g2KXYsdis2KfYuSDYp9mE2KjZitin2YbYp9iqINmD2YXYpyDZh9mKINmB2Yog2K3Yp9mE2Kkg2KfZhNmB2LTZhFxuICB9XG59O1xuXG4vKipcbiAqINit2LPYp9ioIGNoZWNrc3VtINmE2YTYqNmK2KfZhtin2KpcbiAqL1xuY29uc3QgY2FsY3VsYXRlQ2hlY2tzdW0gPSAoZGF0YTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgbGV0IGhhc2ggPSAwO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBjaGFyID0gZGF0YS5jaGFyQ29kZUF0KGkpO1xuICAgIGhhc2ggPSAoKGhhc2ggPDwgNSkgLSBoYXNoKSArIGNoYXI7XG4gICAgaGFzaCA9IGhhc2ggJiBoYXNoOyAvLyDYqtit2YjZitmEINil2YTZiSAzMmJpdCBpbnRlZ2VyXG4gIH1cbiAgcmV0dXJuIE1hdGguYWJzKGhhc2gpLnRvU3RyaW5nKDE2KTtcbn07XG5cbi8qKlxuICog2YHYrdi1INit2KfZhNipINin2YTYqtiu2LLZitmGXG4gKi9cbmV4cG9ydCBjb25zdCBjaGVja1N0b3JhZ2VIZWFsdGggPSAoKTogU3RvcmFnZUhlYWx0aCA9PiB7XG4gIGNvbnN0IGhlYWx0aDogU3RvcmFnZUhlYWx0aCA9IHtcbiAgICBpc0hlYWx0aHk6IHRydWUsXG4gICAgaXNzdWVzOiBbXSxcbiAgICByZWNvbW1lbmRhdGlvbnM6IFtdLFxuICAgIHN0b3JhZ2VVc2VkOiAwLFxuICAgIHN0b3JhZ2VMaW1pdDogU1RPUkFHRV9DT05GSUcuTUFYX1NUT1JBR0VfU0laRSxcbiAgICBiYWNrdXBzQ291bnQ6IDAsXG4gIH07XG5cbiAgdHJ5IHtcbiAgICAvLyDYrdiz2KfYqCDYrdis2YUg2KfZhNiq2K7YstmK2YYg2KfZhNmF2LPYqtiu2K/ZhVxuICAgIGxldCB0b3RhbFNpemUgPSAwO1xuICAgIGZvciAobGV0IGtleSBpbiBsb2NhbFN0b3JhZ2UpIHtcbiAgICAgIGlmIChsb2NhbFN0b3JhZ2UuaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICB0b3RhbFNpemUgKz0gbG9jYWxTdG9yYWdlW2tleV0ubGVuZ3RoO1xuICAgICAgfVxuICAgIH1cbiAgICBoZWFsdGguc3RvcmFnZVVzZWQgPSB0b3RhbFNpemU7XG5cbiAgICAvLyDZgdit2LUg2KfZhNit2K8g2KfZhNij2YLYtdmJXG4gICAgaWYgKHRvdGFsU2l6ZSA+IFNUT1JBR0VfQ09ORklHLk1BWF9TVE9SQUdFX1NJWkUgKiAwLjgpIHtcbiAgICAgIGhlYWx0aC5pc0hlYWx0aHkgPSBmYWxzZTtcbiAgICAgIGhlYWx0aC5pc3N1ZXMucHVzaCgn2KfZhNiq2K7YstmK2YYg2YrZgtiq2LHYqCDZhdmGINin2YTYrdivINin2YTYo9mC2LXZiScpO1xuICAgICAgaGVhbHRoLnJlY29tbWVuZGF0aW9ucy5wdXNoKCfYqtmG2LjZitmBINin2YTYqNmK2KfZhtin2Kog2KfZhNmC2K/ZitmF2KknKTtcbiAgICB9XG5cbiAgICAvLyDZgdit2LUg2KfZhNmG2LPYriDYp9mE2KfYrdiq2YrYp9i32YrYqVxuICAgIGNvbnN0IGJhY2t1cEtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpLmZpbHRlcihrZXkgPT4ga2V5LnN0YXJ0c1dpdGgoJ2JhY2t1cC0nKSk7XG4gICAgaGVhbHRoLmJhY2t1cHNDb3VudCA9IGJhY2t1cEtleXMubGVuZ3RoO1xuXG4gICAgaWYgKGhlYWx0aC5iYWNrdXBzQ291bnQgPT09IDApIHtcbiAgICAgIGhlYWx0aC5pc3N1ZXMucHVzaCgn2YTYpyDYqtmI2KzYryDZhtiz2K4g2KfYrdiq2YrYp9i32YrYqScpO1xuICAgICAgaGVhbHRoLnJlY29tbWVuZGF0aW9ucy5wdXNoKCfYpdmG2LTYp9ihINmG2LPYrtipINin2K3YqtmK2KfYt9mK2Kkg2YHZiNix2YrYqScpO1xuICAgIH1cblxuICAgIC8vINmB2K3YtSDYp9mE2KjZitin2YbYp9iqINin2YTYo9iz2KfYs9mK2KlcbiAgICBjb25zdCBpbnZvaWNlc0RhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaW52b2ljZXMnKTtcbiAgICBpZiAoIWludm9pY2VzRGF0YSkge1xuICAgICAgaGVhbHRoLmlzc3Vlcy5wdXNoKCfZhNinINiq2YjYrNivINio2YrYp9mG2KfYqiDZgdmI2KfYqtmK2LEnKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgaW52b2ljZXMgPSBKU09OLnBhcnNlKGludm9pY2VzRGF0YSk7XG4gICAgICAgIGlmICghQXJyYXkuaXNBcnJheShpbnZvaWNlcykpIHtcbiAgICAgICAgICBoZWFsdGguaXNIZWFsdGh5ID0gZmFsc2U7XG4gICAgICAgICAgaGVhbHRoLmlzc3Vlcy5wdXNoKCfYqNmK2KfZhtin2Kog2KfZhNmB2YjYp9iq2YrYsSDYqtin2YTZgdipJyk7XG4gICAgICAgICAgaGVhbHRoLnJlY29tbWVuZGF0aW9ucy5wdXNoKCfYp9iz2KrYsdiv2KfYryDZhdmGINin2YTZhtiz2K7YqSDYp9mE2KfYrdiq2YrYp9i32YrYqScpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBoZWFsdGguaXNIZWFsdGh5ID0gZmFsc2U7XG4gICAgICAgIGhlYWx0aC5pc3N1ZXMucHVzaCgn2YHYtNmEINmB2Yog2YLYsdin2KHYqSDYqNmK2KfZhtin2Kog2KfZhNmB2YjYp9iq2YrYsScpO1xuICAgICAgICBoZWFsdGgucmVjb21tZW5kYXRpb25zLnB1c2goJ9in2LPYqtix2K/Yp9ivINmF2YYg2KfZhNmG2LPYrtipINin2YTYp9it2KrZitin2LfZitipJyk7XG4gICAgICB9XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaGVhbHRoLmlzSGVhbHRoeSA9IGZhbHNlO1xuICAgIGhlYWx0aC5pc3N1ZXMucHVzaCgn2YHYtNmEINmB2Yog2YHYrdi1INit2KfZhNipINin2YTYqtiu2LLZitmGJyk7XG4gICAgaGVhbHRoLnJlY29tbWVuZGF0aW9ucy5wdXNoKCfYpdi52KfYr9ipINiq2LTYutmK2YQg2KfZhNiq2LfYqNmK2YInKTtcbiAgfVxuXG4gIHJldHVybiBoZWFsdGg7XG59O1xuXG4vKipcbiAqINil2YbYtNin2KEg2YbYs9iu2Kkg2KfYrdiq2YrYp9i32YrYqVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlQmFja3VwID0gKCk6IHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZTogc3RyaW5nOyBiYWNrdXBJZD86IHN0cmluZyB9ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG4gICAgY29uc3QgYmFja3VwSWQgPSBgYmFja3VwLSR7RGF0ZS5ub3coKX1gO1xuXG4gICAgLy8g2KzZhdi5INin2YTYqNmK2KfZhtin2KpcbiAgICBjb25zdCBkYXRhOiBTdG9yYWdlRGF0YSA9IHtcbiAgICAgIGludm9pY2VzOiBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdpbnZvaWNlcycpIHx8ICdbXScpLFxuICAgICAgc2V0dGluZ3M6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2NvbXBhbnlTZXR0aW5ncycpIHx8ICd7fScpLFxuICAgICAgY291bnRlcnM6IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2ludm9pY2VDb3VudGVycycpIHx8ICd7fScpLFxuICAgIH07XG5cbiAgICAvLyDYpdmG2LTYp9ihINin2YTZhtiz2K7YqSDYp9mE2KfYrdiq2YrYp9i32YrYqVxuICAgIGNvbnN0IGJhY2t1cDogQmFja3VwRGF0YSA9IHtcbiAgICAgIGRhdGEsXG4gICAgICB0aW1lc3RhbXAsXG4gICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxuICAgICAgY2hlY2tzdW06IGNhbGN1bGF0ZUNoZWNrc3VtKEpTT04uc3RyaW5naWZ5KGRhdGEpKSxcbiAgICB9O1xuXG4gICAgLy8g2KrYtNmB2YrYsSDYp9mE2YbYs9iu2Kkg2KfZhNin2K3YqtmK2KfYt9mK2KlcbiAgICBjb25zdCBlbmNyeXB0ZWRCYWNrdXAgPSBlbmNyeXB0RGF0YShKU09OLnN0cmluZ2lmeShiYWNrdXApKTtcblxuICAgIC8vINit2YHYuCDYp9mE2YbYs9iu2Kkg2KfZhNin2K3YqtmK2KfYt9mK2KlcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShiYWNrdXBJZCwgZW5jcnlwdGVkQmFja3VwKTtcblxuICAgIC8vINiq2YbYuNmK2YEg2KfZhNmG2LPYriDYp9mE2YLYr9mK2YXYqVxuICAgIGNsZWFudXBPbGRCYWNrdXBzKCk7XG5cbiAgICBjb25zb2xlLmxvZygn4pyFIEJhY2t1cCBjcmVhdGVkIHN1Y2Nlc3NmdWxseTonLCBiYWNrdXBJZCk7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAn2KrZhSDYpdmG2LTYp9ihINin2YTZhtiz2K7YqSDYp9mE2KfYrdiq2YrYp9i32YrYqSDYqNmG2KzYp9itJyxcbiAgICAgIGJhY2t1cElkLFxuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgQmFja3VwIGNyZWF0aW9uIGZhaWxlZDonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ9mB2LTZhCDZgdmKINil2YbYtNin2KEg2KfZhNmG2LPYrtipINin2YTYp9it2KrZitin2LfZitipOiAnICsgKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlLFxuICAgIH07XG4gIH1cbn07XG5cbi8qKlxuICog2KrZhti42YrZgSDYp9mE2YbYs9iuINin2YTYp9it2KrZitin2LfZitipINin2YTZgtiv2YrZhdipXG4gKi9cbmNvbnN0IGNsZWFudXBPbGRCYWNrdXBzID0gKCk6IHZvaWQgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGJhY2t1cEtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpXG4gICAgICAuZmlsdGVyKGtleSA9PiBrZXkuc3RhcnRzV2l0aCgnYmFja3VwLScpKVxuICAgICAgLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgY29uc3QgdGltZUEgPSBwYXJzZUludChhLnJlcGxhY2UoJ2JhY2t1cC0nLCAnJykpO1xuICAgICAgICBjb25zdCB0aW1lQiA9IHBhcnNlSW50KGIucmVwbGFjZSgnYmFja3VwLScsICcnKSk7XG4gICAgICAgIHJldHVybiB0aW1lQiAtIHRpbWVBOyAvLyDYqtix2KrZitioINiq2YbYp9iy2YTZiiAo2KfZhNij2K3Yr9irINij2YjZhNin2YspXG4gICAgICB9KTtcblxuICAgIC8vINit2LDZgSDYp9mE2YbYs9iuINin2YTYstin2KbYr9ipXG4gICAgaWYgKGJhY2t1cEtleXMubGVuZ3RoID4gU1RPUkFHRV9DT05GSUcuTUFYX0JBQ0tVUFMpIHtcbiAgICAgIGNvbnN0IHRvRGVsZXRlID0gYmFja3VwS2V5cy5zbGljZShTVE9SQUdFX0NPTkZJRy5NQVhfQkFDS1VQUyk7XG4gICAgICB0b0RlbGV0ZS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIFJlbW92ZWQgb2xkIGJhY2t1cDonLCBrZXkpO1xuICAgICAgfSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gY2xlYW51cCBvbGQgYmFja3VwczonLCBlcnJvcik7XG4gIH1cbn07XG5cbi8qKlxuICog2KfYs9iq2LHYr9in2K8g2YXZhiDYp9mE2YbYs9iu2Kkg2KfZhNin2K3YqtmK2KfYt9mK2KlcbiAqL1xuZXhwb3J0IGNvbnN0IHJlc3RvcmVGcm9tQmFja3VwID0gKGJhY2t1cElkPzogc3RyaW5nKTogeyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfSA9PiB7XG4gIHRyeSB7XG4gICAgbGV0IHRhcmdldEJhY2t1cElkID0gYmFja3VwSWQ7XG5cbiAgICAvLyDYpdiw2Kcg2YTZhSDZitiq2YUg2KrYrdiv2YrYryDYp9mE2YbYs9iu2KnYjCDYp9iz2KrYrtiv2YUg2KfZhNij2K3Yr9irXG4gICAgaWYgKCF0YXJnZXRCYWNrdXBJZCkge1xuICAgICAgY29uc3QgYmFja3VwS2V5cyA9IE9iamVjdC5rZXlzKGxvY2FsU3RvcmFnZSlcbiAgICAgICAgLmZpbHRlcihrZXkgPT4ga2V5LnN0YXJ0c1dpdGgoJ2JhY2t1cC0nKSlcbiAgICAgICAgLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgICAgICBjb25zdCB0aW1lQSA9IHBhcnNlSW50KGEucmVwbGFjZSgnYmFja3VwLScsICcnKSk7XG4gICAgICAgICAgY29uc3QgdGltZUIgPSBwYXJzZUludChiLnJlcGxhY2UoJ2JhY2t1cC0nLCAnJykpO1xuICAgICAgICAgIHJldHVybiB0aW1lQiAtIHRpbWVBO1xuICAgICAgICB9KTtcblxuICAgICAgaWYgKGJhY2t1cEtleXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ9mE2Kcg2KrZiNis2K8g2YbYs9iuINin2K3YqtmK2KfYt9mK2Kkg2YXYqtin2K3YqScsXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIHRhcmdldEJhY2t1cElkID0gYmFja3VwS2V5c1swXTtcbiAgICB9XG5cbiAgICAvLyDZgtix2KfYodipINin2YTZhtiz2K7YqSDYp9mE2KfYrdiq2YrYp9i32YrYqVxuICAgIGNvbnN0IGVuY3J5cHRlZEJhY2t1cCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRhcmdldEJhY2t1cElkKTtcbiAgICBpZiAoIWVuY3J5cHRlZEJhY2t1cCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICfYp9mE2YbYs9iu2Kkg2KfZhNin2K3YqtmK2KfYt9mK2Kkg2LrZitixINmF2YjYrNmI2K/YqScsXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vINmB2YMg2KfZhNiq2LTZgdmK2LFcbiAgICBjb25zdCBkZWNyeXB0ZWREYXRhID0gZGVjcnlwdERhdGEoZW5jcnlwdGVkQmFja3VwKTtcbiAgICBjb25zdCBiYWNrdXA6IEJhY2t1cERhdGEgPSBKU09OLnBhcnNlKGRlY3J5cHRlZERhdGEpO1xuXG4gICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2LPZhNin2YXYqSDYp9mE2KjZitin2YbYp9iqXG4gICAgY29uc3QgY3VycmVudENoZWNrc3VtID0gY2FsY3VsYXRlQ2hlY2tzdW0oSlNPTi5zdHJpbmdpZnkoYmFja3VwLmRhdGEpKTtcbiAgICBpZiAoY3VycmVudENoZWNrc3VtICE9PSBiYWNrdXAuY2hlY2tzdW0pIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAn2KfZhNmG2LPYrtipINin2YTYp9it2KrZitin2LfZitipINiq2KfZhNmB2KkgKGNoZWNrc3VtIG1pc21hdGNoKScsXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vINin2LPYqtix2K/Yp9ivINin2YTYqNmK2KfZhtin2KpcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnaW52b2ljZXMnLCBKU09OLnN0cmluZ2lmeShiYWNrdXAuZGF0YS5pbnZvaWNlcykpO1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjb21wYW55U2V0dGluZ3MnLCBKU09OLnN0cmluZ2lmeShiYWNrdXAuZGF0YS5zZXR0aW5ncykpO1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdpbnZvaWNlQ291bnRlcnMnLCBKU09OLnN0cmluZ2lmeShiYWNrdXAuZGF0YS5jb3VudGVycykpO1xuXG4gICAgY29uc29sZS5sb2coJ+KchSBEYXRhIHJlc3RvcmVkIHN1Y2Nlc3NmdWxseSBmcm9tOicsIHRhcmdldEJhY2t1cElkKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6IGDYqtmFINin2LPYqtix2K/Yp9ivINin2YTYqNmK2KfZhtin2Kog2KjZhtis2KfYrSDZhdmGINin2YTZhtiz2K7YqTogJHtiYWNrdXAudGltZXN0YW1wfWAsXG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBSZXN0b3JlIGZhaWxlZDonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ9mB2LTZhCDZgdmKINin2LPYqtix2K/Yp9ivINin2YTYqNmK2KfZhtin2Ko6ICcgKyAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UsXG4gICAgfTtcbiAgfVxufTtcblxuLyoqXG4gKiDYp9mE2K3Zgdi4INin2YTYotmF2YYg2YTZhNio2YrYp9mG2KfYqlxuICovXG5leHBvcnQgY29uc3Qgc2VjdXJlU2V0SXRlbSA9IChrZXk6IHN0cmluZywgZGF0YTogYW55KTogeyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2YHYrdi1INit2KfZhNipINin2YTYqtiu2LLZitmGINij2YjZhNin2YtcbiAgICBjb25zdCBoZWFsdGggPSBjaGVja1N0b3JhZ2VIZWFsdGgoKTtcbiAgICBpZiAoIWhlYWx0aC5pc0hlYWx0aHkgJiYgaGVhbHRoLnN0b3JhZ2VVc2VkID4gU1RPUkFHRV9DT05GSUcuTUFYX1NUT1JBR0VfU0laRSAqIDAuOSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICfYp9mE2KrYrtiy2YrZhiDZhdmF2KrZhNimIC0g2YrYsdis2Ykg2KrZhti42YrZgSDYp9mE2KjZitin2YbYp9iqJyxcbiAgICAgIH07XG4gICAgfVxuXG4gICAgLy8g2KrYrdmI2YrZhCDYp9mE2KjZitin2YbYp9iqINil2YTZiSBKU09OXG4gICAgY29uc3QganNvbkRhdGEgPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcblxuICAgIC8vINmB2K3YtSDYrdis2YUg2KfZhNio2YrYp9mG2KfYqlxuICAgIGlmIChqc29uRGF0YS5sZW5ndGggPiBTVE9SQUdFX0NPTkZJRy5NQVhfU1RPUkFHRV9TSVpFICogMC4xKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBMYXJnZSBkYXRhIGJlaW5nIHNhdmVkOicsIE1hdGgucm91bmQoanNvbkRhdGEubGVuZ3RoIC8gMTAyNCksICdLQicpO1xuICAgIH1cblxuICAgIC8vINin2YTYrdmB2LhcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShrZXksIGpzb25EYXRhKTtcblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINin2YTYrdmB2LhcbiAgICBjb25zdCBzYXZlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgaWYgKHNhdmVkICE9PSBqc29uRGF0YSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfZgdi02YQg2YHZiiDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2K3Zgdi4Jyk7XG4gICAgfVxuXG4gICAgLy8g2KXZhti02KfYoSDZhtiz2K7YqSDYp9it2KrZitin2LfZitipINiq2YTZgtin2KbZitipINmE2YTYqNmK2KfZhtin2Kog2KfZhNmF2YfZhdipXG4gICAgaWYgKGtleSA9PT0gJ2ludm9pY2VzJyB8fCBrZXkgPT09ICdjb21wYW55U2V0dGluZ3MnKSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IGNyZWF0ZUJhY2t1cCgpLCAxMDAwKTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICfYqtmFINin2YTYrdmB2Lgg2KjZhtis2KfYrScsXG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTZWN1cmUgc2F2ZSBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIG1lc3NhZ2U6ICfZgdi02YQg2YHZiiDYp9mE2K3Zgdi4OiAnICsgKGVycm9yIGFzIEVycm9yKS5tZXNzYWdlLFxuICAgIH07XG4gIH1cbn07XG5cbi8qKlxuICog2KfZhNmC2LHYp9ih2Kkg2KfZhNii2YXZhtipINmE2YTYqNmK2KfZhtin2KpcbiAqL1xuZXhwb3J0IGNvbnN0IHNlY3VyZUdldEl0ZW0gPSA8VD4oa2V5OiBzdHJpbmcsIGRlZmF1bHRWYWx1ZTogVCk6IHsgc3VjY2VzczogYm9vbGVhbjsgZGF0YTogVDsgbWVzc2FnZT86IHN0cmluZyB9ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpO1xuICAgIFxuICAgIGlmICghc3RvcmVkKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBkYXRhOiBkZWZhdWx0VmFsdWUsXG4gICAgICAgIG1lc3NhZ2U6ICfZhNinINiq2YjYrNivINio2YrYp9mG2KfYqiDZhdit2YHZiNi42KnYjCDYp9iz2KrYrtiv2KfZhSDYp9mE2YLZitmFINin2YTYp9mB2KrYsdin2LbZitipJyxcbiAgICAgIH07XG4gICAgfVxuXG4gICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShzdG9yZWQpO1xuICAgIFxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmG2YjYuSDYp9mE2KjZitin2YbYp9iqXG4gICAgaWYgKHR5cGVvZiBwYXJzZWQgIT09IHR5cGVvZiBkZWZhdWx0VmFsdWUpIHtcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIERhdGEgdHlwZSBtaXNtYXRjaCBmb3Iga2V5OicsIGtleSk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZGF0YTogZGVmYXVsdFZhbHVlLFxuICAgICAgICBtZXNzYWdlOiAn2YbZiNi5INin2YTYqNmK2KfZhtin2Kog2LrZitixINmF2KrYt9in2KjZgicsXG4gICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogcGFyc2VkLFxuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgU2VjdXJlIHJlYWQgZmFpbGVkOicsIGVycm9yKTtcbiAgICBcbiAgICAvLyDZhdit2KfZiNmE2Kkg2KfZhNin2LPYqtix2K/Yp9ivINmF2YYg2KfZhNmG2LPYrtipINin2YTYp9it2KrZitin2LfZitipXG4gICAgaWYgKGtleSA9PT0gJ2ludm9pY2VzJykge1xuICAgICAgY29uc3QgcmVzdG9yZVJlc3VsdCA9IHJlc3RvcmVGcm9tQmFja3VwKCk7XG4gICAgICBpZiAocmVzdG9yZVJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIC8vINil2LnYp9iv2Kkg2KfZhNmF2K3Yp9mI2YTYqSDYqNi52K8g2KfZhNin2LPYqtix2K/Yp9ivXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KTtcbiAgICAgICAgICBpZiAoc3RvcmVkKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgICAgICBkYXRhOiBKU09OLnBhcnNlKHN0b3JlZCksXG4gICAgICAgICAgICAgIG1lc3NhZ2U6ICfYqtmFINin2YTYp9iz2KrYsdiv2KfYryDZhdmGINin2YTZhtiz2K7YqSDYp9mE2KfYrdiq2YrYp9i32YrYqScsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAocmV0cnlFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBSZXRyeSBhZnRlciByZXN0b3JlIGZhaWxlZDonLCByZXRyeUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGRhdGE6IGRlZmF1bHRWYWx1ZSxcbiAgICAgIG1lc3NhZ2U6ICfZgdi02YQg2YHZiiDZgtix2KfYodipINin2YTYqNmK2KfZhtin2Ko6ICcgKyAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UsXG4gICAgfTtcbiAgfVxufTtcblxuLy8g2KrYtdiv2YrYsSDYp9mE2KXYudiv2KfYr9in2Kog2YTZhNin2LPYqtiu2K/Yp9mFINin2YTYrtin2LHYrNmKXG5leHBvcnQgeyBTVE9SQUdFX0NPTkZJRyB9O1xuIl0sIm5hbWVzIjpbIlNUT1JBR0VfQ09ORklHIiwiTUFYX1NUT1JBR0VfU0laRSIsIkJBQ0tVUF9JTlRFUlZBTCIsIk1BWF9CQUNLVVBTIiwiRU5DUllQVElPTl9LRVkiLCJlbmNyeXB0RGF0YSIsImRhdGEiLCJlbmNvZGVkIiwiYnRvYSIsInNjcmFtYmxlZCIsInNwbGl0IiwicmV2ZXJzZSIsImpvaW4iLCJlcnJvciIsImNvbnNvbGUiLCJkZWNyeXB0RGF0YSIsImVuY3J5cHRlZERhdGEiLCJkZWNvZGVkIiwiYXRvYiIsIndpdGhvdXRLZXkiLCJyZXBsYWNlIiwidW5zY3JhbWJsZWQiLCJjYWxjdWxhdGVDaGVja3N1bSIsImhhc2giLCJpIiwibGVuZ3RoIiwiY2hhciIsImNoYXJDb2RlQXQiLCJNYXRoIiwiYWJzIiwidG9TdHJpbmciLCJjaGVja1N0b3JhZ2VIZWFsdGgiLCJoZWFsdGgiLCJpc0hlYWx0aHkiLCJpc3N1ZXMiLCJyZWNvbW1lbmRhdGlvbnMiLCJzdG9yYWdlVXNlZCIsInN0b3JhZ2VMaW1pdCIsImJhY2t1cHNDb3VudCIsInRvdGFsU2l6ZSIsImtleSIsImxvY2FsU3RvcmFnZSIsImhhc093blByb3BlcnR5IiwicHVzaCIsImJhY2t1cEtleXMiLCJPYmplY3QiLCJrZXlzIiwiZmlsdGVyIiwic3RhcnRzV2l0aCIsImludm9pY2VzRGF0YSIsImdldEl0ZW0iLCJpbnZvaWNlcyIsIkpTT04iLCJwYXJzZSIsIkFycmF5IiwiaXNBcnJheSIsImNyZWF0ZUJhY2t1cCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImJhY2t1cElkIiwibm93Iiwic2V0dGluZ3MiLCJjb3VudGVycyIsImJhY2t1cCIsInZlcnNpb24iLCJjaGVja3N1bSIsInN0cmluZ2lmeSIsImVuY3J5cHRlZEJhY2t1cCIsInNldEl0ZW0iLCJjbGVhbnVwT2xkQmFja3VwcyIsImxvZyIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwic29ydCIsImEiLCJiIiwidGltZUEiLCJwYXJzZUludCIsInRpbWVCIiwidG9EZWxldGUiLCJzbGljZSIsImZvckVhY2giLCJyZW1vdmVJdGVtIiwicmVzdG9yZUZyb21CYWNrdXAiLCJ0YXJnZXRCYWNrdXBJZCIsImRlY3J5cHRlZERhdGEiLCJjdXJyZW50Q2hlY2tzdW0iLCJzZWN1cmVTZXRJdGVtIiwianNvbkRhdGEiLCJ3YXJuIiwicm91bmQiLCJzYXZlZCIsIkVycm9yIiwic2V0VGltZW91dCIsInNlY3VyZUdldEl0ZW0iLCJkZWZhdWx0VmFsdWUiLCJzdG9yZWQiLCJwYXJzZWQiLCJyZXN0b3JlUmVzdWx0IiwicmV0cnlFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/secureStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"af7364717194\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzkyZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZjczNjQ3MTcxOTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/customer-codes/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/customer-codes/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\231\src\app\customer-codes\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الفواتير - Invoice Management System\",\n    description: \"نظام شامل لإدارة فواتير الخدمات مع دعم كامل للغة العربية\",\n    keywords: \"فواتير, إدارة, خدمات, استشارات, باترون, تصنيع\",\n    authors: [\n        {\n            name: \"Invoice Management System\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().className)} font-arabic`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();