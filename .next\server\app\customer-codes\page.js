/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/customer-codes/page";
exports.ids = ["app/customer-codes/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'customer-codes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/customer-codes/page.tsx */ \"(rsc)/./src/app/customer-codes/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/customer-codes/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/customer-codes/page\",\n        pathname: \"/customer-codes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZjdXN0b21lci1jb2RlcyUyRnBhZ2UmcGFnZT0lMkZjdXN0b21lci1jb2RlcyUyRnBhZ2UmYXBwUGF0aHM9JTJGY3VzdG9tZXItY29kZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGY3VzdG9tZXItY29kZXMlMkZwYWdlLnRzeCZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0NBRCUyMFVTRVIlMjBYNjQlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDMjMxJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1Qiw4S0FBa0k7QUFDeko7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQW9IO0FBQzdJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbmZhcGFwcC8/ZjQwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdjdXN0b21lci1jb2RlcycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcc3JjXFxcXGFwcFxcXFxjdXN0b21lci1jb2Rlc1xcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcY3VzdG9tZXItY29kZXNcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcY3VzdG9tZXItY29kZXNcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9jdXN0b21lci1jb2Rlcy9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2N1c3RvbWVyLWNvZGVzL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2N1c3RvbWVyLWNvZGVzXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CAppInitializer.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CAppInitializer.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppInitializer.tsx */ \"(ssr)/./src/components/AppInitializer.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkNhaXJvJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmFyYWJpYyUyMiUyQyUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtY2Fpcm8lMjIlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJjYWlybyUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0NBRCUyMFVTRVIlMjBYNjQlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDMjMxJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNDQUQlMjBVU0VSJTIwWDY0JTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1QzIzMSU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNBcHBJbml0aWFsaXplci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvP2QzNjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXEFwcEluaXRpYWxpemVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CAppInitializer.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/customer-codes/page.tsx */ \"(ssr)/./src/app/customer-codes/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNzcmMlNUNhcHAlNUNjdXN0b21lci1jb2RlcyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmFwYXBwLz85ZGI1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQ0FEIFVTRVIgWDY0XFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXDIzMVxcXFxzcmNcXFxcYXBwXFxcXGN1c3RvbWVyLWNvZGVzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/customer-codes/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/customer-codes/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CustomerCodeManager */ \"(ssr)/./src/components/CustomerCodeManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CustomerCodesPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة أكواد العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة وتتبع أكواد العملاء والعدادات الخاصة بالنظام\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/customers\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"إدارة العملاء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"الصفحة الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-blue-50 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-blue-800 mb-4\",\n                            children: \"معلومات مهمة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"تنسيق أكواد العملاء:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• التنسيق: p + رقم المعاملة (2 رقم) + رقم العميل (4 أرقام)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• مثال: p013621 (معاملة 01، عميل 3621)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• الأكواد تتزايد تلقائياً مع كل عميل جديد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"إدارة العدادات:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• يمكن تعديل العدادات يدوياً عند الحاجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• رقم المعاملة يزيد عند بدء معاملة جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• إعادة التعيين تحذف جميع العدادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-yellow-100 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"تنبيه:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" تأكد من عمل نسخة احتياطية قبل إجراء أي تعديلات على العدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerCodesPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/customer-codes/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AppInitializer.tsx":
/*!*******************************************!*\
  !*** ./src/components/AppInitializer.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_appInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/appInitializer */ \"(ssr)/./src/utils/appInitializer.ts\");\n/* harmony import */ var _ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ErrorBoundary */ \"(ssr)/./src/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AppInitializer = ({ children })=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isInitialized: false,\n        isLoading: true,\n        error: null,\n        details: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initialize = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDE80 بدء تهيئة التطبيق...\");\n                // تهيئة التطبيق\n                const initResult = await (0,_utils_appInitializer__WEBPACK_IMPORTED_MODULE_2__.initializeApp)();\n                if (initResult.success) {\n                    console.log(\"✅ تم تهيئة التطبيق بنجاح\");\n                    setState({\n                        isInitialized: true,\n                        isLoading: false,\n                        error: null,\n                        details: initResult.details\n                    });\n                } else {\n                    console.warn(\"⚠️ تم تهيئة التطبيق مع بعض المشاكل\");\n                    setState({\n                        isInitialized: true,\n                        isLoading: false,\n                        error: initResult.message,\n                        details: initResult.details\n                    });\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDCA5 فشل حرج في تهيئة التطبيق:\", error);\n                setState({\n                    isInitialized: false,\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : \"خطأ غير معروف\",\n                    details: null\n                });\n            }\n        };\n        initialize();\n    }, []);\n    // شاشة التحميل\n    if (state.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-800 mb-2\",\n                            children: \"تهيئة التطبيق\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm\",\n                            children: \"جاري تحميل النظام الموحد...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-gray-100 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"تهيئة المتاجر والبيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    // شاشة الخطأ الحرج\n    if (!state.isInitialized && state.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-600\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-red-800 mb-2\",\n                            children: \"فشل في تهيئة التطبيق\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 text-sm mb-4\",\n                            children: state.error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                            children: \"إعادة المحاولة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, undefined);\n    }\n    // تحذير في حالة وجود مشاكل جزئية\n    const hasPartialIssues = state.error && state.isInitialized;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            hasPartialIssues && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border-b border-yellow-200 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-yellow-600 flex-shrink-0\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"تحذير:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \",\n                                    state.error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setState((prev)=>({\n                                        ...prev,\n                                        error: null\n                                    })),\n                            className: \"text-yellow-600 hover:text-yellow-800 p-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\AppInitializer.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppInitializer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppInitializer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CustomerCodeManager.tsx":
/*!************************************************!*\
  !*** ./src/components/CustomerCodeManager.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(ssr)/./src/utils/customerCodeGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CustomerCodeManager = ()=>{\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editCounters, setEditCounters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        lastTransactionNumber: 1,\n        lastCustomerNumber: 0,\n        totalCustomers: 0\n    });\n    const [searchCode, setSearchCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResult, setSearchResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadStats = ()=>{\n        try {\n            const codeStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n            setStats(codeStats);\n            // تحميل العدادات الحالية\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureGetItem)(\"customerCounters\");\n            if (result.success && result.data) {\n                setEditCounters(result.data);\n            }\n        } catch (error) {\n            console.error(\"خطأ في تحميل إحصائيات الأكواد:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStats();\n    }, []);\n    const handleGenerateNewCode = ()=>{\n        try {\n            const newCode = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            console.log(\"تم إنشاء كود جديد:\", newCode);\n            loadStats(); // إعادة تحميل الإحصائيات\n        } catch (error) {\n            console.error(\"خطأ في إنشاء كود جديد:\", error);\n            alert(\"حدث خطأ في إنشاء كود جديد\");\n        }\n    };\n    const handleIncrementTransaction = ()=>{\n        try {\n            (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.incrementTransactionNumber)();\n            loadStats();\n            alert(\"تم الانتقال للمعاملة التالية بنجاح\");\n        } catch (error) {\n            console.error(\"خطأ في تحديث رقم المعاملة:\", error);\n            alert(\"حدث خطأ في تحديث رقم المعاملة\");\n        }\n    };\n    const handleSaveCounters = ()=>{\n        try {\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureSetItem)(\"customerCounters\", editCounters);\n            if (result.success) {\n                loadStats();\n                setShowEditModal(false);\n                alert(\"تم حفظ العدادات بنجاح\");\n            } else {\n                throw new Error(result.error);\n            }\n        } catch (error) {\n            console.error(\"خطأ في حفظ العدادات:\", error);\n            alert(\"حدث خطأ في حفظ العدادات\");\n        }\n    };\n    const handleResetCounters = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع العدادات؟ هذا الإجراء لا يمكن التراجع عنه.\")) {\n            try {\n                (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.resetCustomerCounters)();\n                loadStats();\n                alert(\"تم إعادة تعيين العدادات بنجاح\");\n            } catch (error) {\n                console.error(\"خطأ في إعادة تعيين العدادات:\", error);\n                alert(\"حدث خطأ في إعادة تعيين العدادات\");\n            }\n        }\n    };\n    const handleSearchCode = ()=>{\n        if (!searchCode.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        try {\n            const customer = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(searchCode.trim());\n            setSearchResult(customer);\n        } catch (error) {\n            console.error(\"خطأ في البحث:\", error);\n            setSearchResult(null);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"إدارة أكواد العملاء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowEditModal(true),\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"تعديل العدادات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadStats,\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"تحديث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-800 mb-2\",\n                                children: \"إجمالي العملاء\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-blue-600\",\n                                children: stats.totalCustomers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-2\",\n                                children: \"الأكواد المولدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: stats.generatedCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                                children: \"الأكواد المخصصة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-yellow-600\",\n                                children: stats.customCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-purple-800 mb-2\",\n                                children: \"آخر كود\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl font-bold text-purple-600\",\n                                children: stats.lastGeneratedCode || \"لا يوجد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"رقم المعاملة الحالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastTransactionNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"آخر رقم عميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastCustomerNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"الكود التالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.nextCode\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-3\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-700 mb-3\",\n                                children: \"إنشاء كود عميل جديد للاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGenerateNewCode,\n                                className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-orange-800 mb-3\",\n                                children: \"الانتقال للمعاملة التالية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-orange-700 mb-3\",\n                                children: \"زيادة رقم المعاملة للانتقال لمعاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIncrementTransaction,\n                                className: \"w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                                children: \"معاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-blue-800 mb-3\",\n                        children: \"البحث عن كود عميل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchCode,\n                                onChange: (e)=>setSearchCode(e.target.value),\n                                placeholder: \"أدخل كود العميل (مثل: p013621)\",\n                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSearchCode,\n                                className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-800\",\n                                children: \"نتيجة البحث:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الاسم:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الهاتف:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.phone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الكود:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.clientCode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined),\n                    searchCode && !searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: \"لم يتم العثور على عميل بهذا الكود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-red-800 mb-3\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"تحذير:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            \" هذا الإجراء سيعيد تعيين جميع العدادات إلى القيم الافتراضية\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleResetCounters,\n                        className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            showEditModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"تعديل العدادات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"رقم المعاملة الحالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastTransactionNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastTransactionNumber: parseInt(e.target.value) || 1\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"آخر رقم عميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastCustomerNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastCustomerNumber: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"إجمالي العملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.totalCustomers,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    totalCustomers: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveCounters,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"حفظ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowEditModal(false),\n                                    className: \"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerCodeManager);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CustomerCodeManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.logErrorToService = (error, errorInfo)=>{\n            try {\n                // حفظ تفاصيل الخطأ في localStorage للمراجعة\n                const errorLog = {\n                    timestamp: new Date().toISOString(),\n                    message: error.message,\n                    stack: error.stack,\n                    componentStack: errorInfo.componentStack,\n                    userAgent: navigator.userAgent,\n                    url: window.location.href\n                };\n                const existingLogs = JSON.parse(localStorage.getItem(\"error-logs\") || \"[]\");\n                existingLogs.push(errorLog);\n                // الاحتفاظ بآخر 10 أخطاء فقط\n                if (existingLogs.length > 10) {\n                    existingLogs.splice(0, existingLogs.length - 10);\n                }\n                localStorage.setItem(\"error-logs\", JSON.stringify(existingLogs));\n            } catch (logError) {\n                console.error(\"❌ Failed to log error:\", logError);\n            }\n        };\n        this.handleReload = ()=>{\n            window.location.reload();\n        };\n        this.handleReset = ()=>{\n            this.setState({\n                hasError: false,\n                error: null,\n                errorInfo: null\n            });\n        };\n        this.handleClearData = ()=>{\n            if (confirm(\"هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.\")) {\n                localStorage.clear();\n                window.location.reload();\n            }\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorInfo: null\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"\\uD83D\\uDEA8 Error Boundary caught an error:\", error);\n        console.error(\"\\uD83D\\uDD0D Error Info:\", errorInfo);\n        this.setState({\n            error,\n            errorInfo\n        });\n        // تسجيل الخطأ للمراقبة\n        this.logErrorToService(error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            // يمكن عرض واجهة خطأ مخصصة\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl max-w-2xl w-full p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-10 h-10 text-red-600\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-red-800 mb-2\",\n                                    children: \"حدث خطأ في التطبيق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-red-800 mb-2\",\n                                    children: \"تفاصيل الخطأ:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-700 font-mono bg-white p-3 rounded border overflow-auto max-h-32\",\n                                    children: this.state.error?.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleReload,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"إعادة تحميل الصفحة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleReset,\n                                    className: \"w-full bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"المحاولة مرة أخرى\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleClearData,\n                                    className: \"w-full bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V7a1 1 0 00-1-1z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"حذف البيانات وإعادة البدء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer font-medium hover:text-gray-800\",\n                                        children: \"عرض التفاصيل التقنية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 p-3 bg-gray-50 rounded border font-mono text-xs overflow-auto max-h-40\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Stack Trace:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"whitespace-pre-wrap\",\n                                                children: this.state.error?.stack\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            this.state.errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Component Stack:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"whitespace-pre-wrap\",\n                                                        children: this.state.errorInfo.componentStack\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeCustomerStore: () => (/* binding */ initializeCustomerStore),\n/* harmony export */   useCustomerStore: () => (/* binding */ useCustomerStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(ssr)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/storageKeys */ \"(ssr)/./src/utils/storageKeys.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(ssr)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n\n/**\n * تحميل العملاء من النظام الموحد\n */ const loadCustomersFromStorage = async ()=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, []);\n        if (result.success) {\n            console.log(\"✅ تم تحميل العملاء من النظام الموحد:\", result.data.length);\n            return result.data;\n        } else {\n            console.error(\"❌ فشل في تحميل العملاء:\", result.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في تحميل العملاء:\", error);\n        return [];\n    }\n};\n/**\n * حفظ العملاء في النظام الموحد\n */ const saveCustomersToStorage = async (customers)=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, customers);\n        if (result.success) {\n            console.log(\"✅ تم حفظ العملاء في النظام الموحد:\", customers.length);\n            return true;\n        } else {\n            console.error(\"❌ فشل في حفظ العملاء:\", result.message);\n            return false;\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في حفظ العملاء:\", error);\n        return false;\n    }\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return `customer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        customers: [],\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = await loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = await saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        // إضافة عملاء متعددين (للاستيراد)\n        addMultipleCustomers: async (customersData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                const newCustomers = [];\n                const errors = [];\n                for (const customerData of customersData){\n                    try {\n                        let clientCode = customerData.clientCode;\n                        // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد\n                        if (!clientCode) {\n                            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                            clientCode = codeData.code;\n                        }\n                        // التحقق من عدم تكرار الكود\n                        const existingCustomer = [\n                            ...customers,\n                            ...newCustomers\n                        ].find((c)=>c.clientCode === clientCode);\n                        if (existingCustomer) {\n                            errors.push(`كود العميل ${clientCode} مستخدم بالفعل للعميل ${customerData.name}`);\n                            continue;\n                        }\n                        const newCustomer = {\n                            ...customerData,\n                            id: generateCustomerId(),\n                            clientCode\n                        };\n                        newCustomers.push(newCustomer);\n                    } catch (error) {\n                        errors.push(`خطأ في إضافة العميل ${customerData.name}: ${error instanceof Error ? error.message : \"خطأ غير معروف\"}`);\n                    }\n                }\n                if (newCustomers.length > 0) {\n                    const updatedCustomers = [\n                        ...customers,\n                        ...newCustomers\n                    ];\n                    // حفظ في التخزين\n                    const saved = await saveCustomersToStorage(updatedCustomers);\n                    if (!saved) {\n                        throw new Error(\"فشل في حفظ بيانات العملاء\");\n                    }\n                    set({\n                        customers: updatedCustomers,\n                        isLoading: false\n                    });\n                    return {\n                        success: true,\n                        imported: newCustomers.length,\n                        errors,\n                        customers: newCustomers\n                    };\n                } else {\n                    set({\n                        isLoading: false\n                    });\n                    return {\n                        success: false,\n                        error: \"لم يتم إضافة أي عميل\",\n                        imported: 0,\n                        errors\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage,\n                    imported: 0,\n                    errors: []\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = await saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = await saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: get().currentCustomer?.id === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const searchTerm = query.toLowerCase().trim();\n            // البحث الأساسي المحسن\n            const basicResults = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm) || customer.phone.includes(query) || customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm) || customer.businessName && customer.businessName.toLowerCase().includes(searchTerm) || customer.province && customer.province.toLowerCase().includes(searchTerm) || customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm) || customer.services.some((service)=>service.toLowerCase().includes(searchTerm)));\n            // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج\n            if (basicResults.length === 0 && searchTerm.length > 2) {\n                const fuzzyResults = customers.filter((customer)=>{\n                    const name = customer.name.toLowerCase();\n                    const words = searchTerm.split(\" \");\n                    return words.some((word)=>name.includes(word) && word.length > 1);\n                });\n                return fuzzyResults;\n            }\n            // ترتيب النتائج حسب الأولوية\n            return basicResults.sort((a, b)=>{\n                // أولوية للمطابقة الدقيقة في بداية الاسم\n                const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);\n                const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);\n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                // أولوية للمطابقة في كود العميل\n                const aCodeMatch = a.clientCode?.toLowerCase().includes(searchTerm);\n                const bCodeMatch = b.clientCode?.toLowerCase().includes(searchTerm);\n                if (aCodeMatch && !bCodeMatch) return -1;\n                if (!aCodeMatch && bCodeMatch) return 1;\n                // ترتيب أبجدي كافتراضي\n                return a.name.localeCompare(b.name, \"ar\");\n            });\n        },\n        // الاقتراحات السريعة للبحث\n        getQuickSuggestions: (query, limit = 5)=>{\n            const { customers } = get();\n            if (!query.trim()) return [];\n            const searchTerm = query.toLowerCase().trim();\n            const suggestions = customers.filter((customer)=>customer.name.toLowerCase().startsWith(searchTerm) || customer.phone.startsWith(query) || customer.clientCode && customer.clientCode.toLowerCase().startsWith(searchTerm)).slice(0, limit).map((customer)=>({\n                    id: customer.id,\n                    name: customer.name,\n                    phone: customer.phone,\n                    clientCode: customer.clientCode,\n                    businessName: customer.businessName,\n                    type: customer.clientCode?.toLowerCase().startsWith(searchTerm) ? \"code\" : customer.phone.startsWith(query) ? \"phone\" : \"name\",\n                    matchText: customer.clientCode?.toLowerCase().startsWith(searchTerm) ? customer.clientCode : customer.phone.startsWith(query) ? customer.phone : customer.name\n                }));\n            return suggestions;\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n// دالة تهيئة متجر العملاء\nconst initializeCustomerStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 تهيئة متجر العملاء...\");\n        const customers = await loadCustomersFromStorage();\n        useCustomerStore.setState({\n            customers\n        });\n        console.log(\"✅ تم تهيئة متجر العملاء بنجاح مع\", customers.length, \"عميل\");\n    } catch (error) {\n        console.error(\"❌ فشل في تهيئة متجر العملاء:\", error);\n        useCustomerStore.setState({\n            customers: []\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/customerStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeInvoiceStore: () => (/* binding */ initializeInvoiceStore),\n/* harmony export */   useInvoiceStore: () => (/* binding */ useInvoiceStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(ssr)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(ssr)/./src/utils/storageKeys.ts\");\n\n\n\n\n// تحميل البيانات من النظام الموحد\nconst loadInvoicesFromStorage = async ()=>{\n    if (false) {}\n    return [];\n};\n// إنشاء المتجر مع تهيئة فارغة أولاً\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: [],\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            service.models?.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            service.files?.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            service.models?.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            let productsSubtotal = 0;\n                            service.items?.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            service.materials?.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            service.samples?.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: async ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || `INV-${Date.now()}`,\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من النظام الموحد للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n                    currentStoredInvoices = result.success ? result.data : invoices;\n                    if (!result.success) {\n                        console.error(\"❌ Error reading current invoices from unified storage:\", result.message);\n                    }\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في النظام الموحد\n                try {\n                    const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, uniqueInvoices);\n                    if (saveResult.success) {\n                        console.log(\"✅ Invoice saved via unified storage. Total invoices:\", uniqueInvoices.length);\n                    } else {\n                        console.error(\"❌ Error saving invoice via unified storage:\", saveResult.message);\n                        // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                        if (false) {}\n                    }\n                } catch (error) {\n                    console.error(\"❌ Critical error saving invoice:\", error);\n                    if (false) {}\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (false) {}\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return currentInvoice?.services.filter((s)=>s.serviceType === type) || [];\n        }\n    }));\n// دالة تهيئة المتجر - يجب استدعاؤها عند بدء التطبيق\nconst initializeInvoiceStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 Initializing invoice store with unified storage...\");\n        const invoices = await loadInvoicesFromStorage();\n        useInvoiceStore.setState({\n            invoices\n        });\n        console.log(\"✅ Invoice store initialized successfully with\", invoices.length, \"invoices\");\n    } catch (error) {\n        console.error(\"❌ Failed to initialize invoice store:\", error);\n        // في حالة الفشل، استخدام البيانات الافتراضية\n        useInvoiceStore.setState({\n            invoices: []\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/invoiceStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/settingsStore.ts":
/*!************************************!*\
  !*** ./src/store/settingsStore.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeSettingsStore: () => (/* binding */ initializeSettingsStore),\n/* harmony export */   useSettingsStore: () => (/* binding */ useSettingsStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(ssr)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(ssr)/./src/utils/storageKeys.ts\");\n\n\n\n\n// Default company settings\nconst defaultSettings = {\n    companyName: \"OKA Group\",\n    companyLogo: null,\n    companyStamp: null,\n    email: \"<EMAIL>\",\n    complaintsPhone: \"0114954118\",\n    address: \"73 ش 6 اكتوبر الجراش جسر السويس\",\n    slogan: \"سعداء خدمتكم OKA Group\",\n    wishMessage: \"نتمني لكم دوام التوفيق ...\",\n    description: \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\"\n};\n// Load settings from storage on initialization\nconst loadInitialSettings = ()=>{\n    if (false) {}\n    return defaultSettings;\n};\nconst useSettingsStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        settings: loadInitialSettings(),\n        updateSettings: async (newSettings)=>{\n            const { settings } = get();\n            const updatedSettings = {\n                ...settings,\n                ...newSettings\n            };\n            set({\n                settings: updatedSettings\n            });\n            // Auto-save to unified storage\n            try {\n                const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, updatedSettings);\n                if (saveResult.success) {\n                    console.log(\"✅ Company settings saved successfully via unified storage\");\n                } else {\n                    console.error(\"❌ Error saving company settings via unified storage:\", saveResult.message);\n                }\n            } catch (error) {\n                console.error(\"❌ Critical error saving company settings:\", error);\n            }\n        },\n        uploadLogo: async (logoFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    const logoDataUrl = e.target?.result;\n                    // Update settings with new logo\n                    get().updateSettings({\n                        companyLogo: logoDataUrl\n                    });\n                    console.log(\"✅ Company logo uploaded successfully\");\n                    resolve(logoDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company logo\");\n                    reject(new Error(\"فشل في رفع شعار الشركة\"));\n                };\n                reader.readAsDataURL(logoFile);\n            });\n        },\n        uploadStamp: async (stampFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    const stampDataUrl = e.target?.result;\n                    // Update settings with new stamp\n                    get().updateSettings({\n                        companyStamp: stampDataUrl\n                    });\n                    console.log(\"✅ Company stamp uploaded successfully\");\n                    resolve(stampDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company stamp\");\n                    reject(new Error(\"فشل في رفع ختم الشركة\"));\n                };\n                reader.readAsDataURL(stampFile);\n            });\n        },\n        loadSettings: async ()=>{\n            try {\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n                if (result.success) {\n                    set({\n                        settings: {\n                            ...defaultSettings,\n                            ...result.data\n                        }\n                    });\n                    console.log(\"✅ تم تحميل إعدادات الشركة من النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في تحميل إعدادات الشركة:\", result.message);\n                    set({\n                        settings: defaultSettings\n                    });\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في تحميل الإعدادات:\", error);\n                set({\n                    settings: defaultSettings\n                });\n            }\n        },\n        saveSettings: async ()=>{\n            try {\n                const { settings } = get();\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, settings);\n                if (result.success) {\n                    console.log(\"✅ تم حفظ إعدادات الشركة في النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في حفظ إعدادات الشركة:\", result.message);\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في حفظ الإعدادات:\", error);\n            }\n        }\n    }));\n// دالة تهيئة متجر الإعدادات\nconst initializeSettingsStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 تهيئة متجر الإعدادات...\");\n        // تحميل الإعدادات من النظام الموحد\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n        if (result.success) {\n            const settings = {\n                ...defaultSettings,\n                ...result.data\n            };\n            useSettingsStore.setState({\n                settings\n            });\n            console.log(\"✅ تم تهيئة متجر الإعدادات بنجاح\");\n        } else {\n            console.error(\"❌ فشل في تحميل الإعدادات، استخدام الافتراضية\");\n            useSettingsStore.setState({\n                settings: defaultSettings\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ فشل في تهيئة متجر الإعدادات:\", error);\n        useSettingsStore.setState({\n            settings: defaultSettings\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/settingsStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/appInitializer.ts":
/*!*************************************!*\
  !*** ./src/utils/appInitializer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAppHealth: () => (/* binding */ checkAppHealth),\n/* harmony export */   initializeApp: () => (/* binding */ initializeApp),\n/* harmony export */   reinitializeApp: () => (/* binding */ reinitializeApp)\n/* harmony export */ });\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/store/invoiceStore */ \"(ssr)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/customerStore */ \"(ssr)/./src/store/customerStore.ts\");\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/settingsStore */ \"(ssr)/./src/store/settingsStore.ts\");\n/* harmony import */ var _unifiedStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unifiedStorage */ \"(ssr)/./src/utils/unifiedStorage.ts\");\n/**\n * مهيئ التطبيق - يقوم بتهيئة جميع المتاجر والأنظمة\n * يجب استدعاؤه عند بدء التطبيق\n */ \n\n\n\n/**\n * تهيئة شاملة للتطبيق\n */ const initializeApp = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDE80 بدء تهيئة التطبيق...\");\n        const initResults = {\n            unifiedStorage: false,\n            invoiceStore: false,\n            customerStore: false,\n            settingsStore: false,\n            systemHealth: null\n        };\n        // 1. فحص وتهيئة النظام الموحد\n        try {\n            console.log(\"\\uD83D\\uDD27 فحص النظام الموحد...\");\n            const healthCheck = await _unifiedStorage__WEBPACK_IMPORTED_MODULE_3__.UnifiedStorageManager.checkUnifiedSystemHealth();\n            initResults.systemHealth = healthCheck;\n            if (!healthCheck.isHealthy) {\n                console.log(\"⚠️ النظام يحتاج لإصلاحات، بدء عملية الدمج...\");\n                const mergeResult = await _unifiedStorage__WEBPACK_IMPORTED_MODULE_3__.UnifiedStorageManager.mergeDuplicateKeys();\n                console.log(\"\\uD83D\\uDD04 نتيجة الدمج:\", mergeResult);\n            }\n            initResults.unifiedStorage = true;\n            console.log(\"✅ النظام الموحد جاهز\");\n        } catch (error) {\n            console.error(\"❌ فشل في تهيئة النظام الموحد:\", error);\n            initResults.unifiedStorage = false;\n        }\n        // 2. تهيئة متجر الفواتير\n        try {\n            console.log(\"\\uD83D\\uDCCB تهيئة متجر الفواتير...\");\n            await (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_0__.initializeInvoiceStore)();\n            initResults.invoiceStore = true;\n            console.log(\"✅ متجر الفواتير جاهز\");\n        } catch (error) {\n            console.error(\"❌ فشل في تهيئة متجر الفواتير:\", error);\n            initResults.invoiceStore = false;\n        }\n        // 3. تهيئة متجر العملاء\n        try {\n            console.log(\"\\uD83D\\uDC65 تهيئة متجر العملاء...\");\n            await (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_1__.initializeCustomerStore)();\n            initResults.customerStore = true;\n            console.log(\"✅ متجر العملاء جاهز\");\n        } catch (error) {\n            console.error(\"❌ فشل في تهيئة متجر العملاء:\", error);\n            initResults.customerStore = false;\n        }\n        // 4. تهيئة متجر الإعدادات\n        try {\n            console.log(\"⚙️ تهيئة متجر الإعدادات...\");\n            await (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.initializeSettingsStore)();\n            initResults.settingsStore = true;\n            console.log(\"✅ متجر الإعدادات جاهز\");\n        } catch (error) {\n            console.error(\"❌ فشل في تهيئة متجر الإعدادات:\", error);\n            initResults.settingsStore = false;\n        }\n        // 5. تقييم النتائج\n        const allSuccess = initResults.unifiedStorage && initResults.invoiceStore && initResults.customerStore && initResults.settingsStore;\n        if (allSuccess) {\n            console.log(\"\\uD83C\\uDF89 تم تهيئة التطبيق بنجاح!\");\n            return {\n                success: true,\n                message: \"تم تهيئة التطبيق بنجاح\",\n                details: initResults\n            };\n        } else {\n            console.warn(\"⚠️ تم تهيئة التطبيق مع بعض المشاكل\");\n            return {\n                success: false,\n                message: \"تم تهيئة التطبيق مع بعض المشاكل\",\n                details: initResults\n            };\n        }\n    } catch (error) {\n        console.error(\"\\uD83D\\uDCA5 فشل حرج في تهيئة التطبيق:\", error);\n        return {\n            success: false,\n            message: `فشل في تهيئة التطبيق: ${error.message}`,\n            details: {\n                error: error\n            }\n        };\n    }\n};\n/**\n * فحص حالة التطبيق\n */ const checkAppHealth = async ()=>{\n    try {\n        // فحص النظام الموحد\n        const systemHealth = await _unifiedStorage__WEBPACK_IMPORTED_MODULE_3__.UnifiedStorageManager.checkUnifiedSystemHealth();\n        // فحص المتاجر\n        const storeChecks = {\n            invoiceStore:  false && 0\n        };\n        const allIssues = [\n            ...systemHealth.issues\n        ];\n        const allRecommendations = [\n            ...systemHealth.recommendations\n        ];\n        if (!storeChecks.invoiceStore) {\n            allIssues.push(\"متجر الفواتير غير مهيأ\");\n            allRecommendations.push(\"تشغيل تهيئة التطبيق\");\n        }\n        return {\n            isHealthy: systemHealth.isHealthy && storeChecks.invoiceStore,\n            issues: allIssues,\n            recommendations: allRecommendations,\n            details: {\n                systemHealth,\n                storeChecks\n            }\n        };\n    } catch (error) {\n        return {\n            isHealthy: false,\n            issues: [\n                `خطأ في فحص التطبيق: ${error.message}`\n            ],\n            recommendations: [\n                \"إعادة تشغيل التطبيق\"\n            ],\n            details: {\n                error\n            }\n        };\n    }\n};\n/**\n * إعادة تهيئة التطبيق (في حالة المشاكل)\n */ const reinitializeApp = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 إعادة تهيئة التطبيق...\");\n        // مسح الحالة الحالية\n        if (false) {}\n        // تهيئة جديدة\n        const result = await initializeApp();\n        if (result.success) {\n            console.log(\"✅ تمت إعادة التهيئة بنجاح\");\n            return {\n                success: true,\n                message: \"تمت إعادة تهيئة التطبيق بنجاح\"\n            };\n        } else {\n            console.error(\"❌ فشلت إعادة التهيئة\");\n            return {\n                success: false,\n                message: \"فشلت إعادة تهيئة التطبيق\"\n            };\n        }\n    } catch (error) {\n        console.error(\"\\uD83D\\uDCA5 خطأ حرج في إعادة التهيئة:\", error);\n        return {\n            success: false,\n            message: `فشل في إعادة التهيئة: ${error.message}`\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/appInitializer.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/customerCodeGenerator.ts":
/*!********************************************!*\
  !*** ./src/utils/customerCodeGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCodeDuplication: () => (/* binding */ checkCodeDuplication),\n/* harmony export */   checkCustomerDataDuplication: () => (/* binding */ checkCustomerDataDuplication),\n/* harmony export */   findCustomerByCode: () => (/* binding */ findCustomerByCode),\n/* harmony export */   generateCustomerCode: () => (/* binding */ generateCustomerCode),\n/* harmony export */   getCustomerCodeStats: () => (/* binding */ getCustomerCodeStats),\n/* harmony export */   getCustomerCodeSuggestions: () => (/* binding */ getCustomerCodeSuggestions),\n/* harmony export */   incrementTransactionNumber: () => (/* binding */ incrementTransactionNumber),\n/* harmony export */   isCustomerCodeUnique: () => (/* binding */ isCustomerCodeUnique),\n/* harmony export */   parseCustomerCode: () => (/* binding */ parseCustomerCode),\n/* harmony export */   resetCustomerCounters: () => (/* binding */ resetCustomerCounters),\n/* harmony export */   validateCustomerCode: () => (/* binding */ validateCustomerCode),\n/* harmony export */   validateCustomerUniqueness: () => (/* binding */ validateCustomerUniqueness)\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/**\n * نظام إنشاء وإدارة أكواد العملاء\n * التنسيق: p013621 (p01 = رقم المعاملة، 3621 = الرقم المسلسل للعميل)\n */ \n// إعدادات نظام الأكواد\nconst CODE_CONFIG = {\n    PREFIX: \"p\",\n    TRANSACTION_DIGITS: 2,\n    CUSTOMER_DIGITS: 4,\n    STARTING_TRANSACTION: 1,\n    STARTING_CUSTOMER: 3621\n};\n/**\n * تحميل العدادات من التخزين الآمن\n */ const loadCounters = ()=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customerCounters\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    // القيم الافتراضية\n    return {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n};\n/**\n * حفظ العدادات في التخزين الآمن\n */ const saveCounters = (counters)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customerCounters\", counters);\n    if (!result.success) {\n        console.error(\"فشل في حفظ عدادات العملاء:\", result.error);\n        throw new Error(\"فشل في حفظ عدادات العملاء\");\n    }\n};\n/**\n * تنسيق رقم بعدد أرقام محدد\n */ const formatNumber = (num, digits)=>{\n    return num.toString().padStart(digits, \"0\");\n};\n/**\n * إنشاء كود عميل جديد\n */ const generateCustomerCode = ()=>{\n    const counters = loadCounters();\n    // زيادة العدادات\n    counters.lastCustomerNumber += 1;\n    counters.totalCustomers += 1;\n    // إنشاء الكود\n    const transactionPart = formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS);\n    const customerPart = formatNumber(counters.lastCustomerNumber, CODE_CONFIG.CUSTOMER_DIGITS);\n    const code = `${CODE_CONFIG.PREFIX}${transactionPart}${customerPart}`;\n    // حفظ العدادات المحدثة\n    saveCounters(counters);\n    return {\n        code,\n        transactionNumber: counters.lastTransactionNumber,\n        customerNumber: counters.lastCustomerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: false\n    };\n};\n/**\n * التحقق من صحة كود العميل\n */ const validateCustomerCode = (code)=>{\n    if (!code || typeof code !== \"string\") {\n        return {\n            isValid: false,\n            error: \"الكود مطلوب\"\n        };\n    }\n    // التحقق من البادئة\n    if (!code.startsWith(CODE_CONFIG.PREFIX)) {\n        return {\n            isValid: false,\n            error: `يجب أن يبدأ الكود بـ \"${CODE_CONFIG.PREFIX}\"`\n        };\n    }\n    // التحقق من الطول\n    const expectedLength = 1 + CODE_CONFIG.TRANSACTION_DIGITS + CODE_CONFIG.CUSTOMER_DIGITS;\n    if (code.length !== expectedLength) {\n        return {\n            isValid: false,\n            error: `يجب أن يكون طول الكود ${expectedLength} أحرف`\n        };\n    }\n    // التحقق من الأرقام\n    const numberPart = code.slice(1);\n    if (!/^\\d+$/.test(numberPart)) {\n        return {\n            isValid: false,\n            error: \"يجب أن يحتوي الكود على أرقام فقط بعد البادئة\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * تحليل كود العميل\n */ const parseCustomerCode = (code)=>{\n    const validation = validateCustomerCode(code);\n    if (!validation.isValid) {\n        return null;\n    }\n    const numberPart = code.slice(1);\n    const transactionNumber = parseInt(numberPart.slice(0, CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    const customerNumber = parseInt(numberPart.slice(CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    return {\n        code,\n        transactionNumber,\n        customerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: true\n    };\n};\n/**\n * التحقق من تفرد كود العميل\n */ const isCustomerCodeUnique = (code, excludeClientId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return true; // إذا لم توجد عملاء، فالكود فريد\n    }\n    const customers = result.data;\n    return !customers.some((customer)=>customer.clientCode === code && customer.id !== excludeClientId);\n};\n/**\n * الحصول على إحصائيات أكواد العملاء\n */ const getCustomerCodeStats = ()=>{\n    const counters = loadCounters();\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    const customers = result.success && result.data ? result.data : [];\n    const customCodes = customers.filter((c)=>c.clientCode && !c.clientCode.match(/^p\\d+$/));\n    const generatedCodes = customers.filter((c)=>c.clientCode && c.clientCode.match(/^p\\d+$/));\n    return {\n        totalCustomers: customers.length,\n        generatedCodes: generatedCodes.length,\n        customCodes: customCodes.length,\n        nextCode: `${CODE_CONFIG.PREFIX}${formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS)}${formatNumber(counters.lastCustomerNumber + 1, CODE_CONFIG.CUSTOMER_DIGITS)}`,\n        lastTransactionNumber: counters.lastTransactionNumber,\n        lastCustomerNumber: counters.lastCustomerNumber\n    };\n};\n/**\n * إعادة تعيين العدادات (للاستخدام في حالات الطوارئ)\n */ const resetCustomerCounters = ()=>{\n    const defaultCounters = {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n    saveCounters(defaultCounters);\n};\n/**\n * تحديث رقم المعاملة (للانتقال لمعاملة جديدة)\n */ const incrementTransactionNumber = ()=>{\n    const counters = loadCounters();\n    counters.lastTransactionNumber += 1;\n    saveCounters(counters);\n};\n/**\n * البحث عن عميل بالكود\n */ const findCustomerByCode = (code)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return null;\n    }\n    return result.data.find((customer)=>customer.clientCode === code) || null;\n};\n/**\n * اقتراحات أكواد العملاء (للبحث التلقائي)\n */ const getCustomerCodeSuggestions = (query, limit = 10)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return [];\n    }\n    const customers = result.data;\n    const lowerQuery = query.toLowerCase();\n    return customers.filter((customer)=>customer.clientCode?.toLowerCase().includes(lowerQuery) || customer.name.toLowerCase().includes(lowerQuery) || customer.phone.includes(query)).slice(0, limit);\n};\n/**\n * التحقق من تكرار كود العميل\n */ const checkCodeDuplication = (code, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const existingCustomer = customers.find((customer)=>customer.clientCode === code && customer.id !== excludeId);\n    return {\n        isDuplicate: !!existingCustomer,\n        existingCustomer\n    };\n};\n/**\n * التحقق من تكرار بيانات العميل (الاسم + الهاتف)\n */ const checkCustomerDataDuplication = (name, phone, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const trimmedName = name.trim().toLowerCase();\n    const trimmedPhone = phone.trim();\n    // البحث عن تطابق في الاسم والهاتف\n    const nameMatch = customers.find((customer)=>customer.name.trim().toLowerCase() === trimmedName && customer.id !== excludeId);\n    const phoneMatch = customers.find((customer)=>customer.phone.trim() === trimmedPhone && customer.id !== excludeId);\n    if (nameMatch && phoneMatch && nameMatch.id === phoneMatch.id) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"both\",\n            existingCustomer: nameMatch\n        };\n    } else if (nameMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"name\",\n            existingCustomer: nameMatch\n        };\n    } else if (phoneMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"phone\",\n            existingCustomer: phoneMatch\n        };\n    }\n    return {\n        isDuplicate: false\n    };\n};\n/**\n * التحقق الشامل من التكرار قبل إضافة/تحديث العميل\n */ const validateCustomerUniqueness = (customerData, excludeId)=>{\n    const errors = [];\n    const warnings = [];\n    const suggestions = [];\n    // التحقق من كود العميل إذا تم تمريره\n    if (customerData.clientCode) {\n        const codeValidation = validateCustomerCode(customerData.clientCode);\n        if (!codeValidation.isValid) {\n            errors.push(codeValidation.error);\n        } else {\n            const codeDuplication = checkCodeDuplication(customerData.clientCode, excludeId);\n            if (codeDuplication.isDuplicate) {\n                errors.push(`كود العميل \"${customerData.clientCode}\" مستخدم بالفعل للعميل: ${codeDuplication.existingCustomer?.name}`);\n                suggestions.push(\"يمكنك ترك حقل الكود فارغاً لإنشاء كود جديد تلقائياً\");\n            }\n        }\n    }\n    // التحقق من تكرار بيانات العميل\n    const dataDuplication = checkCustomerDataDuplication(customerData.name, customerData.phone, excludeId);\n    if (dataDuplication.isDuplicate) {\n        const existing = dataDuplication.existingCustomer;\n        switch(dataDuplication.duplicateType){\n            case \"both\":\n                errors.push(`عميل بنفس الاسم ورقم الهاتف موجود بالفعل: ${existing.name} (${existing.clientCode})`);\n                break;\n            case \"name\":\n                warnings.push(`يوجد عميل بنفس الاسم: ${existing.name} (${existing.clientCode}) - هاتف: ${existing.phone}`);\n                suggestions.push(\"تأكد من أن هذا عميل مختلف أم نفس العميل\");\n                break;\n            case \"phone\":\n                warnings.push(`يوجد عميل بنفس رقم الهاتف: ${existing.name} (${existing.clientCode})`);\n                suggestions.push(\"تأكد من صحة رقم الهاتف أو أن هذا عميل مختلف\");\n                break;\n        }\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings,\n        suggestions: suggestions.length > 0 ? suggestions : undefined\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/customerCodeGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: () => (/* binding */ STORAGE_CONFIG),\n/* harmony export */   checkStorageHealth: () => (/* binding */ checkStorageHealth),\n/* harmony export */   createBackup: () => (/* binding */ createBackup),\n/* harmony export */   restoreFromBackup: () => (/* binding */ restoreFromBackup),\n/* harmony export */   secureGetItem: () => (/* binding */ secureGetItem),\n/* harmony export */   secureSetItem: () => (/* binding */ secureSetItem)\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = `backup-${Date.now()}`;\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: `تم استرداد البيانات بنجاح من النسخة: ${backup.timestamp}`\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/secureStorage.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/storageKeys.ts":
/*!**********************************!*\
  !*** ./src/utils/storageKeys.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CATEGORIES: () => (/* binding */ STORAGE_CATEGORIES),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   getKeyCategory: () => (/* binding */ getKeyCategory),\n/* harmony export */   getStorageKeysStats: () => (/* binding */ getStorageKeysStats),\n/* harmony export */   validateStorageKey: () => (/* binding */ validateStorageKey)\n/* harmony export */ });\n/**\n * مدير مفاتيح التخزين الموحد\n * يضمن استخدام مفاتيح ثابتة ومنظمة لجميع عمليات التخزين\n */ /**\n * مفاتيح التخزين الأساسية\n * جميع عمليات التخزين يجب أن تستخدم هذه المفاتيح فقط\n */ const STORAGE_KEYS = {\n    // البيانات الأساسية\n    INVOICES: \"invoices\",\n    CUSTOMERS: \"customers\",\n    CUSTOMER_COUNTERS: \"customerCounters\",\n    // إعدادات الشركة (مفتاح موحد)\n    COMPANY_SETTINGS: \"companySettings\",\n    // النسخ الاحتياطية\n    BACKUP_PREFIX: \"backup-\",\n    // البيانات المؤقتة\n    TEMP_PREFIX: \"temp-\",\n    DRAFT_PREFIX: \"draft-\",\n    // مفاتيح قديمة للدمج (سيتم حذفها)\n    LEGACY_COMPANY_SETTINGS: \"company-settings\",\n    LEGACY_COMPANY_INFO: \"companyInfo\",\n    // مفاتيح للتنظيف\n    CLEANUP_KEYS: [\n        \"test-invoices\",\n        \"demo-data\",\n        \"backup-invoices\",\n        \"temp-invoice\",\n        \"draft-invoice\"\n    ]\n};\n/**\n * فئات المفاتيح لتنظيم أفضل\n */ const STORAGE_CATEGORIES = {\n    CORE_DATA: [\n        STORAGE_KEYS.INVOICES,\n        STORAGE_KEYS.CUSTOMERS,\n        STORAGE_KEYS.CUSTOMER_COUNTERS,\n        STORAGE_KEYS.COMPANY_SETTINGS\n    ],\n    BACKUPS: (key)=>key.startsWith(STORAGE_KEYS.BACKUP_PREFIX),\n    TEMPORARY: (key)=>key.startsWith(STORAGE_KEYS.TEMP_PREFIX) || key.startsWith(STORAGE_KEYS.DRAFT_PREFIX),\n    LEGACY: [\n        STORAGE_KEYS.LEGACY_COMPANY_SETTINGS,\n        STORAGE_KEYS.LEGACY_COMPANY_INFO\n    ]\n};\n/**\n * التحقق من صحة مفتاح التخزين\n */ const validateStorageKey = (key)=>{\n    const allKeys = Object.values(STORAGE_KEYS).flat();\n    return allKeys.includes(key);\n};\n/**\n * الحصول على فئة المفتاح\n */ const getKeyCategory = (key)=>{\n    if (STORAGE_CATEGORIES.CORE_DATA.includes(key)) {\n        return \"CORE_DATA\";\n    }\n    if (STORAGE_CATEGORIES.BACKUPS(key)) {\n        return \"BACKUPS\";\n    }\n    if (STORAGE_CATEGORIES.TEMPORARY(key)) {\n        return \"TEMPORARY\";\n    }\n    if (STORAGE_CATEGORIES.LEGACY.includes(key)) {\n        return \"LEGACY\";\n    }\n    return \"UNKNOWN\";\n};\n/**\n * إحصائيات المفاتيح\n */ const getStorageKeysStats = ()=>{\n    const allStorageKeys = Object.keys(localStorage);\n    return {\n        total: allStorageKeys.length,\n        coreData: allStorageKeys.filter((key)=>STORAGE_CATEGORIES.CORE_DATA.includes(key)).length,\n        backups: allStorageKeys.filter(STORAGE_CATEGORIES.BACKUPS).length,\n        temporary: allStorageKeys.filter(STORAGE_CATEGORIES.TEMPORARY).length,\n        legacy: allStorageKeys.filter((key)=>STORAGE_CATEGORIES.LEGACY.includes(key)).length,\n        unknown: allStorageKeys.filter((key)=>getKeyCategory(key) === \"UNKNOWN\").length\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/storageKeys.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/unifiedStorage.ts":
/*!*************************************!*\
  !*** ./src/utils/unifiedStorage.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedStorageManager: () => (/* binding */ UnifiedStorageManager)\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _storageKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./storageKeys */ \"(ssr)/./src/utils/storageKeys.ts\");\n/**\n * مدير التخزين الموحد\n * يضمن استخدام secureStorage في جميع العمليات مع حماية شاملة\n */ \n\n/**\n * مدير التخزين الموحد\n * يوفر واجهة موحدة لجميع عمليات التخزين\n */ class UnifiedStorageManager {\n    /**\n   * حفظ آمن مع التحقق من المفتاح\n   */ static async setItem(key, data) {\n        const timestamp = new Date().toISOString();\n        try {\n            // التحقق من صحة المفتاح\n            const category = (0,_storageKeys__WEBPACK_IMPORTED_MODULE_1__.getKeyCategory)(key);\n            if (category === \"UNKNOWN\" && !key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)) {\n                console.warn(`⚠️ Using unknown storage key: ${key}`);\n            }\n            // استخدام النظام الآمن\n            const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(key, data);\n            // تسجيل العملية\n            const dataSize = JSON.stringify(data).length;\n            console.log(`🔒 Unified Storage: Saved ${key}`, {\n                success: result.success,\n                dataSize: `${Math.round(dataSize / 1024 * 100) / 100} KB`,\n                category,\n                timestamp\n            });\n            return {\n                success: result.success,\n                data,\n                message: result.message,\n                timestamp\n            };\n        } catch (error) {\n            const errorMessage = `فشل في حفظ ${key}: ${error.message}`;\n            console.error(\"❌ Unified Storage Save Error:\", error);\n            return {\n                success: false,\n                message: errorMessage,\n                timestamp\n            };\n        }\n    }\n    /**\n   * قراءة آمنة مع التحقق من المفتاح\n   */ static async getItem(key, defaultValue) {\n        const timestamp = new Date().toISOString();\n        try {\n            // التحقق من صحة المفتاح\n            const category = (0,_storageKeys__WEBPACK_IMPORTED_MODULE_1__.getKeyCategory)(key);\n            if (category === \"UNKNOWN\" && !key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)) {\n                console.warn(`⚠️ Reading from unknown storage key: ${key}`);\n            }\n            // استخدام النظام الآمن\n            const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(key, defaultValue);\n            // تسجيل العملية\n            console.log(`🔓 Unified Storage: Loaded ${key}`, {\n                success: result.success,\n                hasData: result.data !== defaultValue,\n                category,\n                timestamp\n            });\n            return {\n                success: result.success,\n                data: result.data,\n                message: result.message || \"تم تحميل البيانات بنجاح\",\n                timestamp\n            };\n        } catch (error) {\n            const errorMessage = `فشل في قراءة ${key}: ${error.message}`;\n            console.error(\"❌ Unified Storage Read Error:\", error);\n            return {\n                success: false,\n                data: defaultValue,\n                message: errorMessage,\n                timestamp\n            };\n        }\n    }\n    /**\n   * دمج البيانات المكررة من المفاتيح القديمة\n   */ static async mergeDuplicateKeys() {\n        const stats = {\n            success: true,\n            merged: [],\n            errors: [],\n            totalSize: 0,\n            duplicatesRemoved: 0\n        };\n        try {\n            console.log(\"\\uD83D\\uDD04 بدء عملية دمج البيانات المكررة...\");\n            // دمج إعدادات الشركة\n            await this.mergeCompanySettings(stats);\n            // تنظيف المفاتيح القديمة\n            await this.cleanupLegacyKeys(stats);\n            // إنشاء نسخة احتياطية بعد الدمج\n            if (stats.merged.length > 0) {\n                const backupResult = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n                if (backupResult.success) {\n                    console.log(\"✅ تم إنشاء نسخة احتياطية بعد الدمج\");\n                }\n            }\n            console.log(\"✅ اكتملت عملية دمج البيانات:\", stats);\n            return stats;\n        } catch (error) {\n            stats.success = false;\n            stats.errors.push(`خطأ عام في الدمج: ${error.message}`);\n            console.error(\"❌ فشل في دمج البيانات:\", error);\n            return stats;\n        }\n    }\n    /**\n   * دمج إعدادات الشركة من المفاتيح المختلفة\n   */ static async mergeCompanySettings(stats) {\n        try {\n            // قراءة الإعدادات الحالية\n            const currentResult = await this.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.COMPANY_SETTINGS, {});\n            let mergedSettings = {\n                ...currentResult.data\n            };\n            // دمج من company-settings\n            const legacySettings = localStorage.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.LEGACY_COMPANY_SETTINGS);\n            if (legacySettings) {\n                try {\n                    const parsed = JSON.parse(legacySettings);\n                    mergedSettings = {\n                        ...mergedSettings,\n                        ...parsed\n                    };\n                    stats.totalSize += legacySettings.length;\n                    console.log(\"\\uD83D\\uDCE6 دمج بيانات من company-settings\");\n                } catch (error) {\n                    stats.errors.push(`خطأ في تحليل company-settings: ${error.message}`);\n                }\n            }\n            // دمج من companyInfo\n            const companyInfo = localStorage.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.LEGACY_COMPANY_INFO);\n            if (companyInfo) {\n                try {\n                    const parsed = JSON.parse(companyInfo);\n                    mergedSettings = {\n                        ...mergedSettings,\n                        ...parsed\n                    };\n                    stats.totalSize += companyInfo.length;\n                    console.log(\"\\uD83D\\uDCE6 دمج بيانات من companyInfo\");\n                } catch (error) {\n                    stats.errors.push(`خطأ في تحليل companyInfo: ${error.message}`);\n                }\n            }\n            // حفظ الإعدادات المدموجة إذا كان هناك تغيير\n            if (legacySettings || companyInfo) {\n                const saveResult = await this.setItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.COMPANY_SETTINGS, mergedSettings);\n                if (saveResult.success) {\n                    stats.merged.push(\"companySettings\");\n                    console.log(\"✅ تم دمج إعدادات الشركة بنجاح\");\n                } else {\n                    stats.errors.push(\"فشل في حفظ إعدادات الشركة المدموجة\");\n                }\n            }\n        } catch (error) {\n            stats.errors.push(`خطأ في دمج إعدادات الشركة: ${error.message}`);\n        }\n    }\n    /**\n   * تنظيف المفاتيح القديمة بعد الدمج\n   */ static async cleanupLegacyKeys(stats) {\n        try {\n            // حذف المفاتيح القديمة\n            const legacyKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.LEGACY;\n            for (const key of legacyKeys){\n                if (localStorage.getItem(key)) {\n                    localStorage.removeItem(key);\n                    stats.duplicatesRemoved++;\n                    console.log(`🗑️ تم حذف المفتاح القديم: ${key}`);\n                }\n            }\n            // تنظيف مفاتيح أخرى غير مرغوب فيها\n            const cleanupKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CLEANUP_KEYS;\n            for (const key of cleanupKeys){\n                if (localStorage.getItem(key)) {\n                    localStorage.removeItem(key);\n                    stats.duplicatesRemoved++;\n                    console.log(`🗑️ تم تنظيف المفتاح: ${key}`);\n                }\n            }\n        } catch (error) {\n            stats.errors.push(`خطأ في تنظيف المفاتيح: ${error.message}`);\n        }\n    }\n    /**\n   * فحص حالة النظام الموحد\n   */ static async checkUnifiedSystemHealth() {\n        try {\n            // فحص النظام الأساسي\n            const storageHealth = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.checkStorageHealth)();\n            // فحص المفاتيح\n            const allKeys = Object.keys(localStorage);\n            const coreDataKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.CORE_DATA;\n            const missingCoreKeys = coreDataKeys.filter((key)=>!allKeys.includes(key));\n            const legacyKeys = allKeys.filter((key)=>_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.LEGACY.includes(key));\n            const issues = [\n                ...storageHealth.issues\n            ];\n            const recommendations = [\n                ...storageHealth.recommendations\n            ];\n            // فحص المفاتيح المفقودة\n            if (missingCoreKeys.length > 0) {\n                issues.push(`مفاتيح أساسية مفقودة: ${missingCoreKeys.join(\", \")}`);\n                recommendations.push(\"تهيئة البيانات الأساسية\");\n            }\n            // فحص المفاتيح القديمة\n            if (legacyKeys.length > 0) {\n                issues.push(`مفاتيح قديمة موجودة: ${legacyKeys.join(\", \")}`);\n                recommendations.push(\"تشغيل عملية دمج البيانات\");\n            }\n            return {\n                isHealthy: storageHealth.isHealthy && issues.length === storageHealth.issues.length,\n                issues,\n                recommendations,\n                keyStats: {\n                    total: allKeys.length,\n                    coreData: coreDataKeys.filter((key)=>allKeys.includes(key)).length,\n                    legacy: legacyKeys.length,\n                    backups: allKeys.filter((key)=>key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)).length\n                },\n                storageHealth\n            };\n        } catch (error) {\n            return {\n                isHealthy: false,\n                issues: [\n                    `خطأ في فحص النظام: ${error.message}`\n                ],\n                recommendations: [\n                    \"إعادة تشغيل التطبيق\"\n                ],\n                keyStats: {},\n                storageHealth: {}\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/unifiedStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"af7364717194\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzkyZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZjczNjQ3MTcxOTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/customer-codes/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/customer-codes/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\231\src\app\customer-codes\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_AppInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AppInitializer */ \"(rsc)/./src/components/AppInitializer.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الفواتير - Invoice Management System\",\n    description: \"نظام شامل لإدارة فواتير الخدمات مع دعم كامل للغة العربية\",\n    keywords: \"فواتير, إدارة, خدمات, استشارات, باترون, تصنيع\",\n    authors: [\n        {\n            name: \"Invoice Management System\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_3___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_3___default().className)} font-arabic`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppInitializer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AppInitializer.tsx":
/*!*******************************************!*\
  !*** ./src/components/AppInitializer.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\231\src\components\AppInitializer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcustomer-codes%2Fpage&page=%2Fcustomer-codes%2Fpage&appPaths=%2Fcustomer-codes%2Fpage&pagePath=private-next-app-dir%2Fcustomer-codes%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();