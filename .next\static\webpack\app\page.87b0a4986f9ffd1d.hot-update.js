"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/pdfGenerator.ts":
/*!***********************************!*\
  !*** ./src/utils/pdfGenerator.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateInvoicePDF: function() { return /* binding */ generateInvoicePDF; },\n/* harmony export */   generateInvoicePDFFromHTML: function() { return /* binding */ generateInvoicePDFFromHTML; }\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// دالة تنسيق العملة المحسنة\nconst formatCurrency = (amount)=>{\n    const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n    if (isNaN(numericAmount)) {\n        return \"0.00 جنيه\";\n    }\n    return new Intl.NumberFormat(\"ar-EG\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(numericAmount) + \" جنيه\";\n};\n// دالة الحصول على اسم الخدمة\nconst getServiceName = (serviceType)=>{\n    const serviceNames = {\n        consultation: \"استشارة\",\n        pattern: \"باترون\",\n        pattern_printing: \"طباعة باترون\",\n        manufacturing: \"تصنيع\",\n        samples: \"عينات\",\n        shipping: \"شحن\",\n        marketing: \"تسويق\",\n        photography: \"تصوير\",\n        products: \"منتجات\",\n        raw_purchases: \"مشتريات خام\"\n    };\n    return serviceNames[serviceType] || serviceType;\n};\n// دالة الحصول على اسم طريقة الدفع\nconst getPaymentMethodName = (method)=>{\n    const methods = {\n        cash: \"نقدي\",\n        bank_transfer: \"تحويل بنكي\",\n        credit_card: \"بطاقة ائتمان\",\n        installments: \"أقساط\"\n    };\n    return methods[method] || method;\n};\n// دالة الحصول على اسم وسيلة التواصل\nconst getContactMethodName = (method)=>{\n    const methods = {\n        phone: \"هاتف\",\n        whatsapp: \"واتساب\",\n        email: \"بريد إلكتروني\",\n        facebook: \"فيسبوك\",\n        instagram: \"انستغرام\",\n        direct: \"مباشر\",\n        meta: \"ميتا\",\n        meeting: \"لقاء مباشر\"\n    };\n    return methods[method] || method;\n};\n// دالة تنسيق التاريخ\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"ar-EG\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n};\n// دالة حساب تفاصيل الخدمة\nconst calculateServiceDetails = (service)=>{\n    let serviceTotal = 0;\n    let serviceDetails = \"\";\n    let quantity = \"\";\n    let unitPrice = 0;\n    switch(service.serviceType){\n        case \"consultation\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"\".concat(service.topic || \"استشارة\", \" - \").concat(service.hours || 0, \" ساعة\");\n            quantity = \"\".concat(service.hours || 0, \" ساعة\");\n            unitPrice = service.cost || 0;\n            break;\n        case \"pattern\":\n            var _service_models, _service_models1, _service_models2, _service_models3;\n            serviceTotal = ((_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.reduce((sum, model)=>sum + (model.finalAmount || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.length) || 0, \" نموذج باترون\");\n            quantity = \"\".concat(((_service_models2 = service.models) === null || _service_models2 === void 0 ? void 0 : _service_models2.length) || 0, \" نموذج\");\n            unitPrice = serviceTotal / (((_service_models3 = service.models) === null || _service_models3 === void 0 ? void 0 : _service_models3.length) || 1);\n            break;\n        case \"pattern_printing\":\n            var _service_files, _service_files1, _service_files2;\n            serviceTotal = ((_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n            serviceDetails = \"طباعة \".concat(((_service_files1 = service.files) === null || _service_files1 === void 0 ? void 0 : _service_files1.length) || 0, \" ملف\");\n            quantity = \"\".concat(((_service_files2 = service.files) === null || _service_files2 === void 0 ? void 0 : _service_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n            unitPrice = serviceTotal;\n            break;\n        case \"shipping\":\n            serviceTotal = service.total || 0;\n            serviceDetails = \"شحن \".concat(service.item || \"منتج\");\n            quantity = \"\".concat(service.quantity || 1);\n            unitPrice = service.unitPrice || 0;\n            break;\n        case \"products\":\n            var _service_items, _service_items1, _service_items2, _service_items3;\n            serviceTotal = ((_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_items1 = service.items) === null || _service_items1 === void 0 ? void 0 : _service_items1.length) || 0, \" منتج\");\n            quantity = \"\".concat(((_service_items2 = service.items) === null || _service_items2 === void 0 ? void 0 : _service_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_items3 = service.items) === null || _service_items3 === void 0 ? void 0 : _service_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n            break;\n        case \"manufacturing\":\n            var _service_models4, _service_models5, _service_models6, _service_models7;\n            serviceTotal = ((_service_models4 = service.models) === null || _service_models4 === void 0 ? void 0 : _service_models4.reduce((sum, model)=>sum + ((model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0)) * (model.quantity || 1), 0)) || 0;\n            serviceDetails = \"تصنيع \".concat(((_service_models5 = service.models) === null || _service_models5 === void 0 ? void 0 : _service_models5.length) || 0, \" نموذج\");\n            quantity = \"\".concat(((_service_models6 = service.models) === null || _service_models6 === void 0 ? void 0 : _service_models6.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_models7 = service.models) === null || _service_models7 === void 0 ? void 0 : _service_models7.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 1);\n            break;\n        case \"photography\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"تصوير \".concat(service.location === \"indoor\" ? \"داخلي\" : \"خارجي\").concat(service.withModel ? \" مع موديل\" : \"\");\n            quantity = \"1 جلسة\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"marketing\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = service.description || \"خدمات تسويقية\";\n            quantity = service.duration || \"1 شهر\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"samples\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"إنتاج عينات\";\n            quantity = \"1\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"raw_purchases\":\n            var _service_materials, _service_materials1, _service_materials2, _service_materials3;\n            serviceTotal = ((_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.reduce((sum, material)=>sum + (material.total || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_materials1 = service.materials) === null || _service_materials1 === void 0 ? void 0 : _service_materials1.length) || 0, \" مادة خام\");\n            quantity = \"\".concat(((_service_materials2 = service.materials) === null || _service_materials2 === void 0 ? void 0 : _service_materials2.reduce((sum, material)=>sum + (material.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_materials3 = service.materials) === null || _service_materials3 === void 0 ? void 0 : _service_materials3.reduce((sum, material)=>sum + (material.quantity || 0), 0)) || 1);\n            break;\n        default:\n            serviceTotal = service.cost || service.total || 0;\n            serviceDetails = getServiceName(service.serviceType);\n            quantity = \"1\";\n            unitPrice = serviceTotal;\n    }\n    return {\n        total: serviceTotal,\n        details: serviceDetails,\n        quantity,\n        unitPrice\n    };\n};\n// الدالة الرئيسية لتصدير PDF المحسنة\nconst generateInvoicePDF = async (invoice)=>{\n    try {\n        // البحث عن عنصر المعاينة\n        const element = document.getElementById(\"invoice-content\");\n        if (!element) {\n            throw new Error(\"عنصر المعاينة غير موجود\");\n        }\n        // التحقق من صحة العنصر\n        const rect = element.getBoundingClientRect();\n        if (rect.width === 0 || rect.height === 0) {\n            throw new Error(\"العنصر غير مرئي أو لا يحتوي على محتوى\");\n        }\n        await generatePDFFromPreview(element, invoice);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء ملف PDF: \" + error.message);\n    }\n};\n// دالة تصدير PDF محسنة من المعاينة\nconst generatePDFFromPreview = async (element, invoice)=>{\n    let canvas = null;\n    try {\n        // تحسين العنصر للتصدير\n        const originalStyle = element.style.cssText;\n        element.style.cssText = \"\\n      \".concat(originalStyle, \"\\n      width: 794px !important;\\n      max-width: 794px !important;\\n      min-height: 1123px !important;\\n      background: white !important;\\n      font-family: Arial, sans-serif !important;\\n      direction: rtl !important;\\n      overflow: visible !important;\\n    \");\n        // انتظار تحديث التخطيط\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // إنشاء canvas محسن للجودة العالية\n        canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, {\n            scale: 3,\n            useCORS: true,\n            allowTaint: false,\n            backgroundColor: \"#ffffff\",\n            logging: false,\n            width: 794,\n            height: element.scrollHeight,\n            scrollX: 0,\n            scrollY: 0,\n            windowWidth: 794,\n            windowHeight: element.scrollHeight,\n            foreignObjectRendering: false,\n            imageTimeout: 20000,\n            removeContainer: true,\n            onclone: (clonedDoc)=>{\n                // تحسين النصوص العربية والتخطيط في النسخة المستنسخة\n                const clonedElement = clonedDoc.getElementById(\"invoice-content\");\n                if (clonedElement) {\n                    // تحسين الخطوط والاتجاه\n                    clonedElement.style.fontFamily = 'Arial, \"Helvetica Neue\", Helvetica, sans-serif';\n                    clonedElement.style.direction = \"rtl\";\n                    clonedElement.style.textAlign = \"right\";\n                    clonedElement.style.fontSize = \"14px\";\n                    clonedElement.style.lineHeight = \"1.6\";\n                    clonedElement.style.color = \"#000000\";\n                    // تحسين جودة الصور\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.imageRendering = \"high-quality\";\n                        img.style.imageRendering = \"-webkit-optimize-contrast\";\n                    });\n                    // تحسين الجداول\n                    const tables = clonedElement.querySelectorAll(\"table\");\n                    tables.forEach((table)=>{\n                        table.style.borderCollapse = \"collapse\";\n                        table.style.width = \"100%\";\n                        table.style.fontSize = \"13px\";\n                    });\n                    // تحسين الخلفيات المتدرجة للطباعة\n                    const gradients = clonedElement.querySelectorAll('[class*=\"gradient\"]');\n                    gradients.forEach((el)=>{\n                        const computedStyle = window.getComputedStyle(el);\n                        if (computedStyle.backgroundImage.includes(\"gradient\")) {\n                            el.style.backgroundImage = \"none\";\n                            el.style.backgroundColor = \"#f8fafc\";\n                        }\n                    });\n                    // تحسين الحدود والظلال\n                    const shadows = clonedElement.querySelectorAll('[class*=\"shadow\"]');\n                    shadows.forEach((el)=>{\n                        el.style.boxShadow = \"0 1px 3px rgba(0,0,0,0.1)\";\n                    });\n                }\n            }\n        });\n        // استعادة الستايل الأصلي\n        element.style.cssText = originalStyle;\n        if (!canvas) {\n            throw new Error(\"فشل في إنشاء Canvas\");\n        }\n        // إنشاء PDF محسن\n        const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__.jsPDF({\n            orientation: \"portrait\",\n            unit: \"pt\",\n            format: \"a4\",\n            compress: true,\n            precision: 2\n        });\n        // تحويل Canvas إلى صورة عالية الجودة\n        const imgData = canvas.toDataURL(\"image/jpeg\", 0.95);\n        // حساب الأبعاد بدقة للصفحات المتعددة\n        const pdfWidth = 595.28; // عرض A4 بالنقاط\n        const pdfHeight = 841.89; // ارتفاع A4 بالنقاط\n        const margin = 20; // هامش مقلل لاستغلال أفضل للمساحة\n        const maxWidth = pdfWidth - margin * 2;\n        const maxHeight = pdfHeight - margin * 2;\n        // حساب أبعاد الصورة النهائية\n        const imgWidth = maxWidth;\n        const imgHeight = canvas.height * maxWidth / canvas.width;\n        // معالجة الصفحات المتعددة\n        if (imgHeight <= maxHeight) {\n            // الصورة تتسع في صفحة واحدة\n            const y = margin + (maxHeight - imgHeight) / 2; // توسيط عمودي\n            pdf.addImage(imgData, \"JPEG\", margin, y, imgWidth, imgHeight, undefined, \"FAST\");\n        } else {\n            // الصورة تحتاج لعدة صفحات - تقسيم ذكي\n            let heightLeft = imgHeight;\n            let position = 0;\n            let pageNumber = 1;\n            // الصفحة الأولى\n            pdf.addImage(imgData, \"JPEG\", margin, margin, imgWidth, imgHeight, undefined, \"FAST\");\n            heightLeft -= maxHeight;\n            // الصفحات الإضافية\n            while(heightLeft >= 0){\n                position = heightLeft - imgHeight;\n                pdf.addPage();\n                pdf.addImage(imgData, \"JPEG\", margin, position + margin, imgWidth, imgHeight, undefined, \"FAST\");\n                heightLeft -= maxHeight;\n                pageNumber++;\n            }\n            // إضافة أرقام الصفحات وتاريخ الطباعة\n            const totalPages = pdf.internal.getNumberOfPages();\n            const printDate = new Date().toLocaleDateString(\"ar-EG\");\n            for(let i = 1; i <= totalPages; i++){\n                pdf.setPage(i);\n                // رقم الصفحة\n                pdf.setFontSize(10);\n                pdf.setTextColor(100, 100, 100);\n                pdf.text(\"صفحة \".concat(i, \" من \").concat(totalPages), pdfWidth - margin, pdfHeight - 10, {\n                    align: \"right\"\n                });\n                // تاريخ الطباعة في الصفحة الأولى فقط\n                if (i === 1) {\n                    pdf.text(\"تاريخ الطباعة: \".concat(printDate), margin, pdfHeight - 10, {\n                        align: \"left\"\n                    });\n                }\n            }\n        }\n        // إنشاء اسم الملف محسن\n        const date = new Date().toISOString().split(\"T\")[0];\n        const sanitizedClientName = invoice.client.name.replace(/[^\\u0600-\\u06FF\\w\\s-]/g, \"\") // السماح بالأحرف العربية\n        .trim().substring(0, 20); // تحديد الطول\n        const filename = \"فاتورة_\".concat(sanitizedClientName, \"_\").concat(invoice.id, \"_\").concat(date, \".pdf\");\n        // حفظ الملف\n        pdf.save(filename);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء PDF: \" + error.message);\n    } finally{\n        // تنظيف الذاكرة\n        if (canvas) {\n            canvas.remove();\n            canvas = null;\n        }\n    }\n};\n// دالة إنشاء PDF من HTML مباشرة (بديل محسن)\nconst generateInvoicePDFFromHTML = async (invoice)=>{\n    try {\n        // الحصول على إعدادات الشركة\n        const settings = JSON.parse(localStorage.getItem(\"company-settings\") || \"{}\");\n        // إنشاء HTML محسن للطباعة\n        const htmlContent = createPrintableInvoiceHTML(invoice, settings);\n        // إنشاء نافذة جديدة للطباعة\n        const printWindow = window.open(\"\", \"_blank\", \"width=794,height=1123\");\n        if (!printWindow) {\n            throw new Error(\"فشل في فتح نافذة الطباعة\");\n        }\n        // كتابة المحتوى\n        printWindow.document.write(htmlContent);\n        printWindow.document.close();\n        // انتظار تحميل المحتوى\n        await new Promise((resolve)=>{\n            printWindow.onload = resolve;\n            setTimeout(resolve, 1000); // fallback\n        });\n        // البحث عن العنصر في النافذة الجديدة\n        const element = printWindow.document.querySelector(\".invoice-container\");\n        if (!element) {\n            printWindow.close();\n            throw new Error(\"عنصر الفاتورة غير موجود\");\n        }\n        // إنشاء canvas من النافذة الجديدة\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, {\n            scale: 2,\n            useCORS: true,\n            allowTaint: false,\n            backgroundColor: \"#ffffff\",\n            logging: false,\n            width: 794,\n            height: 1123,\n            foreignObjectRendering: false,\n            imageTimeout: 15000\n        });\n        // إغلاق النافذة\n        printWindow.close();\n        if (!canvas) {\n            throw new Error(\"فشل في إنشاء Canvas\");\n        }\n        // إنشاء PDF\n        const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__.jsPDF({\n            orientation: \"portrait\",\n            unit: \"pt\",\n            format: \"a4\",\n            compress: true\n        });\n        const imgData = canvas.toDataURL(\"image/jpeg\", 0.95);\n        pdf.addImage(imgData, \"JPEG\", 0, 0, 595.28, 841.89, undefined, \"FAST\");\n        // حفظ الملف\n        const date = new Date().toISOString().split(\"T\")[0];\n        const sanitizedClientName = invoice.client.name.replace(/[^\\u0600-\\u06FF\\w\\s-]/g, \"\").trim().substring(0, 20);\n        const filename = \"فاتورة_\".concat(sanitizedClientName, \"_\").concat(invoice.id, \"_\").concat(date, \".pdf\");\n        pdf.save(filename);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء PDF: \" + error.message);\n    }\n};\n// دالة إنشاء HTML محسن للطباعة\nconst createPrintableInvoiceHTML = (invoice, settings)=>{\n    const logoSrc = settings.companyLogo || \"\";\n    const stampSrc = settings.companyStamp || \"\";\n    return '\\n<!DOCTYPE html>\\n<html dir=\"rtl\" lang=\"ar\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>فاتورة '.concat(invoice.client.name, '</title>\\n    <style>\\n        * {\\n            margin: 0;\\n            padding: 0;\\n            box-sizing: border-box;\\n        }\\n\\n        body {\\n            font-family: \\'Arial\\', sans-serif;\\n            font-size: 12pt;\\n            line-height: 1.4;\\n            color: #333;\\n            background: white;\\n            direction: rtl;\\n        }\\n\\n        .invoice-container {\\n            width: 794px;\\n            min-height: 1123px;\\n            margin: 0 auto;\\n            padding: 40px;\\n            background: white;\\n            position: relative;\\n        }\\n\\n        .header {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding-bottom: 20px;\\n            border-bottom: 2px solid #2196f3;\\n        }\\n\\n        .invoice-info {\\n            flex: 1;\\n            max-width: 350px;\\n        }\\n\\n        .invoice-info h2 {\\n            font-size: 24pt;\\n            color: #2196f3;\\n            margin-bottom: 15px;\\n            font-weight: bold;\\n        }\\n\\n        .info-box {\\n            background: transparent;\\n            padding: 15px;\\n            border: none;\\n        }\\n\\n        .info-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 8px;\\n            padding: 4px 0;\\n        }\\n\\n        .info-label {\\n            font-weight: bold;\\n            color: #495057;\\n            min-width: 120px;\\n        }\\n\\n        .info-value {\\n            color: #212529;\\n            font-weight: 500;\\n        }\\n\\n        .logo-section {\\n            flex-shrink: 0;\\n            text-align: center;\\n            margin-left: 30px;\\n        }\\n\\n        .logo-section img {\\n            width: 180px;\\n            height: 180px;\\n            object-fit: contain;\\n            border-radius: 8px;\\n        }\\n\\n        .company-description {\\n            margin-top: 10px;\\n            font-size: 11pt;\\n            color: #666;\\n            max-width: 180px;\\n            line-height: 1.3;\\n        }\\n\\n        .services-section {\\n            margin-bottom: 30px;\\n        }\\n\\n        .section-title {\\n            font-size: 16pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            margin-bottom: 15px;\\n            padding-bottom: 8px;\\n            border-bottom: 1px solid #e9ecef;\\n        }\\n\\n        .services-table {\\n            width: 100%;\\n            border-collapse: collapse;\\n            margin-bottom: 20px;\\n            font-size: 11pt;\\n        }\\n\\n        .services-table th {\\n            background: #2196f3;\\n            color: white;\\n            padding: 12px 8px;\\n            text-align: center;\\n            font-weight: bold;\\n            border: 1px solid #1976d2;\\n        }\\n\\n        .services-table td {\\n            padding: 10px 8px;\\n            text-align: center;\\n            border: 1px solid #ddd;\\n            vertical-align: middle;\\n        }\\n\\n        .services-table tbody tr:nth-child(even) {\\n            background: #f8f9fa;\\n        }\\n\\n        .services-table tbody tr:hover {\\n            background: #e3f2fd;\\n        }\\n\\n        .service-type {\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .service-details {\\n            text-align: right;\\n            padding-right: 12px;\\n        }\\n\\n        .service-price,\\n        .service-total {\\n            font-weight: bold;\\n            color: #4caf50;\\n        }\\n\\n        .total-section {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background: #f8f9fa;\\n            border-radius: 8px;\\n            border: 1px solid #e9ecef;\\n        }\\n\\n        .total-details {\\n            flex: 1;\\n        }\\n\\n        .total-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 10px;\\n            padding: 8px 0;\\n            font-size: 13pt;\\n        }\\n\\n        .total-label {\\n            font-weight: bold;\\n            color: #495057;\\n        }\\n\\n        .total-value {\\n            font-weight: bold;\\n            color: #212529;\\n        }\\n\\n        .grand-total {\\n            color: #2196f3;\\n            font-size: 16pt;\\n        }\\n\\n        .paid-amount {\\n            color: #4caf50;\\n        }\\n\\n        .remaining-amount {\\n            color: #ff9800;\\n        }\\n\\n        .remaining-zero {\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp {\\n            flex-shrink: 0;\\n            margin-left: 30px;\\n            text-align: center;\\n            background: #e3f2fd;\\n            padding: 15px;\\n            border-radius: 50%;\\n            width: 120px;\\n            height: 120px;\\n            display: flex;\\n            flex-direction: column;\\n            justify-content: center;\\n            align-items: center;\\n            border: 3px solid #2196f3;\\n            transform: rotate(-5deg);\\n        }\\n\\n        .stamp-title {\\n            font-size: 14pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-status {\\n            font-size: 10pt;\\n            color: #1976d2;\\n            font-weight: bold;\\n        }\\n\\n        .footer {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-top: 40px;\\n            padding: 20px;\\n            background: white;\\n            border-top: 1px solid #e9ecef;\\n            position: relative;\\n            direction: rtl;\\n        }\\n\\n        .footer-left {\\n            flex: 1;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .footer-right {\\n            flex: 1;\\n            text-align: right;\\n            direction: rtl;\\n            position: relative;\\n        }\\n\\n        .contact-info,\\n        .headquarters-info {\\n            font-size: 11pt;\\n            line-height: 1.5;\\n            color: #495057;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info div,\\n        .headquarters-info div {\\n            margin-bottom: 5px;\\n        }\\n\\n        .company-stamp {\\n            position: relative;\\n            margin-top: 15px;\\n            transform: none;\\n            opacity: 1;\\n            background: white;\\n            border: 1px solid #ddd;\\n            border-radius: 8px;\\n            padding: 10px;\\n            width: 160px;\\n            height: auto;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n\\n        .company-stamp img {\\n            width: 140px;\\n            height: auto;\\n            max-height: 100px;\\n            object-fit: contain;\\n            border-radius: 4px;\\n        }\\n\\n        .stamp-content {\\n            background: rgba(33, 150, 243, 0.1);\\n            border: 2px solid #2196f3;\\n            border-radius: 50%;\\n            width: 100px;\\n            height: 100px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .stamp-title {\\n            font-size: 14pt;\\n            margin-bottom: 2px;\\n        }\\n\\n        .stamp-subtitle {\\n            font-size: 10pt;\\n        }\\n\\n        .thank-you {\\n            text-align: center;\\n            margin-top: 30px;\\n            font-size: 14pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            padding: 15px;\\n            background: #f8f9fa;\\n            border-radius: 8px;\\n            border: 1px solid #e9ecef;\\n        }\\n\\n        @media print {\\n            body {\\n                -webkit-print-color-adjust: exact;\\n                print-color-adjust: exact;\\n            }\\n\\n            .invoice-container {\\n                width: 100%;\\n                max-width: none;\\n                margin: 0;\\n                padding: 20px;\\n            }\\n\\n            .header,\\n            .services-section,\\n            .total-section,\\n            .footer {\\n                page-break-inside: avoid;\\n                break-inside: avoid;\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\"invoice-container\">\\n        <!-- Header Section -->\\n        <div class=\"header\">\\n            <div class=\"invoice-info\">\\n                <h2>فاتورة خدمات</h2>\\n                <div class=\"info-box\">\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الفاتورة:</span>\\n                        <span class=\"info-value\">').concat(invoice.id, '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">التاريخ:</span>\\n                        <span class=\"info-value\">').concat(formatDate(invoice.createdAt), '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">اسم العميل:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.name, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.businessName ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">الاسم التجاري:</span>\\n                        <span class=\"info-value\">'.concat(invoice.client.businessName, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الهاتف:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.phone, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.province ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">المحافظة:</span>\\n                        <span class=\"info-value\">'.concat(invoice.client.province, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">كود العميل:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.clientCode || \"تلقائي\", '</span>\\n                    </div>\\n                    <div class=\"info-row\" style=\"border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;\">\\n                        <span class=\"info-label\">وسيلة التواصل:</span>\\n                        <span class=\"info-value\">').concat(getContactMethodName(invoice.client.contactMethod), '</span>\\n                    </div>\\n                </div>\\n            </div>\\n\\n            <div class=\"logo-section\">\\n                ').concat(logoSrc ? '<img src=\"'.concat(logoSrc, '\" alt=\"شعار الشركة\">') : '\\n                <div style=\"width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9; border-radius: 8px;\">\\n                    <span style=\"color: #999; font-size: 14pt;\">شعار الشركة</span>\\n                </div>\\n                ', '\\n                <div class=\"company-description\">\\n                    ').concat(settings.description || \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\", '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <!-- Services Section -->\\n        <div class=\"services-section\">\\n            <h3 class=\"section-title\">تفاصيل الخدمات والمنتجات</h3>\\n\\n            <table class=\"services-table\">\\n                <thead>\\n                    <tr>\\n                        <th style=\"width: 50px;\">م</th>\\n                        <th style=\"width: 120px;\">نوع الخدمة</th>\\n                        <th style=\"width: 200px;\">التفاصيل</th>\\n                        <th style=\"width: 100px;\">الكمية/المقاس</th>\\n                        <th style=\"width: 120px;\">السعر</th>\\n                        <th style=\"width: 120px;\">المجموع</th>\\n                    </tr>\\n                </thead>\\n                <tbody>\\n                    ').concat(invoice.services.map((service, index)=>{\n        const serviceData = calculateServiceDetails(service);\n        return \"\\n                        <tr>\\n                            <td>\".concat(index + 1, '</td>\\n                            <td class=\"service-type\">').concat(getServiceName(service.serviceType), '</td>\\n                            <td class=\"service-details\">').concat(serviceData.details, \"</td>\\n                            <td>\").concat(serviceData.quantity, '</td>\\n                            <td class=\"service-price\">').concat(formatCurrency(serviceData.unitPrice), '</td>\\n                            <td class=\"service-total\">').concat(formatCurrency(serviceData.total), \"</td>\\n                        </tr>\\n                      \");\n    }).join(\"\"), '\\n                </tbody>\\n            </table>\\n        </div>\\n\\n        <!-- Total Summary -->\\n        <div class=\"total-section\">\\n            <div class=\"total-details\">\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المجموع الكلي:</span>\\n                    <span class=\"total-value grand-total\">').concat(formatCurrency(invoice.total), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المدفوع:</span>\\n                    <span class=\"total-value paid-amount\">').concat(formatCurrency(invoice.paidAmount || 0), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المتبقي:</span>\\n                    <span class=\"total-value remaining-amount ').concat((invoice.remainingAmount || 0) > 0 ? \"remaining-positive\" : \"remaining-zero\", '\">\\n                        ').concat(formatCurrency(invoice.remainingAmount || 0), '\\n                    </span>\\n                </div>\\n                <div class=\"total-row\" style=\"border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;\">\\n                    <span class=\"total-label\">طريقة الدفع:</span>\\n                    <span class=\"total-value\">').concat(getPaymentMethodName(invoice.paymentMethod), '</span>\\n                </div>\\n            </div>\\n\\n            <div class=\"payment-stamp\">\\n                <div class=\"stamp-title\">OKA Group</div>\\n                <div class=\"stamp-status\">').concat(invoice.paymentStatus || \"غير محدد\", '</div>\\n            </div>\\n        </div>\\n\\n        <!-- Footer -->\\n        <div class=\"footer\">\\n            <div class=\"footer-left\">\\n                <div class=\"contact-info\">\\n                    <div><strong>معلومات التواصل:</strong></div>\\n                    <div>\\uD83D\\uDCE7 البريد الإلكتروني: <EMAIL></div>\\n                    <div>\\uD83D\\uDCDE للشكاوى والاقتراحات: 0114954118</div>\\n                </div>\\n            </div>\\n\\n            <div class=\"footer-right\">\\n                <div class=\"headquarters-info\">\\n                    <div><strong>المقر الإداري:</strong></div>\\n                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>\\n                </div>\\n\\n                <div class=\"company-stamp\">\\n                    ').concat(stampSrc ? '\\n                    <img src=\"'.concat(stampSrc, '\" alt=\"ختم الشركة\">\\n                    ') : '\\n                    <div class=\"stamp-content\">\\n                        <div class=\"stamp-title\">OKA</div>\\n                        <div class=\"stamp-subtitle\">GROUP</div>\\n                    </div>\\n                    ', '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div class=\"thank-you\">\\n            شكراً لتعاملكم معنا\\n        </div>\\n    </div>\\n</body>\\n</html>\\n  ');\n};\n// تصدير الدالة الرئيسية\n/* harmony default export */ __webpack_exports__[\"default\"] = (generateInvoicePDF);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/pdfGenerator.ts\n"));

/***/ })

});