"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInvoiceStore: function() { return /* binding */ useInvoiceStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// تحميل البيانات من التخزين الآمن\nconst loadInvoicesFromStorage = ()=>{\n    if (true) {\n        const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"invoices\", []);\n        if (!result.success) {\n            console.error(\"❌ Error loading invoices:\", result.message);\n        }\n        return result.data;\n    }\n    return [];\n};\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: loadInvoicesFromStorage(),\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            var _service_models;\n                            (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            var _service_files;\n                            (_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            var _service_models1;\n                            (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            var _service_items;\n                            let productsSubtotal = 0;\n                            (_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            var _service_materials;\n                            (_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            var _service_samples;\n                            (_service_samples = service.samples) === null || _service_samples === void 0 ? void 0 : _service_samples.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: async ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || \"INV-\".concat(Date.now()),\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من النظام الموحد للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n                    currentStoredInvoices = result.success ? result.data : invoices;\n                    if (!result.success) {\n                        console.error(\"❌ Error reading current invoices from unified storage:\", result.message);\n                    }\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في النظام الموحد\n                try {\n                    const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, uniqueInvoices);\n                    if (saveResult.success) {\n                        console.log(\"✅ Invoice saved via unified storage. Total invoices:\", uniqueInvoices.length);\n                    } else {\n                        console.error(\"❌ Error saving invoice via unified storage:\", saveResult.message);\n                        // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                        if (true) {\n                            alert(\"تحذير: فشل في حفظ الفاتورة. \" + saveResult.message);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"❌ Critical error saving invoice:\", error);\n                    if (true) {\n                        alert(\"خطأ حرج: فشل في حفظ الفاتورة. يرجى المحاولة مرة أخرى.\");\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (true) {\n                    alert(\"تحذير: فشل في حذف الفاتورة. \" + saveResult.message);\n                }\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return (currentInvoice === null || currentInvoice === void 0 ? void 0 : currentInvoice.services.filter((s)=>s.serviceType === type)) || [];\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/invoiceStore.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/storageKeys.ts":
/*!**********************************!*\
  !*** ./src/utils/storageKeys.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CATEGORIES: function() { return /* binding */ STORAGE_CATEGORIES; },\n/* harmony export */   STORAGE_KEYS: function() { return /* binding */ STORAGE_KEYS; },\n/* harmony export */   getKeyCategory: function() { return /* binding */ getKeyCategory; },\n/* harmony export */   getStorageKeysStats: function() { return /* binding */ getStorageKeysStats; },\n/* harmony export */   validateStorageKey: function() { return /* binding */ validateStorageKey; }\n/* harmony export */ });\n/**\n * مدير مفاتيح التخزين الموحد\n * يضمن استخدام مفاتيح ثابتة ومنظمة لجميع عمليات التخزين\n */ /**\n * مفاتيح التخزين الأساسية\n * جميع عمليات التخزين يجب أن تستخدم هذه المفاتيح فقط\n */ const STORAGE_KEYS = {\n    // البيانات الأساسية\n    INVOICES: \"invoices\",\n    CUSTOMERS: \"customers\",\n    CUSTOMER_COUNTERS: \"customerCounters\",\n    // إعدادات الشركة (مفتاح موحد)\n    COMPANY_SETTINGS: \"companySettings\",\n    // النسخ الاحتياطية\n    BACKUP_PREFIX: \"backup-\",\n    // البيانات المؤقتة\n    TEMP_PREFIX: \"temp-\",\n    DRAFT_PREFIX: \"draft-\",\n    // مفاتيح قديمة للدمج (سيتم حذفها)\n    LEGACY_COMPANY_SETTINGS: \"company-settings\",\n    LEGACY_COMPANY_INFO: \"companyInfo\",\n    // مفاتيح للتنظيف\n    CLEANUP_KEYS: [\n        \"test-invoices\",\n        \"demo-data\",\n        \"backup-invoices\",\n        \"temp-invoice\",\n        \"draft-invoice\"\n    ]\n};\n/**\n * فئات المفاتيح لتنظيم أفضل\n */ const STORAGE_CATEGORIES = {\n    CORE_DATA: [\n        STORAGE_KEYS.INVOICES,\n        STORAGE_KEYS.CUSTOMERS,\n        STORAGE_KEYS.CUSTOMER_COUNTERS,\n        STORAGE_KEYS.COMPANY_SETTINGS\n    ],\n    BACKUPS: (key)=>key.startsWith(STORAGE_KEYS.BACKUP_PREFIX),\n    TEMPORARY: (key)=>key.startsWith(STORAGE_KEYS.TEMP_PREFIX) || key.startsWith(STORAGE_KEYS.DRAFT_PREFIX),\n    LEGACY: [\n        STORAGE_KEYS.LEGACY_COMPANY_SETTINGS,\n        STORAGE_KEYS.LEGACY_COMPANY_INFO\n    ]\n};\n/**\n * التحقق من صحة مفتاح التخزين\n */ const validateStorageKey = (key)=>{\n    const allKeys = Object.values(STORAGE_KEYS).flat();\n    return allKeys.includes(key);\n};\n/**\n * الحصول على فئة المفتاح\n */ const getKeyCategory = (key)=>{\n    if (STORAGE_CATEGORIES.CORE_DATA.includes(key)) {\n        return \"CORE_DATA\";\n    }\n    if (STORAGE_CATEGORIES.BACKUPS(key)) {\n        return \"BACKUPS\";\n    }\n    if (STORAGE_CATEGORIES.TEMPORARY(key)) {\n        return \"TEMPORARY\";\n    }\n    if (STORAGE_CATEGORIES.LEGACY.includes(key)) {\n        return \"LEGACY\";\n    }\n    return \"UNKNOWN\";\n};\n/**\n * إحصائيات المفاتيح\n */ const getStorageKeysStats = ()=>{\n    const allStorageKeys = Object.keys(localStorage);\n    return {\n        total: allStorageKeys.length,\n        coreData: allStorageKeys.filter((key)=>STORAGE_CATEGORIES.CORE_DATA.includes(key)).length,\n        backups: allStorageKeys.filter(STORAGE_CATEGORIES.BACKUPS).length,\n        temporary: allStorageKeys.filter(STORAGE_CATEGORIES.TEMPORARY).length,\n        legacy: allStorageKeys.filter((key)=>STORAGE_CATEGORIES.LEGACY.includes(key)).length,\n        unknown: allStorageKeys.filter((key)=>getKeyCategory(key) === \"UNKNOWN\").length\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/storageKeys.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/unifiedStorage.ts":
/*!*************************************!*\
  !*** ./src/utils/unifiedStorage.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedStorageManager: function() { return /* binding */ UnifiedStorageManager; }\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _storageKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n/**\n * مدير التخزين الموحد\n * يضمن استخدام secureStorage في جميع العمليات مع حماية شاملة\n */ \n\n/**\n * مدير التخزين الموحد\n * يوفر واجهة موحدة لجميع عمليات التخزين\n */ class UnifiedStorageManager {\n    /**\n   * حفظ آمن مع التحقق من المفتاح\n   */ static async setItem(key, data) {\n        const timestamp = new Date().toISOString();\n        try {\n            // التحقق من صحة المفتاح\n            const category = (0,_storageKeys__WEBPACK_IMPORTED_MODULE_1__.getKeyCategory)(key);\n            if (category === \"UNKNOWN\" && !key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)) {\n                console.warn(\"⚠️ Using unknown storage key: \".concat(key));\n            }\n            // استخدام النظام الآمن\n            const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(key, data);\n            // تسجيل العملية\n            const dataSize = JSON.stringify(data).length;\n            console.log(\"\\uD83D\\uDD12 Unified Storage: Saved \".concat(key), {\n                success: result.success,\n                dataSize: \"\".concat(Math.round(dataSize / 1024 * 100) / 100, \" KB\"),\n                category,\n                timestamp\n            });\n            return {\n                success: result.success,\n                data,\n                message: result.message,\n                timestamp\n            };\n        } catch (error) {\n            const errorMessage = \"فشل في حفظ \".concat(key, \": \").concat(error.message);\n            console.error(\"❌ Unified Storage Save Error:\", error);\n            return {\n                success: false,\n                message: errorMessage,\n                timestamp\n            };\n        }\n    }\n    /**\n   * قراءة آمنة مع التحقق من المفتاح\n   */ static async getItem(key, defaultValue) {\n        const timestamp = new Date().toISOString();\n        try {\n            // التحقق من صحة المفتاح\n            const category = (0,_storageKeys__WEBPACK_IMPORTED_MODULE_1__.getKeyCategory)(key);\n            if (category === \"UNKNOWN\" && !key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)) {\n                console.warn(\"⚠️ Reading from unknown storage key: \".concat(key));\n            }\n            // استخدام النظام الآمن\n            const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(key, defaultValue);\n            // تسجيل العملية\n            console.log(\"\\uD83D\\uDD13 Unified Storage: Loaded \".concat(key), {\n                success: result.success,\n                hasData: result.data !== defaultValue,\n                category,\n                timestamp\n            });\n            return {\n                success: result.success,\n                data: result.data,\n                message: result.message || \"تم تحميل البيانات بنجاح\",\n                timestamp\n            };\n        } catch (error) {\n            const errorMessage = \"فشل في قراءة \".concat(key, \": \").concat(error.message);\n            console.error(\"❌ Unified Storage Read Error:\", error);\n            return {\n                success: false,\n                data: defaultValue,\n                message: errorMessage,\n                timestamp\n            };\n        }\n    }\n    /**\n   * دمج البيانات المكررة من المفاتيح القديمة\n   */ static async mergeDuplicateKeys() {\n        const stats = {\n            success: true,\n            merged: [],\n            errors: [],\n            totalSize: 0,\n            duplicatesRemoved: 0\n        };\n        try {\n            console.log(\"\\uD83D\\uDD04 بدء عملية دمج البيانات المكررة...\");\n            // دمج إعدادات الشركة\n            await this.mergeCompanySettings(stats);\n            // تنظيف المفاتيح القديمة\n            await this.cleanupLegacyKeys(stats);\n            // إنشاء نسخة احتياطية بعد الدمج\n            if (stats.merged.length > 0) {\n                const backupResult = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n                if (backupResult.success) {\n                    console.log(\"✅ تم إنشاء نسخة احتياطية بعد الدمج\");\n                }\n            }\n            console.log(\"✅ اكتملت عملية دمج البيانات:\", stats);\n            return stats;\n        } catch (error) {\n            stats.success = false;\n            stats.errors.push(\"خطأ عام في الدمج: \".concat(error.message));\n            console.error(\"❌ فشل في دمج البيانات:\", error);\n            return stats;\n        }\n    }\n    /**\n   * دمج إعدادات الشركة من المفاتيح المختلفة\n   */ static async mergeCompanySettings(stats) {\n        try {\n            // قراءة الإعدادات الحالية\n            const currentResult = await this.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.COMPANY_SETTINGS, {});\n            let mergedSettings = {\n                ...currentResult.data\n            };\n            // دمج من company-settings\n            const legacySettings = localStorage.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.LEGACY_COMPANY_SETTINGS);\n            if (legacySettings) {\n                try {\n                    const parsed = JSON.parse(legacySettings);\n                    mergedSettings = {\n                        ...mergedSettings,\n                        ...parsed\n                    };\n                    stats.totalSize += legacySettings.length;\n                    console.log(\"\\uD83D\\uDCE6 دمج بيانات من company-settings\");\n                } catch (error) {\n                    stats.errors.push(\"خطأ في تحليل company-settings: \".concat(error.message));\n                }\n            }\n            // دمج من companyInfo\n            const companyInfo = localStorage.getItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.LEGACY_COMPANY_INFO);\n            if (companyInfo) {\n                try {\n                    const parsed = JSON.parse(companyInfo);\n                    mergedSettings = {\n                        ...mergedSettings,\n                        ...parsed\n                    };\n                    stats.totalSize += companyInfo.length;\n                    console.log(\"\\uD83D\\uDCE6 دمج بيانات من companyInfo\");\n                } catch (error) {\n                    stats.errors.push(\"خطأ في تحليل companyInfo: \".concat(error.message));\n                }\n            }\n            // حفظ الإعدادات المدموجة إذا كان هناك تغيير\n            if (legacySettings || companyInfo) {\n                const saveResult = await this.setItem(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.COMPANY_SETTINGS, mergedSettings);\n                if (saveResult.success) {\n                    stats.merged.push(\"companySettings\");\n                    console.log(\"✅ تم دمج إعدادات الشركة بنجاح\");\n                } else {\n                    stats.errors.push(\"فشل في حفظ إعدادات الشركة المدموجة\");\n                }\n            }\n        } catch (error) {\n            stats.errors.push(\"خطأ في دمج إعدادات الشركة: \".concat(error.message));\n        }\n    }\n    /**\n   * تنظيف المفاتيح القديمة بعد الدمج\n   */ static async cleanupLegacyKeys(stats) {\n        try {\n            // حذف المفاتيح القديمة\n            const legacyKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.LEGACY;\n            for (const key of legacyKeys){\n                if (localStorage.getItem(key)) {\n                    localStorage.removeItem(key);\n                    stats.duplicatesRemoved++;\n                    console.log(\"\\uD83D\\uDDD1️ تم حذف المفتاح القديم: \".concat(key));\n                }\n            }\n            // تنظيف مفاتيح أخرى غير مرغوب فيها\n            const cleanupKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CLEANUP_KEYS;\n            for (const key of cleanupKeys){\n                if (localStorage.getItem(key)) {\n                    localStorage.removeItem(key);\n                    stats.duplicatesRemoved++;\n                    console.log(\"\\uD83D\\uDDD1️ تم تنظيف المفتاح: \".concat(key));\n                }\n            }\n        } catch (error) {\n            stats.errors.push(\"خطأ في تنظيف المفاتيح: \".concat(error.message));\n        }\n    }\n    /**\n   * فحص حالة النظام الموحد\n   */ static async checkUnifiedSystemHealth() {\n        try {\n            // فحص النظام الأساسي\n            const storageHealth = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.checkStorageHealth)();\n            // فحص المفاتيح\n            const allKeys = Object.keys(localStorage);\n            const coreDataKeys = _storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.CORE_DATA;\n            const missingCoreKeys = coreDataKeys.filter((key)=>!allKeys.includes(key));\n            const legacyKeys = allKeys.filter((key)=>_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_CATEGORIES.LEGACY.includes(key));\n            const issues = [\n                ...storageHealth.issues\n            ];\n            const recommendations = [\n                ...storageHealth.recommendations\n            ];\n            // فحص المفاتيح المفقودة\n            if (missingCoreKeys.length > 0) {\n                issues.push(\"مفاتيح أساسية مفقودة: \".concat(missingCoreKeys.join(\", \")));\n                recommendations.push(\"تهيئة البيانات الأساسية\");\n            }\n            // فحص المفاتيح القديمة\n            if (legacyKeys.length > 0) {\n                issues.push(\"مفاتيح قديمة موجودة: \".concat(legacyKeys.join(\", \")));\n                recommendations.push(\"تشغيل عملية دمج البيانات\");\n            }\n            return {\n                isHealthy: storageHealth.isHealthy && issues.length === storageHealth.issues.length,\n                issues,\n                recommendations,\n                keyStats: {\n                    total: allKeys.length,\n                    coreData: coreDataKeys.filter((key)=>allKeys.includes(key)).length,\n                    legacy: legacyKeys.length,\n                    backups: allKeys.filter((key)=>key.startsWith(_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.BACKUP_PREFIX)).length\n                },\n                storageHealth\n            };\n        } catch (error) {\n            return {\n                isHealthy: false,\n                issues: [\n                    \"خطأ في فحص النظام: \".concat(error.message)\n                ],\n                recommendations: [\n                    \"إعادة تشغيل التطبيق\"\n                ],\n                keyStats: {},\n                storageHealth: {}\n            };\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/unifiedStorage.ts\n"));

/***/ })

});