/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/statistics/page";
exports.ids = ["app/statistics/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstatistics%2Fpage&page=%2Fstatistics%2Fpage&appPaths=%2Fstatistics%2Fpage&pagePath=private-next-app-dir%2Fstatistics%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstatistics%2Fpage&page=%2Fstatistics%2Fpage&appPaths=%2Fstatistics%2Fpage&pagePath=private-next-app-dir%2Fstatistics%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'statistics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/statistics/page.tsx */ \"(rsc)/./src/app/statistics/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/statistics/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/statistics/page\",\n        pathname: \"/statistics\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstatistics%2Fpage&page=%2Fstatistics%2Fpage&appPaths=%2Fstatistics%2Fpage&pagePath=private-next-app-dir%2Fstatistics%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cstatistics%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cstatistics%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/statistics/page.tsx */ \"(ssr)/./src/app/statistics/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNzcmMlNUNhcHAlNUNzdGF0aXN0aWNzJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvPzA5MDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcc3RhdGlzdGljc1xcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cstatistics%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/statistics/page.tsx":
/*!*************************************!*\
  !*** ./src/app/statistics/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(ssr)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/formatters */ \"(ssr)/./src/utils/formatters.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst StatisticsPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeFilter, setTimeFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"month\");\n    const [serviceFilter, setServiceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تحميل البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadInvoicesData();\n    }, []);\n    // إعادة حساب الإحصائيات عند تغيير الفلاتر\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (invoices.length > 0) {\n            calculateStatistics();\n        }\n    }, [\n        invoices,\n        timeFilter,\n        serviceFilter\n    ]);\n    const loadInvoicesData = async ()=>{\n        try {\n            setLoading(true);\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureGetItem)(\"invoices\", []);\n            if (result.success) {\n                setInvoices(result.data);\n            }\n        } catch (error) {\n            console.error(\"خطأ في تحميل البيانات:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getFilteredInvoices = ()=>{\n        let filtered = [\n            ...invoices\n        ];\n        const now = new Date();\n        // فلترة حسب الوقت\n        switch(timeFilter){\n            case \"today\":\n                filtered = filtered.filter((inv)=>{\n                    const invDate = new Date(inv.createdAt);\n                    return invDate.toDateString() === now.toDateString();\n                });\n                break;\n            case \"week\":\n                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n                filtered = filtered.filter((inv)=>new Date(inv.createdAt) >= weekAgo);\n                break;\n            case \"month\":\n                const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);\n                filtered = filtered.filter((inv)=>new Date(inv.createdAt) >= monthAgo);\n                break;\n            case \"quarter\":\n                const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);\n                filtered = filtered.filter((inv)=>new Date(inv.createdAt) >= quarterStart);\n                break;\n            case \"year\":\n                const yearStart = new Date(now.getFullYear(), 0, 1);\n                filtered = filtered.filter((inv)=>new Date(inv.createdAt) >= yearStart);\n                break;\n        }\n        // فلترة حسب نوع الخدمة\n        if (serviceFilter !== \"all\") {\n            filtered = filtered.filter((inv)=>inv.services.some((service)=>service.serviceType === serviceFilter));\n        }\n        return filtered;\n    };\n    const calculateStatistics = ()=>{\n        const filteredInvoices = getFilteredInvoices();\n        if (filteredInvoices.length === 0) {\n            setStatistics({\n                totalRevenue: 0,\n                totalInvoices: 0,\n                averageInvoiceValue: 0,\n                paidInvoices: 0,\n                partiallyPaidInvoices: 0,\n                unpaidInvoices: 0,\n                collectionRate: 0,\n                serviceStats: [],\n                dailyStats: [],\n                monthlyStats: []\n            });\n            return;\n        }\n        // الحسابات الأساسية\n        const totalRevenue = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + inv.paidAmount, 0);\n        const totalInvoices = filteredInvoices.length;\n        const averageInvoiceValue = totalRevenue / totalInvoices;\n        // حالات الدفع\n        const paidInvoices = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const partiallyPaidInvoices = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidInvoices = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const collectionRate = totalRevenue > 0 ? totalPaid / totalRevenue * 100 : 0;\n        // إحصائيات الخدمات\n        const serviceStats = calculateServiceStats(filteredInvoices, totalRevenue);\n        // الإحصائيات اليومية والشهرية\n        const dailyStats = calculateDailyStats(filteredInvoices);\n        const monthlyStats = calculateMonthlyStats(filteredInvoices);\n        setStatistics({\n            totalRevenue,\n            totalInvoices,\n            averageInvoiceValue,\n            paidInvoices,\n            partiallyPaidInvoices,\n            unpaidInvoices,\n            collectionRate,\n            serviceStats,\n            dailyStats,\n            monthlyStats\n        });\n    };\n    const calculateServiceStats = (invoices, totalRevenue)=>{\n        const serviceMap = new Map();\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        invoices.forEach((invoice)=>{\n            invoice.services.forEach((service)=>{\n                const current = serviceMap.get(service.serviceType) || {\n                    count: 0,\n                    revenue: 0,\n                    name: serviceNames[service.serviceType]\n                };\n                // حساب إيرادات الخدمة\n                let serviceRevenue = 0;\n                switch(service.serviceType){\n                    case \"consultation\":\n                        serviceRevenue = service.cost || 0;\n                        break;\n                    case \"pattern\":\n                    case \"manufacturing\":\n                        serviceRevenue = service.models?.reduce((sum, model)=>sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0), 0) || 0;\n                        break;\n                    case \"pattern_printing\":\n                        serviceRevenue = service.files?.reduce((sum, file)=>sum + (file.cost || 0), 0) || 0;\n                        break;\n                    case \"shipping\":\n                        serviceRevenue = service.total || 0;\n                        break;\n                    case \"marketing\":\n                    case \"photography\":\n                        serviceRevenue = service.cost || 0;\n                        break;\n                    case \"products\":\n                        serviceRevenue = service.items?.reduce((sum, item)=>sum + (item.total || 0), 0) || 0;\n                        break;\n                    case \"raw_purchases\":\n                        serviceRevenue = service.materials?.reduce((sum, material)=>sum + (material.total || 0), 0) || 0;\n                        break;\n                    case \"samples\":\n                        serviceRevenue = service.samples?.reduce((sum, sample)=>sum + (sample.cost || 0), 0) || 0;\n                        break;\n                }\n                serviceMap.set(service.serviceType, {\n                    count: current.count + 1,\n                    revenue: current.revenue + serviceRevenue,\n                    name: current.name\n                });\n            });\n        });\n        return Array.from(serviceMap.entries()).map(([serviceType, data])=>({\n                serviceType,\n                serviceName: data.name,\n                count: data.count,\n                revenue: data.revenue,\n                percentage: totalRevenue > 0 ? data.revenue / totalRevenue * 100 : 0\n            })).sort((a, b)=>b.revenue - a.revenue);\n    };\n    const calculateDailyStats = (invoices)=>{\n        const dailyMap = new Map();\n        invoices.forEach((invoice)=>{\n            const date = new Date(invoice.createdAt).toISOString().split(\"T\")[0];\n            const current = dailyMap.get(date) || {\n                invoices: 0,\n                revenue: 0,\n                paidAmount: 0\n            };\n            dailyMap.set(date, {\n                invoices: current.invoices + 1,\n                revenue: current.revenue + invoice.total,\n                paidAmount: current.paidAmount + invoice.paidAmount\n            });\n        });\n        return Array.from(dailyMap.entries()).map(([date, data])=>({\n                date,\n                ...data\n            })).sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 30); // آخر 30 يوم\n    };\n    const calculateMonthlyStats = (invoices)=>{\n        const monthlyMap = new Map();\n        invoices.forEach((invoice)=>{\n            const date = new Date(invoice.createdAt);\n            const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}`;\n            const current = monthlyMap.get(month) || {\n                invoices: 0,\n                revenue: 0,\n                paidAmount: 0\n            };\n            monthlyMap.set(month, {\n                invoices: current.invoices + 1,\n                revenue: current.revenue + invoice.total,\n                paidAmount: current.paidAmount + invoice.paidAmount\n            });\n        });\n        return Array.from(monthlyMap.entries()).map(([month, data])=>({\n                month,\n                ...data\n            })).sort((a, b)=>b.month.localeCompare(a.month)).slice(0, 12); // آخر 12 شهر\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل الإحصائيات...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-l from-blue-600 to-blue-800 text-white shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.back(),\n                                        className: \"p-2 hover:bg-blue-700 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"\\uD83D\\uDCCA الإحصائيات الشاملة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm\",\n                                                children: \"تحليل مفصل لأداء الأعمال والمبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadInvoicesData,\n                                    className: \"bg-blue-500 hover:bg-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm p-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                children: \"\\uD83D\\uDD0D فلاتر البحث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"الفترة الزمنية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: timeFilter,\n                                                onChange: (e)=>setTimeFilter(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"today\",\n                                                        children: \"اليوم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"week\",\n                                                        children: \"آخر أسبوع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"month\",\n                                                        children: \"الشهر الحالي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"quarter\",\n                                                        children: \"الربع الحالي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"year\",\n                                                        children: \"السنة الحالية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"جميع الفترات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"نوع الخدمة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: serviceFilter,\n                                                onChange: (e)=>setServiceFilter(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"جميع الخدمات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"consultation\",\n                                                        children: \"استشارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pattern\",\n                                                        children: \"باترون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pattern_printing\",\n                                                        children: \"طباعة باترون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"manufacturing\",\n                                                        children: \"تصنيع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"samples\",\n                                                        children: \"عينات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"shipping\",\n                                                        children: \"شحن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"marketing\",\n                                                        children: \"تسويق\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"photography\",\n                                                        children: \"تصوير\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"products\",\n                                                        children: \"منتجات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"raw_purchases\",\n                                                        children: \"مشتريات خام\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined),\n                    statistics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-100 text-sm\",\n                                                            children: \"إجمالي الإيرادات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(statistics.totalRevenue)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-400 bg-opacity-30 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-100 text-sm\",\n                                                            children: \"عدد الفواتير\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: statistics.totalInvoices\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-400 bg-opacity-30 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-purple-100 text-sm\",\n                                                            children: \"متوسط قيمة الفاتورة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(statistics.averageInvoiceValue)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-400 bg-opacity-30 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-orange-100 text-sm\",\n                                                            children: \"نسبة التحصيل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: [\n                                                                statistics.collectionRate.toFixed(1),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-orange-400 bg-opacity-30 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-800\",\n                                                    children: \"\\uD83D\\uDCB0 حالة المدفوعات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-600 font-medium\",\n                                                                children: \"مدفوع بالكامل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold\",\n                                                                children: statistics.paidInvoices\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-yellow-600 font-medium\",\n                                                                children: \"مدفوع جزئياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold\",\n                                                                children: statistics.partiallyPaidInvoices\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-600 font-medium\",\n                                                                children: \"غير مدفوع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold\",\n                                                                children: statistics.unpaidInvoices\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"\\uD83C\\uDFC6 أكثر الخدمات طلباً\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: statistics.serviceStats.slice(0, 5).map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-blue-100 text-blue-800 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-700\",\n                                                                        children: service.serviceName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-left\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(service.revenue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            service.count,\n                                                                            \" فاتورة • \",\n                                                                            service.percentage.toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.serviceType, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"\\uD83D\\uDCC8 الإيرادات اليومية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-end justify-between gap-2\",\n                                                children: statistics.dailyStats.slice(0, 10).reverse().map((day, index)=>{\n                                                    const maxRevenue = Math.max(...statistics.dailyStats.map((d)=>d.revenue));\n                                                    const height = maxRevenue > 0 ? day.revenue / maxRevenue * 200 : 0;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md w-full transition-all duration-300 hover:from-blue-600 hover:to-blue-500\",\n                                                                        style: {\n                                                                            height: `${height}px`,\n                                                                            minHeight: \"4px\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap z-10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatDate)(day.date)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(day.revenue)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    day.invoices,\n                                                                                    \" فاتورة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                                lineNumber: 510,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 507,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mt-2 transform rotate-45 origin-left\",\n                                                                children: new Date(day.date).getDate()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, day.date, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"\\uD83E\\uDD67 توزيع الخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: statistics.serviceStats.map((service, index)=>{\n                                                    const colors = [\n                                                        \"bg-blue-500\",\n                                                        \"bg-green-500\",\n                                                        \"bg-yellow-500\",\n                                                        \"bg-red-500\",\n                                                        \"bg-purple-500\",\n                                                        \"bg-pink-500\",\n                                                        \"bg-indigo-500\",\n                                                        \"bg-gray-500\",\n                                                        \"bg-orange-500\",\n                                                        \"bg-teal-500\"\n                                                    ];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `w-4 h-4 rounded-full ${colors[index % colors.length]}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                                children: service.serviceName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    service.percentage.toFixed(1),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `h-2 rounded-full ${colors[index % colors.length]}`,\n                                                                            style: {\n                                                                                width: `${service.percentage}%`\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.serviceType, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 23\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"\\uD83D\\uDCC5 الإحصائيات اليومية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"التاريخ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"الفواتير\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"الإيرادات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"المحصل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: statistics.dailyStats.slice(0, 10).map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-600\",\n                                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatDate)(day.date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-900 font-medium\",\n                                                                            children: day.invoices\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-900 font-medium\",\n                                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(day.revenue)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-green-600 font-medium\",\n                                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(day.paidAmount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, day.date, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"\\uD83D\\uDCCA الإحصائيات الشهرية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"الشهر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"الفواتير\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"الإيرادات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-right py-2 font-semibold text-gray-700\",\n                                                                        children: \"المحصل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: statistics.monthlyStats.slice(0, 6).map((month)=>{\n                                                                const monthNames = [\n                                                                    \"يناير\",\n                                                                    \"فبراير\",\n                                                                    \"مارس\",\n                                                                    \"أبريل\",\n                                                                    \"مايو\",\n                                                                    \"يونيو\",\n                                                                    \"يوليو\",\n                                                                    \"أغسطس\",\n                                                                    \"سبتمبر\",\n                                                                    \"أكتوبر\",\n                                                                    \"نوفمبر\",\n                                                                    \"ديسمبر\"\n                                                                ];\n                                                                const [year, monthNum] = month.month.split(\"-\");\n                                                                const monthName = monthNames[parseInt(monthNum) - 1];\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-600\",\n                                                                            children: [\n                                                                                monthName,\n                                                                                \" \",\n                                                                                year\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-900 font-medium\",\n                                                                            children: month.invoices\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-gray-900 font-medium\",\n                                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(month.revenue)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-2 text-green-600 font-medium\",\n                                                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(month.paidAmount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, month.month, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 27\n                                                                }, undefined);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const csvData = generateCSVReport(statistics);\n                                                    downloadCSV(csvData, `statistics-${new Date().toISOString().split(\"T\")[0]}.csv`);\n                                                },\n                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"تصدير CSV\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.print(),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"طباعة التقرير\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\statistics\\\\page.tsx\",\n        lineNumber: 296,\n        columnNumber: 5\n    }, undefined);\n    // Helper functions\n    function generateCSVReport(stats) {\n        const headers = [\n            \"نوع البيانات\",\n            \"القيمة\",\n            \"التفاصيل\"\n        ];\n        const rows = [\n            [\n                \"إجمالي الإيرادات\",\n                (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(stats.totalRevenue),\n                \"\"\n            ],\n            [\n                \"عدد الفواتير\",\n                stats.totalInvoices.toString(),\n                \"\"\n            ],\n            [\n                \"متوسط قيمة الفاتورة\",\n                (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(stats.averageInvoiceValue),\n                \"\"\n            ],\n            [\n                \"نسبة التحصيل\",\n                `${stats.collectionRate.toFixed(1)}%`,\n                \"\"\n            ],\n            [\n                \"فواتير مدفوعة\",\n                stats.paidInvoices.toString(),\n                \"\"\n            ],\n            [\n                \"فواتير مدفوعة جزئياً\",\n                stats.partiallyPaidInvoices.toString(),\n                \"\"\n            ],\n            [\n                \"فواتير غير مدفوعة\",\n                stats.unpaidInvoices.toString(),\n                \"\"\n            ],\n            ...stats.serviceStats.map((service)=>[\n                    \"خدمة\",\n                    service.serviceName,\n                    `${service.count} فاتورة - ${(0,_utils_formatters__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(service.revenue)} - ${service.percentage.toFixed(1)}%`\n                ])\n        ];\n        return [\n            headers,\n            ...rows\n        ].map((row)=>row.join(\",\")).join(\"\\n\");\n    }\n    function downloadCSV(csvContent, filename) {\n        const blob = new Blob([\n            \"\\uFEFF\" + csvContent\n        ], {\n            type: \"text/csv;charset=utf-8;\"\n        });\n        const link = document.createElement(\"a\");\n        if (link.download !== undefined) {\n            const url = URL.createObjectURL(blob);\n            link.setAttribute(\"href\", url);\n            link.setAttribute(\"download\", filename);\n            link.style.visibility = \"hidden\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatisticsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/statistics/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/formatters.ts":
/*!*********************************!*\
  !*** ./src/utils/formatters.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatEgyptianPhone: () => (/* binding */ formatEgyptianPhone),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   isValidEgyptianPhone: () => (/* binding */ isValidEgyptianPhone),\n/* harmony export */   sanitizeFilename: () => (/* binding */ sanitizeFilename),\n/* harmony export */   toTitleCase: () => (/* binding */ toTitleCase),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/**\n * دوال التنسيق المشتركة\n * Shared formatting utilities\n */ /**\n * تنسيق العملة مع التحقق من صحة البيانات\n * Format currency with data validation\n * \n * @param amount - المبلغ (رقم، نص، أو قيمة فارغة)\n * @returns نص منسق بالعملة المصرية\n */ const formatCurrency = (amount)=>{\n    // التحقق من صحة القيمة وتحويلها لرقم\n    const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n    // التحقق من أن النتيجة رقم صحيح\n    if (isNaN(numericAmount)) {\n        console.warn(\"⚠️ Invalid amount passed to formatCurrency:\", amount);\n        return \"0.00 جنيه\";\n    }\n    return `${numericAmount.toFixed(2)} جنيه`;\n};\n/**\n * تنسيق التاريخ بالتقويم الميلادي\n * Format date using Gregorian calendar\n * \n * @param dateString - نص التاريخ\n * @param format - نوع التنسيق ('short' | 'long')\n * @returns تاريخ منسق بالعربية\n */ const formatDate = (dateString, format = \"short\")=>{\n    try {\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: format === \"long\" ? \"long\" : \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    } catch (error) {\n        console.warn(\"⚠️ Invalid date passed to formatDate:\", dateString);\n        return \"تاريخ غير صحيح\";\n    }\n};\n/**\n * تنسيق الرقم مع فواصل الآلاف\n * Format number with thousands separators\n * \n * @param num - الرقم المراد تنسيقه\n * @returns رقم منسق مع فواصل\n */ const formatNumber = (num)=>{\n    const numericValue = typeof num === \"number\" ? num : parseFloat(String(num || 0));\n    if (isNaN(numericValue)) {\n        console.warn(\"⚠️ Invalid number passed to formatNumber:\", num);\n        return \"0\";\n    }\n    return numericValue.toLocaleString(\"ar-EG\");\n};\n/**\n * تنسيق النسبة المئوية\n * Format percentage\n * \n * @param value - القيمة (0-1 أو 0-100)\n * @param isDecimal - هل القيمة عشرية (0-1) أم مئوية (0-100)\n * @returns نسبة مئوية منسقة\n */ const formatPercentage = (value, isDecimal = true)=>{\n    const numericValue = typeof value === \"number\" ? value : parseFloat(String(value || 0));\n    if (isNaN(numericValue)) {\n        console.warn(\"⚠️ Invalid percentage passed to formatPercentage:\", value);\n        return \"0%\";\n    }\n    const percentage = isDecimal ? numericValue * 100 : numericValue;\n    return `${percentage.toFixed(1)}%`;\n};\n/**\n * تنظيف النص للاستخدام في أسماء الملفات\n * Sanitize text for filename usage\n * \n * @param text - النص المراد تنظيفه\n * @returns نص منظف وآمن للاستخدام في أسماء الملفات\n */ const sanitizeFilename = (text)=>{\n    return text.replace(/[^\\w\\s\\u0600-\\u06FF-]/g, \"\") // إزالة الأحرف الخاصة مع الاحتفاظ بالعربية\n    .replace(/\\s+/g, \"_\") // استبدال المسافات بـ _\n    .trim();\n};\n/**\n * تحويل النص إلى عنوان (أول حرف كبير)\n * Convert text to title case\n * \n * @param text - النص المراد تحويله\n * @returns نص بصيغة العنوان\n */ const toTitleCase = (text)=>{\n    return text.split(\" \").map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(\" \");\n};\n/**\n * اختصار النص مع إضافة نقاط\n * Truncate text with ellipsis\n * \n * @param text - النص المراد اختصاره\n * @param maxLength - الطول الأقصى\n * @returns نص مختصر مع نقاط إذا لزم الأمر\n */ const truncateText = (text, maxLength = 50)=>{\n    if (text.length <= maxLength) {\n        return text;\n    }\n    return text.substring(0, maxLength - 3) + \"...\";\n};\n/**\n * التحقق من صحة رقم الهاتف المصري\n * Validate Egyptian phone number\n * \n * @param phone - رقم الهاتف\n * @returns true إذا كان الرقم صحيحاً\n */ const isValidEgyptianPhone = (phone)=>{\n    // أرقام الهاتف المصرية: 01xxxxxxxxx (11 رقم)\n    const phoneRegex = /^01[0-9]{9}$/;\n    return phoneRegex.test(phone.replace(/\\s+/g, \"\"));\n};\n/**\n * تنسيق رقم الهاتف المصري\n * Format Egyptian phone number\n * \n * @param phone - رقم الهاتف\n * @returns رقم هاتف منسق\n */ const formatEgyptianPhone = (phone)=>{\n    const cleanPhone = phone.replace(/\\s+/g, \"\");\n    if (cleanPhone.length === 11 && cleanPhone.startsWith(\"01\")) {\n        return `${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 7)} ${cleanPhone.substring(7)}`;\n    }\n    return phone; // إرجاع الرقم كما هو إذا لم يكن بالصيغة المتوقعة\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/formatters.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: () => (/* binding */ STORAGE_CONFIG),\n/* harmony export */   checkStorageHealth: () => (/* binding */ checkStorageHealth),\n/* harmony export */   createBackup: () => (/* binding */ createBackup),\n/* harmony export */   restoreFromBackup: () => (/* binding */ restoreFromBackup),\n/* harmony export */   secureGetItem: () => (/* binding */ secureGetItem),\n/* harmony export */   secureSetItem: () => (/* binding */ secureSetItem)\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = `backup-${Date.now()}`;\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: `تم استرداد البيانات بنجاح من النسخة: ${backup.timestamp}`\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/secureStorage.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"af7364717194\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mYXBhcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzkyZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhZjczNjQ3MTcxOTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الفواتير - Invoice Management System\",\n    description: \"نظام شامل لإدارة فواتير الخدمات مع دعم كامل للغة العربية\",\n    keywords: \"فواتير, إدارة, خدمات, استشارات, باترون, تصنيع\",\n    authors: [\n        {\n            name: \"Invoice Management System\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_latin_weight_300_400_600_700_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_2___default().className)} font-arabic`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/statistics/page.tsx":
/*!*************************************!*\
  !*** ./src/app/statistics/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\231\src\app\statistics\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstatistics%2Fpage&page=%2Fstatistics%2Fpage&appPaths=%2Fstatistics%2Fpage&pagePath=private-next-app-dir%2Fstatistics%2Fpage.tsx&appDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();