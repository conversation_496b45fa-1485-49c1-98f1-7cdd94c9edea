"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInvoiceStore: function() { return /* binding */ useInvoiceStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// تحميل البيانات من النظام الموحد\nconst loadInvoicesFromStorage = async ()=>{\n    if (true) {\n        try {\n            const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n            if (!result.success) {\n                console.error(\"❌ Error loading invoices from unified storage:\", result.message);\n                return [];\n            }\n            console.log(\"✅ Loaded invoices from unified storage:\", result.data.length);\n            return result.data;\n        } catch (error) {\n            console.error(\"❌ Critical error loading invoices:\", error);\n            return [];\n        }\n    }\n    return [];\n};\n// إنشاء المتجر مع تهيئة فارغة أولاً\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: [],\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            var _service_models;\n                            (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            var _service_files;\n                            (_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            var _service_models1;\n                            (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            var _service_items;\n                            let productsSubtotal = 0;\n                            (_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            var _service_materials;\n                            (_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            var _service_samples;\n                            (_service_samples = service.samples) === null || _service_samples === void 0 ? void 0 : _service_samples.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: async ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || \"INV-\".concat(Date.now()),\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من النظام الموحد للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n                    currentStoredInvoices = result.success ? result.data : invoices;\n                    if (!result.success) {\n                        console.error(\"❌ Error reading current invoices from unified storage:\", result.message);\n                    }\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في النظام الموحد\n                try {\n                    const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, uniqueInvoices);\n                    if (saveResult.success) {\n                        console.log(\"✅ Invoice saved via unified storage. Total invoices:\", uniqueInvoices.length);\n                    } else {\n                        console.error(\"❌ Error saving invoice via unified storage:\", saveResult.message);\n                        // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                        if (true) {\n                            alert(\"تحذير: فشل في حفظ الفاتورة. \" + saveResult.message);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"❌ Critical error saving invoice:\", error);\n                    if (true) {\n                        alert(\"خطأ حرج: فشل في حفظ الفاتورة. يرجى المحاولة مرة أخرى.\");\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (true) {\n                    alert(\"تحذير: فشل في حذف الفاتورة. \" + saveResult.message);\n                }\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return (currentInvoice === null || currentInvoice === void 0 ? void 0 : currentInvoice.services.filter((s)=>s.serviceType === type)) || [];\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/invoiceStore.ts\n"));

/***/ })

});