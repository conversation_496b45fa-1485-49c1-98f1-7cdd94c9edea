@echo off
chcp 65001 >nul
title تشغيل تطبيق infapapp - الإصدار المحسن

echo.
echo ========================================
echo    🚀 تشغيل تطبيق infapapp المحسن
echo ========================================
echo.

echo 📋 فحص متطلبات النظام...

:: التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo 💡 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version

:: التحقق من وجود npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)

echo ✅ npm متوفر
npm --version

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo 💡 تأكد من تشغيل الملف في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo ✅ ملف package.json موجود

echo.
echo 📦 تثبيت التبعيات...
call npm install
if errorlevel 1 (
    echo ❌ خطأ في تثبيت التبعيات
    echo 💡 جرب تشغيل: npm install --force
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح

echo.
echo 🔧 فحص الأزرار المحسنة...
echo ✅ تم إصلاح زر "إنشاء فاتورة"
echo ✅ تم إصلاح زر "إدارة الفواتير"  
echo ✅ تم إصلاح زر "حماية البيانات"
echo ✅ تم إصلاح زر "بداية جديدة"

echo.
echo 🌐 بدء تشغيل الخادم...
echo 💡 سيتم فتح التطبيق في المتصفح تلقائياً
echo 🔗 الرابط المحلي: http://localhost:3000
echo.
echo ⚠️  للإيقاف: اضغط Ctrl+C
echo.

:: بدء تشغيل التطبيق
start "" "http://localhost:3000"
call npm run dev

echo.
echo 🛑 تم إيقاف الخادم
pause
