"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicesList.tsx":
/*!*****************************************!*\
  !*** ./src/components/InvoicesList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst InvoicesList = ()=>{\n    _s();\n    const { invoices } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date_desc\");\n    const [filteredInvoices, setFilteredInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previewInvoice, setPreviewInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [posInvoice, setPosInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // إعادة تحميل البيانات من التخزين الآمن عند تحميل المكون\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInvoices = ()=>{\n            // استخدام النظام الآمن لتحميل البيانات\n            const result = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState().invoices;\n            // التحقق من عدم وجود تكرار في البيانات\n            const uniqueInvoices = result.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n            // تحديث الحالة المحلية\n            setFilteredInvoices(uniqueInvoices);\n        };\n        // تحميل فوري\n        loadInvoices();\n        // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري\n        const handleStorageChange = (e)=>{\n            if (e.key === \"invoices\") {\n                loadInvoices();\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = [\n            ...invoices\n        ];\n        // البحث\n        if (searchTerm) {\n            filtered = filtered.filter((invoice)=>{\n                var _invoice_id;\n                return invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.client.phone.includes(searchTerm) || ((_invoice_id = invoice.id) === null || _invoice_id === void 0 ? void 0 : _invoice_id.includes(searchTerm));\n            });\n        }\n        // الفلترة\n        if (filterBy !== \"all\") {\n            filtered = filtered.filter((invoice)=>{\n                switch(filterBy){\n                    case \"consultation\":\n                        return invoice.services.some((s)=>s.serviceType === \"consultation\");\n                    case \"pattern\":\n                        return invoice.services.some((s)=>s.serviceType === \"pattern\");\n                    case \"photography\":\n                        return invoice.services.some((s)=>s.serviceType === \"photography\");\n                    case \"shipping\":\n                        return invoice.services.some((s)=>s.serviceType === \"shipping\");\n                    case \"high_value\":\n                        return invoice.total > 1000;\n                    case \"recent\":\n                        const oneWeekAgo = new Date();\n                        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n                        return new Date(invoice.createdAt) > oneWeekAgo;\n                    case \"paid_full\":\n                        return invoice.paymentStatus === \"مدفوع بالكامل\";\n                    case \"paid_partial\":\n                        return invoice.paymentStatus === \"مدفوع جزئياً\";\n                    case \"unpaid\":\n                        return invoice.paymentStatus === \"غير مدفوع\" || !invoice.paymentStatus;\n                    default:\n                        return true;\n                }\n            });\n        }\n        // الترتيب\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"date_desc\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"date_asc\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"amount_desc\":\n                    return b.total - a.total;\n                case \"amount_asc\":\n                    return a.total - b.total;\n                case \"client_name\":\n                    return a.client.name.localeCompare(b.client.name, \"ar\");\n                default:\n                    return 0;\n            }\n        });\n        setFilteredInvoices(filtered);\n    }, [\n        invoices,\n        searchTerm,\n        filterBy,\n        sortBy\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            \"consultation\": \"استشارات\",\n            \"pattern\": \"باترون\",\n            \"pattern_printing\": \"طباعة باترون\",\n            \"manufacturing\": \"تصنيع\",\n            \"samples\": \"عينات\",\n            \"shipping\": \"شحن\",\n            \"marketing\": \"تسويق\",\n            \"photography\": \"تصوير\",\n            \"products\": \"منتجات\",\n            \"raw_purchases\": \"مشتريات خام\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getServiceNames = (services)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return services.map((s)=>serviceNames[s.serviceType] || s.serviceType).join(\"، \");\n    };\n    // دالة عرض رسائل التأكيد المحسنة\n    const showSuccessMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 3000);\n    };\n    const showErrorMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 4000);\n    };\n    const handleExportPDF = async (invoice)=>{\n        try {\n            // فتح المعاينة أولاً للحصول على نفس التنسيق\n            setPreviewInvoice(invoice);\n            // انتظار قصير للتأكد من عرض المعاينة\n            setTimeout(async ()=>{\n                try {\n                    await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n                    showSuccessMessage(\"تم تصدير PDF للفاتورة \".concat(invoice.id, \" بنجاح! \\uD83C\\uDF89\"));\n                    // إغلاق المعاينة بعد التصدير بثانية واحدة\n                    setTimeout(()=>{\n                        setPreviewInvoice(null);\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"❌ Error exporting PDF:\", error);\n                    showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n                    setPreviewInvoice(null);\n                }\n            }, 500);\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تصدير PDF.\");\n        }\n    };\n    const handlePreviewInvoice = (invoice)=>{\n        setPreviewInvoice(invoice);\n    };\n    const handlePOSInvoice = (invoice)=>{\n        setPosInvoice(invoice);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewInvoice(null);\n    };\n    const handleEditFromPreview = ()=>{\n        // For now, just close the preview\n        // In a full implementation, this would navigate to edit mode\n        setPreviewInvoice(null);\n        alert(\"وظيفة التعديل ستكون متاحة قريباً\");\n    };\n    const handleExportFromPreview = async ()=>{\n        if (previewInvoice) {\n            await handleExportPDF(previewInvoice);\n        }\n    };\n    // دالة حذف الفاتورة مع تأكيد\n    const handleDeleteInvoice = (invoice)=>{\n        const confirmMessage = \"هل أنت متأكد من حذف هذه الفاتورة؟\\n\\nالفاتورة: \".concat(invoice.id, \"\\nالعميل: \").concat(invoice.client.name, \"\\nالمبلغ: \").concat(formatCurrency(invoice.total), \"\\n\\n⚠️ هذا الإجراء لا يمكن التراجع عنه!\");\n        if (window.confirm(confirmMessage)) {\n            const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState();\n            store.deleteInvoice(invoice.id);\n            if (previewInvoice && previewInvoice.id === invoice.id) {\n                setPreviewInvoice(null);\n            }\n            setTimeout(()=>{\n                const updatedInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                setFilteredInvoices(updatedInvoices);\n            }, 100);\n            showSuccessMessage(\"تم حذف الفاتورة \".concat(invoice.id, \" بنجاح! \\uD83D\\uDDD1️\"));\n        }\n    };\n    const getTotalStats = ()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const count = filteredInvoices.length;\n        const avgAmount = count > 0 ? total / count : 0;\n        // حساب المبالغ المدفوعة والمتبقية\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const totalRemaining = total - totalPaid;\n        // حساب عدد الفواتير حسب حالة الدفع\n        const paidFullCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const paidPartialCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        // حساب نسبة التحصيل\n        const collectionRate = total > 0 ? totalPaid / total * 100 : 0;\n        return {\n            total,\n            count,\n            avgAmount,\n            totalPaid,\n            totalRemaining,\n            paidFullCount,\n            paidPartialCount,\n            unpaidCount,\n            collectionRate\n        };\n    };\n    const stats = getTotalStats();\n    // حساب إحصائيات اليوم الحالي مع مقارنة بالأمس\n    const getTodayStats = ()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const todayStr = today.toDateString();\n        const yesterdayStr = yesterday.toDateString();\n        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === todayStr);\n        const yesterdayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === yesterdayStr);\n        const todayTotal = todayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const todayPaid = todayInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const todayRemaining = todayTotal - todayPaid;\n        const yesterdayTotal = yesterdayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const yesterdayCount = yesterdayInvoices.length;\n        const todayPaidFullCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const todayPaidPartialCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const todayUnpaidCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const todayCollectionRate = todayTotal > 0 ? todayPaid / todayTotal * 100 : 0;\n        // حساب التغيير مقارنة بالأمس\n        const countChange = todayInvoices.length - yesterdayCount;\n        const revenueChange = todayTotal - yesterdayTotal;\n        const countChangePercent = yesterdayCount > 0 ? countChange / yesterdayCount * 100 : 0;\n        const revenueChangePercent = yesterdayTotal > 0 ? revenueChange / yesterdayTotal * 100 : 0;\n        return {\n            count: todayInvoices.length,\n            total: todayTotal,\n            paid: todayPaid,\n            remaining: todayRemaining,\n            paidFullCount: todayPaidFullCount,\n            paidPartialCount: todayPaidPartialCount,\n            unpaidCount: todayUnpaidCount,\n            collectionRate: todayCollectionRate,\n            countChange,\n            revenueChange,\n            countChangePercent,\n            revenueChangePercent,\n            yesterdayCount,\n            yesterdayTotal\n        };\n    };\n    const todayStats = getTodayStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"إدارة الفواتير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-indigo-500 to-purple-600 text-white rounded-xl p-6 mb-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"\\uD83D\\uDCCA إحصائيات اليوم الحالي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-sm\",\n                                            children: new Date().toLocaleDateString(\"ar-EG\", {\n                                                weekday: \"long\",\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: todayStats.count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"فواتير اليوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.countChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.countChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.countChange > 0 ? \"↗️\" : \"↘️\",\n                                                    Math.abs(todayStats.countChange),\n                                                    \" عن الأمس\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"إجمالي المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.revenueChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.revenueChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.revenueChange > 0 ? \"↗️\" : \"↘️\",\n                                                    todayStats.revenueChangePercent.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.paid)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"المبلغ المحصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: [\n                                                    \"متبقي: \",\n                                                    formatCurrency(todayStats.remaining)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    todayStats.collectionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"نسبة التحصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: todayStats.count > 0 ? \"متوسط: \".concat(formatCurrency(todayStats.total / todayStats.count)) : \"لا توجد فواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-indigo-400 border-opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-green-200\",\n                                                            children: todayStats.paidFullCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"مدفوع بالكامل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-yellow-200\",\n                                                            children: todayStats.paidPartialCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"مدفوع جزئياً\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-red-200\",\n                                                            children: todayStats.unpaidCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-indigo-100\",\n                                                            children: \"غير مدفوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-indigo-100 mb-2\",\n                                                    children: \"\\uD83C\\uDFC6 أهم الخدمات اليوم:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: (()=>{\n                                                        const today = new Date().toDateString();\n                                                        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === today);\n                                                        const serviceCount = new Map();\n                                                        todayInvoices.forEach((invoice)=>{\n                                                            invoice.services.forEach((service)=>{\n                                                                const serviceName = getServiceName(service.serviceType);\n                                                                serviceCount.set(serviceName, (serviceCount.get(serviceName) || 0) + 1);\n                                                            });\n                                                        });\n                                                        const topServices = Array.from(serviceCount.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 3);\n                                                        if (topServices.length === 0) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-indigo-200\",\n                                                                children: \"لا توجد خدمات اليوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 30\n                                                            }, undefined);\n                                                        }\n                                                        return topServices.map((param, index)=>{\n                                                            let [serviceName, count] = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-indigo-200\",\n                                                                        children: [\n                                                                            index + 1,\n                                                                            \". \",\n                                                                            serviceName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                        lineNumber: 496,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-semibold\",\n                                                                        children: count\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, serviceName, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        });\n                                                    })()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-700\",\n                                        children: \"إجمالي الفواتير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: stats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg border-r-4 border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"إجمالي المبلغ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatCurrency(stats.total)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 p-4 rounded-lg border-r-4 border-emerald-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-emerald-700\",\n                                        children: \"المبلغ المدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-emerald-800\",\n                                        children: formatCurrency(stats.totalPaid)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg border-r-4 border-red-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-red-700\",\n                                        children: \"المبلغ المتبقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-800\",\n                                        children: formatCurrency(stats.totalRemaining)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-teal-50 p-4 rounded-lg border-r-4 border-teal-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-teal-700\",\n                                        children: \"مدفوع بالكامل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-teal-800\",\n                                        children: stats.paidFullCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-teal-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-orange-700\",\n                                        children: \"مدفوع جزئياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: stats.paidPartialCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg border-r-4 border-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-700\",\n                                        children: \"غير مدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: stats.unpaidCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-700\",\n                                        children: \"نسبة التحصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: [\n                                            stats.collectionRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: [\n                                            \"متوسط الفاتورة: \",\n                                            formatCurrency(stats.avgAmount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البحث\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث بالاسم، الهاتف، أو رقم الفاتورة\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"فلترة حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterBy,\n                                            onChange: (e)=>setFilterBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"consultation\",\n                                                    children: \"استشارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pattern\",\n                                                    children: \"باترون\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"photography\",\n                                                    children: \"تصوير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shipping\",\n                                                    children: \"شحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high_value\",\n                                                    children: \"قيمة عالية (+1000 جنيه)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"recent\",\n                                                    children: \"الأسبوع الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_full\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_partial\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"unpaid\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ترتيب حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_desc\",\n                                                    children: \"التاريخ (الأحدث أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_asc\",\n                                                    children: \"التاريخ (الأقدم أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_desc\",\n                                                    children: \"المبلغ (الأعلى أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_asc\",\n                                                    children: \"المبلغ (الأقل أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"client_name\",\n                                                    children: \"اسم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, undefined),\n            filteredInvoices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-lg mb-4\",\n                        children: invoices.length === 0 ? \"لا توجد فواتير محفوظة\" : \"لا توجد نتائج للبحث\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 11\n                    }, undefined),\n                    invoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"قم بإنشاء فاتورة جديدة لتظهر هنا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 615,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: [\n                                                        \"فاتورة #\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"السيلز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.salesperson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الخدمات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getServiceNames(invoice.services)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"طريقة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.paymentMethod\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"حالة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: invoice.paymentStatus || \"غير محدد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المدفوع / المتبقي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatCurrency(invoice.paidAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" / \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                            children: formatCurrency(invoice.remainingAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-2 rounded text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-700 font-medium\",\n                                                children: \"\\uD83D\\uDE9A يتطلب شحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleExportPDF(invoice),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDCC4 PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePreviewInvoice(invoice),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDC41️ معاينة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(invoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83E\\uDDFE POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteInvoice(invoice),\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2\",\n                                        children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, invoice.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 624,\n                columnNumber: 9\n            }, undefined),\n            previewInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"معاينة الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClosePreview,\n                                    className: \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    invoice: previewInvoice,\n                                    onEdit: handleEditFromPreview,\n                                    onExportPDF: handleExportFromPreview\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(previewInvoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83E\\uDDFE فاتورة POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteInvoice(previewInvoice),\n                                                className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 745,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                    lineNumber: 735,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 734,\n                columnNumber: 9\n            }, undefined),\n            posInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                invoice: posInvoice,\n                onClose: ()=>setPosInvoice(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 776,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n        lineNumber: 379,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicesList, \"vNSHrZ6bGj05wXDBkzjUPPk++oo=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore\n    ];\n});\n_c = InvoicesList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicesList);\nvar _c;\n$RefreshReg$(_c, \"InvoicesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicesList.tsx\n"));

/***/ })

});