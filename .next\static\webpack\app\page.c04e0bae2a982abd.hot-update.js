"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InvoiceForm */ \"(app-pages-browser)/./src/components/InvoiceForm.tsx\");\n/* harmony import */ var _components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvoicesList */ \"(app-pages-browser)/./src/components/InvoicesList.tsx\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"client\");\n    const [currentClient, setCurrentClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentInvoice, setCurrentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { saveInvoice, currentClient: storeClient, clearCurrentClient } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore)();\n    // استخدام العميل من store إذا كان متوفراً\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (storeClient && !currentClient) {\n            setCurrentClient(storeClient);\n            setCurrentStep(\"invoice\");\n            // مسح العميل من store بعد الاستخدام\n            clearCurrentClient();\n        }\n    }, [\n        storeClient,\n        currentClient,\n        clearCurrentClient\n    ]);\n    const handleClientSubmit = (client)=>{\n        setCurrentClient(client);\n        setCurrentStep(\"invoice\");\n    };\n    const handleInvoiceSubmit = (invoice)=>{\n        // إنشاء ID فريد مع ترقيم تسلسلي\n        const generateSequentialId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const monthYearKey = \"\".concat(month, \"-\").concat(year);\n            // قراءة العدادات المحفوظة باستخدام النظام الآمن\n            const countersResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoiceCounters\", {});\n            let counters = {};\n            if (countersResult.success) {\n                counters = countersResult.data;\n            } else {\n                console.error(\"❌ Error reading invoice counters:\", countersResult.message);\n                counters = {};\n            }\n            // الحصول على العداد الحالي للشهر والسنة\n            const currentCounter = counters[monthYearKey] || 0;\n            const newCounter = currentCounter + 1;\n            // تحديث العداد\n            counters[monthYearKey] = newCounter;\n            // حفظ العدادات المحدثة باستخدام النظام الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureSetItem)(\"invoiceCounters\", counters);\n            if (!saveResult.success) {\n                console.error(\"❌ Error saving invoice counters:\", saveResult.message);\n            }\n            // إنشاء ID بالتنسيق المطلوب\n            const sequentialNumber = String(newCounter).padStart(3, \"0\");\n            const invoiceId = \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(sequentialNumber);\n            return invoiceId;\n        };\n        const invoiceWithId = {\n            ...invoice,\n            id: invoice.id || generateSequentialId(),\n            createdAt: invoice.createdAt || new Date().toISOString()\n        };\n        // تعيين الفاتورة الحالية\n        setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة في المتجر\n        const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore.getState();\n        store.setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)\n        store.saveInvoice();\n        // التحقق من النتيجة باستخدام النظام الآمن\n        setTimeout(()=>{\n            const invoicesResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoices\", []);\n            if (invoicesResult.success) {\n                const savedInvoice = invoicesResult.data.find((inv)=>inv.id === invoiceWithId.id);\n                if (!savedInvoice) {\n                    console.error(\"❌ Invoice not found in storage after save attempt\");\n                }\n            } else {\n                console.error(\"❌ Error verifying save:\", invoicesResult.message);\n            }\n        }, 100);\n        setCurrentStep(\"preview\");\n    };\n    const handleEditInvoice = ()=>{\n        setCurrentStep(\"invoice\");\n    };\n    const handleExportPDF = async ()=>{\n        if (currentInvoice) {\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__.generateInvoicePDF)(currentInvoice);\n            } catch (error) {\n                alert(\"حدث خطأ أثناء تصدير PDF: \" + error.message);\n            }\n        }\n    };\n    const handleStartOver = ()=>{\n        console.log(\"\\uD83D\\uDD04 بداية جديدة - إعادة تعيين النظام\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n        setCurrentStep(\"client\");\n        setCurrentPage(\"home\");\n    };\n    const handleManageInvoices = ()=>{\n        console.log(\"\\uD83D\\uDCCB الانتقال لصفحة إدارة الفواتير\");\n        setCurrentPage(\"invoices\");\n    };\n    const handleCreateInvoice = ()=>{\n        console.log(\"➕ بدء إنشاء فاتورة جديدة\");\n        setCurrentPage(\"home\");\n        setCurrentStep(\"client\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n    };\n    const handleDataProtection = ()=>{\n        console.log(\"\\uD83D\\uDEE1️ الانتقال لصفحة حماية البيانات\");\n        router.push(\"/data-protection\");\n    };\n    const handleStatistics = ()=>{\n        console.log(\"\\uD83D\\uDCCA الانتقال لصفحة الإحصائيات الشاملة\");\n        router.push(\"/statistics\");\n    };\n    const handleCustomerManagement = ()=>{\n        console.log(\"\\uD83D\\uDC65 الانتقال لصفحة إدارة العملاء\");\n        router.push(\"/customers\");\n    };\n    const handleCustomerCodes = ()=>{\n        console.log(\"\\uD83D\\uDD22 الانتقال لصفحة إدارة أكواد العملاء\");\n        router.push(\"/customer-codes\");\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case \"client\":\n                return \"إدخال بيانات العميل\";\n            case \"invoice\":\n                return \"إنشاء الفاتورة\";\n            case \"preview\":\n                return \"معاينة الفاتورة\";\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"نظام إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm hidden sm:block\",\n                                                children: \"إدارة شاملة للفواتير والخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex gap-2 sm:gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateInvoice,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"home\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إنشاء فاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إنشاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleManageInvoices,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"invoices\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerManagement,\n                                        className: \"bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة العملاء وأكواد العملاء\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerCodes,\n                                        className: \"bg-teal-500 hover:bg-teal-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة أكواد العملاء والعدادات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"أكواد العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDD22\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDataProtection,\n                                        className: \"bg-purple-500 hover:bg-purple-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"لوحة تحكم حماية البيانات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"حماية البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDEE1️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStatistics,\n                                        className: \"bg-indigo-500 hover:bg-indigo-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"الإحصائيات الشاملة والتقارير\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"الإحصائيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartOver,\n                                        className: \"bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"بداية جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 sm:space-x-12 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"client\" ? \"text-blue-600 scale-105\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"client\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"invoice\" || currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"بيانات العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"invoice\" ? \"text-blue-600 scale-105\" : currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"invoice\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"إنشاء الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"preview\" ? \"text-blue-600 scale-105\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"preview\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : \"bg-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"معاينة وتصدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري المعاينة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"التقدم: \",\n                                                currentStep === \"client\" ? \"33%\" : currentStep === \"invoice\" ? \"66%\" : \"100%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                    children: [\n                        currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 sm:mb-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full shadow-md \".concat(currentStep === \"client\" ? \"bg-blue-600\" : currentStep === \"invoice\" ? \"bg-green-600\" : \"bg-purple-600\"),\n                                                        children: [\n                                                            currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl sm:text-3xl font-bold text-gray-800 mb-2\",\n                                                                children: getStepTitle(currentStep)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm sm:text-base leading-relaxed\",\n                                                                children: [\n                                                                    currentStep === \"client\" && \"أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة\",\n                                                                    currentStep === \"invoice\" && \"اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة\",\n                                                                    currentStep === \"preview\" && \"راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 rounded-lg border-l-4 \".concat(currentStep === \"client\" ? \"bg-blue-50 border-blue-400\" : currentStep === \"invoice\" ? \"bg-green-50 border-green-400\" : \"bg-purple-50 border-purple-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mt-0.5 flex-shrink-0 \".concat(currentStep === \"client\" ? \"text-blue-600\" : currentStep === \"invoice\" ? \"text-green-600\" : \"text-purple-600\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-blue-800 mb-2\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-blue-700 space-y-1 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تأكد من صحة رقم الهاتف للتواصل\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 475,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• اختر الخدمات المطلوبة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن ترك الحقول الاختيارية فارغة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكنك اختيار عميل موجود من النموذج أدناه\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 478,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-blue-100 border border-blue-200 rounded-lg p-3 mb-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-800 font-medium text-sm\",\n                                                                                                children: \"هل تريد إدارة العملاء المسجلين؟\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 483,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-600 text-xs mt-1\",\n                                                                                                children: \"يمكنك عرض وإدارة جميع العملاء المسجلين\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 484,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 482,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCustomerManagement,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 491,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 490,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \"إدارة العملاء\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-teal-100 border border-teal-200 rounded-lg p-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-teal-800 font-medium text-sm\",\n                                                                                                children: \"إدارة أكواد العملاء والعدادات\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 501,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-teal-600 text-xs mt-1\",\n                                                                                                children: \"تتبع وإدارة أكواد العملاء وإحصائيات النظام\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 502,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 500,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCustomerCodes,\n                                                                                        className: \"bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    fillRule: \"evenodd\",\n                                                                                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                                    clipRule: \"evenodd\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 509,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 508,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \"أكواد العملاء\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 504,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-green-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-green-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• أدخل تفاصيل كل خدمة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تحقق من الأسعار والكميات\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 522,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يتم حساب المجموع تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-purple-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-purple-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• راجع جميع البيانات قبل التصدير\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن طباعة الفاتورة مباشرة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• الفاتورة محفوظة تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInRight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: handleClientSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"invoice\" && currentClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInLeft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                client: currentClient,\n                                                onSubmit: handleInvoiceSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"preview\" && currentInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInUp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                invoice: currentInvoice,\n                                                onEdit: handleEditInvoice,\n                                                onExportPDF: handleExportPDF\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPage === \"invoices\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fadeIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 p-2 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"نظام إدارة الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm leading-relaxed\",\n                                            children: \"نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-blue-400\",\n                                            children: \"الخدمات المدعومة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الاستشارات والتصميم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الباترون والطباعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصنيع والعينات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الشحن والتسويق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصوير والمنتجات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-green-400\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"ترقيم تسلسلي للفواتير\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"تصدير PDF احترافي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"حساب المدفوعات التلقائي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"واجهة عربية متجاوبة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إدارة شاملة للعملاء\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"\\xa9 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-xs mt-2\",\n                                    children: \"تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SBp39x2T7Rlgt08WkQMecazsngQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});