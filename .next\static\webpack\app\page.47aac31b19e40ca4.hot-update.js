"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicePreview.tsx":
/*!*******************************************!*\
  !*** ./src/components/InvoicePreview.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/settingsStore */ \"(app-pages-browser)/./src/store/settingsStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// دالة عرض رسائل التأكيد المحسنة\nconst showSuccessMessage = (message)=>{\n    // إنشاء عنصر toast مؤقت\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    // إزالة الرسالة بعد 3 ثوان\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 3000);\n};\nconst showErrorMessage = (message)=>{\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 4000);\n};\nconst InvoicePreview = (param)=>{\n    let { invoice, onEdit, onExportPDF } = param;\n    _s();\n    const [showCompanySettings, setShowCompanySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPOSInvoice, setShowPOSInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { settings, loadSettings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore)();\n    // Load company settings on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        loadSettings\n    ]);\n    // دالة تصدير PDF محسنة مع رسائل التأكيد\n    const handleExportPDF = async ()=>{\n        setIsExporting(true);\n        try {\n            // إضافة كلاس التحسين للعنصر\n            const element = document.getElementById(\"invoice-content\");\n            if (element) {\n                element.classList.add(\"pdf-optimized\");\n            }\n            // محاولة الطريقة المحسنة أولاً\n            await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n            showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            // إزالة كلاس التحسين\n            if (element) {\n                element.classList.remove(\"pdf-optimized\");\n            }\n        } catch (error) {\n            console.error(\"PDF Export Error:\", error);\n            // محاولة الطريقة البديلة\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDFFromHTML)(invoice);\n                showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            } catch (fallbackError) {\n                console.error(\"Fallback PDF Export Error:\", fallbackError);\n                showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n            }\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن/تعبئة\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getPaymentMethodName = (method)=>{\n        const methods = {\n            cash: \"نقدي\",\n            card: \"بطاقة\",\n            transfer: \"تحويل\",\n            installment: \"تقسيط\"\n        };\n        return methods[method] || method;\n    };\n    const getContactMethodName = (method)=>{\n        const methods = {\n            whatsapp: \"واتساب\",\n            meta: \"ميتا\",\n            meeting: \"مقابلة\",\n            phone: \"هاتف\"\n        };\n        return methods[method] || method;\n    };\n    // Generate client code based on name and phone\n    const generateClientCode = (name, phone)=>{\n        const nameCode = name.substring(0, 3).toUpperCase();\n        const phoneCode = phone.substring(-4);\n        return \"\".concat(nameCode, \"-\").concat(phoneCode);\n    };\n    // دالة فتح صفحة الطباعة في تبويب جديد محسنة\n    const openPrintView = (invoice)=>{\n        try {\n            // إنشاء HTML للفاتورة مع تنسيق الطباعة\n            const printHTML = createPrintableInvoiceHTML(invoice);\n            // فتح نافذة جديدة\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(printHTML);\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم فتح حوار الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        showSuccessMessage(\"تم فتح صفحة الطباعة بنجاح! \\uD83D\\uDDA8️\");\n                    }, 500);\n                };\n            } else {\n                showErrorMessage(\"فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.\");\n            }\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تحضير صفحة الطباعة.\");\n        }\n    };\n    // دالة إنشاء HTML للطباعة\n    const createPrintableInvoiceHTML = (invoice)=>{\n        const logoSrc = settings.companyLogo || \"\";\n        return '\\n<!DOCTYPE html>\\n<html lang=\"ar\" dir=\"rtl\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>فاتورة '.concat(invoice.id, \" - \").concat(invoice.client.name, '</title>\\n    <style>\\n        @page {\\n            size: A4;\\n            margin: 15mm;\\n        }\\n\\n        * {\\n            margin: 0;\\n            padding: 0;\\n            box-sizing: border-box;\\n        }\\n\\n        body {\\n            font-family: Arial, sans-serif;\\n            direction: rtl;\\n            line-height: 1.4;\\n            color: #333;\\n            background: white;\\n        }\\n\\n        .invoice-container {\\n            max-width: 100%;\\n            margin: 0 auto;\\n            background: white;\\n            padding: 20px;\\n        }\\n\\n        /* Header Section */\\n        .header {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background: linear-gradient(to left, #f0f8ff, white);\\n            border: 2px solid #2196f3;\\n            border-radius: 10px;\\n        }\\n\\n        .invoice-info {\\n            flex: 1;\\n            text-align: right;\\n        }\\n\\n        .invoice-info h2 {\\n            color: #2196f3;\\n            font-size: 24px;\\n            margin-bottom: 15px;\\n            font-weight: bold;\\n        }\\n\\n        .info-box {\\n            background: transparent;\\n            padding: 15px;\\n            border: none;\\n            box-shadow: none;\\n            max-width: 400px;\\n        }\\n\\n        .info-row {\\n            margin-bottom: 8px;\\n            font-size: 14px;\\n        }\\n\\n        .info-label {\\n            font-weight: bold;\\n            color: #555;\\n        }\\n\\n        .info-value {\\n            color: #333;\\n        }\\n\\n        .logo-section {\\n            flex-shrink: 0;\\n            text-align: center;\\n            margin-left: 30px;\\n        }\\n\\n        .logo-section img {\\n            max-width: 180px;\\n            max-height: 180px;\\n            object-fit: contain;\\n        }\\n\\n        .company-description {\\n            margin-top: 15px;\\n            font-size: 13px;\\n            color: #666;\\n            max-width: 200px;\\n            line-height: 1.5;\\n        }\\n\\n        /* Services Table */\\n        .services-section {\\n            margin-bottom: 30px;\\n        }\\n\\n        .section-title {\\n            font-size: 18px;\\n            font-weight: bold;\\n            color: #333;\\n            margin-bottom: 15px;\\n            padding-bottom: 8px;\\n            border-bottom: 2px solid #2196f3;\\n        }\\n\\n        .services-table {\\n            width: 100%;\\n            border-collapse: separate;\\n            border-spacing: 0;\\n            border: 1px solid #ddd;\\n            margin: 15px 0;\\n            table-layout: fixed;\\n        }\\n\\n        .services-table th {\\n            background-color: #2196f3;\\n            color: white;\\n            padding: 12px 8px;\\n            text-align: center;\\n            font-weight: bold;\\n            border: 1px solid #ddd;\\n            font-size: 13px;\\n        }\\n\\n        .services-table td {\\n            padding: 10px 8px;\\n            border: 1px solid #ddd;\\n            text-align: center;\\n            vertical-align: middle;\\n            word-wrap: break-word;\\n            font-size: 12px;\\n            line-height: 1.3;\\n        }\\n\\n        .services-table tr:nth-child(even) {\\n            background-color: #f9f9f9;\\n        }\\n\\n        .services-table tr:nth-child(odd) {\\n            background-color: white;\\n        }\\n\\n        .service-type {\\n            color: #2196f3;\\n            font-weight: bold;\\n        }\\n\\n        .service-details {\\n            text-align: right;\\n            white-space: normal;\\n        }\\n\\n        .service-price {\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .service-total {\\n            font-weight: bold;\\n            color: #4caf50;\\n        }\\n\\n        /* Total Summary */\\n        .total-section {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: center;\\n            background: linear-gradient(to right, #e8f5e8, #f0f8f0);\\n            padding: 20px;\\n            border-radius: 10px;\\n            border: 2px solid #4caf50;\\n            margin-bottom: 30px;\\n        }\\n\\n        .total-details {\\n            text-align: right;\\n        }\\n\\n        .total-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 8px;\\n            min-width: 300px;\\n        }\\n\\n        .total-label {\\n            font-weight: bold;\\n            color: #555;\\n            margin-left: 20px;\\n        }\\n\\n        .total-value {\\n            font-weight: bold;\\n        }\\n\\n        .grand-total {\\n            font-size: 20px;\\n            color: #4caf50;\\n        }\\n\\n        .paid-amount {\\n            font-size: 16px;\\n            color: #2196f3;\\n        }\\n\\n        .remaining-amount {\\n            font-size: 16px;\\n        }\\n\\n        .remaining-positive {\\n            color: #f44336;\\n        }\\n\\n        .remaining-zero {\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp {\\n            background: transparent;\\n            color: #333;\\n            padding: 15px;\\n            border: 3px solid #333;\\n            border-radius: 15px;\\n            text-align: center;\\n            transform: rotate(3deg);\\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\\n            min-width: 120px;\\n        }\\n\\n        .payment-stamp.paid-full {\\n            border-color: #4caf50;\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp.paid-partial {\\n            border-color: #ff9800;\\n            color: #ff9800;\\n        }\\n\\n        .payment-stamp.unpaid {\\n            border-color: #f44336;\\n            color: #f44336;\\n        }\\n\\n        .stamp-title {\\n            font-size: 14px;\\n            font-weight: bold;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-status {\\n            font-size: 12px;\\n            font-weight: bold;\\n        }\\n\\n        /* Footer */\\n        .footer {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-top: 40px;\\n            padding: 20px;\\n            background: white;\\n            border-top: 1px solid #e9ecef;\\n            min-height: 120px;\\n            direction: rtl;\\n        }\\n\\n        .footer-left {\\n            width: 45%;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 10px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .footer-right {\\n            width: 50%;\\n            text-align: right;\\n            direction: rtl;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: flex-end;\\n            justify-content: flex-start;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 15px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .company-stamp {\\n            position: relative;\\n            transform: none;\\n            opacity: 1;\\n            border-radius: 8px;\\n            padding: 10px;\\n            width: 160px;\\n            height: auto;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            margin-bottom: 15px;\\n            background: white;\\n            border: 1px solid #ddd;\\n            box-shadow: none;\\n        }\\n\\n        .company-stamp img {\\n            width: 140px;\\n            height: auto;\\n            max-height: 100px;\\n            object-fit: contain;\\n            border-radius: 4px;\\n        }\\n\\n        .company-stamp .stamp-content {\\n            background: rgba(33, 150, 243, 0.1);\\n            border: 2px solid #2196f3;\\n            border-radius: 8px;\\n            width: 100%;\\n            height: 100px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n\\n        .stamp-content {\\n            text-align: center;\\n            color: #2196f3;\\n            font-weight: bold;\\n            font-size: 14px;\\n            line-height: 1.2;\\n            direction: rtl;\\n        }\\n\\n        .stamp-title {\\n            font-size: 16px;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-subtitle {\\n            font-size: 12px;\\n            opacity: 0.8;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #555;\\n            text-align: right;\\n            line-height: 1.6;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .thank-you {\\n            text-align: center;\\n            margin-top: 20px;\\n            padding: 15px;\\n            background: #f8f9fa;\\n            border: 1px solid #e9ecef;\\n            border-radius: 8px;\\n            color: #2196f3;\\n            font-size: 14px;\\n            font-weight: bold;\\n            direction: rtl;\\n        }\\n\\n        /* Print specific styles */\\n        @media print {\\n            body {\\n                -webkit-print-color-adjust: exact;\\n                print-color-adjust: exact;\\n                margin: 0;\\n                padding: 0;\\n            }\\n\\n            .invoice-container {\\n                padding: 0;\\n                margin: 0;\\n                max-width: 100%;\\n            }\\n\\n            .header {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-table {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n            }\\n\\n            .total-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .footer {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                position: relative;\\n                clear: both;\\n                margin-top: 30px;\\n            }\\n\\n            .footer-left,\\n            .footer-right {\\n                float: none;\\n                display: inline-block;\\n                vertical-align: top;\\n            }\\n\\n            .company-stamp {\\n                transform: none;\\n            }\\n\\n            .thank-you {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-top: 15px;\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\"invoice-container\">\\n        <!-- Header Section -->\\n        <div class=\"header\">\\n            <div class=\"invoice-info\">\\n                <h2>فاتورة خدمات</h2>\\n                <div class=\"info-box\">\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الفاتورة: </span>\\n                        <span class=\"info-value\">').concat(invoice.id, '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">التاريخ: </span>\\n                        <span class=\"info-value\">').concat(formatDate(invoice.createdAt), '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">اسم العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.name, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.businessName ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">الاسم التجاري: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.businessName, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الهاتف: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.phone, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.province ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">المحافظة: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.province, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">كود العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone), '</span>\\n                    </div>\\n                    <div class=\"info-row\" style=\"border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;\">\\n                        <span class=\"info-label\">وسيلة التواصل: </span>\\n                        <span class=\"info-value\">').concat(getContactMethodName(invoice.client.contactMethod), '</span>\\n                    </div>\\n                </div>\\n            </div>\\n\\n            <div class=\"logo-section\">\\n                ').concat(logoSrc ? '<img src=\"'.concat(logoSrc, '\" alt=\"شعار الشركة\">') : '\\n                <div style=\"width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9;\">\\n                    <span style=\"color: #999;\">شعار الشركة</span>\\n                </div>\\n                ', '\\n                <div class=\"company-description\">\\n                    ').concat(settings.description || \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\", '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <!-- Services Section -->\\n        <div class=\"services-section\">\\n            <h3 class=\"section-title\">تفاصيل الخدمات والمنتجات</h3>\\n\\n            <table class=\"services-table\">\\n                <thead>\\n                    <tr>\\n                        <th style=\"width: 50px;\">م</th>\\n                        <th style=\"width: 120px;\">نوع الخدمة</th>\\n                        <th style=\"width: 200px;\">التفاصيل</th>\\n                        <th style=\"width: 100px;\">الكمية/المقاس</th>\\n                        <th style=\"width: 120px;\">السعر</th>\\n                        <th style=\"width: 120px;\">المجموع</th>\\n                    </tr>\\n                </thead>\\n                <tbody>\\n                    ').concat(invoice.services.map((service, index)=>{\n            // Calculate service details (same logic as in component)\n            let serviceTotal = 0;\n            let serviceDetails = \"\";\n            let quantity = \"\";\n            let unitPrice = 0;\n            if (service.serviceType === \"consultation\") {\n                const consultationService = service;\n                serviceTotal = consultationService.cost || 0;\n                serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                unitPrice = consultationService.cost || 0;\n            } else if (service.serviceType === \"pattern\") {\n                var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                const patternService = service;\n                serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n            } else if (service.serviceType === \"pattern_printing\") {\n                var _printingService_files, _printingService_files1, _printingService_files2;\n                const printingService = service;\n                serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n            } else if (service.serviceType === \"shipping\") {\n                const shippingService = service;\n                serviceTotal = shippingService.total || 0;\n                serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                quantity = \"\".concat(shippingService.quantity || 1);\n                unitPrice = shippingService.unitPrice || 0;\n            } else if (service.serviceType === \"manufacturing\") {\n                var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                const manufacturingService = service;\n                serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n            } else if (service.serviceType === \"products\") {\n                var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                const productsService = service;\n                let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                // طرح الخصم إن وجد\n                if (productsService.discountAmount) {\n                    productsSubtotal -= productsService.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n            } else {\n                serviceTotal = service.cost || service.total || 0;\n                serviceDetails = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n            }\n            return \"\\n                        <tr>\\n                            <td>\".concat(index + 1, '</td>\\n                            <td class=\"service-type\">').concat(getServiceName(service.serviceType), '</td>\\n                            <td class=\"service-details\">').concat(serviceDetails, \"</td>\\n                            <td>\").concat(quantity, '</td>\\n                            <td class=\"service-price\">').concat(formatCurrency(unitPrice), '</td>\\n                            <td class=\"service-total\">').concat(formatCurrency(serviceTotal), \"</td>\\n                        </tr>\\n                      \");\n        }).join(\"\"), '\\n                </tbody>\\n            </table>\\n        </div>\\n\\n        <!-- Total Summary -->\\n        <div class=\"total-section\">\\n            <div class=\"total-details\">\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المجموع الكلي:</span>\\n                    <span class=\"total-value grand-total\">').concat(formatCurrency(invoice.total), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المدفوع:</span>\\n                    <span class=\"total-value paid-amount\">').concat(formatCurrency(invoice.paidAmount || 0), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المتبقي:</span>\\n                    <span class=\"total-value remaining-amount ').concat((invoice.remainingAmount || 0) > 0 ? \"remaining-positive\" : \"remaining-zero\", '\">\\n                        ').concat(formatCurrency(invoice.remainingAmount || 0), '\\n                    </span>\\n                </div>\\n                <div class=\"total-row\" style=\"border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;\">\\n                    <span class=\"total-label\">طريقة الدفع:</span>\\n                    <span class=\"total-value\">').concat(getPaymentMethodName(invoice.paymentMethod), \"</span>\\n                </div>\\n                \").concat(invoice.services.some((service)=>{\n            if (service.serviceType === \"pattern\") {\n                var _service_models;\n                return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n            }\n            if (service.serviceType === \"manufacturing\") {\n                var _service_models1;\n                return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n            }\n            return false;\n        }) ? '\\n                <div class=\"total-row\" style=\"border-top: 1px solid #ff9800; padding-top: 8px; margin-top: 8px; color: #ff9800;\">\\n                    <span class=\"total-label\" style=\"font-size: 12px;\">ملاحظة:</span>\\n                    <span class=\"total-value\" style=\"font-size: 12px;\">يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر</span>\\n                </div>\\n                ' : \"\", '\\n            </div>\\n\\n            <div class=\"payment-stamp ').concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"paid-full\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"paid-partial\" : \"unpaid\", '\">\\n                <div class=\"stamp-title\">OKA Group</div>\\n                <div class=\"stamp-status\">').concat(invoice.paymentStatus || \"غير محدد\", '</div>\\n            </div>\\n        </div>\\n\\n        <!-- Footer -->\\n        <div class=\"footer\">\\n            <!-- Right side: Contact Information -->\\n            <div class=\"footer-left\">\\n                <div class=\"contact-info\">\\n                    <div><strong>معلومات التواصل:</strong></div>\\n                    <div>\\uD83D\\uDCE7 البريد الإلكتروني: <EMAIL></div>\\n                    <div>\\uD83D\\uDCDE للشكاوى والاقتراحات: 0114954118</div>\\n                </div>\\n            </div>\\n\\n            <!-- Left side: Headquarters + Company Stamp -->\\n            <div class=\"footer-right\">\\n                <div class=\"headquarters-info\">\\n                    <div><strong>المقر الإداري:</strong></div>\\n                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>\\n                </div>\\n\\n                <div class=\"company-stamp\">\\n                    ').concat(settings.companyStamp ? '\\n                    <img\\n                        src=\"'.concat(settings.companyStamp, '\"\\n                        alt=\"ختم الشركة\"\\n                    >\\n                    ') : '\\n                    <div class=\"stamp-content\">\\n                        <div class=\"stamp-title\">OKA</div>\\n                        <div class=\"stamp-subtitle\">GROUP</div>\\n                    </div>\\n                    ', '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div class=\"thank-you\">\\n            شكراً لتعاملكم معنا\\n        </div>\\n    </div>\\n</body>\\n</html>\\n    ');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl sm:text-3xl font-bold text-gray-800\",\n                        children: \"معاينة الفاتورة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompanySettings(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"إعدادات الشركة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"تعديل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportPDF,\n                                disabled: isExporting,\n                                className: \"btn-success \".concat(isExporting ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                children: isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 animate-spin\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"جاري التصدير...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"تصدير PDF\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>openPrintView(invoice),\n                                className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"طباعة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowPOSInvoice(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"فاتورة POS\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 963,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"invoice-content\",\n                className: \"bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden print-break-inside-avoid\",\n                style: {\n                    fontFamily: \"Arial, sans-serif\",\n                    lineHeight: \"1.4\",\n                    position: \"relative\",\n                    zIndex: 1,\n                    direction: \"rtl\",\n                    width: \"100%\",\n                    maxWidth: \"794px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-blue-50 via-white to-blue-50 p-8 border-b-4 border-blue-300 print-header print-break-inside-avoid relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -translate-y-32 translate-x-32\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 w-48 h-48 bg-blue-100 rounded-full translate-y-24 -translate-x-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: settings.companyLogo,\n                                                                alt: \"شعار الشركة\",\n                                                                className: \"w-56 h-56 object-contain mx-auto drop-shadow-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-48 h-4 bg-blue-200 rounded-full opacity-30 blur-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-56 h-56 border-3 border-dashed border-blue-300 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-50 to-white mx-auto shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-blue-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-24 h-24 mx-auto mb-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1016,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: \"شعار الشركة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1018,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl shadow-lg mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: \"OKA GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-100 text-sm\",\n                                                            children: \"مجموعة أوكا للخدمات المتكاملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1027,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-xs mx-auto bg-white p-4 rounded-lg shadow-sm border border-blue-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 leading-relaxed font-medium\",\n                                                        children: settings.description || \"نقدم خدمات متكاملة في مجال التصميم والتصنيع والاستشارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-right space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-4xl font-bold text-blue-600 mb-2\",\n                                                            children: \"فاتورة خدمات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-6 rounded-2xl shadow-lg border-2 border-blue-100 max-w-md ml-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-blue-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-blue-600\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            invoice.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"رقم الفاتورة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800 font-medium\",\n                                                                        children: formatDate(invoice.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"التاريخ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t-2 border-blue-100 my-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-bold text-gray-700 text-center mb-3 bg-blue-50 py-2 rounded-lg\",\n                                                                        children: \"بيانات العميل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1070,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1069,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.businessName\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1076,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم التجاري:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1077,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1075,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1082,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الهاتف:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1083,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1081,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.province && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.province\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1088,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"المحافظة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1089,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1087,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1094,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"كود العميل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1095,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1093,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between pt-2 border-t border-gray-200\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: getContactMethodName(invoice.client.contactMethod)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1099,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"وسيلة التواصل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1100,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1047,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-t-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-center flex items-center justify-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"تفاصيل الخدمات والمنتجات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto print-break-inside-avoid bg-white rounded-b-xl shadow-lg border-2 border-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-16\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-white text-blue-600 rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm\",\n                                                                        children: \"م\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1138,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1137,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"نوع الخدمة\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1135,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-48\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1146,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1145,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"التفاصيل\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-28\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1154,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"الكمية\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1151,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1162,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1163,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1161,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"السعر\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1160,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1170,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"المجموع\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: invoice.services.map((service, index)=>{\n                                                        // Calculate service total based on service type\n                                                        let serviceTotal = 0;\n                                                        let serviceDetails = \"\";\n                                                        let quantity = \"\";\n                                                        let unitPrice = 0;\n                                                        if (service.serviceType === \"consultation\") {\n                                                            const consultationService = service;\n                                                            serviceTotal = consultationService.cost || 0;\n                                                            serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                                                            quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                                                            unitPrice = consultationService.cost || 0;\n                                                        } else if (service.serviceType === \"pattern\") {\n                                                            var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                                                            const patternService = service;\n                                                            serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                                                            quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                                                            unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"pattern_printing\") {\n                                                            var _printingService_files, _printingService_files1, _printingService_files2;\n                                                            const printingService = service;\n                                                            serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                                                            quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                                                            unitPrice = serviceTotal;\n                                                        } else if (service.serviceType === \"shipping\") {\n                                                            const shippingService = service;\n                                                            serviceTotal = shippingService.total || 0;\n                                                            serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                                                            quantity = \"\".concat(shippingService.quantity || 1);\n                                                            unitPrice = shippingService.unitPrice || 0;\n                                                        } else if (service.serviceType === \"manufacturing\") {\n                                                            var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                                                            const manufacturingService = service;\n                                                            serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                                                                return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                                                            }, 0)) || 0;\n                                                            serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                                                            quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"products\") {\n                                                            var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                                                            const productsService = service;\n                                                            let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                                                            // طرح الخصم إن وجد\n                                                            if (productsService.discountAmount) {\n                                                                productsSubtotal -= productsService.discountAmount || 0;\n                                                            }\n                                                            serviceTotal = Math.max(0, productsSubtotal);\n                                                            serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                                                            quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                                                        } else {\n                                                            // Default for other service types\n                                                            serviceTotal = service.cost || service.total || 0;\n                                                            serviceDetails = getServiceName(service.serviceType);\n                                                            quantity = \"1\";\n                                                            unitPrice = serviceTotal;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            style: {\n                                                                backgroundColor: index % 2 === 0 ? \"#f9f9f9\" : \"white\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\"\n                                                                    },\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1244,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\",\n                                                                        color: \"#2196f3\",\n                                                                        fontWeight: \"bold\"\n                                                                    },\n                                                                    children: getServiceName(service.serviceType)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"right\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        maxWidth: \"200px\"\n                                                                    },\n                                                                    children: serviceDetails\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1274,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\"\n                                                                    },\n                                                                    children: quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: \"#2196f3\"\n                                                                    },\n                                                                    children: formatCurrency(unitPrice)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1302,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: \"#4caf50\"\n                                                                    },\n                                                                    children: formatCurrency(serviceTotal)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, service.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 flex justify-between items-center bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border-2 border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المجموع الكلي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1345,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: formatCurrency(invoice.total)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المبلغ المدفوع:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1349,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatCurrency(invoice.paidAmount || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المبلغ المتبقي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold \".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                        children: formatCurrency(invoice.remainingAmount || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1352,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-2 border-t border-green-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"طريقة الدفع: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-800\",\n                                                        children: getPaymentMethodName(invoice.paymentMethod)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            invoice.services.some((service)=>{\n                                                if (service.serviceType === \"pattern\") {\n                                                    var _service_models;\n                                                    return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n                                                }\n                                                if (service.serviceType === \"manufacturing\") {\n                                                    var _service_models1;\n                                                    return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n                                                }\n                                                return false;\n                                            }) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-2 border-t border-orange-300 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-orange-600 font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-700\",\n                                                            children: \"ملاحظة: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1373,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1372,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1371,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1343,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 ml-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative rounded-2xl p-6 text-center shadow-xl transform rotate-3 border-4 bg-transparent \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"border-green-500 text-green-600\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"border-orange-500 text-orange-600\" : \"border-red-500 text-red-600\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-lg mb-1\",\n                                                        children: \"OKA Group\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-sm\",\n                                                        children: invoice.paymentStatus || \"غير محدد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1387,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1382,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1381,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 11\n                            }, undefined),\n                            (invoice.requiresShipping || invoice.shippingAddress || invoice.notes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-lg border border-yellow-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-yellow-700 mb-4\",\n                                            children: \"معلومات إضافية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700\",\n                                                            children: \"يتطلب شحن:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1405,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600 font-semibold\",\n                                                            children: \"نعم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1406,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1404,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.shippingAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-2 border-t border-yellow-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700 block mb-1\",\n                                                            children: \"عنوان الشحن:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-800 text-sm\",\n                                                            children: invoice.shippingAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1413,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1411,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-2 border-t border-yellow-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700 block mb-1\",\n                                                            children: \"ملاحظات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1419,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-800 text-sm\",\n                                                            children: invoice.notes\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1420,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1402,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1400,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1399,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1112,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-t border-gray-200 p-6 relative\",\n                        style: {\n                            direction: \"rtl\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-gray-700 mb-3\",\n                                                children: \"معلومات التواصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1434,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"البريد الإلكتروني: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1439,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600\",\n                                                                children: settings.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1438,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1442,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"للشكاوى والاقتراحات: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1451,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600 font-bold\",\n                                                                children: settings.complaintsPhone\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1452,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-green-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1455,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1433,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-semibold text-gray-700\",\n                                                        children: \"المقر الإداري: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1464,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: settings.address\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1465,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1463,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end mt-4\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 bg-white border border-gray-300 rounded-lg p-2 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: settings.companyStamp,\n                                                        alt: \"ختم الشركة\",\n                                                        className: \"max-w-full max-h-full object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1472,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1471,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 border-2 border-dashed border-gray-400 rounded-lg flex items-center justify-center bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 mx-auto mb-1\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1482,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs\",\n                                                                children: \"ختم الشركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1484,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1480,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1479,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1461,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center bg-blue-50 p-3 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 font-bold\",\n                                        children: \"شكراً لتعاملكم معنا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1495,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1494,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1493,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1430,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 976,\n                columnNumber: 7\n            }, undefined),\n            showCompanySettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"إعدادات الشركة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1508,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1514,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1509,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1507,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1506,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.companyName,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        companyName: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1524,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1540,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: settings.email,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        email: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1539,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"هاتف الشكاوى والاقتراحات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                value: settings.complaintsPhone,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        complaintsPhone: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رقم هاتف الشكاوى\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1554,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.address,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        address: e.target.value\n                                                    });\n                                                },\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1585,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.slogan,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        slogan: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1586,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1584,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1600,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.wishMessage,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        wishMessage: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1601,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1599,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1615,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.description,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        description: e.target.value\n                                                    });\n                                                },\n                                                rows: 2,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1616,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1614,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1630,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors\",\n                                                children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyLogo,\n                                                            alt: \"شعار الشركة\",\n                                                            className: \"w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1634,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1639,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1633,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1665,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1664,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع شعار الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1667,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1668,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1663,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1631,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"ختم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1697,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyStamp,\n                                                            alt: \"ختم الشركة\",\n                                                            className: \"w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1701,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1706,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1700,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-12 h-12 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1731,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع ختم الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1734,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1735,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1730,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1698,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1522,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1521,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-6 py-4 rounded-b-lg border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"سيتم حفظ الإعدادات تلقائياً عند التغيير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1770,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1766,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1765,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                    lineNumber: 1504,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1503,\n                columnNumber: 9\n            }, undefined),\n            showPOSInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                invoice: invoice,\n                onClose: ()=>setShowPOSInvoice(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1784,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n        lineNumber: 913,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicePreview, \"rxHFl2SkeEMTva1xN8F43PiUNL8=\", false, function() {\n    return [\n        _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore\n    ];\n});\n_c = InvoicePreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicePreview);\nvar _c;\n$RefreshReg$(_c, \"InvoicePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicePreview.tsx\n"));

/***/ })

});