/**
 * دوال التنسيق المشتركة
 * Shared formatting utilities
 */

/**
 * تنسيق العملة مع التحقق من صحة البيانات
 * Format currency with data validation
 * 
 * @param amount - المبلغ (رقم، نص، أو قيمة فارغة)
 * @returns نص منسق بالعملة المصرية
 */
export const formatCurrency = (amount: number | string | undefined | null): string => {
  // التحقق من صحة القيمة وتحويلها لرقم
  const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));
  
  // التحقق من أن النتيجة رقم صحيح
  if (isNaN(numericAmount)) {
    console.warn('⚠️ Invalid amount passed to formatCurrency:', amount);
    return '0.00 جنيه';
  }
  
  return `${numericAmount.toFixed(2)} جنيه`;
};

/**
 * تنسيق التاريخ بالتقويم الميلادي
 * Format date using Gregorian calendar
 * 
 * @param dateString - نص التاريخ
 * @param format - نوع التنسيق ('short' | 'long')
 * @returns تاريخ منسق بالعربية
 */
export const formatDate = (dateString: string, format: 'short' | 'long' = 'short'): string => {
  try {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: format === 'long' ? 'long' : 'short',
      day: 'numeric',
      calendar: 'gregory', // التقويم الميلادي
    });
  } catch (error) {
    console.warn('⚠️ Invalid date passed to formatDate:', dateString);
    return 'تاريخ غير صحيح';
  }
};

/**
 * تنسيق الرقم مع فواصل الآلاف
 * Format number with thousands separators
 * 
 * @param num - الرقم المراد تنسيقه
 * @returns رقم منسق مع فواصل
 */
export const formatNumber = (num: number | string | undefined | null): string => {
  const numericValue = typeof num === 'number' ? num : parseFloat(String(num || 0));
  
  if (isNaN(numericValue)) {
    console.warn('⚠️ Invalid number passed to formatNumber:', num);
    return '0';
  }
  
  return numericValue.toLocaleString('ar-EG');
};

/**
 * تنسيق النسبة المئوية
 * Format percentage
 * 
 * @param value - القيمة (0-1 أو 0-100)
 * @param isDecimal - هل القيمة عشرية (0-1) أم مئوية (0-100)
 * @returns نسبة مئوية منسقة
 */
export const formatPercentage = (value: number | string | undefined | null, isDecimal: boolean = true): string => {
  const numericValue = typeof value === 'number' ? value : parseFloat(String(value || 0));
  
  if (isNaN(numericValue)) {
    console.warn('⚠️ Invalid percentage passed to formatPercentage:', value);
    return '0%';
  }
  
  const percentage = isDecimal ? numericValue * 100 : numericValue;
  return `${percentage.toFixed(1)}%`;
};

/**
 * تنظيف النص للاستخدام في أسماء الملفات
 * Sanitize text for filename usage
 * 
 * @param text - النص المراد تنظيفه
 * @returns نص منظف وآمن للاستخدام في أسماء الملفات
 */
export const sanitizeFilename = (text: string): string => {
  return text
    .replace(/[^\w\s\u0600-\u06FF-]/g, '') // إزالة الأحرف الخاصة مع الاحتفاظ بالعربية
    .replace(/\s+/g, '_') // استبدال المسافات بـ _
    .trim();
};

/**
 * تحويل النص إلى عنوان (أول حرف كبير)
 * Convert text to title case
 * 
 * @param text - النص المراد تحويله
 * @returns نص بصيغة العنوان
 */
export const toTitleCase = (text: string): string => {
  return text
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * اختصار النص مع إضافة نقاط
 * Truncate text with ellipsis
 * 
 * @param text - النص المراد اختصاره
 * @param maxLength - الطول الأقصى
 * @returns نص مختصر مع نقاط إذا لزم الأمر
 */
export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * التحقق من صحة رقم الهاتف المصري
 * Validate Egyptian phone number
 * 
 * @param phone - رقم الهاتف
 * @returns true إذا كان الرقم صحيحاً
 */
export const isValidEgyptianPhone = (phone: string): boolean => {
  // أرقام الهاتف المصرية: 01xxxxxxxxx (11 رقم)
  const phoneRegex = /^01[0-9]{9}$/;
  return phoneRegex.test(phone.replace(/\s+/g, ''));
};

/**
 * تنسيق رقم الهاتف المصري
 * Format Egyptian phone number
 * 
 * @param phone - رقم الهاتف
 * @returns رقم هاتف منسق
 */
export const formatEgyptianPhone = (phone: string): string => {
  const cleanPhone = phone.replace(/\s+/g, '');
  
  if (cleanPhone.length === 11 && cleanPhone.startsWith('01')) {
    return `${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 7)} ${cleanPhone.substring(7)}`;
  }
  
  return phone; // إرجاع الرقم كما هو إذا لم يكن بالصيغة المتوقعة
};
