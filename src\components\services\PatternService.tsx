'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { PatternService, PatternModel } from '@/types';

interface PatternServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const PatternServiceComponent: React.FC<PatternServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as PatternService;
  const isEnabled = serviceData?.enabled || false;

  // الحصول على بيانات العميل لاستخدام اسم السيلز
  const clientSalesperson = values.client?.salesperson || '';

  const paymentMethods = [
    { value: 'cash', label: 'نقدي' },
    { value: 'card', label: 'بطاقة' },
    { value: 'transfer', label: 'تحويل' },
    { value: 'installment', label: 'تقسيط' },
  ];

  const defaultModel: PatternModel = {
    modelName: '',
    sizesCount: 1,
    cost: 0,
    deposit: 0,
    finalAmount: 0,
    printOption: false,
    paymentMethod: '',
    salesperson: clientSalesperson, // تعبئة تلقائية من بيانات العميل
    shippingInfo: '',
  };

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'pattern');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              // تعبئة النموذج الافتراضي مع اسم السيلز من العميل
              const modelWithSalesperson = { ...defaultModel, salesperson: clientSalesperson };
              setFieldValue(`${namePrefix}.models`, [modelWithSalesperson]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة الباترون</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-green-500">
          <FieldArray name={`${namePrefix}.models`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">النماذج</h3>
                  <button
                    type="button"
                    onClick={() => {
                      const modelWithSalesperson = { ...defaultModel, salesperson: clientSalesperson };
                      push(modelWithSalesperson);
                    }}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة نموذج جديد
                  </button>
                </div>

                {serviceData?.models?.map((model, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">النموذج {index + 1}</h4>
                      {serviceData.models.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* اسم النموذج */}
                      <div className="form-field">
                        <label className="form-label">اسم النموذج</label>
                        <Field
                          name={`${namePrefix}.models.${index}.modelName`}
                          type="text"
                          className="form-input"
                          placeholder="أدخل اسم النموذج"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.modelName`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* عدد المقاسات */}
                      <div className="form-field">
                        <label className="form-label">عدد المقاسات</label>
                        <Field
                          name={`${namePrefix}.models.${index}.sizesCount`}
                          type="number"
                          min="1"
                          className="form-input"
                          placeholder="عدد المقاسات"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.sizesCount`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* التكلفة */}
                      <div className="form-field">
                        <label className="form-label">التكلفة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.cost`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="التكلفة الإجمالية"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.cost`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* العربون */}
                      <div className="form-field">
                        <label className="form-label">العربون (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.deposit`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="مبلغ العربون"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.deposit`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* المبلغ النهائي */}
                      <div className="form-field">
                        <label className="form-label">المبلغ النهائي (جنيه)</label>
                        <Field
                          name={`${namePrefix}.models.${index}.finalAmount`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="المبلغ النهائي"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.finalAmount`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* طريقة الدفع */}
                      <div className="form-field">
                        <label className="form-label">طريقة الدفع</label>
                        <Field 
                          as="select" 
                          name={`${namePrefix}.models.${index}.paymentMethod`} 
                          className="form-input"
                        >
                          <option value="">اختر طريقة الدفع</option>
                          {paymentMethods.map((method) => (
                            <option key={method.value} value={method.value}>
                              {method.label}
                            </option>
                          ))}
                        </Field>
                        <ErrorMessage 
                          name={`${namePrefix}.models.${index}.paymentMethod`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* اسم السيلز */}
                      <div className="form-field">
                        <label className="form-label">اسم السيلز</label>
                        <Field
                          name={`${namePrefix}.models.${index}.salesperson`}
                          type="text"
                          className="form-input bg-gray-100"
                          placeholder="اسم السيلز المسؤول"
                          value={clientSalesperson}
                          readOnly
                        />
                        <div className="text-xs text-gray-500 mt-1">
                          يتم تعبئة هذا الحقل تلقائياً من بيانات العميل
                        </div>
                      </div>
                    </div>

                    {/* خيار الطباعة */}
                    <div className="form-field mt-4">
                      <label className="flex items-center">
                        <Field
                          type="checkbox"
                          name={`${namePrefix}.models.${index}.printOption`}
                          className="form-checkbox"
                        />
                        <span className="text-sm">يتطلب طباعة</span>
                      </label>
                    </div>

                    {/* معلومات الشحن */}
                    <div className="form-field">
                      <label className="form-label">معلومات الشحن</label>
                      <Field
                        name={`${namePrefix}.models.${index}.shippingInfo`}
                        as="textarea"
                        rows="2"
                        className="form-input"
                        placeholder="معلومات الشحن (اختياري)"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>
        </div>
      )}
    </div>
  );
};

export default PatternServiceComponent;
