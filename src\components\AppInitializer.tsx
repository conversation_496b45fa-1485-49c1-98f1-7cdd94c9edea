'use client';

import React, { useEffect, useState } from 'react';
import { initializeApp } from '@/utils/appInitializer';
import ErrorBoundary from './ErrorBoundary';

interface AppInitializerProps {
  children: React.ReactNode;
}

interface InitializationState {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  details: any;
}

const AppInitializer: React.FC<AppInitializerProps> = ({ children }) => {
  const [state, setState] = useState<InitializationState>({
    isInitialized: false,
    isLoading: true,
    error: null,
    details: null
  });

  useEffect(() => {
    const initialize = async () => {
      try {
        console.log('🚀 بدء تهيئة التطبيق...');
        
        // تهيئة التطبيق
        const initResult = await initializeApp();
        
        if (initResult.success) {
          console.log('✅ تم تهيئة التطبيق بنجاح');
          setState({
            isInitialized: true,
            isLoading: false,
            error: null,
            details: initResult.details
          });
        } else {
          console.warn('⚠️ تم تهيئة التطبيق مع بعض المشاكل');
          setState({
            isInitialized: true, // نسمح بالمتابعة حتى مع وجود مشاكل
            isLoading: false,
            error: initResult.message,
            details: initResult.details
          });
        }
      } catch (error) {
        console.error('💥 فشل حرج في تهيئة التطبيق:', error);
        setState({
          isInitialized: false,
          isLoading: false,
          error: error instanceof Error ? error.message : 'خطأ غير معروف',
          details: null
        });
      }
    };

    initialize();
  }, []);

  // شاشة التحميل
  if (state.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">تهيئة التطبيق</h2>
            <p className="text-gray-600 text-sm">جاري تحميل النظام الموحد...</p>
            <div className="mt-4 bg-gray-100 rounded-lg p-3">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span>تهيئة المتاجر والبيانات</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // شاشة الخطأ الحرج
  if (!state.isInitialized && state.error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
              </svg>
            </div>
            <h2 className="text-xl font-bold text-red-800 mb-2">فشل في تهيئة التطبيق</h2>
            <p className="text-red-600 text-sm mb-4">{state.error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  // تحذير في حالة وجود مشاكل جزئية
  const hasPartialIssues = state.error && state.isInitialized;

  return (
    <ErrorBoundary>
      {hasPartialIssues && (
        <div className="bg-yellow-50 border-b border-yellow-200 p-3">
          <div className="max-w-7xl mx-auto flex items-center gap-3">
            <svg className="w-5 h-5 text-yellow-600 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
            </svg>
            <div className="flex-1">
              <p className="text-sm text-yellow-800">
                <span className="font-medium">تحذير:</span> {state.error}
              </p>
            </div>
            <button
              onClick={() => setState(prev => ({ ...prev, error: null }))}
              className="text-yellow-600 hover:text-yellow-800 p-1"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      )}
      {children}
    </ErrorBoundary>
  );
};

export default AppInitializer;
