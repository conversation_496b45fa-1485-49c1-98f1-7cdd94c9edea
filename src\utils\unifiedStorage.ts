/**
 * مدير التخزين الموحد
 * يضمن استخدام secureStorage في جميع العمليات مع حماية شاملة
 */

import { secureGetItem, secureSetItem, createBackup, checkStorageHealth } from './secureStorage';
import { STORAGE_KEYS, STORAGE_CATEGORIES, getKeyCategory } from './storageKeys';

/**
 * نتيجة عملية التخزين
 */
interface StorageResult<T = any> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
}

/**
 * إحصائيات عملية الدمج
 */
interface MergeStats {
  success: boolean;
  merged: string[];
  errors: string[];
  totalSize: number;
  duplicatesRemoved: number;
}

/**
 * مدير التخزين الموحد
 * يوفر واجهة موحدة لجميع عمليات التخزين
 */
export class UnifiedStorageManager {
  
  /**
   * حفظ آمن مع التحقق من المفتاح
   */
  static async setItem<T>(key: string, data: T): Promise<StorageResult<T>> {
    const timestamp = new Date().toISOString();
    
    try {
      // التحقق من صحة المفتاح
      const category = getKeyCategory(key);
      if (category === 'UNKNOWN' && !key.startsWith(STORAGE_KEYS.BACKUP_PREFIX)) {
        console.warn(`⚠️ Using unknown storage key: ${key}`);
      }
      
      // استخدام النظام الآمن
      const result = secureSetItem(key, data);
      
      // تسجيل العملية
      const dataSize = JSON.stringify(data).length;
      console.log(`🔒 Unified Storage: Saved ${key}`, {
        success: result.success,
        dataSize: `${Math.round(dataSize / 1024 * 100) / 100} KB`,
        category,
        timestamp
      });
      
      return {
        success: result.success,
        data,
        message: result.message,
        timestamp
      };
      
    } catch (error) {
      const errorMessage = `فشل في حفظ ${key}: ${(error as Error).message}`;
      console.error('❌ Unified Storage Save Error:', error);
      
      return {
        success: false,
        message: errorMessage,
        timestamp
      };
    }
  }
  
  /**
   * قراءة آمنة مع التحقق من المفتاح
   */
  static async getItem<T>(key: string, defaultValue: T): Promise<StorageResult<T>> {
    const timestamp = new Date().toISOString();
    
    try {
      // التحقق من صحة المفتاح
      const category = getKeyCategory(key);
      if (category === 'UNKNOWN' && !key.startsWith(STORAGE_KEYS.BACKUP_PREFIX)) {
        console.warn(`⚠️ Reading from unknown storage key: ${key}`);
      }
      
      // استخدام النظام الآمن
      const result = secureGetItem(key, defaultValue);
      
      // تسجيل العملية
      console.log(`🔓 Unified Storage: Loaded ${key}`, {
        success: result.success,
        hasData: result.data !== defaultValue,
        category,
        timestamp
      });
      
      return {
        success: result.success,
        data: result.data,
        message: result.message || 'تم تحميل البيانات بنجاح',
        timestamp
      };
      
    } catch (error) {
      const errorMessage = `فشل في قراءة ${key}: ${(error as Error).message}`;
      console.error('❌ Unified Storage Read Error:', error);
      
      return {
        success: false,
        data: defaultValue,
        message: errorMessage,
        timestamp
      };
    }
  }
  
  /**
   * دمج البيانات المكررة من المفاتيح القديمة
   */
  static async mergeDuplicateKeys(): Promise<MergeStats> {
    const stats: MergeStats = {
      success: true,
      merged: [],
      errors: [],
      totalSize: 0,
      duplicatesRemoved: 0
    };
    
    try {
      console.log('🔄 بدء عملية دمج البيانات المكررة...');
      
      // دمج إعدادات الشركة
      await this.mergeCompanySettings(stats);
      
      // تنظيف المفاتيح القديمة
      await this.cleanupLegacyKeys(stats);
      
      // إنشاء نسخة احتياطية بعد الدمج
      if (stats.merged.length > 0) {
        const backupResult = createBackup();
        if (backupResult.success) {
          console.log('✅ تم إنشاء نسخة احتياطية بعد الدمج');
        }
      }
      
      console.log('✅ اكتملت عملية دمج البيانات:', stats);
      return stats;
      
    } catch (error) {
      stats.success = false;
      stats.errors.push(`خطأ عام في الدمج: ${(error as Error).message}`);
      console.error('❌ فشل في دمج البيانات:', error);
      return stats;
    }
  }
  
  /**
   * دمج إعدادات الشركة من المفاتيح المختلفة
   */
  private static async mergeCompanySettings(stats: MergeStats): Promise<void> {
    try {
      // قراءة الإعدادات الحالية
      const currentResult = await this.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});
      let mergedSettings = { ...currentResult.data };
      
      // دمج من company-settings
      const legacySettings = localStorage.getItem(STORAGE_KEYS.LEGACY_COMPANY_SETTINGS);
      if (legacySettings) {
        try {
          const parsed = JSON.parse(legacySettings);
          mergedSettings = { ...mergedSettings, ...parsed };
          stats.totalSize += legacySettings.length;
          console.log('📦 دمج بيانات من company-settings');
        } catch (error) {
          stats.errors.push(`خطأ في تحليل company-settings: ${(error as Error).message}`);
        }
      }
      
      // دمج من companyInfo
      const companyInfo = localStorage.getItem(STORAGE_KEYS.LEGACY_COMPANY_INFO);
      if (companyInfo) {
        try {
          const parsed = JSON.parse(companyInfo);
          mergedSettings = { ...mergedSettings, ...parsed };
          stats.totalSize += companyInfo.length;
          console.log('📦 دمج بيانات من companyInfo');
        } catch (error) {
          stats.errors.push(`خطأ في تحليل companyInfo: ${(error as Error).message}`);
        }
      }
      
      // حفظ الإعدادات المدموجة إذا كان هناك تغيير
      if (legacySettings || companyInfo) {
        const saveResult = await this.setItem(STORAGE_KEYS.COMPANY_SETTINGS, mergedSettings);
        if (saveResult.success) {
          stats.merged.push('companySettings');
          console.log('✅ تم دمج إعدادات الشركة بنجاح');
        } else {
          stats.errors.push('فشل في حفظ إعدادات الشركة المدموجة');
        }
      }
      
    } catch (error) {
      stats.errors.push(`خطأ في دمج إعدادات الشركة: ${(error as Error).message}`);
    }
  }
  
  /**
   * تنظيف المفاتيح القديمة بعد الدمج
   */
  private static async cleanupLegacyKeys(stats: MergeStats): Promise<void> {
    try {
      // حذف المفاتيح القديمة
      const legacyKeys = STORAGE_CATEGORIES.LEGACY;
      
      for (const key of legacyKeys) {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          stats.duplicatesRemoved++;
          console.log(`🗑️ تم حذف المفتاح القديم: ${key}`);
        }
      }
      
      // تنظيف مفاتيح أخرى غير مرغوب فيها
      const cleanupKeys = STORAGE_KEYS.CLEANUP_KEYS;
      for (const key of cleanupKeys) {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          stats.duplicatesRemoved++;
          console.log(`🗑️ تم تنظيف المفتاح: ${key}`);
        }
      }
      
    } catch (error) {
      stats.errors.push(`خطأ في تنظيف المفاتيح: ${(error as Error).message}`);
    }
  }
  
  /**
   * فحص حالة النظام الموحد
   */
  static async checkUnifiedSystemHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
    keyStats: any;
    storageHealth: any;
  }> {
    try {
      // فحص النظام الأساسي
      const storageHealth = checkStorageHealth();
      
      // فحص المفاتيح
      const allKeys = Object.keys(localStorage);
      const coreDataKeys = STORAGE_CATEGORIES.CORE_DATA;
      const missingCoreKeys = coreDataKeys.filter(key => !allKeys.includes(key));
      const legacyKeys = allKeys.filter(key => STORAGE_CATEGORIES.LEGACY.includes(key as any));
      
      const issues: string[] = [...storageHealth.issues];
      const recommendations: string[] = [...storageHealth.recommendations];
      
      // فحص المفاتيح المفقودة
      if (missingCoreKeys.length > 0) {
        issues.push(`مفاتيح أساسية مفقودة: ${missingCoreKeys.join(', ')}`);
        recommendations.push('تهيئة البيانات الأساسية');
      }
      
      // فحص المفاتيح القديمة
      if (legacyKeys.length > 0) {
        issues.push(`مفاتيح قديمة موجودة: ${legacyKeys.join(', ')}`);
        recommendations.push('تشغيل عملية دمج البيانات');
      }
      
      return {
        isHealthy: storageHealth.isHealthy && issues.length === storageHealth.issues.length,
        issues,
        recommendations,
        keyStats: {
          total: allKeys.length,
          coreData: coreDataKeys.filter(key => allKeys.includes(key)).length,
          legacy: legacyKeys.length,
          backups: allKeys.filter(key => key.startsWith(STORAGE_KEYS.BACKUP_PREFIX)).length
        },
        storageHealth
      };
      
    } catch (error) {
      return {
        isHealthy: false,
        issues: [`خطأ في فحص النظام: ${(error as Error).message}`],
        recommendations: ['إعادة تشغيل التطبيق'],
        keyStats: {},
        storageHealth: {}
      };
    }
  }
}
