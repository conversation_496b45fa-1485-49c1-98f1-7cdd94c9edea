"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/POSInvoice.tsx":
/*!***************************************!*\
  !*** ./src/components/POSInvoice.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst POSInvoice = (param)=>{\n    let { invoice, onClose } = param;\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"ar-EG\", {\n            style: \"currency\",\n            currency: \"EGP\",\n            minimumFractionDigits: 2\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\",\n            samples: \"عينات\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getServiceDetails = (service)=>{\n        let details = \"\";\n        let quantity = \"\";\n        let unitPrice = 0;\n        let serviceTotal = 0;\n        switch(service.serviceType){\n            case \"consultation\":\n                serviceTotal = service.cost || 0;\n                details = \"\".concat(service.topic || \"استشارة\", \" - \").concat(service.hours || 0, \" ساعة\");\n                quantity = \"\".concat(service.hours || 0, \" ساعة\");\n                unitPrice = service.cost || 0;\n                break;\n            case \"pattern\":\n                var _service_models, _service_models1, _service_models2, _service_models3;\n                serviceTotal = ((_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                details = \"\".concat(((_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_service_models2 = service.models) === null || _service_models2 === void 0 ? void 0 : _service_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_service_models3 = service.models) === null || _service_models3 === void 0 ? void 0 : _service_models3.length) || 1);\n                break;\n            case \"pattern_printing\":\n                var _service_files, _service_files1, _service_files2;\n                serviceTotal = ((_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                details = \"طباعة \".concat(((_service_files1 = service.files) === null || _service_files1 === void 0 ? void 0 : _service_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_service_files2 = service.files) === null || _service_files2 === void 0 ? void 0 : _service_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n                break;\n            case \"manufacturing\":\n                var _service_models4, _service_models5, _service_models6, _service_models7;\n                serviceTotal = ((_service_models4 = service.models) === null || _service_models4 === void 0 ? void 0 : _service_models4.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                details = \"تصنيع \".concat(((_service_models5 = service.models) === null || _service_models5 === void 0 ? void 0 : _service_models5.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_service_models6 = service.models) === null || _service_models6 === void 0 ? void 0 : _service_models6.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_models7 = service.models) === null || _service_models7 === void 0 ? void 0 : _service_models7.length) || 1);\n                break;\n            case \"shipping\":\n                serviceTotal = service.total || 0;\n                details = \"شحن \".concat(service.item || \"منتج\");\n                quantity = \"\".concat(service.quantity || 1);\n                unitPrice = service.unitPrice || 0;\n                break;\n            case \"products\":\n                var _service_items, _service_items1, _service_items2, _service_items3;\n                let productsSubtotal = ((_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                if (service.discountAmount) {\n                    productsSubtotal -= service.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                details = \"\".concat(((_service_items1 = service.items) === null || _service_items1 === void 0 ? void 0 : _service_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_service_items2 = service.items) === null || _service_items2 === void 0 ? void 0 : _service_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_items3 = service.items) === null || _service_items3 === void 0 ? void 0 : _service_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                break;\n            default:\n                serviceTotal = service.cost || service.total || 0;\n                details = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n        }\n        return {\n            details,\n            quantity,\n            unitPrice,\n            serviceTotal\n        };\n    };\n    const handlePrint = ()=>{\n        // إنشاء نافذة طباعة منفصلة\n        const printWindow = window.open(\"\", \"_blank\", \"width=300,height=600\");\n        if (printWindow) {\n            var _document_querySelector;\n            const posContent = (_document_querySelector = document.querySelector(\".pos-invoice-content\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.innerHTML;\n            if (posContent) {\n                printWindow.document.write('\\n          <!DOCTYPE html>\\n          <html dir=\"rtl\" lang=\"ar\">\\n          <head>\\n            <meta charset=\"UTF-8\">\\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n            <title>فاتورة POS - '.concat(invoice.id, \"</title>\\n            <style>\\n              * {\\n                margin: 0;\\n                padding: 0;\\n                box-sizing: border-box;\\n              }\\n\\n              body {\\n                font-family: 'Courier New', monospace;\\n                font-size: 10px;\\n                line-height: 1.2;\\n                color: black;\\n                background: white;\\n                width: 80mm;\\n                margin: 0 auto;\\n                padding: 2mm;\\n              }\\n\\n              @page {\\n                size: 80mm auto;\\n                margin: 2mm;\\n              }\\n\\n              @media print {\\n                body {\\n                  width: 80mm !important;\\n                  font-size: 9px !important;\\n                }\\n              }\\n\\n              .text-center { text-align: center; }\\n              .text-sm { font-size: 9px; }\\n              .text-xs { font-size: 8px; }\\n              .text-lg { font-size: 12px; }\\n              .font-bold { font-weight: bold; }\\n              .font-semibold { font-weight: 600; }\\n              .mb-1 { margin-bottom: 2px; }\\n              .mb-2 { margin-bottom: 4px; }\\n              .mb-3 { margin-bottom: 6px; }\\n              .pb-3 { padding-bottom: 6px; }\\n              .pt-3 { padding-top: 6px; }\\n              .mr-3 { margin-right: 6px; }\\n              .mt-2 { margin-top: 4px; }\\n\\n              .border-b { border-bottom: 1px solid #ccc; }\\n              .border-dashed { border-style: dashed; }\\n              .border-gray-400 { border-color: #999; }\\n\\n              .flex { display: flex; }\\n              .justify-between { justify-content: space-between; }\\n\\n              .text-green-600 { color: #059669; }\\n              .text-orange-600 { color: #ea580c; }\\n              .text-red-600 { color: #dc2626; }\\n              .text-gray-600 { color: #4b5563; }\\n              .text-gray-700 { color: #374151; }\\n\\n              /* Logo Styles for Print */\\n              .inline-block { display: inline-block; }\\n              .bg-gray-100 { background-color: #f3f4f6; }\\n              .rounded-lg { border-radius: 6px; }\\n              .p-2 { padding: 4px; }\\n              .w-16 { width: 32px; }\\n              .h-16 { height: 32px; }\\n              .mx-auto { margin-left: auto; margin-right: auto; }\\n              .bg-gradient-to-br { background: linear-gradient(135deg, #2563eb, #1d4ed8); }\\n              .flex { display: flex; }\\n              .items-center { align-items: center; }\\n              .justify-center { justify-content: center; }\\n              .text-white { color: white; }\\n              .leading-tight { line-height: 1.1; }\\n              .space-y-1 > * + * { margin-top: 2px; }\\n            </style>\\n          </head>\\n          <body>\\n            \").concat(posContent, \"\\n          </body>\\n          </html>\\n        \"));\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        printWindow.close();\n                    }, 250);\n                };\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-34d022bd924bccec\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-34d022bd924bccec\" + \" \" + \"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center p-4 border-b border-gray-200 print:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xl font-bold\",\n                                children: \"فاتورة POS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"12px\",\n                            lineHeight: \"1.4\"\n                        },\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"pos-invoice-content p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-b border-dashed border-gray-400 pb-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"inline-block bg-gray-100 rounded-lg p-2 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-16 h-16 mx-auto bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-white font-bold text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-lg leading-tight\",\n                                                            children: \"OKA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs leading-tight\",\n                                                            children: \"GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-lg font-bold mb-1\",\n                                        children: \"OKA GROUP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-semibold text-gray-700\",\n                                        children: \"مجموعة أوكا للخدمات المتكاملة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mt-2 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs\",\n                                                children: \"\\uD83D\\uDCE7 البريد: <EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs\",\n                                                children: \"\\uD83D\\uDCDE الهاتف: 0114954118\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs\",\n                                                children: \"\\uD83D\\uDCCD المقر: 73 ش 6 اكتوبر الجراش جسر السويس\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-3 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"رقم الفاتورة:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                children: [\n                                                    \"#\",\n                                                    invoice.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"التاريخ:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: formatDate(invoice.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"العميل:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                children: invoice.client.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"الهاتف:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: invoice.client.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t border-dashed border-gray-400 pt-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center font-bold mb-2 text-sm\",\n                                        children: \"تفاصيل الطلب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    invoice.services.map((service, index)=>{\n                                        const serviceInfo = getServiceDetails(service);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between font-bold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: [\n                                                                index + 1,\n                                                                \". \",\n                                                                getServiceName(service.serviceType)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: formatCurrency(serviceInfo.serviceTotal)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-600 mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: serviceInfo.details\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\",\n                                                            children: [\n                                                                \"الكمية: \",\n                                                                serviceInfo.quantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, service.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t border-dashed border-gray-400 pt-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"المجموع الكلي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: formatCurrency(invoice.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"المبلغ المدفوع:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: formatCurrency(invoice.paidAmount || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: \"المبلغ المتبقي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\",\n                                                children: formatCurrency(invoice.total - (invoice.paidAmount || 0))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t border-dashed border-gray-400 pt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-bold\",\n                                    children: [\n                                        \"حالة الدفع:\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"mr-2 \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"text-green-600\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"text-orange-600\" : \"text-red-600\"),\n                                            children: invoice.paymentStatus\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t border-dashed border-gray-400 pt-3 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34d022bd924bccec\",\n                                        children: \"شكراً لتعاملكم معنا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34d022bd924bccec\",\n                                        children: \"Thank you\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mt-2\",\n                                        children: [\n                                            \"تاريخ الطباعة: \",\n                                            new Date().toLocaleDateString(\"ar-EG\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"p-4 border-t border-gray-200 print:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePrint,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"\\uD83D\\uDDA8️ طباعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"إغلاق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"34d022bd924bccec\",\n                children: '@media print{*.jsx-34d022bd924bccec{-webkit-print-color-adjust:exact!important;color-adjust:exact!important}body.jsx-34d022bd924bccec{margin:0!important;padding:0!important;background:white!important}.pos-print-content.jsx-34d022bd924bccec{width:80mm!important;max-width:80mm!important;font-family:\"Courier New\",monospace!important;font-size:10px!important;line-height:1.2!important;color:black!important;background:white!important;margin:0!important;padding:2mm!important}.pos-invoice-content.jsx-34d022bd924bccec{width:100%!important;max-width:100%!important;font-size:10px!important;line-height:1.2!important;padding:0!important;margin:0!important}.pos-invoice-content.jsx-34d022bd924bccec .inline-block.jsx-34d022bd924bccec{display:inline-block!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gray-100.jsx-34d022bd924bccec{background-color:#f3f4f6!important}.pos-invoice-content.jsx-34d022bd924bccec .rounded-lg.jsx-34d022bd924bccec{-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important}.pos-invoice-content.jsx-34d022bd924bccec .w-16.jsx-34d022bd924bccec{width:32px!important;height:32px!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gradient-to-br.jsx-34d022bd924bccec{background:-webkit-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-moz-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-o-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:linear-gradient(135deg,#2563eb,#1d4ed8)!important}.pos-invoice-content.jsx-34d022bd924bccec .text-white.jsx-34d022bd924bccec{color:white!important}.pos-invoice-content.jsx-34d022bd924bccec .font-semibold.jsx-34d022bd924bccec{font-weight:600!important}@page{size:80mm auto;margin:2mm}.print\\\\\\\\.jsx-34d022bd924bccec:hidden{display:none!important}}.pos-invoice-content.jsx-34d022bd924bccec{font-family:\"Courier New\",monospace;font-size:12px;line-height:1.4;color:#333}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, undefined);\n};\n_c = POSInvoice;\n/* harmony default export */ __webpack_exports__[\"default\"] = (POSInvoice);\nvar _c;\n$RefreshReg$(_c, \"POSInvoice\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/POSInvoice.tsx\n"));

/***/ })

});