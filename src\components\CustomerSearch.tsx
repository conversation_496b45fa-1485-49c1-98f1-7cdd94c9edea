'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useCustomerStore } from '@/store/customerStore';
import { Client } from '@/types';

interface CustomerSearchProps {
  onSelectCustomer?: (customer: Client) => void;
  placeholder?: string;
  showFullDetails?: boolean;
  className?: string;
}

const CustomerSearch: React.FC<CustomerSearchProps> = ({
  onSelectCustomer,
  placeholder = "البحث عن عميل...",
  showFullDetails = false,
  className = ""
}) => {
  const { customers, getQuickSuggestions } = useCustomerStore();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (query.trim()) {
      const newSuggestions = getQuickSuggestions(query, 8);
      setSuggestions(newSuggestions);
      setIsOpen(newSuggestions.length > 0);
      setSelectedIndex(-1);
    } else {
      setSuggestions([]);
      setIsOpen(false);
      setSelectedIndex(-1);
    }
  }, [query, getQuickSuggestions]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleSelectSuggestion = (suggestion: any) => {
    const customer = customers.find(c => c.id === suggestion.id);
    if (customer && onSelectCustomer) {
      onSelectCustomer(customer);
    }
    setQuery(suggestion.name);
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSelectSuggestion(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    // تأخير إغلاق القائمة للسماح بالنقر على الاقتراحات
    setTimeout(() => {
      if (!suggestionsRef.current?.contains(document.activeElement)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    }, 150);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'code':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
          </svg>
        );
      case 'phone':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenovenRule"/>
          </svg>
        );
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 font-semibold">{part}</span>
      ) : (
        <span key={index}>{part}</span>
      )
    );
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
          </svg>
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={() => query.trim() && suggestions.length > 0 && setIsOpen(true)}
          className={`w-full px-4 py-3 pr-10 text-right border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
            isOpen ? 'rounded-b-none border-b-0' : ''
          }`}
          placeholder={placeholder}
          autoComplete="off"
        />
      </div>

      {/* قائمة الاقتراحات */}
      {isOpen && suggestions.length > 0 && (
        <div 
          ref={suggestionsRef}
          className="absolute z-50 w-full bg-white border border-gray-300 border-t-0 rounded-b-lg shadow-lg max-h-80 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.id}
              onClick={() => handleSelectSuggestion(suggestion)}
              className={`px-4 py-3 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0 ${
                index === selectedIndex 
                  ? 'bg-blue-50 border-blue-200' 
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getTypeIcon(suggestion.type)}
                  <div className="text-right">
                    <div className="font-medium text-gray-900">
                      {highlightMatch(suggestion.name, query)}
                    </div>
                    {showFullDetails && (
                      <div className="text-sm text-gray-600 mt-1">
                        <div>📱 {suggestion.phone}</div>
                        {suggestion.businessName && (
                          <div>🏢 {suggestion.businessName}</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-left">
                  <div className="text-sm font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {suggestion.clientCode}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {suggestion.type === 'code' && 'كود العميل'}
                    {suggestion.type === 'phone' && 'رقم الهاتف'}
                    {suggestion.type === 'name' && 'اسم العميل'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* رسالة عدم وجود نتائج */}
      {query.trim() && !isOpen && suggestions.length === 0 && (
        <div className="absolute z-50 w-full bg-white border border-gray-300 border-t-0 rounded-b-lg shadow-lg p-4 text-center text-gray-500">
          <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"/>
          </svg>
          <p>لا توجد نتائج للبحث "{query}"</p>
          <p className="text-sm mt-1">جرب البحث بكلمات أخرى</p>
        </div>
      )}
    </div>
  );
};

export default CustomerSearch;
