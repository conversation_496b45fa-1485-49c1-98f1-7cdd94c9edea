"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InvoiceForm */ \"(app-pages-browser)/./src/components/InvoiceForm.tsx\");\n/* harmony import */ var _components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvoicesList */ \"(app-pages-browser)/./src/components/InvoicesList.tsx\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"client\");\n    const [currentClient, setCurrentClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentInvoice, setCurrentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { saveInvoice } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore)();\n    const handleClientSubmit = (client)=>{\n        setCurrentClient(client);\n        setCurrentStep(\"invoice\");\n    };\n    const handleInvoiceSubmit = (invoice)=>{\n        // إنشاء ID فريد مع ترقيم تسلسلي\n        const generateSequentialId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const monthYearKey = \"\".concat(month, \"-\").concat(year);\n            // قراءة العدادات المحفوظة باستخدام النظام الآمن\n            const countersResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoiceCounters\", {});\n            let counters = {};\n            if (countersResult.success) {\n                counters = countersResult.data;\n            } else {\n                console.error(\"❌ Error reading invoice counters:\", countersResult.message);\n                counters = {};\n            }\n            // الحصول على العداد الحالي للشهر والسنة\n            const currentCounter = counters[monthYearKey] || 0;\n            const newCounter = currentCounter + 1;\n            // تحديث العداد\n            counters[monthYearKey] = newCounter;\n            // حفظ العدادات المحدثة باستخدام النظام الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureSetItem)(\"invoiceCounters\", counters);\n            if (!saveResult.success) {\n                console.error(\"❌ Error saving invoice counters:\", saveResult.message);\n            }\n            // إنشاء ID بالتنسيق المطلوب\n            const sequentialNumber = String(newCounter).padStart(3, \"0\");\n            const invoiceId = \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(sequentialNumber);\n            return invoiceId;\n        };\n        const invoiceWithId = {\n            ...invoice,\n            id: invoice.id || generateSequentialId(),\n            createdAt: invoice.createdAt || new Date().toISOString()\n        };\n        // تعيين الفاتورة الحالية\n        setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة في المتجر\n        const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore.getState();\n        store.setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)\n        store.saveInvoice();\n        // التحقق من النتيجة باستخدام النظام الآمن\n        setTimeout(()=>{\n            const invoicesResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoices\", []);\n            if (invoicesResult.success) {\n                const savedInvoice = invoicesResult.data.find((inv)=>inv.id === invoiceWithId.id);\n                if (!savedInvoice) {\n                    console.error(\"❌ Invoice not found in storage after save attempt\");\n                }\n            } else {\n                console.error(\"❌ Error verifying save:\", invoicesResult.message);\n            }\n        }, 100);\n        setCurrentStep(\"preview\");\n    };\n    const handleEditInvoice = ()=>{\n        setCurrentStep(\"invoice\");\n    };\n    const handleExportPDF = async ()=>{\n        if (currentInvoice) {\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__.generateInvoicePDF)(currentInvoice);\n            } catch (error) {\n                alert(\"حدث خطأ أثناء تصدير PDF: \" + error.message);\n            }\n        }\n    };\n    const handleStartOver = ()=>{\n        console.log(\"\\uD83D\\uDD04 بداية جديدة - إعادة تعيين النظام\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n        setCurrentStep(\"client\");\n        setCurrentPage(\"home\");\n    };\n    const handleManageInvoices = ()=>{\n        console.log(\"\\uD83D\\uDCCB الانتقال لصفحة إدارة الفواتير\");\n        setCurrentPage(\"invoices\");\n    };\n    const handleCreateInvoice = ()=>{\n        console.log(\"➕ بدء إنشاء فاتورة جديدة\");\n        setCurrentPage(\"home\");\n        setCurrentStep(\"client\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n    };\n    const handleDataProtection = ()=>{\n        console.log(\"\\uD83D\\uDEE1️ الانتقال لصفحة حماية البيانات\");\n        router.push(\"/data-protection\");\n    };\n    const handleStatistics = ()=>{\n        console.log(\"\\uD83D\\uDCCA الانتقال لصفحة الإحصائيات الشاملة\");\n        router.push(\"/statistics\");\n    };\n    const handleCustomerManagement = ()=>{\n        console.log(\"\\uD83D\\uDC65 الانتقال لصفحة إدارة العملاء\");\n        router.push(\"/customers\");\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case \"client\":\n                return \"إدخال بيانات العميل\";\n            case \"invoice\":\n                return \"إنشاء الفاتورة\";\n            case \"preview\":\n                return \"معاينة الفاتورة\";\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"نظام إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm hidden sm:block\",\n                                                children: \"إدارة شاملة للفواتير والخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex gap-2 sm:gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateInvoice,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"home\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إنشاء فاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إنشاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleManageInvoices,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"invoices\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerManagement,\n                                        className: \"bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة العملاء وأكواد العملاء\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDataProtection,\n                                        className: \"bg-purple-500 hover:bg-purple-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"لوحة تحكم حماية البيانات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"حماية البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDEE1️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStatistics,\n                                        className: \"bg-indigo-500 hover:bg-indigo-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"الإحصائيات الشاملة والتقارير\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"الإحصائيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartOver,\n                                        className: \"bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"بداية جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 sm:space-x-12 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"client\" ? \"text-blue-600 scale-105\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"client\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"invoice\" || currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"بيانات العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"invoice\" ? \"text-blue-600 scale-105\" : currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"invoice\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"إنشاء الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"preview\" ? \"text-blue-600 scale-105\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"preview\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : \"bg-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"معاينة وتصدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري المعاينة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"التقدم: \",\n                                                currentStep === \"client\" ? \"33%\" : currentStep === \"invoice\" ? \"66%\" : \"100%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                    children: [\n                        currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 sm:mb-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full shadow-md \".concat(currentStep === \"client\" ? \"bg-blue-600\" : currentStep === \"invoice\" ? \"bg-green-600\" : \"bg-purple-600\"),\n                                                        children: [\n                                                            currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl sm:text-3xl font-bold text-gray-800 mb-2\",\n                                                                children: getStepTitle(currentStep)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm sm:text-base leading-relaxed\",\n                                                                children: [\n                                                                    currentStep === \"client\" && \"أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة\",\n                                                                    currentStep === \"invoice\" && \"اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة\",\n                                                                    currentStep === \"preview\" && \"راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 rounded-lg border-l-4 \".concat(currentStep === \"client\" ? \"bg-blue-50 border-blue-400\" : currentStep === \"invoice\" ? \"bg-green-50 border-green-400\" : \"bg-purple-50 border-purple-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mt-0.5 flex-shrink-0 \".concat(currentStep === \"client\" ? \"text-blue-600\" : currentStep === \"invoice\" ? \"text-green-600\" : \"text-purple-600\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-blue-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-blue-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تأكد من صحة رقم الهاتف للتواصل\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• اختر الخدمات المطلوبة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن ترك الحقول الاختيارية فارغة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 452,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-green-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 458,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-green-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• أدخل تفاصيل كل خدمة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تحقق من الأسعار والكميات\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يتم حساب المجموع تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-purple-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-purple-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• راجع جميع البيانات قبل التصدير\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 470,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن طباعة الفاتورة مباشرة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• الفاتورة محفوظة تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 472,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInRight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: handleClientSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"invoice\" && currentClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInLeft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                client: currentClient,\n                                                onSubmit: handleInvoiceSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"preview\" && currentInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInUp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                invoice: currentInvoice,\n                                                onEdit: handleEditInvoice,\n                                                onExportPDF: handleExportPDF\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPage === \"invoices\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fadeIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 p-2 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"نظام إدارة الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm leading-relaxed\",\n                                            children: \"نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-blue-400\",\n                                            children: \"الخدمات المدعومة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الاستشارات والتصميم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الباترون والطباعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصنيع والعينات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الشحن والتسويق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصوير والمنتجات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-green-400\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"ترقيم تسلسلي للفواتير\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"تصدير PDF احترافي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"حساب المدفوعات التلقائي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"واجهة عربية متجاوبة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إدارة شاملة للعملاء\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"\\xa9 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-xs mt-2\",\n                                    children: \"تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 522,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"ajprPN+d0zOGiGGgrc38Ayw/dac=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ClientForm.tsx":
/*!***************************************!*\
  !*** ./src/components/ClientForm.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst availableServices = [\n    {\n        value: \"consultation\",\n        label: \"استشارات\"\n    },\n    {\n        value: \"pattern\",\n        label: \"باترون\"\n    },\n    {\n        value: \"pattern_printing\",\n        label: \"طباعة باترون\"\n    },\n    {\n        value: \"manufacturing\",\n        label: \"تصنيع\"\n    },\n    {\n        value: \"samples\",\n        label: \"عينات\"\n    },\n    {\n        value: \"shipping\",\n        label: \"شحن/تعبئة\"\n    },\n    {\n        value: \"marketing\",\n        label: \"تسويق\"\n    },\n    {\n        value: \"photography\",\n        label: \"تصوير\"\n    },\n    {\n        value: \"products\",\n        label: \"منتجات\"\n    },\n    {\n        value: \"raw_purchases\",\n        label: \"مشتريات خام\"\n    }\n];\nconst contactMethods = [\n    {\n        value: \"whatsapp\",\n        label: \"واتساب\"\n    },\n    {\n        value: \"meta\",\n        label: \"ميتا\"\n    },\n    {\n        value: \"meeting\",\n        label: \"مقابلة\"\n    },\n    {\n        value: \"phone\",\n        label: \"هاتف\"\n    }\n];\nconst categories = [\n    {\n        value: \"A\",\n        label: \"فئة A\"\n    },\n    {\n        value: \"B\",\n        label: \"فئة B\"\n    },\n    {\n        value: \"C\",\n        label: \"فئة C\"\n    }\n];\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_2__.object({\n    name: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم العميل مطلوب\"),\n    phone: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"رقم الموبايل مطلوب\"),\n    services: yup__WEBPACK_IMPORTED_MODULE_2__.array().min(1, \"يجب اختيار خدمة واحدة على الأقل\"),\n    contactMethod: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"وسيلة التواصل مطلوبة\"),\n    salesperson: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"اسم السيلز مطلوب\"),\n    initialCategory: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"تصنيف العميل مطلوب\"),\n    clientCode: yup__WEBPACK_IMPORTED_MODULE_2__.string().test(\"valid-code\", \"تنسيق كود العميل غير صحيح\", function(value) {\n        if (!value) return true; // الكود اختياري\n        const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.validateCustomerCode)(value);\n        return validation.isValid;\n    })\n});\nconst ClientForm = (param)=>{\n    let { onSubmit, initialValues } = param;\n    _s();\n    const setCurrentClient = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore)((state)=>state.setCurrentClient);\n    const { generateNewCustomerCode, validateCode, getSuggestions, suggestions, clearSuggestions } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore)();\n    const [showCodeGenerator, setShowCodeGenerator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [codeStats, setCodeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_5__.getCustomerCodeStats)();\n        setCodeStats(stats);\n    }, []);\n    const defaultValues = {\n        name: \"\",\n        phone: \"\",\n        services: [],\n        contactMethod: \"whatsapp\",\n        salesperson: \"\",\n        initialCategory: \"A\",\n        businessName: \"\",\n        province: \"\",\n        clientCode: \"\",\n        ...initialValues\n    };\n    const handleSubmit = (values)=>{\n        // إذا لم يتم تحديد كود العميل، إنشاء كود تلقائي\n        const finalValues = {\n            ...values,\n            clientCode: values.clientCode || generateNewCustomerCode()\n        };\n        setCurrentClient(finalValues);\n        onSubmit(finalValues);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"بيانات العميل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"أدخل المعلومات الأساسية للعميل لبدء إنشاء الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-blue-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"الخطوة 1 من 3 - معلومات العميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Formik, {\n                initialValues: defaultValues,\n                validationSchema: validationSchema,\n                onSubmit: handleSubmit,\n                children: (param)=>{\n                    let { values, setFieldValue, errors, touched } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-blue-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"المعلومات الأساسية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" اسم العميل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"name\",\n                                                                type: \"text\",\n                                                                className: \"form-input-enhanced pr-10 \".concat(errors.name && touched.name ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                placeholder: \"أدخل اسم العميل الكامل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"name\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" رقم الموبايل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                name: \"phone\",\n                                                                type: \"tel\",\n                                                                className: \"form-input-enhanced pr-10 \".concat(errors.phone && touched.phone ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                placeholder: \"01xxxxxxxxx\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"phone\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"الاسم التجاري\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 183,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 182,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"businessName\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"اسم الشركة أو المتجر (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"businessName\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"المحافظة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"province\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        placeholder: \"المحافظة (اختياري)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"province\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"كود العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 222,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { field, form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ...field,\n                                                                                type: \"text\",\n                                                                                className: \"form-input-enhanced pr-10\",\n                                                                                placeholder: \"مثال: \".concat((codeStats === null || codeStats === void 0 ? void 0 : codeStats.nextCode) || \"p013621\"),\n                                                                                onChange: (e)=>{\n                                                                                    field.onChange(e);\n                                                                                    if (e.target.value) {\n                                                                                        getSuggestions(e.target.value);\n                                                                                    } else {\n                                                                                        clearSuggestions();\n                                                                                    }\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    field.onBlur();\n                                                                                    clearSuggestions();\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto\",\n                                                                        children: suggestions.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\",\n                                                                                onClick: ()=>{\n                                                                                    // يمكن إضافة منطق لملء البيانات من العميل المقترح\n                                                                                    clearSuggestions();\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex justify-between items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium text-blue-600\",\n                                                                                                children: customer.clientCode\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 261,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: customer.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                                lineNumber: 262,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: customer.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowCodeGenerator(!showCodeGenerator),\n                                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"إعدادات الكود\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"clientCode\",\n                                                                        children: (param)=>{\n                                                                            let { form } = param;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>{\n                                                                                    const newCode = generateNewCustomerCode();\n                                                                                    form.setFieldValue(\"clientCode\", newCode);\n                                                                                },\n                                                                                className: \"flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 295,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 294,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    \"إنشاء كود جديد\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 27\n                                                                            }, undefined);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            showCodeGenerator && codeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-50 rounded-lg p-4 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-gray-800\",\n                                                                        children: \"إحصائيات أكواد العملاء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"إجمالي العملاء:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 309,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-blue-600 mr-2\",\n                                                                                        children: codeStats.totalCustomers\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 310,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"الكود التالي:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 313,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-green-600 mr-2\",\n                                                                                        children: codeStats.nextCode\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 314,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مُنشأة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-purple-600 mr-2\",\n                                                                                        children: codeStats.generatedCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 318,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"أكواد مخصصة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium text-orange-600 mr-2\",\n                                                                                        children: codeStats.customCodes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 322,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-blue-600 leading-relaxed\",\n                                                                        children: \"تنسيق الكود: p + رقم المعاملة (01) + رقم العميل (3621). إذا تُرك فارغاً، سيتم إنشاء كود تلقائياً.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"clientCode\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"الخدمات المطلوبة\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 text-sm\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"اختر الخدمات التي يحتاجها العميل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: availableServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"service-card group cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                type: \"checkbox\",\n                                                                name: \"services\",\n                                                                value: service.value,\n                                                                className: \"sr-only\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"service-card-content\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"service-icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"service-label\",\n                                                                        children: service.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, service.value, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                name: \"services\",\n                                                component: \"div\",\n                                                className: \"error-message-enhanced mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-purple-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"التواصل والمبيعات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \" وسيلة التواصل المفضلة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3\",\n                                                        children: contactMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"contact-method-card group cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        type: \"radio\",\n                                                                        name: \"contactMethod\",\n                                                                        value: method.value,\n                                                                        className: \"sr-only\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"contact-method-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"contact-icon\",\n                                                                                children: [\n                                                                                    method.value === \"whatsapp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 410,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 409,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meta\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 414,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"meeting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 419,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    method.value === \"phone\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-5 h-5\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 20 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                            lineNumber: 425,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                        lineNumber: 424,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"contact-label\",\n                                                                                children: method.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, method.value, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                        name: \"contactMethod\",\n                                                        component: \"div\",\n                                                        className: \"error-message-enhanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" اسم السيلز\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        name: \"salesperson\",\n                                                                        type: \"text\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.salesperson && touched.salesperson ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        placeholder: \"اسم مندوب المبيعات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"salesperson\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" تصنيف العميل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                            lineNumber: 465,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.Field, {\n                                                                        as: \"select\",\n                                                                        name: \"initialCategory\",\n                                                                        className: \"form-input-enhanced pr-10 \".concat(errors.initialCategory && touched.initialCategory ? \"border-red-300 focus:ring-red-500\" : \"\"),\n                                                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: category.value,\n                                                                                children: category.label\n                                                                            }, category.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_6__.ErrorMessage, {\n                                                                name: \"initialCategory\",\n                                                                component: \"div\",\n                                                                className: \"error-message-enhanced\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center items-center gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary-enhanced group w-full sm:w-auto\",\n                                        disabled: Object.keys(errors).length > 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"حفظ بيانات العميل والمتابعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"البيانات محفوظة محلياً وآمنة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\ClientForm.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientForm, \"DruD6AUCRPknWrISIeafELjmiC0=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_4__.useCustomerStore\n    ];\n});\n_c = ClientForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientForm);\nvar _c;\n$RefreshReg$(_c, \"ClientForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ClientForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerStore: function() { return /* binding */ useCustomerStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n/**\n * تحميل العملاء من التخزين الآمن\n */ const loadCustomersFromStorage = ()=>{\n    const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    return [];\n};\n/**\n * حفظ العملاء في التخزين الآمن\n */ const saveCustomersToStorage = (customers)=>{\n    const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customers\", customers);\n    return result.success;\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return \"customer-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)((set, get)=>({\n        customers: loadCustomersFromStorage(),\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                var _get_currentCustomer;\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: ((_get_currentCustomer = get().currentCustomer) === null || _get_currentCustomer === void 0 ? void 0 : _get_currentCustomer.id) === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const lowerQuery = query.toLowerCase();\n            return customers.filter((customer)=>{\n                var _customer_clientCode, _customer_businessName;\n                return customer.name.toLowerCase().includes(lowerQuery) || customer.phone.includes(query) || ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().includes(lowerQuery)) || ((_customer_businessName = customer.businessName) === null || _customer_businessName === void 0 ? void 0 : _customer_businessName.toLowerCase().includes(lowerQuery));\n            });\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_1__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/customerStore.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/customerCodeGenerator.ts":
/*!********************************************!*\
  !*** ./src/utils/customerCodeGenerator.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findCustomerByCode: function() { return /* binding */ findCustomerByCode; },\n/* harmony export */   generateCustomerCode: function() { return /* binding */ generateCustomerCode; },\n/* harmony export */   getCustomerCodeStats: function() { return /* binding */ getCustomerCodeStats; },\n/* harmony export */   getCustomerCodeSuggestions: function() { return /* binding */ getCustomerCodeSuggestions; },\n/* harmony export */   incrementTransactionNumber: function() { return /* binding */ incrementTransactionNumber; },\n/* harmony export */   isCustomerCodeUnique: function() { return /* binding */ isCustomerCodeUnique; },\n/* harmony export */   parseCustomerCode: function() { return /* binding */ parseCustomerCode; },\n/* harmony export */   resetCustomerCounters: function() { return /* binding */ resetCustomerCounters; },\n/* harmony export */   validateCustomerCode: function() { return /* binding */ validateCustomerCode; }\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/**\n * نظام إنشاء وإدارة أكواد العملاء\n * التنسيق: p013621 (p01 = رقم المعاملة، 3621 = الرقم المسلسل للعميل)\n */ \n// إعدادات نظام الأكواد\nconst CODE_CONFIG = {\n    PREFIX: \"p\",\n    TRANSACTION_DIGITS: 2,\n    CUSTOMER_DIGITS: 4,\n    STARTING_TRANSACTION: 1,\n    STARTING_CUSTOMER: 3621\n};\n/**\n * تحميل العدادات من التخزين الآمن\n */ const loadCounters = ()=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customerCounters\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    // القيم الافتراضية\n    return {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n};\n/**\n * حفظ العدادات في التخزين الآمن\n */ const saveCounters = (counters)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customerCounters\", counters);\n    if (!result.success) {\n        console.error(\"فشل في حفظ عدادات العملاء:\", result.error);\n        throw new Error(\"فشل في حفظ عدادات العملاء\");\n    }\n};\n/**\n * تنسيق رقم بعدد أرقام محدد\n */ const formatNumber = (num, digits)=>{\n    return num.toString().padStart(digits, \"0\");\n};\n/**\n * إنشاء كود عميل جديد\n */ const generateCustomerCode = ()=>{\n    const counters = loadCounters();\n    // زيادة العدادات\n    counters.lastCustomerNumber += 1;\n    counters.totalCustomers += 1;\n    // إنشاء الكود\n    const transactionPart = formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS);\n    const customerPart = formatNumber(counters.lastCustomerNumber, CODE_CONFIG.CUSTOMER_DIGITS);\n    const code = \"\".concat(CODE_CONFIG.PREFIX).concat(transactionPart).concat(customerPart);\n    // حفظ العدادات المحدثة\n    saveCounters(counters);\n    return {\n        code,\n        transactionNumber: counters.lastTransactionNumber,\n        customerNumber: counters.lastCustomerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: false\n    };\n};\n/**\n * التحقق من صحة كود العميل\n */ const validateCustomerCode = (code)=>{\n    if (!code || typeof code !== \"string\") {\n        return {\n            isValid: false,\n            error: \"الكود مطلوب\"\n        };\n    }\n    // التحقق من البادئة\n    if (!code.startsWith(CODE_CONFIG.PREFIX)) {\n        return {\n            isValid: false,\n            error: 'يجب أن يبدأ الكود بـ \"'.concat(CODE_CONFIG.PREFIX, '\"')\n        };\n    }\n    // التحقق من الطول\n    const expectedLength = 1 + CODE_CONFIG.TRANSACTION_DIGITS + CODE_CONFIG.CUSTOMER_DIGITS;\n    if (code.length !== expectedLength) {\n        return {\n            isValid: false,\n            error: \"يجب أن يكون طول الكود \".concat(expectedLength, \" أحرف\")\n        };\n    }\n    // التحقق من الأرقام\n    const numberPart = code.slice(1);\n    if (!/^\\d+$/.test(numberPart)) {\n        return {\n            isValid: false,\n            error: \"يجب أن يحتوي الكود على أرقام فقط بعد البادئة\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * تحليل كود العميل\n */ const parseCustomerCode = (code)=>{\n    const validation = validateCustomerCode(code);\n    if (!validation.isValid) {\n        return null;\n    }\n    const numberPart = code.slice(1);\n    const transactionNumber = parseInt(numberPart.slice(0, CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    const customerNumber = parseInt(numberPart.slice(CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    return {\n        code,\n        transactionNumber,\n        customerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: true\n    };\n};\n/**\n * التحقق من تفرد كود العميل\n */ const isCustomerCodeUnique = (code, excludeClientId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return true; // إذا لم توجد عملاء، فالكود فريد\n    }\n    const customers = result.data;\n    return !customers.some((customer)=>customer.clientCode === code && customer.id !== excludeClientId);\n};\n/**\n * الحصول على إحصائيات أكواد العملاء\n */ const getCustomerCodeStats = ()=>{\n    const counters = loadCounters();\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    const customers = result.success && result.data ? result.data : [];\n    const customCodes = customers.filter((c)=>c.clientCode && !c.clientCode.match(/^p\\d+$/));\n    const generatedCodes = customers.filter((c)=>c.clientCode && c.clientCode.match(/^p\\d+$/));\n    return {\n        totalCustomers: customers.length,\n        generatedCodes: generatedCodes.length,\n        customCodes: customCodes.length,\n        nextCode: \"\".concat(CODE_CONFIG.PREFIX).concat(formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS)).concat(formatNumber(counters.lastCustomerNumber + 1, CODE_CONFIG.CUSTOMER_DIGITS)),\n        lastTransactionNumber: counters.lastTransactionNumber,\n        lastCustomerNumber: counters.lastCustomerNumber\n    };\n};\n/**\n * إعادة تعيين العدادات (للاستخدام في حالات الطوارئ)\n */ const resetCustomerCounters = ()=>{\n    const defaultCounters = {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n    saveCounters(defaultCounters);\n};\n/**\n * تحديث رقم المعاملة (للانتقال لمعاملة جديدة)\n */ const incrementTransactionNumber = ()=>{\n    const counters = loadCounters();\n    counters.lastTransactionNumber += 1;\n    saveCounters(counters);\n};\n/**\n * البحث عن عميل بالكود\n */ const findCustomerByCode = (code)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return null;\n    }\n    return result.data.find((customer)=>customer.clientCode === code) || null;\n};\n/**\n * اقتراحات أكواد العملاء (للبحث التلقائي)\n */ const getCustomerCodeSuggestions = function(query) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return [];\n    }\n    const customers = result.data;\n    const lowerQuery = query.toLowerCase();\n    return customers.filter((customer)=>{\n        var _customer_clientCode;\n        return ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().includes(lowerQuery)) || customer.name.toLowerCase().includes(lowerQuery) || customer.phone.includes(query);\n    }).slice(0, limit);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/customerCodeGenerator.ts\n"));

/***/ })

});