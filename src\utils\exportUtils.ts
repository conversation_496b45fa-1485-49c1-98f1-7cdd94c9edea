/**
 * أدوات التصدير والطباعة للبيانات
 * يوفر وظائف تصدير البيانات بصيغ مختلفة
 */

import { OrderTrackingData } from '@/components/CustomerOrdersTracker';
import { Invoice, Client } from '@/types';

// تصدير البيانات كـ CSV
export const exportToCSV = (data: OrderTrackingData[], filename: string = 'orders-data') => {
  try {
    // تحديد الأعمدة
    const headers = [
      'التاريخ',
      'اسم العميل',
      'كود العميل',
      'رقم الهاتف',
      'الموديل',
      'عدد المقاسات',
      'الحالة',
      'العربون',
      'المتبقي',
      'تكلفة الطباعة',
      'طريقة التواصل',
      'السيلز',
      'المبلغ الإجمالي',
      'آخر تحديث'
    ];

    // تحويل البيانات إلى صفوف CSV
    const csvRows = [
      headers.join(','), // رأس الجدول
      ...data.map(order => [
        new Date(order.date).toLocaleDateString('ar-EG'),
        `"${order.customerName}"`,
        order.customerCode,
        order.phone,
        `"${order.model}"`,
        order.sizesCount,
        `"${order.status}"`,
        order.deposit,
        order.remaining,
        order.printingCost,
        `"${order.chat}"`,
        `"${order.salesperson}"`,
        order.totalAmount,
        new Date(order.lastUpdate).toLocaleDateString('ar-EG')
      ].join(','))
    ];

    // إنشاء محتوى CSV
    const csvContent = csvRows.join('\n');
    
    // إضافة BOM للدعم العربي
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    
    // تحميل الملف
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return { success: true, message: 'تم تصدير البيانات بصيغة CSV بنجاح' };
  } catch (error) {
    console.error('خطأ في تصدير CSV:', error);
    return { success: false, message: 'فشل في تصدير البيانات' };
  }
};

// تصدير البيانات كـ Excel (HTML table format)
export const exportToExcel = (data: OrderTrackingData[], filename: string = 'orders-data') => {
  try {
    // إنشاء جدول HTML
    const tableHTML = `
      <table border="1" style="border-collapse: collapse; direction: rtl;">
        <thead>
          <tr style="background-color: #f3f4f6; font-weight: bold;">
            <th style="padding: 8px;">التاريخ</th>
            <th style="padding: 8px;">اسم العميل</th>
            <th style="padding: 8px;">كود العميل</th>
            <th style="padding: 8px;">رقم الهاتف</th>
            <th style="padding: 8px;">الموديل</th>
            <th style="padding: 8px;">عدد المقاسات</th>
            <th style="padding: 8px;">الحالة</th>
            <th style="padding: 8px;">العربون</th>
            <th style="padding: 8px;">المتبقي</th>
            <th style="padding: 8px;">تكلفة الطباعة</th>
            <th style="padding: 8px;">طريقة التواصل</th>
            <th style="padding: 8px;">السيلز</th>
            <th style="padding: 8px;">المبلغ الإجمالي</th>
            <th style="padding: 8px;">آخر تحديث</th>
          </tr>
        </thead>
        <tbody>
          ${data.map(order => `
            <tr>
              <td style="padding: 8px;">${new Date(order.date).toLocaleDateString('ar-EG')}</td>
              <td style="padding: 8px;">${order.customerName}</td>
              <td style="padding: 8px;">${order.customerCode}</td>
              <td style="padding: 8px;">${order.phone}</td>
              <td style="padding: 8px;">${order.model}</td>
              <td style="padding: 8px;">${order.sizesCount}</td>
              <td style="padding: 8px;">${order.status}</td>
              <td style="padding: 8px;">${order.deposit.toLocaleString('ar-EG')}</td>
              <td style="padding: 8px;">${order.remaining.toLocaleString('ar-EG')}</td>
              <td style="padding: 8px;">${order.printingCost.toLocaleString('ar-EG')}</td>
              <td style="padding: 8px;">${order.chat}</td>
              <td style="padding: 8px;">${order.salesperson}</td>
              <td style="padding: 8px;">${order.totalAmount.toLocaleString('ar-EG')}</td>
              <td style="padding: 8px;">${new Date(order.lastUpdate).toLocaleDateString('ar-EG')}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    `;

    // إنشاء محتوى Excel
    const excelContent = `
      <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel">
        <head>
          <meta charset="UTF-8">
          <meta name="ProgId" content="Excel.Sheet">
          <meta name="Generator" content="Microsoft Excel 15">
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f3f4f6; font-weight: bold; }
          </style>
        </head>
        <body>
          <h2 style="text-align: center;">تقرير تتبع طلبات العملاء</h2>
          <p style="text-align: center;">تاريخ التصدير: ${new Date().toLocaleDateString('ar-EG')}</p>
          ${tableHTML}
        </body>
      </html>
    `;

    // تحميل الملف
    const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.xls`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return { success: true, message: 'تم تصدير البيانات بصيغة Excel بنجاح' };
  } catch (error) {
    console.error('خطأ في تصدير Excel:', error);
    return { success: false, message: 'فشل في تصدير البيانات' };
  }
};

// طباعة تقرير البيانات
export const printOrdersReport = (data: OrderTrackingData[], title: string = 'تقرير تتبع طلبات العملاء') => {
  try {
    // إنشاء محتوى الطباعة
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              direction: rtl;
              margin: 20px;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #4f46e5;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #4f46e5;
              margin: 0;
              font-size: 24px;
            }
            .header p {
              margin: 5px 0;
              color: #666;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 12px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: right;
            }
            th {
              background-color: #f8fafc;
              font-weight: bold;
              color: #374151;
            }
            tr:nth-child(even) {
              background-color: #f9fafb;
            }
            .summary {
              margin-top: 30px;
              padding: 20px;
              background-color: #f3f4f6;
              border-radius: 8px;
            }
            .summary h3 {
              margin-top: 0;
              color: #4f46e5;
            }
            .summary-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin-top: 15px;
            }
            .summary-item {
              text-align: center;
              padding: 10px;
              background-color: white;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
            }
            .summary-value {
              font-size: 18px;
              font-weight: bold;
              color: #4f46e5;
            }
            .summary-label {
              font-size: 12px;
              color: #6b7280;
              margin-top: 5px;
            }
            @media print {
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: auto; }
              tr { page-break-inside: avoid; page-break-after: auto; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>عدد الطلبات: ${data.length}</p>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>العميل</th>
                <th>كود العميل</th>
                <th>الهاتف</th>
                <th>الموديل</th>
                <th>المقاسات</th>
                <th>الحالة</th>
                <th>العربون</th>
                <th>المتبقي</th>
                <th>السيلز</th>
              </tr>
            </thead>
            <tbody>
              ${data.map(order => `
                <tr>
                  <td>${new Date(order.date).toLocaleDateString('ar-EG')}</td>
                  <td>${order.customerName}</td>
                  <td>${order.customerCode}</td>
                  <td>${order.phone}</td>
                  <td>${order.model}</td>
                  <td>${order.sizesCount}</td>
                  <td>${order.status}</td>
                  <td>${order.deposit.toLocaleString('ar-EG')} جنيه</td>
                  <td>${order.remaining.toLocaleString('ar-EG')} جنيه</td>
                  <td>${order.salesperson}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="summary">
            <h3>ملخص الإحصائيات</h3>
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-value">${data.length}</div>
                <div class="summary-label">إجمالي الطلبات</div>
              </div>
              <div class="summary-item">
                <div class="summary-value">${data.reduce((sum, order) => sum + order.totalAmount, 0).toLocaleString('ar-EG')}</div>
                <div class="summary-label">إجمالي المبيعات (جنيه)</div>
              </div>
              <div class="summary-item">
                <div class="summary-value">${data.reduce((sum, order) => sum + order.deposit, 0).toLocaleString('ar-EG')}</div>
                <div class="summary-label">إجمالي العرابين (جنيه)</div>
              </div>
              <div class="summary-item">
                <div class="summary-value">${data.reduce((sum, order) => sum + order.remaining, 0).toLocaleString('ar-EG')}</div>
                <div class="summary-label">إجمالي المتبقي (جنيه)</div>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    // فتح نافذة الطباعة
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();
      
      // انتظار تحميل المحتوى ثم طباعة
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }

    return { success: true, message: 'تم فتح نافذة الطباعة بنجاح' };
  } catch (error) {
    console.error('خطأ في الطباعة:', error);
    return { success: false, message: 'فشل في طباعة التقرير' };
  }
};

// تصدير إحصائيات مفصلة
export const exportDetailedStats = (data: OrderTrackingData[]) => {
  const stats = {
    totalOrders: data.length,
    totalRevenue: data.reduce((sum, order) => sum + order.totalAmount, 0),
    totalDeposits: data.reduce((sum, order) => sum + order.deposit, 0),
    totalRemaining: data.reduce((sum, order) => sum + order.remaining, 0),
    statusBreakdown: data.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    salespersonBreakdown: data.reduce((acc, order) => {
      acc[order.salesperson] = (acc[order.salesperson] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    averageOrderValue: data.length > 0 ? data.reduce((sum, order) => sum + order.totalAmount, 0) / data.length : 0,
    exportDate: new Date().toISOString()
  };

  const blob = new Blob([JSON.stringify(stats, null, 2)], { type: 'application/json' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `orders-statistics-${new Date().toISOString().split('T')[0]}.json`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  return { success: true, message: 'تم تصدير الإحصائيات بنجاح' };
};
