/**
 * مدير مفاتيح التخزين الموحد
 * يضمن استخدام مفاتيح ثابتة ومنظمة لجميع عمليات التخزين
 */

/**
 * مفاتيح التخزين الأساسية
 * جميع عمليات التخزين يجب أن تستخدم هذه المفاتيح فقط
 */
export const STORAGE_KEYS = {
  // البيانات الأساسية
  INVOICES: 'invoices',
  CUSTOMERS: 'customers',
  CUSTOMER_COUNTERS: 'customerCounters',
  
  // إعدادات الشركة (مفتاح موحد)
  COMPANY_SETTINGS: 'companySettings',
  
  // النسخ الاحتياطية
  BACKUP_PREFIX: 'backup-',
  
  // البيانات المؤقتة
  TEMP_PREFIX: 'temp-',
  DRAFT_PREFIX: 'draft-',
  
  // مفاتيح قديمة للدمج (سيتم حذفها)
  LEGACY_COMPANY_SETTINGS: 'company-settings',
  LEGACY_COMPANY_INFO: 'companyInfo',
  
  // مفاتيح للتنظيف
  CLEANUP_KEYS: [
    'test-invoices',
    'demo-data', 
    'backup-invoices',
    'temp-invoice',
    'draft-invoice'
  ]
} as const;

/**
 * نوع للتحقق من صحة المفاتيح
 */
export type StorageKey = typeof STORAGE_KEYS[keyof typeof STORAGE_KEYS];

/**
 * فئات المفاتيح لتنظيم أفضل
 */
export const STORAGE_CATEGORIES = {
  CORE_DATA: [
    STORAGE_KEYS.INVOICES,
    STORAGE_KEYS.CUSTOMERS,
    STORAGE_KEYS.CUSTOMER_COUNTERS,
    STORAGE_KEYS.COMPANY_SETTINGS
  ],
  
  BACKUPS: (key: string) => key.startsWith(STORAGE_KEYS.BACKUP_PREFIX),
  
  TEMPORARY: (key: string) => 
    key.startsWith(STORAGE_KEYS.TEMP_PREFIX) || 
    key.startsWith(STORAGE_KEYS.DRAFT_PREFIX),
    
  LEGACY: [
    STORAGE_KEYS.LEGACY_COMPANY_SETTINGS,
    STORAGE_KEYS.LEGACY_COMPANY_INFO
  ]
};

/**
 * التحقق من صحة مفتاح التخزين
 */
export const validateStorageKey = (key: string): boolean => {
  const allKeys = Object.values(STORAGE_KEYS).flat();
  return allKeys.includes(key as any);
};

/**
 * الحصول على فئة المفتاح
 */
export const getKeyCategory = (key: string): string => {
  if (STORAGE_CATEGORIES.CORE_DATA.includes(key as any)) {
    return 'CORE_DATA';
  }
  if (STORAGE_CATEGORIES.BACKUPS(key)) {
    return 'BACKUPS';
  }
  if (STORAGE_CATEGORIES.TEMPORARY(key)) {
    return 'TEMPORARY';
  }
  if (STORAGE_CATEGORIES.LEGACY.includes(key as any)) {
    return 'LEGACY';
  }
  return 'UNKNOWN';
};

/**
 * إحصائيات المفاتيح
 */
export const getStorageKeysStats = () => {
  const allStorageKeys = Object.keys(localStorage);
  
  return {
    total: allStorageKeys.length,
    coreData: allStorageKeys.filter(key => 
      STORAGE_CATEGORIES.CORE_DATA.includes(key as any)
    ).length,
    backups: allStorageKeys.filter(STORAGE_CATEGORIES.BACKUPS).length,
    temporary: allStorageKeys.filter(STORAGE_CATEGORIES.TEMPORARY).length,
    legacy: allStorageKeys.filter(key => 
      STORAGE_CATEGORIES.LEGACY.includes(key as any)
    ).length,
    unknown: allStorageKeys.filter(key => 
      getKeyCategory(key) === 'UNKNOWN'
    ).length
  };
};
