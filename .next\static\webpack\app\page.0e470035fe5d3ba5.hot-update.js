"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/settingsStore.ts":
/*!************************************!*\
  !*** ./src/store/settingsStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeSettingsStore: function() { return /* binding */ initializeSettingsStore; },\n/* harmony export */   useSettingsStore: function() { return /* binding */ useSettingsStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// Default company settings\nconst defaultSettings = {\n    companyName: \"OKA Group\",\n    companyLogo: null,\n    companyStamp: null,\n    email: \"<EMAIL>\",\n    complaintsPhone: \"0114954118\",\n    address: \"73 ش 6 اكتوبر الجراش جسر السويس\",\n    slogan: \"سعداء خدمتكم OKA Group\",\n    wishMessage: \"نتمني لكم دوام التوفيق ...\",\n    description: \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\"\n};\n// Load settings from storage on initialization\nconst loadInitialSettings = ()=>{\n    if (true) {\n        const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"companySettings\", defaultSettings);\n        if (result.success) {\n            console.log(\"✅ Company settings loaded on initialization\");\n            return {\n                ...defaultSettings,\n                ...result.data\n            };\n        } else {\n            console.error(\"❌ Error loading company settings on initialization:\", result.message);\n        }\n    }\n    return defaultSettings;\n};\nconst useSettingsStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        settings: loadInitialSettings(),\n        updateSettings: async (newSettings)=>{\n            const { settings } = get();\n            const updatedSettings = {\n                ...settings,\n                ...newSettings\n            };\n            set({\n                settings: updatedSettings\n            });\n            // Auto-save to unified storage\n            try {\n                const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, updatedSettings);\n                if (saveResult.success) {\n                    console.log(\"✅ Company settings saved successfully via unified storage\");\n                } else {\n                    console.error(\"❌ Error saving company settings via unified storage:\", saveResult.message);\n                }\n            } catch (error) {\n                console.error(\"❌ Critical error saving company settings:\", error);\n            }\n        },\n        uploadLogo: async (logoFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    const logoDataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    // Update settings with new logo\n                    get().updateSettings({\n                        companyLogo: logoDataUrl\n                    });\n                    console.log(\"✅ Company logo uploaded successfully\");\n                    resolve(logoDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company logo\");\n                    reject(new Error(\"فشل في رفع شعار الشركة\"));\n                };\n                reader.readAsDataURL(logoFile);\n            });\n        },\n        uploadStamp: async (stampFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    const stampDataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    // Update settings with new stamp\n                    get().updateSettings({\n                        companyStamp: stampDataUrl\n                    });\n                    console.log(\"✅ Company stamp uploaded successfully\");\n                    resolve(stampDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company stamp\");\n                    reject(new Error(\"فشل في رفع ختم الشركة\"));\n                };\n                reader.readAsDataURL(stampFile);\n            });\n        },\n        loadSettings: async ()=>{\n            try {\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n                if (result.success) {\n                    set({\n                        settings: {\n                            ...defaultSettings,\n                            ...result.data\n                        }\n                    });\n                    console.log(\"✅ تم تحميل إعدادات الشركة من النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في تحميل إعدادات الشركة:\", result.message);\n                    set({\n                        settings: defaultSettings\n                    });\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في تحميل الإعدادات:\", error);\n                set({\n                    settings: defaultSettings\n                });\n            }\n        },\n        saveSettings: async ()=>{\n            try {\n                const { settings } = get();\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, settings);\n                if (result.success) {\n                    console.log(\"✅ تم حفظ إعدادات الشركة في النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في حفظ إعدادات الشركة:\", result.message);\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في حفظ الإعدادات:\", error);\n            }\n        }\n    }));\n// دالة تهيئة متجر الإعدادات\nconst initializeSettingsStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 تهيئة متجر الإعدادات...\");\n        // تحميل الإعدادات من النظام الموحد\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n        if (result.success) {\n            const settings = {\n                ...defaultSettings,\n                ...result.data\n            };\n            useSettingsStore.setState({\n                settings\n            });\n            console.log(\"✅ تم تهيئة متجر الإعدادات بنجاح\");\n        } else {\n            console.error(\"❌ فشل في تحميل الإعدادات، استخدام الافتراضية\");\n            useSettingsStore.setState({\n                settings: defaultSettings\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ فشل في تهيئة متجر الإعدادات:\", error);\n        useSettingsStore.setState({\n            settings: defaultSettings\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/settingsStore.ts\n"));

/***/ })

});