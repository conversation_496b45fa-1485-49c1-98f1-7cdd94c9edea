import { create } from 'zustand';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';
import { UnifiedStorageManager } from '@/utils/unifiedStorage';
import { STORAGE_KEYS } from '@/utils/storageKeys';

interface CompanySettings {
  companyName: string;
  companyLogo: string | null;
  companyStamp: string | null;
  email: string;
  complaintsPhone: string;
  address: string;
  slogan: string;
  wishMessage: string;
  description: string;
}

interface SettingsStore {
  settings: CompanySettings;
  
  // Actions
  updateSettings: (newSettings: Partial<CompanySettings>) => void;
  uploadLogo: (logoFile: File) => Promise<string>;
  uploadStamp: (stampFile: File) => Promise<string>;
  loadSettings: () => void;
  saveSettings: () => void;
}

// Default company settings
const defaultSettings: CompanySettings = {
  companyName: 'OKA Group',
  companyLogo: null,
  companyStamp: null,
  email: '<EMAIL>',
  complaintsPhone: '0114954118',
  address: '73 ش 6 اكتوبر الجراش جسر السويس',
  slogan: 'سعداء خدمتكم OKA Group',
  wishMessage: 'نتمني لكم دوام التوفيق ...',
  description: 'شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة'
};

// Load settings from storage on initialization
const loadInitialSettings = (): CompanySettings => {
  if (typeof window !== 'undefined') {
    const result = secureGetItem<CompanySettings>('companySettings', defaultSettings);
    if (result.success) {
      console.log('✅ Company settings loaded on initialization');
      return { ...defaultSettings, ...result.data };
    } else {
      console.error('❌ Error loading company settings on initialization:', result.message);
    }
  }
  return defaultSettings;
};

export const useSettingsStore = create<SettingsStore>((set, get) => ({
  settings: loadInitialSettings(),

  updateSettings: async (newSettings: Partial<CompanySettings>) => {
    const { settings } = get();
    const updatedSettings = { ...settings, ...newSettings };
    set({ settings: updatedSettings });

    // Auto-save to unified storage
    try {
      const saveResult = await UnifiedStorageManager.setItem(STORAGE_KEYS.COMPANY_SETTINGS, updatedSettings);
      if (saveResult.success) {
        console.log('✅ Company settings saved successfully via unified storage');
      } else {
        console.error('❌ Error saving company settings via unified storage:', saveResult.message);
      }
    } catch (error) {
      console.error('❌ Critical error saving company settings:', error);
    }
  },

  uploadLogo: async (logoFile: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const logoDataUrl = e.target?.result as string;
        
        // Update settings with new logo
        get().updateSettings({ companyLogo: logoDataUrl });
        
        console.log('✅ Company logo uploaded successfully');
        resolve(logoDataUrl);
      };
      
      reader.onerror = () => {
        console.error('❌ Error uploading company logo');
        reject(new Error('فشل في رفع شعار الشركة'));
      };
      
      reader.readAsDataURL(logoFile);
    });
  },

  uploadStamp: async (stampFile: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const stampDataUrl = e.target?.result as string;
        
        // Update settings with new stamp
        get().updateSettings({ companyStamp: stampDataUrl });
        
        console.log('✅ Company stamp uploaded successfully');
        resolve(stampDataUrl);
      };
      
      reader.onerror = () => {
        console.error('❌ Error uploading company stamp');
        reject(new Error('فشل في رفع ختم الشركة'));
      };
      
      reader.readAsDataURL(stampFile);
    });
  },

  loadSettings: async () => {
    try {
      const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);
      if (result.success) {
        set({ settings: { ...defaultSettings, ...result.data } });
        console.log('✅ تم تحميل إعدادات الشركة من النظام الموحد');
      } else {
        console.error('❌ فشل في تحميل إعدادات الشركة:', result.message);
        set({ settings: defaultSettings });
      }
    } catch (error) {
      console.error('❌ خطأ حرج في تحميل الإعدادات:', error);
      set({ settings: defaultSettings });
    }
  },

  saveSettings: async () => {
    try {
      const { settings } = get();
      const result = await UnifiedStorageManager.setItem(STORAGE_KEYS.COMPANY_SETTINGS, settings);
      if (result.success) {
        console.log('✅ تم حفظ إعدادات الشركة في النظام الموحد');
      } else {
        console.error('❌ فشل في حفظ إعدادات الشركة:', result.message);
      }
    } catch (error) {
      console.error('❌ خطأ حرج في حفظ الإعدادات:', error);
    }
  }
}));

// دالة تهيئة متجر الإعدادات
export const initializeSettingsStore = async () => {
  try {
    console.log('🔄 تهيئة متجر الإعدادات...');

    // تحميل الإعدادات من النظام الموحد
    const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);
    if (result.success) {
      const settings = { ...defaultSettings, ...result.data };
      useSettingsStore.setState({ settings });
      console.log('✅ تم تهيئة متجر الإعدادات بنجاح');
    } else {
      console.error('❌ فشل في تحميل الإعدادات، استخدام الافتراضية');
      useSettingsStore.setState({ settings: defaultSettings });
    }
  } catch (error) {
    console.error('❌ فشل في تهيئة متجر الإعدادات:', error);
    useSettingsStore.setState({ settings: defaultSettings });
  }
};
