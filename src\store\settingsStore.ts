import { create } from 'zustand';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';

interface CompanySettings {
  companyName: string;
  companyLogo: string | null;
  companyStamp: string | null;
  email: string;
  complaintsPhone: string;
  address: string;
  slogan: string;
  wishMessage: string;
  description: string;
}

interface SettingsStore {
  settings: CompanySettings;
  
  // Actions
  updateSettings: (newSettings: Partial<CompanySettings>) => void;
  uploadLogo: (logoFile: File) => Promise<string>;
  uploadStamp: (stampFile: File) => Promise<string>;
  loadSettings: () => void;
  saveSettings: () => void;
}

// Default company settings
const defaultSettings: CompanySettings = {
  companyName: 'OKA Group',
  companyLogo: null,
  companyStamp: null,
  email: '<EMAIL>',
  complaintsPhone: '0114954118',
  address: '73 ش 6 اكتوبر الجراش جسر السويس',
  slogan: 'سعداء خدمتكم OKA Group',
  wishMessage: 'نتمني لكم دوام التوفيق ...',
  description: 'شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة'
};

export const useSettingsStore = create<SettingsStore>((set, get) => ({
  settings: defaultSettings,

  updateSettings: (newSettings: Partial<CompanySettings>) => {
    const { settings } = get();
    const updatedSettings = { ...settings, ...newSettings };
    set({ settings: updatedSettings });
    
    // Auto-save to secure storage
    const saveResult = secureSetItem('companySettings', updatedSettings);
    if (saveResult.success) {
      console.log('✅ Company settings saved successfully');
    } else {
      console.error('❌ Error saving company settings:', saveResult.message);
    }
  },

  uploadLogo: async (logoFile: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const logoDataUrl = e.target?.result as string;
        
        // Update settings with new logo
        get().updateSettings({ companyLogo: logoDataUrl });
        
        console.log('✅ Company logo uploaded successfully');
        resolve(logoDataUrl);
      };
      
      reader.onerror = () => {
        console.error('❌ Error uploading company logo');
        reject(new Error('فشل في رفع شعار الشركة'));
      };
      
      reader.readAsDataURL(logoFile);
    });
  },

  uploadStamp: async (stampFile: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const stampDataUrl = e.target?.result as string;
        
        // Update settings with new stamp
        get().updateSettings({ companyStamp: stampDataUrl });
        
        console.log('✅ Company stamp uploaded successfully');
        resolve(stampDataUrl);
      };
      
      reader.onerror = () => {
        console.error('❌ Error uploading company stamp');
        reject(new Error('فشل في رفع ختم الشركة'));
      };
      
      reader.readAsDataURL(stampFile);
    });
  },

  loadSettings: () => {
    const result = secureGetItem<CompanySettings>('companySettings', defaultSettings);
    if (result.success) {
      set({ settings: { ...defaultSettings, ...result.data } });
      console.log('✅ Company settings loaded successfully');
    } else {
      console.error('❌ Error loading company settings:', result.message);
      set({ settings: defaultSettings });
    }
  },

  saveSettings: () => {
    const { settings } = get();
    const saveResult = secureSetItem('companySettings', settings);
    if (saveResult.success) {
      console.log('✅ Company settings saved manually');
    } else {
      console.error('❌ Error saving company settings:', saveResult.message);
    }
  }
}));
