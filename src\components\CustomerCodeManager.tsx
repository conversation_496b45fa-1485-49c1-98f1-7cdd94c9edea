'use client';

import React, { useState, useEffect } from 'react';
import { 
  getCustomerCodeStats, 
  generateCustomerCode, 
  resetCustomerCounters,
  incrementTransactionNumber,
  findCustomerByCode
} from '@/utils/customerCodeGenerator';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';

interface CodeStats {
  totalCustomers: number;
  generatedCodes: number;
  customCodes: number;
  lastGeneratedCode: string;
  nextCode: string;
  lastTransactionNumber: number;
  lastCustomerNumber: number;
}

interface CustomerCounters {
  lastTransactionNumber: number;
  lastCustomerNumber: number;
  totalCustomers: number;
}

const CustomerCodeManager: React.FC = () => {
  const [stats, setStats] = useState<CodeStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editCounters, setEditCounters] = useState<CustomerCounters>({
    lastTransactionNumber: 1,
    lastCustomerNumber: 0,
    totalCustomers: 0
  });
  const [searchCode, setSearchCode] = useState('');
  const [searchResult, setSearchResult] = useState<any>(null);

  const loadStats = () => {
    try {
      const codeStats = getCustomerCodeStats();
      setStats(codeStats);
      
      // تحميل العدادات الحالية
      const result = secureGetItem<CustomerCounters>('customerCounters');
      if (result.success && result.data) {
        setEditCounters(result.data);
      }
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات الأكواد:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  const handleGenerateNewCode = () => {
    try {
      const newCode = generateCustomerCode();
      console.log('تم إنشاء كود جديد:', newCode);
      loadStats(); // إعادة تحميل الإحصائيات
    } catch (error) {
      console.error('خطأ في إنشاء كود جديد:', error);
      alert('حدث خطأ في إنشاء كود جديد');
    }
  };

  const handleIncrementTransaction = () => {
    try {
      incrementTransactionNumber();
      loadStats();
      alert('تم الانتقال للمعاملة التالية بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث رقم المعاملة:', error);
      alert('حدث خطأ في تحديث رقم المعاملة');
    }
  };

  const handleSaveCounters = () => {
    try {
      const result = secureSetItem('customerCounters', editCounters);
      if (result.success) {
        loadStats();
        setShowEditModal(false);
        alert('تم حفظ العدادات بنجاح');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('خطأ في حفظ العدادات:', error);
      alert('حدث خطأ في حفظ العدادات');
    }
  };

  const handleResetCounters = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع العدادات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      try {
        resetCustomerCounters();
        loadStats();
        alert('تم إعادة تعيين العدادات بنجاح');
      } catch (error) {
        console.error('خطأ في إعادة تعيين العدادات:', error);
        alert('حدث خطأ في إعادة تعيين العدادات');
      }
    }
  };

  const handleSearchCode = () => {
    if (!searchCode.trim()) {
      setSearchResult(null);
      return;
    }

    try {
      const customer = findCustomerByCode(searchCode.trim());
      setSearchResult(customer);
    } catch (error) {
      console.error('خطأ في البحث:', error);
      setSearchResult(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="mr-3 text-gray-600">جاري التحميل...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">إدارة أكواد العملاء</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setShowEditModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            تعديل العدادات
          </button>
          <button
            onClick={loadStats}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            تحديث
          </button>
        </div>
      </div>

      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* إحصائيات عامة */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">إجمالي العملاء</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.totalCustomers}</p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800 mb-2">الأكواد المولدة</h3>
            <p className="text-3xl font-bold text-green-600">{stats.generatedCodes}</p>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">الأكواد المخصصة</h3>
            <p className="text-3xl font-bold text-yellow-600">{stats.customCodes}</p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-800 mb-2">آخر كود</h3>
            <p className="text-xl font-bold text-purple-600">{stats.lastGeneratedCode || 'لا يوجد'}</p>
          </div>
        </div>
      )}

      {/* معلومات العدادات */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">رقم المعاملة الحالي</h3>
            <p className="text-2xl font-bold text-gray-600">{stats.lastTransactionNumber}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">آخر رقم عميل</h3>
            <p className="text-2xl font-bold text-gray-600">{stats.lastCustomerNumber}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">الكود التالي</h3>
            <p className="text-2xl font-bold text-gray-600">{stats.nextCode}</p>
          </div>
        </div>
      )}

      {/* أدوات الإدارة */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-3">إنشاء كود جديد</h3>
          <p className="text-sm text-green-700 mb-3">إنشاء كود عميل جديد للاختبار</p>
          <button
            onClick={handleGenerateNewCode}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            إنشاء كود جديد
          </button>
        </div>

        <div className="bg-orange-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-orange-800 mb-3">الانتقال للمعاملة التالية</h3>
          <p className="text-sm text-orange-700 mb-3">زيادة رقم المعاملة للانتقال لمعاملة جديدة</p>
          <button
            onClick={handleIncrementTransaction}
            className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            معاملة جديدة
          </button>
        </div>
      </div>

      {/* البحث عن كود */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">البحث عن كود عميل</h3>
        <div className="flex gap-3">
          <input
            type="text"
            value={searchCode}
            onChange={(e) => setSearchCode(e.target.value)}
            placeholder="أدخل كود العميل (مثل: p013621)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            onClick={handleSearchCode}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            بحث
          </button>
        </div>
        
        {searchResult && (
          <div className="mt-4 p-3 bg-white rounded-lg border">
            <h4 className="font-semibold text-gray-800">نتيجة البحث:</h4>
            <p><strong>الاسم:</strong> {searchResult.name}</p>
            <p><strong>الهاتف:</strong> {searchResult.phone}</p>
            <p><strong>الكود:</strong> {searchResult.clientCode}</p>
          </div>
        )}
        
        {searchCode && !searchResult && (
          <div className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
            <p className="text-red-700">لم يتم العثور على عميل بهذا الكود</p>
          </div>
        )}
      </div>

      {/* إعادة تعيين العدادات */}
      <div className="bg-red-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-3">إعادة تعيين العدادات</h3>
        <p className="text-sm text-red-700 mb-3">
          <strong>تحذير:</strong> هذا الإجراء سيعيد تعيين جميع العدادات إلى القيم الافتراضية
        </p>
        <button
          onClick={handleResetCounters}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          إعادة تعيين العدادات
        </button>
      </div>

      {/* نافذة تعديل العدادات */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-xl font-bold text-gray-800 mb-4">تعديل العدادات</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  رقم المعاملة الحالي
                </label>
                <input
                  type="number"
                  value={editCounters.lastTransactionNumber}
                  onChange={(e) => setEditCounters({
                    ...editCounters,
                    lastTransactionNumber: parseInt(e.target.value) || 1
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  آخر رقم عميل
                </label>
                <input
                  type="number"
                  value={editCounters.lastCustomerNumber}
                  onChange={(e) => setEditCounters({
                    ...editCounters,
                    lastCustomerNumber: parseInt(e.target.value) || 0
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  إجمالي العملاء
                </label>
                <input
                  type="number"
                  value={editCounters.totalCustomers}
                  onChange={(e) => setEditCounters({
                    ...editCounters,
                    totalCustomers: parseInt(e.target.value) || 0
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={handleSaveCounters}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                حفظ
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerCodeManager;
