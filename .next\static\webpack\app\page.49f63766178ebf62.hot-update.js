"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/services/SamplesService.tsx":
/*!****************************************************!*\
  !*** ./src/components/services/SamplesService.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SamplesServiceComponent = (param)=>{\n    let { namePrefix, values, setFieldValue } = param;\n    _s();\n    const serviceData = values[namePrefix];\n    const isEnabled = (serviceData === null || serviceData === void 0 ? void 0 : serviceData.enabled) || false;\n    const defaultSample = {\n        sampleName: \"\",\n        quantity: 1,\n        cost: 0,\n        fabricType: \"\",\n        color: \"\",\n        size: \"\",\n        deliveryDate: \"\",\n        notes: \"\"\n    };\n    const sampleTypes = [\n        {\n            value: \"fabric\",\n            label: \"عينة قماش\"\n        },\n        {\n            value: \"garment\",\n            label: \"عينة ملبس\"\n        },\n        {\n            value: \"accessory\",\n            label: \"عينة إكسسوار\"\n        },\n        {\n            value: \"pattern\",\n            label: \"عينة باترون\"\n        },\n        {\n            value: \"color\",\n            label: \"عينة لون\"\n        },\n        {\n            value: \"texture\",\n            label: \"عينة ملمس\"\n        }\n    ];\n    const urgencyLevels = [\n        {\n            value: \"normal\",\n            label: \"عادي\"\n        },\n        {\n            value: \"urgent\",\n            label: \"مستعجل\"\n        },\n        {\n            value: \"very_urgent\",\n            label: \"مستعجل جداً\"\n        }\n    ];\n    // حساب المجموع الكلي\n    const calculateGrandTotal = ()=>{\n        if (!(serviceData === null || serviceData === void 0 ? void 0 : serviceData.samples)) return 0;\n        return serviceData.samples.reduce((total, sample)=>{\n            return total + (Number(sample.cost) || 0);\n        }, 0);\n    };\n    // تحديث المجموع الكلي\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (isEnabled) {\n            const total = calculateGrandTotal();\n            setFieldValue(\"\".concat(namePrefix, \".grandTotal\"), total);\n        }\n    }, [\n        serviceData === null || serviceData === void 0 ? void 0 : serviceData.samples,\n        isEnabled\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"form-section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                        type: \"checkbox\",\n                        name: \"\".concat(namePrefix, \".enabled\"),\n                        className: \"form-checkbox\",\n                        onChange: (e)=>{\n                            setFieldValue(\"\".concat(namePrefix, \".enabled\"), e.target.checked);\n                            if (e.target.checked) {\n                                setFieldValue(\"\".concat(namePrefix, \".serviceType\"), \"samples\");\n                                setFieldValue(\"\".concat(namePrefix, \".id\"), Date.now().toString());\n                                setFieldValue(\"\".concat(namePrefix, \".samples\"), [\n                                    defaultSample\n                                ]);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"خدمة العينات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            isEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 pr-6 border-r-4 border-teal-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.FieldArray, {\n                        name: \"\".concat(namePrefix, \".samples\"),\n                        children: (param)=>{\n                            let { push, remove } = param;\n                            var _serviceData_samples;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-semibold\",\n                                                children: \"قائمة العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>push(defaultSample),\n                                                className: \"btn-primary text-sm px-3 py-1\",\n                                                children: \"إضافة عينة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    serviceData === null || serviceData === void 0 ? void 0 : (_serviceData_samples = serviceData.samples) === null || _serviceData_samples === void 0 ? void 0 : _serviceData_samples.map((sample, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg mb-4 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"العينة \",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        serviceData.samples.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>remove(index),\n                                                            className: \"text-red-500 hover:text-red-700 text-sm\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"اسم العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleName\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"اسم العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.ErrorMessage, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleName\"),\n                                                                    component: \"div\",\n                                                                    className: \"error-message\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"نوع العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    as: \"select\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".sampleType\"),\n                                                                    className: \"form-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر نوع العينة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                            lineNumber: 134,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        sampleTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: type.value,\n                                                                                children: type.label\n                                                                            }, type.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                                lineNumber: 136,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".quantity\"),\n                                                                    type: \"number\",\n                                                                    min: \"1\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"عدد العينات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"التكلفة (جنيه)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".cost\"),\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"تكلفة العينة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 158,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"نوع القماش\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".fabricType\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"نوع القماش\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"اللون\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".color\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"اللون المطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"المقاس\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".size\"),\n                                                                    type: \"text\",\n                                                                    className: \"form-input\",\n                                                                    placeholder: \"المقاس\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"مستوى الأولوية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    as: \"select\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".urgency\"),\n                                                                    className: \"form-input\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر مستوى الأولوية\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        urgencyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: level.value,\n                                                                                children: level.label\n                                                                            }, level.value, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 29\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-field\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"تاريخ التسليم المطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".deliveryDate\"),\n                                                                    type: \"date\",\n                                                                    className: \"form-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"وصف العينة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".description\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"وصف تفصيلي للعينة المطلوبة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"متطلبات خاصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".specialRequirements\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"أي متطلبات خاصة للعينة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".withMeasurements\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مع القياسات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".withPattern\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مع الباترون\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".qualityCheck\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"فحص جودة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                                    type: \"checkbox\",\n                                                                    name: \"\".concat(namePrefix, \".samples.\").concat(index, \".returnRequired\"),\n                                                                    className: \"form-checkbox\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"مطلوب الإرجاع\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"form-field mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                            name: \"\".concat(namePrefix, \".samples.\").concat(index, \".notes\"),\n                                                            as: \"textarea\",\n                                                            rows: \"2\",\n                                                            className: \"form-input\",\n                                                            placeholder: \"أي ملاحظات إضافية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"المجموع الكلي لجميع العينات:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: [\n                                            calculateGrandTotal().toFixed(2),\n                                            \" جنيه\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-green-600 mt-1\",\n                                children: \"شامل جميع العينات المطلوبة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-3\",\n                                children: \"معلومات عامة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"form-label\",\n                                                children: \"الغرض من العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                as: \"select\",\n                                                name: \"\".concat(namePrefix, \".purpose\"),\n                                                className: \"form-input\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"اختر الغرض\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"client_approval\",\n                                                        children: \"موافقة العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"quality_test\",\n                                                        children: \"اختبار جودة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"color_matching\",\n                                                        children: \"مطابقة لون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"size_fitting\",\n                                                        children: \"اختبار مقاس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"production_reference\",\n                                                        children: \"مرجع إنتاج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"market_research\",\n                                                        children: \"بحث سوق\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-field\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"form-label\",\n                                                children: \"مسؤول العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                                name: \"\".concat(namePrefix, \".sampleManager\"),\n                                                type: \"text\",\n                                                className: \"form-input\",\n                                                placeholder: \"اسم المسؤول عن العينات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-field\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"form-label\",\n                                        children: \"شروط خاصة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_2__.Field, {\n                                        name: \"\".concat(namePrefix, \".specialConditions\"),\n                                        as: \"textarea\",\n                                        rows: \"3\",\n                                        className: \"form-input\",\n                                        placeholder: \"أي شروط خاصة بالعينات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\services\\\\SamplesService.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SamplesServiceComponent, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = SamplesServiceComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SamplesServiceComponent);\nvar _c;\n$RefreshReg$(_c, \"SamplesServiceComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/services/SamplesService.tsx\n"));

/***/ })

});