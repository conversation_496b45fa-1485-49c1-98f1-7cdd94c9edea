"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicesList.tsx":
/*!*****************************************!*\
  !*** ./src/components/InvoicesList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst InvoicesList = ()=>{\n    _s();\n    const { invoices } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date_desc\");\n    const [filteredInvoices, setFilteredInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previewInvoice, setPreviewInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [posInvoice, setPosInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // إعادة تحميل البيانات من التخزين الآمن عند تحميل المكون\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInvoices = ()=>{\n            // استخدام النظام الآمن لتحميل البيانات\n            const result = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState().invoices;\n            // التحقق من عدم وجود تكرار في البيانات\n            const uniqueInvoices = result.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n            // تحديث الحالة المحلية\n            setFilteredInvoices(uniqueInvoices);\n        };\n        // تحميل فوري\n        loadInvoices();\n        // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري\n        const handleStorageChange = (e)=>{\n            if (e.key === \"invoices\") {\n                loadInvoices();\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = [\n            ...invoices\n        ];\n        // البحث\n        if (searchTerm) {\n            filtered = filtered.filter((invoice)=>{\n                var _invoice_id;\n                return invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.client.phone.includes(searchTerm) || ((_invoice_id = invoice.id) === null || _invoice_id === void 0 ? void 0 : _invoice_id.includes(searchTerm));\n            });\n        }\n        // الفلترة\n        if (filterBy !== \"all\") {\n            filtered = filtered.filter((invoice)=>{\n                switch(filterBy){\n                    case \"consultation\":\n                        return invoice.services.some((s)=>s.serviceType === \"consultation\");\n                    case \"pattern\":\n                        return invoice.services.some((s)=>s.serviceType === \"pattern\");\n                    case \"photography\":\n                        return invoice.services.some((s)=>s.serviceType === \"photography\");\n                    case \"shipping\":\n                        return invoice.services.some((s)=>s.serviceType === \"shipping\");\n                    case \"high_value\":\n                        return invoice.total > 1000;\n                    case \"recent\":\n                        const oneWeekAgo = new Date();\n                        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n                        return new Date(invoice.createdAt) > oneWeekAgo;\n                    case \"paid_full\":\n                        return invoice.paymentStatus === \"مدفوع بالكامل\";\n                    case \"paid_partial\":\n                        return invoice.paymentStatus === \"مدفوع جزئياً\";\n                    case \"unpaid\":\n                        return invoice.paymentStatus === \"غير مدفوع\" || !invoice.paymentStatus;\n                    default:\n                        return true;\n                }\n            });\n        }\n        // الترتيب\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"date_desc\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"date_asc\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"amount_desc\":\n                    return b.total - a.total;\n                case \"amount_asc\":\n                    return a.total - b.total;\n                case \"client_name\":\n                    return a.client.name.localeCompare(b.client.name, \"ar\");\n                default:\n                    return 0;\n            }\n        });\n        setFilteredInvoices(filtered);\n    }, [\n        invoices,\n        searchTerm,\n        filterBy,\n        sortBy\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceNames = (services)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return services.map((s)=>serviceNames[s.serviceType] || s.serviceType).join(\"، \");\n    };\n    // دالة عرض رسائل التأكيد المحسنة\n    const showSuccessMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 3000);\n    };\n    const showErrorMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 4000);\n    };\n    const handleExportPDF = async (invoice)=>{\n        try {\n            // فتح المعاينة أولاً للحصول على نفس التنسيق\n            setPreviewInvoice(invoice);\n            // انتظار قصير للتأكد من عرض المعاينة\n            setTimeout(async ()=>{\n                try {\n                    await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n                    showSuccessMessage(\"تم تصدير PDF للفاتورة \".concat(invoice.id, \" بنجاح! \\uD83C\\uDF89\"));\n                    // إغلاق المعاينة بعد التصدير بثانية واحدة\n                    setTimeout(()=>{\n                        setPreviewInvoice(null);\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"❌ Error exporting PDF:\", error);\n                    showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n                    setPreviewInvoice(null);\n                }\n            }, 500);\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تصدير PDF.\");\n        }\n    };\n    const handlePreviewInvoice = (invoice)=>{\n        setPreviewInvoice(invoice);\n    };\n    const handlePOSInvoice = (invoice)=>{\n        setPosInvoice(invoice);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewInvoice(null);\n    };\n    const handleEditFromPreview = ()=>{\n        // For now, just close the preview\n        // In a full implementation, this would navigate to edit mode\n        setPreviewInvoice(null);\n        alert(\"وظيفة التعديل ستكون متاحة قريباً\");\n    };\n    const handleExportFromPreview = async ()=>{\n        if (previewInvoice) {\n            await handleExportPDF(previewInvoice);\n        }\n    };\n    // دالة حذف الفاتورة مع تأكيد\n    const handleDeleteInvoice = (invoice)=>{\n        const confirmMessage = \"هل أنت متأكد من حذف هذه الفاتورة؟\\n\\nالفاتورة: \".concat(invoice.id, \"\\nالعميل: \").concat(invoice.client.name, \"\\nالمبلغ: \").concat(formatCurrency(invoice.total), \"\\n\\n⚠️ هذا الإجراء لا يمكن التراجع عنه!\");\n        if (window.confirm(confirmMessage)) {\n            const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState();\n            store.deleteInvoice(invoice.id);\n            if (previewInvoice && previewInvoice.id === invoice.id) {\n                setPreviewInvoice(null);\n            }\n            setTimeout(()=>{\n                const updatedInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                setFilteredInvoices(updatedInvoices);\n            }, 100);\n            showSuccessMessage(\"تم حذف الفاتورة \".concat(invoice.id, \" بنجاح! \\uD83D\\uDDD1️\"));\n        }\n    };\n    const getTotalStats = ()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const count = filteredInvoices.length;\n        const avgAmount = count > 0 ? total / count : 0;\n        // حساب المبالغ المدفوعة والمتبقية\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const totalRemaining = total - totalPaid;\n        // حساب عدد الفواتير حسب حالة الدفع\n        const paidFullCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const paidPartialCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        // حساب نسبة التحصيل\n        const collectionRate = total > 0 ? totalPaid / total * 100 : 0;\n        return {\n            total,\n            count,\n            avgAmount,\n            totalPaid,\n            totalRemaining,\n            paidFullCount,\n            paidPartialCount,\n            unpaidCount,\n            collectionRate\n        };\n    };\n    const stats = getTotalStats();\n    // حساب إحصائيات اليوم الحالي مع مقارنة بالأمس\n    const getTodayStats = ()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const todayStr = today.toDateString();\n        const yesterdayStr = yesterday.toDateString();\n        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === todayStr);\n        const yesterdayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === yesterdayStr);\n        const todayTotal = todayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const todayPaid = todayInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const todayRemaining = todayTotal - todayPaid;\n        const yesterdayTotal = yesterdayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const yesterdayCount = yesterdayInvoices.length;\n        const todayPaidFullCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const todayPaidPartialCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const todayUnpaidCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const todayCollectionRate = todayTotal > 0 ? todayPaid / todayTotal * 100 : 0;\n        // حساب التغيير مقارنة بالأمس\n        const countChange = todayInvoices.length - yesterdayCount;\n        const revenueChange = todayTotal - yesterdayTotal;\n        const countChangePercent = yesterdayCount > 0 ? countChange / yesterdayCount * 100 : 0;\n        const revenueChangePercent = yesterdayTotal > 0 ? revenueChange / yesterdayTotal * 100 : 0;\n        return {\n            count: todayInvoices.length,\n            total: todayTotal,\n            paid: todayPaid,\n            remaining: todayRemaining,\n            paidFullCount: todayPaidFullCount,\n            paidPartialCount: todayPaidPartialCount,\n            unpaidCount: todayUnpaidCount,\n            collectionRate: todayCollectionRate,\n            countChange,\n            revenueChange,\n            countChangePercent,\n            revenueChangePercent,\n            yesterdayCount,\n            yesterdayTotal\n        };\n    };\n    const todayStats = getTodayStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"إدارة الفواتير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-indigo-500 to-purple-600 text-white rounded-xl p-6 mb-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"\\uD83D\\uDCCA إحصائيات اليوم الحالي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-sm\",\n                                            children: new Date().toLocaleDateString(\"ar-EG\", {\n                                                weekday: \"long\",\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: todayStats.count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"فواتير اليوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.countChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.countChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.countChange > 0 ? \"↗️\" : \"↘️\",\n                                                    Math.abs(todayStats.countChange),\n                                                    \" عن الأمس\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"إجمالي المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            todayStats.revenueChange !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 flex items-center justify-center gap-1 \".concat(todayStats.revenueChange > 0 ? \"text-green-200\" : \"text-red-200\"),\n                                                children: [\n                                                    todayStats.revenueChange > 0 ? \"↗️\" : \"↘️\",\n                                                    todayStats.revenueChangePercent.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.paid)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"المبلغ المحصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: [\n                                                    \"متبقي: \",\n                                                    formatCurrency(todayStats.remaining)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    todayStats.collectionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"نسبة التحصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-indigo-100 mt-1\",\n                                                children: todayStats.count > 0 ? \"متوسط: \".concat(formatCurrency(todayStats.total / todayStats.count)) : \"لا توجد فواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-indigo-400 border-opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-green-200\",\n                                                    children: todayStats.paidFullCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-yellow-200\",\n                                                    children: todayStats.paidPartialCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-red-200\",\n                                                    children: todayStats.unpaidCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-700\",\n                                        children: \"إجمالي الفواتير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: stats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg border-r-4 border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"إجمالي المبلغ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatCurrency(stats.total)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 p-4 rounded-lg border-r-4 border-emerald-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-emerald-700\",\n                                        children: \"المبلغ المدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-emerald-800\",\n                                        children: formatCurrency(stats.totalPaid)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg border-r-4 border-red-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-red-700\",\n                                        children: \"المبلغ المتبقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-800\",\n                                        children: formatCurrency(stats.totalRemaining)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-teal-50 p-4 rounded-lg border-r-4 border-teal-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-teal-700\",\n                                        children: \"مدفوع بالكامل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-teal-800\",\n                                        children: stats.paidFullCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-teal-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-orange-700\",\n                                        children: \"مدفوع جزئياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: stats.paidPartialCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg border-r-4 border-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-700\",\n                                        children: \"غير مدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: stats.unpaidCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-700\",\n                                        children: \"نسبة التحصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: [\n                                            stats.collectionRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: [\n                                            \"متوسط الفاتورة: \",\n                                            formatCurrency(stats.avgAmount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البحث\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث بالاسم، الهاتف، أو رقم الفاتورة\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"فلترة حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterBy,\n                                            onChange: (e)=>setFilterBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"consultation\",\n                                                    children: \"استشارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pattern\",\n                                                    children: \"باترون\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"photography\",\n                                                    children: \"تصوير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shipping\",\n                                                    children: \"شحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high_value\",\n                                                    children: \"قيمة عالية (+1000 جنيه)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"recent\",\n                                                    children: \"الأسبوع الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_full\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_partial\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"unpaid\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ترتيب حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_desc\",\n                                                    children: \"التاريخ (الأحدث أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_asc\",\n                                                    children: \"التاريخ (الأقدم أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_desc\",\n                                                    children: \"المبلغ (الأعلى أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_asc\",\n                                                    children: \"المبلغ (الأقل أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"client_name\",\n                                                    children: \"اسم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined),\n            filteredInvoices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-lg mb-4\",\n                        children: invoices.length === 0 ? \"لا توجد فواتير محفوظة\" : \"لا توجد نتائج للبحث\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 11\n                    }, undefined),\n                    invoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"قم بإنشاء فاتورة جديدة لتظهر هنا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 559,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: [\n                                                        \"فاتورة #\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"السيلز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.salesperson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الخدمات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getServiceNames(invoice.services)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"طريقة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.paymentMethod\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"حالة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: invoice.paymentStatus || \"غير محدد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المدفوع / المتبقي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatCurrency(invoice.paidAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" / \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                            children: formatCurrency(invoice.remainingAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-2 rounded text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-700 font-medium\",\n                                                children: \"\\uD83D\\uDE9A يتطلب شحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleExportPDF(invoice),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDCC4 PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePreviewInvoice(invoice),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDC41️ معاينة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(invoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83E\\uDDFE POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteInvoice(invoice),\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2\",\n                                        children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, invoice.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, undefined),\n            previewInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"معاينة الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClosePreview,\n                                    className: \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    invoice: previewInvoice,\n                                    onEdit: handleEditFromPreview,\n                                    onExportPDF: handleExportFromPreview\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(previewInvoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83E\\uDDFE فاتورة POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteInvoice(previewInvoice),\n                                                className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, undefined),\n            posInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                invoice: posInvoice,\n                onClose: ()=>setPosInvoice(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 720,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicesList, \"vNSHrZ6bGj05wXDBkzjUPPk++oo=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore\n    ];\n});\n_c = InvoicesList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicesList);\nvar _c;\n$RefreshReg$(_c, \"InvoicesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicesList.tsx\n"));

/***/ })

});