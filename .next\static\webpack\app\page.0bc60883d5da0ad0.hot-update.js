"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/pdfGenerator.ts":
/*!***********************************!*\
  !*** ./src/utils/pdfGenerator.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateInvoicePDF: function() { return /* binding */ generateInvoicePDF; },\n/* harmony export */   generateInvoicePDFFromHTML: function() { return /* binding */ generateInvoicePDFFromHTML; }\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// دالة تنسيق العملة المحسنة\nconst formatCurrency = (amount)=>{\n    const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n    if (isNaN(numericAmount)) {\n        return \"0.00 جنيه\";\n    }\n    return new Intl.NumberFormat(\"ar-EG\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(numericAmount) + \" جنيه\";\n};\n// دالة الحصول على اسم الخدمة\nconst getServiceName = (serviceType)=>{\n    const serviceNames = {\n        consultation: \"استشارة\",\n        pattern: \"باترون\",\n        pattern_printing: \"طباعة باترون\",\n        manufacturing: \"تصنيع\",\n        samples: \"عينات\",\n        shipping: \"شحن\",\n        marketing: \"تسويق\",\n        photography: \"تصوير\",\n        products: \"منتجات\",\n        raw_purchases: \"مشتريات خام\"\n    };\n    return serviceNames[serviceType] || serviceType;\n};\n// دالة الحصول على اسم طريقة الدفع\nconst getPaymentMethodName = (method)=>{\n    const methods = {\n        cash: \"نقدي\",\n        bank_transfer: \"تحويل بنكي\",\n        credit_card: \"بطاقة ائتمان\",\n        installments: \"أقساط\"\n    };\n    return methods[method] || method;\n};\n// دالة الحصول على اسم وسيلة التواصل\nconst getContactMethodName = (method)=>{\n    const methods = {\n        phone: \"هاتف\",\n        whatsapp: \"واتساب\",\n        email: \"بريد إلكتروني\",\n        facebook: \"فيسبوك\",\n        instagram: \"انستغرام\",\n        direct: \"مباشر\",\n        meta: \"ميتا\",\n        meeting: \"لقاء مباشر\"\n    };\n    return methods[method] || method;\n};\n// دالة تنسيق التاريخ\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"ar-EG\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n};\n// دالة حساب تفاصيل الخدمة\nconst calculateServiceDetails = (service)=>{\n    let serviceTotal = 0;\n    let serviceDetails = \"\";\n    let quantity = \"\";\n    let unitPrice = 0;\n    switch(service.serviceType){\n        case \"consultation\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"\".concat(service.topic || \"استشارة\", \" - \").concat(service.hours || 0, \" ساعة\");\n            quantity = \"\".concat(service.hours || 0, \" ساعة\");\n            unitPrice = service.cost || 0;\n            break;\n        case \"pattern\":\n            var _service_models, _service_models1, _service_models2, _service_models3;\n            serviceTotal = ((_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.reduce((sum, model)=>sum + (model.finalAmount || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.length) || 0, \" نموذج باترون\");\n            quantity = \"\".concat(((_service_models2 = service.models) === null || _service_models2 === void 0 ? void 0 : _service_models2.length) || 0, \" نموذج\");\n            unitPrice = serviceTotal / (((_service_models3 = service.models) === null || _service_models3 === void 0 ? void 0 : _service_models3.length) || 1);\n            break;\n        case \"pattern_printing\":\n            var _service_files, _service_files1, _service_files2;\n            serviceTotal = ((_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n            serviceDetails = \"طباعة \".concat(((_service_files1 = service.files) === null || _service_files1 === void 0 ? void 0 : _service_files1.length) || 0, \" ملف\");\n            quantity = \"\".concat(((_service_files2 = service.files) === null || _service_files2 === void 0 ? void 0 : _service_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n            unitPrice = serviceTotal;\n            break;\n        case \"shipping\":\n            serviceTotal = service.total || 0;\n            serviceDetails = \"شحن \".concat(service.item || \"منتج\");\n            quantity = \"\".concat(service.quantity || 1);\n            unitPrice = service.unitPrice || 0;\n            break;\n        case \"products\":\n            var _service_items, _service_items1, _service_items2, _service_items3;\n            serviceTotal = ((_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_items1 = service.items) === null || _service_items1 === void 0 ? void 0 : _service_items1.length) || 0, \" منتج\");\n            quantity = \"\".concat(((_service_items2 = service.items) === null || _service_items2 === void 0 ? void 0 : _service_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_items3 = service.items) === null || _service_items3 === void 0 ? void 0 : _service_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n            break;\n        case \"manufacturing\":\n            var _service_models4, _service_models5, _service_models6, _service_models7;\n            serviceTotal = ((_service_models4 = service.models) === null || _service_models4 === void 0 ? void 0 : _service_models4.reduce((sum, model)=>sum + ((model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0)) * (model.quantity || 1), 0)) || 0;\n            serviceDetails = \"تصنيع \".concat(((_service_models5 = service.models) === null || _service_models5 === void 0 ? void 0 : _service_models5.length) || 0, \" نموذج\");\n            quantity = \"\".concat(((_service_models6 = service.models) === null || _service_models6 === void 0 ? void 0 : _service_models6.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_models7 = service.models) === null || _service_models7 === void 0 ? void 0 : _service_models7.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 1);\n            break;\n        case \"photography\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"تصوير \".concat(service.location === \"indoor\" ? \"داخلي\" : \"خارجي\").concat(service.withModel ? \" مع موديل\" : \"\");\n            quantity = \"1 جلسة\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"marketing\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = service.description || \"خدمات تسويقية\";\n            quantity = service.duration || \"1 شهر\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"samples\":\n            serviceTotal = service.cost || 0;\n            serviceDetails = \"إنتاج عينات\";\n            quantity = \"1\";\n            unitPrice = service.cost || 0;\n            break;\n        case \"raw_purchases\":\n            var _service_materials, _service_materials1, _service_materials2, _service_materials3;\n            serviceTotal = ((_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.reduce((sum, material)=>sum + (material.total || 0), 0)) || 0;\n            serviceDetails = \"\".concat(((_service_materials1 = service.materials) === null || _service_materials1 === void 0 ? void 0 : _service_materials1.length) || 0, \" مادة خام\");\n            quantity = \"\".concat(((_service_materials2 = service.materials) === null || _service_materials2 === void 0 ? void 0 : _service_materials2.reduce((sum, material)=>sum + (material.quantity || 0), 0)) || 0);\n            unitPrice = serviceTotal / (((_service_materials3 = service.materials) === null || _service_materials3 === void 0 ? void 0 : _service_materials3.reduce((sum, material)=>sum + (material.quantity || 0), 0)) || 1);\n            break;\n        default:\n            serviceTotal = service.cost || service.total || 0;\n            serviceDetails = getServiceName(service.serviceType);\n            quantity = \"1\";\n            unitPrice = serviceTotal;\n    }\n    return {\n        total: serviceTotal,\n        details: serviceDetails,\n        quantity,\n        unitPrice\n    };\n};\n// الدالة الرئيسية لتصدير PDF المحسنة\nconst generateInvoicePDF = async (invoice)=>{\n    try {\n        // البحث عن عنصر المعاينة\n        const element = document.getElementById(\"invoice-content\");\n        if (!element) {\n            throw new Error(\"عنصر المعاينة غير موجود\");\n        }\n        // التحقق من صحة العنصر\n        const rect = element.getBoundingClientRect();\n        if (rect.width === 0 || rect.height === 0) {\n            throw new Error(\"العنصر غير مرئي أو لا يحتوي على محتوى\");\n        }\n        await generatePDFFromPreview(element, invoice);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء ملف PDF: \" + error.message);\n    }\n};\n// دالة تصدير PDF محسنة من المعاينة\nconst generatePDFFromPreview = async (element, invoice)=>{\n    let canvas = null;\n    try {\n        // تحسين العنصر للتصدير\n        const originalStyle = element.style.cssText;\n        element.style.cssText = \"\\n      \".concat(originalStyle, \"\\n      width: 794px !important;\\n      max-width: 794px !important;\\n      min-height: 1123px !important;\\n      background: white !important;\\n      font-family: Arial, sans-serif !important;\\n      direction: rtl !important;\\n      overflow: visible !important;\\n    \");\n        // انتظار تحديث التخطيط\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // إنشاء canvas محسن للجودة العالية\n        canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, {\n            scale: 3,\n            useCORS: true,\n            allowTaint: false,\n            backgroundColor: \"#ffffff\",\n            logging: false,\n            width: 794,\n            height: element.scrollHeight,\n            scrollX: 0,\n            scrollY: 0,\n            windowWidth: 794,\n            windowHeight: element.scrollHeight,\n            foreignObjectRendering: false,\n            imageTimeout: 20000,\n            removeContainer: true,\n            onclone: (clonedDoc)=>{\n                // تحسين النصوص العربية والتخطيط في النسخة المستنسخة\n                const clonedElement = clonedDoc.getElementById(\"invoice-content\");\n                if (clonedElement) {\n                    // تحسين الخطوط والاتجاه\n                    clonedElement.style.fontFamily = 'Arial, \"Helvetica Neue\", Helvetica, sans-serif';\n                    clonedElement.style.direction = \"rtl\";\n                    clonedElement.style.textAlign = \"right\";\n                    clonedElement.style.fontSize = \"14px\";\n                    clonedElement.style.lineHeight = \"1.6\";\n                    clonedElement.style.color = \"#000000\";\n                    // تحسين جودة الصور\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.imageRendering = \"high-quality\";\n                        img.style.imageRendering = \"-webkit-optimize-contrast\";\n                    });\n                    // تحسين الجداول\n                    const tables = clonedElement.querySelectorAll(\"table\");\n                    tables.forEach((table)=>{\n                        table.style.borderCollapse = \"collapse\";\n                        table.style.width = \"100%\";\n                        table.style.fontSize = \"13px\";\n                    });\n                    // تحسين الخلفيات المتدرجة للطباعة\n                    const gradients = clonedElement.querySelectorAll('[class*=\"gradient\"]');\n                    gradients.forEach((el)=>{\n                        const computedStyle = window.getComputedStyle(el);\n                        if (computedStyle.backgroundImage.includes(\"gradient\")) {\n                            el.style.backgroundImage = \"none\";\n                            el.style.backgroundColor = \"#f8fafc\";\n                        }\n                    });\n                    // تحسين الحدود والظلال\n                    const shadows = clonedElement.querySelectorAll('[class*=\"shadow\"]');\n                    shadows.forEach((el)=>{\n                        el.style.boxShadow = \"0 1px 3px rgba(0,0,0,0.1)\";\n                    });\n                }\n            }\n        });\n        // استعادة الستايل الأصلي\n        element.style.cssText = originalStyle;\n        if (!canvas) {\n            throw new Error(\"فشل في إنشاء Canvas\");\n        }\n        // إنشاء PDF محسن\n        const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__.jsPDF({\n            orientation: \"portrait\",\n            unit: \"pt\",\n            format: \"a4\",\n            compress: true,\n            precision: 2\n        });\n        // تحويل Canvas إلى صورة عالية الجودة\n        const imgData = canvas.toDataURL(\"image/jpeg\", 0.95);\n        // حساب الأبعاد بدقة\n        const pdfWidth = 595.28; // عرض A4 بالنقاط\n        const pdfHeight = 841.89; // ارتفاع A4 بالنقاط\n        const margin = 40; // هامش 40 نقطة\n        const maxWidth = pdfWidth - margin * 2;\n        const maxHeight = pdfHeight - margin * 2;\n        // حساب النسبة والأبعاد النهائية\n        const canvasRatio = canvas.width / canvas.height;\n        const pdfRatio = maxWidth / maxHeight;\n        let finalWidth, finalHeight;\n        if (canvasRatio > pdfRatio) {\n            finalWidth = maxWidth;\n            finalHeight = maxWidth / canvasRatio;\n        } else {\n            finalHeight = maxHeight;\n            finalWidth = maxHeight * canvasRatio;\n        }\n        // توسيط الصورة\n        const x = margin + (maxWidth - finalWidth) / 2;\n        const y = margin + (maxHeight - finalHeight) / 2;\n        // إضافة الصورة للPDF\n        pdf.addImage(imgData, \"JPEG\", x, y, finalWidth, finalHeight, undefined, \"FAST\");\n        // إنشاء اسم الملف محسن\n        const date = new Date().toISOString().split(\"T\")[0];\n        const sanitizedClientName = invoice.client.name.replace(/[^\\u0600-\\u06FF\\w\\s-]/g, \"\") // السماح بالأحرف العربية\n        .trim().substring(0, 20); // تحديد الطول\n        const filename = \"فاتورة_\".concat(sanitizedClientName, \"_\").concat(invoice.id, \"_\").concat(date, \".pdf\");\n        // حفظ الملف\n        pdf.save(filename);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء PDF: \" + error.message);\n    } finally{\n        // تنظيف الذاكرة\n        if (canvas) {\n            canvas.remove();\n            canvas = null;\n        }\n    }\n};\n// دالة إنشاء PDF من HTML مباشرة (بديل محسن)\nconst generateInvoicePDFFromHTML = async (invoice)=>{\n    try {\n        // الحصول على إعدادات الشركة\n        const settings = JSON.parse(localStorage.getItem(\"company-settings\") || \"{}\");\n        // إنشاء HTML محسن للطباعة\n        const htmlContent = createPrintableInvoiceHTML(invoice, settings);\n        // إنشاء نافذة جديدة للطباعة\n        const printWindow = window.open(\"\", \"_blank\", \"width=794,height=1123\");\n        if (!printWindow) {\n            throw new Error(\"فشل في فتح نافذة الطباعة\");\n        }\n        // كتابة المحتوى\n        printWindow.document.write(htmlContent);\n        printWindow.document.close();\n        // انتظار تحميل المحتوى\n        await new Promise((resolve)=>{\n            printWindow.onload = resolve;\n            setTimeout(resolve, 1000); // fallback\n        });\n        // البحث عن العنصر في النافذة الجديدة\n        const element = printWindow.document.querySelector(\".invoice-container\");\n        if (!element) {\n            printWindow.close();\n            throw new Error(\"عنصر الفاتورة غير موجود\");\n        }\n        // إنشاء canvas من النافذة الجديدة\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, {\n            scale: 2,\n            useCORS: true,\n            allowTaint: false,\n            backgroundColor: \"#ffffff\",\n            logging: false,\n            width: 794,\n            height: 1123,\n            foreignObjectRendering: false,\n            imageTimeout: 15000\n        });\n        // إغلاق النافذة\n        printWindow.close();\n        if (!canvas) {\n            throw new Error(\"فشل في إنشاء Canvas\");\n        }\n        // إنشاء PDF\n        const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__.jsPDF({\n            orientation: \"portrait\",\n            unit: \"pt\",\n            format: \"a4\",\n            compress: true\n        });\n        const imgData = canvas.toDataURL(\"image/jpeg\", 0.95);\n        pdf.addImage(imgData, \"JPEG\", 0, 0, 595.28, 841.89, undefined, \"FAST\");\n        // حفظ الملف\n        const date = new Date().toISOString().split(\"T\")[0];\n        const sanitizedClientName = invoice.client.name.replace(/[^\\u0600-\\u06FF\\w\\s-]/g, \"\").trim().substring(0, 20);\n        const filename = \"فاتورة_\".concat(sanitizedClientName, \"_\").concat(invoice.id, \"_\").concat(date, \".pdf\");\n        pdf.save(filename);\n    } catch (error) {\n        throw new Error(\"فشل في إنشاء PDF: \" + error.message);\n    }\n};\n// دالة إنشاء HTML محسن للطباعة\nconst createPrintableInvoiceHTML = (invoice, settings)=>{\n    const logoSrc = settings.companyLogo || \"\";\n    const stampSrc = settings.companyStamp || \"\";\n    return '\\n<!DOCTYPE html>\\n<html dir=\"rtl\" lang=\"ar\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>فاتورة '.concat(invoice.client.name, '</title>\\n    <style>\\n        * {\\n            margin: 0;\\n            padding: 0;\\n            box-sizing: border-box;\\n        }\\n\\n        body {\\n            font-family: \\'Arial\\', sans-serif;\\n            font-size: 12pt;\\n            line-height: 1.4;\\n            color: #333;\\n            background: white;\\n            direction: rtl;\\n        }\\n\\n        .invoice-container {\\n            width: 794px;\\n            min-height: 1123px;\\n            margin: 0 auto;\\n            padding: 40px;\\n            background: white;\\n            position: relative;\\n        }\\n\\n        .header {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding-bottom: 20px;\\n            border-bottom: 2px solid #2196f3;\\n        }\\n\\n        .invoice-info {\\n            flex: 1;\\n            max-width: 350px;\\n        }\\n\\n        .invoice-info h2 {\\n            font-size: 24pt;\\n            color: #2196f3;\\n            margin-bottom: 15px;\\n            font-weight: bold;\\n        }\\n\\n        .info-box {\\n            background: transparent;\\n            padding: 15px;\\n            border: none;\\n        }\\n\\n        .info-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 8px;\\n            padding: 4px 0;\\n        }\\n\\n        .info-label {\\n            font-weight: bold;\\n            color: #495057;\\n            min-width: 120px;\\n        }\\n\\n        .info-value {\\n            color: #212529;\\n            font-weight: 500;\\n        }\\n\\n        .logo-section {\\n            flex-shrink: 0;\\n            text-align: center;\\n            margin-left: 30px;\\n        }\\n\\n        .logo-section img {\\n            width: 180px;\\n            height: 180px;\\n            object-fit: contain;\\n            border-radius: 8px;\\n        }\\n\\n        .company-description {\\n            margin-top: 10px;\\n            font-size: 11pt;\\n            color: #666;\\n            max-width: 180px;\\n            line-height: 1.3;\\n        }\\n\\n        .services-section {\\n            margin-bottom: 30px;\\n        }\\n\\n        .section-title {\\n            font-size: 16pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            margin-bottom: 15px;\\n            padding-bottom: 8px;\\n            border-bottom: 1px solid #e9ecef;\\n        }\\n\\n        .services-table {\\n            width: 100%;\\n            border-collapse: collapse;\\n            margin-bottom: 20px;\\n            font-size: 11pt;\\n        }\\n\\n        .services-table th {\\n            background: #2196f3;\\n            color: white;\\n            padding: 12px 8px;\\n            text-align: center;\\n            font-weight: bold;\\n            border: 1px solid #1976d2;\\n        }\\n\\n        .services-table td {\\n            padding: 10px 8px;\\n            text-align: center;\\n            border: 1px solid #ddd;\\n            vertical-align: middle;\\n        }\\n\\n        .services-table tbody tr:nth-child(even) {\\n            background: #f8f9fa;\\n        }\\n\\n        .services-table tbody tr:hover {\\n            background: #e3f2fd;\\n        }\\n\\n        .service-type {\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .service-details {\\n            text-align: right;\\n            padding-right: 12px;\\n        }\\n\\n        .service-price,\\n        .service-total {\\n            font-weight: bold;\\n            color: #4caf50;\\n        }\\n\\n        .total-section {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background: #f8f9fa;\\n            border-radius: 8px;\\n            border: 1px solid #e9ecef;\\n        }\\n\\n        .total-details {\\n            flex: 1;\\n        }\\n\\n        .total-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 10px;\\n            padding: 8px 0;\\n            font-size: 13pt;\\n        }\\n\\n        .total-label {\\n            font-weight: bold;\\n            color: #495057;\\n        }\\n\\n        .total-value {\\n            font-weight: bold;\\n            color: #212529;\\n        }\\n\\n        .grand-total {\\n            color: #2196f3;\\n            font-size: 16pt;\\n        }\\n\\n        .paid-amount {\\n            color: #4caf50;\\n        }\\n\\n        .remaining-amount {\\n            color: #ff9800;\\n        }\\n\\n        .remaining-zero {\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp {\\n            flex-shrink: 0;\\n            margin-left: 30px;\\n            text-align: center;\\n            background: #e3f2fd;\\n            padding: 15px;\\n            border-radius: 50%;\\n            width: 120px;\\n            height: 120px;\\n            display: flex;\\n            flex-direction: column;\\n            justify-content: center;\\n            align-items: center;\\n            border: 3px solid #2196f3;\\n            transform: rotate(-5deg);\\n        }\\n\\n        .stamp-title {\\n            font-size: 14pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-status {\\n            font-size: 10pt;\\n            color: #1976d2;\\n            font-weight: bold;\\n        }\\n\\n        .footer {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-top: 40px;\\n            padding: 20px;\\n            background: white;\\n            border-top: 1px solid #e9ecef;\\n            position: relative;\\n            direction: rtl;\\n        }\\n\\n        .footer-left {\\n            flex: 1;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .footer-right {\\n            flex: 1;\\n            text-align: right;\\n            direction: rtl;\\n            position: relative;\\n        }\\n\\n        .contact-info,\\n        .headquarters-info {\\n            font-size: 11pt;\\n            line-height: 1.5;\\n            color: #495057;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info div,\\n        .headquarters-info div {\\n            margin-bottom: 5px;\\n        }\\n\\n        .company-stamp {\\n            position: relative;\\n            margin-top: 15px;\\n            transform: none;\\n            opacity: 1;\\n            background: white;\\n            border: 1px solid #ddd;\\n            border-radius: 8px;\\n            padding: 10px;\\n            width: 160px;\\n            height: auto;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n\\n        .company-stamp img {\\n            width: 140px;\\n            height: auto;\\n            max-height: 100px;\\n            object-fit: contain;\\n            border-radius: 4px;\\n        }\\n\\n        .stamp-content {\\n            background: rgba(33, 150, 243, 0.1);\\n            border: 2px solid #2196f3;\\n            border-radius: 50%;\\n            width: 100px;\\n            height: 100px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .stamp-title {\\n            font-size: 14pt;\\n            margin-bottom: 2px;\\n        }\\n\\n        .stamp-subtitle {\\n            font-size: 10pt;\\n        }\\n\\n        .thank-you {\\n            text-align: center;\\n            margin-top: 30px;\\n            font-size: 14pt;\\n            font-weight: bold;\\n            color: #2196f3;\\n            padding: 15px;\\n            background: #f8f9fa;\\n            border-radius: 8px;\\n            border: 1px solid #e9ecef;\\n        }\\n\\n        @media print {\\n            body {\\n                -webkit-print-color-adjust: exact;\\n                print-color-adjust: exact;\\n            }\\n\\n            .invoice-container {\\n                width: 100%;\\n                max-width: none;\\n                margin: 0;\\n                padding: 20px;\\n            }\\n\\n            .header,\\n            .services-section,\\n            .total-section,\\n            .footer {\\n                page-break-inside: avoid;\\n                break-inside: avoid;\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\"invoice-container\">\\n        <!-- Header Section -->\\n        <div class=\"header\">\\n            <div class=\"invoice-info\">\\n                <h2>فاتورة خدمات</h2>\\n                <div class=\"info-box\">\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الفاتورة:</span>\\n                        <span class=\"info-value\">').concat(invoice.id, '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">التاريخ:</span>\\n                        <span class=\"info-value\">').concat(formatDate(invoice.createdAt), '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">اسم العميل:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.name, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.businessName ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">الاسم التجاري:</span>\\n                        <span class=\"info-value\">'.concat(invoice.client.businessName, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الهاتف:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.phone, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.province ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">المحافظة:</span>\\n                        <span class=\"info-value\">'.concat(invoice.client.province, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">كود العميل:</span>\\n                        <span class=\"info-value\">').concat(invoice.client.clientCode || \"تلقائي\", '</span>\\n                    </div>\\n                    <div class=\"info-row\" style=\"border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;\">\\n                        <span class=\"info-label\">وسيلة التواصل:</span>\\n                        <span class=\"info-value\">').concat(getContactMethodName(invoice.client.contactMethod), '</span>\\n                    </div>\\n                </div>\\n            </div>\\n\\n            <div class=\"logo-section\">\\n                ').concat(logoSrc ? '<img src=\"'.concat(logoSrc, '\" alt=\"شعار الشركة\">') : '\\n                <div style=\"width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9; border-radius: 8px;\">\\n                    <span style=\"color: #999; font-size: 14pt;\">شعار الشركة</span>\\n                </div>\\n                ', '\\n                <div class=\"company-description\">\\n                    ').concat(settings.description || \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\", '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <!-- Services Section -->\\n        <div class=\"services-section\">\\n            <h3 class=\"section-title\">تفاصيل الخدمات والمنتجات</h3>\\n\\n            <table class=\"services-table\">\\n                <thead>\\n                    <tr>\\n                        <th style=\"width: 50px;\">م</th>\\n                        <th style=\"width: 120px;\">نوع الخدمة</th>\\n                        <th style=\"width: 200px;\">التفاصيل</th>\\n                        <th style=\"width: 100px;\">الكمية/المقاس</th>\\n                        <th style=\"width: 120px;\">السعر</th>\\n                        <th style=\"width: 120px;\">المجموع</th>\\n                    </tr>\\n                </thead>\\n                <tbody>\\n                    ').concat(invoice.services.map((service, index)=>{\n        const serviceData = calculateServiceDetails(service);\n        return \"\\n                        <tr>\\n                            <td>\".concat(index + 1, '</td>\\n                            <td class=\"service-type\">').concat(getServiceName(service.serviceType), '</td>\\n                            <td class=\"service-details\">').concat(serviceData.details, \"</td>\\n                            <td>\").concat(serviceData.quantity, '</td>\\n                            <td class=\"service-price\">').concat(formatCurrency(serviceData.unitPrice), '</td>\\n                            <td class=\"service-total\">').concat(formatCurrency(serviceData.total), \"</td>\\n                        </tr>\\n                      \");\n    }).join(\"\"), '\\n                </tbody>\\n            </table>\\n        </div>\\n\\n        <!-- Total Summary -->\\n        <div class=\"total-section\">\\n            <div class=\"total-details\">\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المجموع الكلي:</span>\\n                    <span class=\"total-value grand-total\">').concat(formatCurrency(invoice.total), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المدفوع:</span>\\n                    <span class=\"total-value paid-amount\">').concat(formatCurrency(invoice.paidAmount || 0), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المتبقي:</span>\\n                    <span class=\"total-value remaining-amount ').concat((invoice.remainingAmount || 0) > 0 ? \"remaining-positive\" : \"remaining-zero\", '\">\\n                        ').concat(formatCurrency(invoice.remainingAmount || 0), '\\n                    </span>\\n                </div>\\n                <div class=\"total-row\" style=\"border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;\">\\n                    <span class=\"total-label\">طريقة الدفع:</span>\\n                    <span class=\"total-value\">').concat(getPaymentMethodName(invoice.paymentMethod), '</span>\\n                </div>\\n            </div>\\n\\n            <div class=\"payment-stamp\">\\n                <div class=\"stamp-title\">OKA Group</div>\\n                <div class=\"stamp-status\">').concat(invoice.paymentStatus || \"غير محدد\", '</div>\\n            </div>\\n        </div>\\n\\n        <!-- Footer -->\\n        <div class=\"footer\">\\n            <div class=\"footer-left\">\\n                <div class=\"contact-info\">\\n                    <div><strong>معلومات التواصل:</strong></div>\\n                    <div>\\uD83D\\uDCE7 البريد الإلكتروني: <EMAIL></div>\\n                    <div>\\uD83D\\uDCDE للشكاوى والاقتراحات: 0114954118</div>\\n                </div>\\n            </div>\\n\\n            <div class=\"footer-right\">\\n                <div class=\"headquarters-info\">\\n                    <div><strong>المقر الإداري:</strong></div>\\n                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>\\n                </div>\\n\\n                <div class=\"company-stamp\">\\n                    ').concat(stampSrc ? '\\n                    <img src=\"'.concat(stampSrc, '\" alt=\"ختم الشركة\">\\n                    ') : '\\n                    <div class=\"stamp-content\">\\n                        <div class=\"stamp-title\">OKA</div>\\n                        <div class=\"stamp-subtitle\">GROUP</div>\\n                    </div>\\n                    ', '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div class=\"thank-you\">\\n            شكراً لتعاملكم معنا\\n        </div>\\n    </div>\\n</body>\\n</html>\\n  ');\n};\n// تصدير الدالة الرئيسية\n/* harmony default export */ __webpack_exports__[\"default\"] = (generateInvoicePDF);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/pdfGenerator.ts\n"));

/***/ })

});