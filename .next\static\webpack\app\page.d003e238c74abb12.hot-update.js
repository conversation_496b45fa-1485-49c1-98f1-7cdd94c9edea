"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/settingsStore.ts":
/*!************************************!*\
  !*** ./src/store/settingsStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeSettingsStore: function() { return /* binding */ initializeSettingsStore; },\n/* harmony export */   useSettingsStore: function() { return /* binding */ useSettingsStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// Default company settings\nconst defaultSettings = {\n    companyName: \"OKA Group\",\n    companyLogo: null,\n    companyStamp: null,\n    email: \"<EMAIL>\",\n    complaintsPhone: \"0114954118\",\n    address: \"73 ش 6 اكتوبر الجراش جسر السويس\",\n    slogan: \"سعداء خدمتكم OKA Group\",\n    wishMessage: \"نتمني لكم دوام التوفيق ...\",\n    description: \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\"\n};\n// Load settings from storage on initialization\nconst loadInitialSettings = ()=>{\n    if (true) {\n        const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"companySettings\", defaultSettings);\n        if (result.success) {\n            console.log(\"✅ Company settings loaded on initialization\");\n            return {\n                ...defaultSettings,\n                ...result.data\n            };\n        } else {\n            console.error(\"❌ Error loading company settings on initialization:\", result.message);\n        }\n    }\n    return defaultSettings;\n};\nconst useSettingsStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        settings: loadInitialSettings(),\n        updateSettings: (newSettings)=>{\n            const { settings } = get();\n            const updatedSettings = {\n                ...settings,\n                ...newSettings\n            };\n            set({\n                settings: updatedSettings\n            });\n            // Auto-save to secure storage\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"companySettings\", updatedSettings);\n            if (saveResult.success) {\n                console.log(\"✅ Company settings saved successfully\");\n            } else {\n                console.error(\"❌ Error saving company settings:\", saveResult.message);\n            }\n        },\n        uploadLogo: async (logoFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    const logoDataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    // Update settings with new logo\n                    get().updateSettings({\n                        companyLogo: logoDataUrl\n                    });\n                    console.log(\"✅ Company logo uploaded successfully\");\n                    resolve(logoDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company logo\");\n                    reject(new Error(\"فشل في رفع شعار الشركة\"));\n                };\n                reader.readAsDataURL(logoFile);\n            });\n        },\n        uploadStamp: async (stampFile)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target;\n                    const stampDataUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    // Update settings with new stamp\n                    get().updateSettings({\n                        companyStamp: stampDataUrl\n                    });\n                    console.log(\"✅ Company stamp uploaded successfully\");\n                    resolve(stampDataUrl);\n                };\n                reader.onerror = ()=>{\n                    console.error(\"❌ Error uploading company stamp\");\n                    reject(new Error(\"فشل في رفع ختم الشركة\"));\n                };\n                reader.readAsDataURL(stampFile);\n            });\n        },\n        loadSettings: async ()=>{\n            try {\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n                if (result.success) {\n                    set({\n                        settings: {\n                            ...defaultSettings,\n                            ...result.data\n                        }\n                    });\n                    console.log(\"✅ تم تحميل إعدادات الشركة من النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في تحميل إعدادات الشركة:\", result.message);\n                    set({\n                        settings: defaultSettings\n                    });\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في تحميل الإعدادات:\", error);\n                set({\n                    settings: defaultSettings\n                });\n            }\n        },\n        saveSettings: async ()=>{\n            try {\n                const { settings } = get();\n                const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, settings);\n                if (result.success) {\n                    console.log(\"✅ تم حفظ إعدادات الشركة في النظام الموحد\");\n                } else {\n                    console.error(\"❌ فشل في حفظ إعدادات الشركة:\", result.message);\n                }\n            } catch (error) {\n                console.error(\"❌ خطأ حرج في حفظ الإعدادات:\", error);\n            }\n        }\n    }));\n// دالة تهيئة متجر الإعدادات\nconst initializeSettingsStore = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD04 تهيئة متجر الإعدادات...\");\n        // تحميل الإعدادات من النظام الموحد\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.COMPANY_SETTINGS, defaultSettings);\n        if (result.success) {\n            const settings = {\n                ...defaultSettings,\n                ...result.data\n            };\n            useSettingsStore.setState({\n                settings\n            });\n            console.log(\"✅ تم تهيئة متجر الإعدادات بنجاح\");\n        } else {\n            console.error(\"❌ فشل في تحميل الإعدادات، استخدام الافتراضية\");\n            useSettingsStore.setState({\n                settings: defaultSettings\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ فشل في تهيئة متجر الإعدادات:\", error);\n        useSettingsStore.setState({\n            settings: defaultSettings\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/settingsStore.ts\n"));

/***/ })

});