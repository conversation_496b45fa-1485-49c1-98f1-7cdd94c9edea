@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations and styles */
@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-scaleIn {
    animation: scaleIn 0.4s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* RTL improvements */
@layer base {
  html {
    direction: rtl;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
  }
}

/* Form improvements */
@layer components {
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .form-input:focus {
    @apply shadow-md;
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }
}

/* Card improvements */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-lg;
}

.card-header {
  @apply bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4;
}

.card-body {
  @apply p-4;
}

.card-footer {
  @apply border-t border-gray-200 p-4 bg-gray-50;
}

/* Loading states */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
}

/* Responsive text */
@layer utilities {
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .heading-responsive {
    @apply text-lg sm:text-xl lg:text-2xl;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

.form-section {
  @apply bg-white rounded-lg shadow-md p-6 mb-6;
}

.form-field {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-checkbox {
  @apply mr-2;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-green-500;
}

.error-message {
  @apply text-red-500 text-sm mt-1;
}

/* Enhanced Form Components */
@layer components {
  .form-label-enhanced {
    @apply block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-1;
  }

  .form-input-enhanced {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white hover:border-gray-400 text-gray-900 placeholder-gray-500;
  }

  .form-input-enhanced:focus {
    @apply shadow-lg ring-blue-500/20 border-blue-500;
  }

  .error-message-enhanced {
    @apply text-red-500 text-sm mt-2 flex items-center gap-1 animate-fadeIn;
  }

  .btn-primary-enhanced {
    @apply bg-gradient-to-l from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Service Cards */
  .service-card {
    @apply relative block;
  }

  .service-card input:checked + .service-card-content {
    @apply bg-gradient-to-br from-green-50 to-emerald-50 border-green-500 text-green-700;
  }

  .service-card input:checked + .service-card-content .service-icon {
    @apply bg-green-500 text-white;
  }

  .service-card-content {
    @apply flex items-center gap-3 p-4 border-2 border-gray-200 rounded-lg transition-all duration-200 hover:border-gray-300 hover:shadow-md bg-white;
  }

  .service-icon {
    @apply w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-500 transition-all duration-200;
  }

  .service-label {
    @apply text-sm font-medium text-gray-700 transition-colors duration-200;
  }

  /* Contact Method Cards */
  .contact-method-card {
    @apply relative block;
  }

  .contact-method-card input:checked + .contact-method-content {
    @apply bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-500 text-purple-700;
  }

  .contact-method-card input:checked + .contact-method-content .contact-icon {
    @apply bg-purple-500 text-white;
  }

  .contact-method-content {
    @apply flex flex-col items-center gap-2 p-4 border-2 border-gray-200 rounded-lg transition-all duration-200 hover:border-gray-300 hover:shadow-md bg-white text-center;
  }

  .contact-icon {
    @apply w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-500 transition-all duration-200;
  }

  .contact-label {
    @apply text-xs font-medium text-gray-700 transition-colors duration-200;
  }

  /* Service Section Styling */
  .service-section {
    @apply border-b border-gray-100 last:border-b-0 pb-6 last:pb-0;
  }

  /* Enhanced Animations */
  .animate-slideInUp {
    animation: slideInUp 0.5s ease-out;
  }

  .animate-bounceIn {
    animation: bounceIn 0.6s ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* Loading States */
  .btn-loading {
    @apply opacity-75 cursor-not-allowed;
  }

  .btn-loading::after {
    content: '';
    @apply inline-block w-4 h-4 ml-2 border-2 border-current border-r-transparent rounded-full animate-spin;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Print Styles for Invoice */
@media print {
  @page {
    size: A4;
    margin: 15mm;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    font-family: Arial, sans-serif;
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: white;
  }

  .invoice-container {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    border: none !important;
    direction: rtl !important;
  }

  .no-print {
    display: none !important;
  }

  .print-break-before {
    page-break-before: always;
    break-before: page;
  }

  .print-break-after {
    page-break-after: always;
    break-after: page;
  }

  .print-break-inside-avoid {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .print-header {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-bottom: 20pt;
  }

  .print-table {
    page-break-inside: avoid;
    break-inside: avoid;
    width: 100%;
    border-collapse: collapse;
  }

  .print-table th,
  .print-table td {
    border: 1pt solid #000;
    padding: 6pt;
    font-size: 10pt;
  }

  .print-footer {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-top: 20pt;
  }

  /* Hide interactive elements */
  button,
  .btn,
  .hover\:shadow-lg,
  .transition-all {
    display: none !important;
  }

  /* Ensure text is black */
  * {
    color: #000 !important;
    background: transparent !important;
  }

  /* Keep important colors */
  .text-blue-600,
  .bg-blue-600 {
    color: #1976d2 !important;
    background-color: #1976d2 !important;
  }

  .text-green-600,
  .bg-green-600 {
    color: #388e3c !important;
    background-color: #388e3c !important;
  }
}

/* PDF Export Optimizations */
.pdf-optimized {
  width: 794px !important;
  min-height: 1123px !important;
  background: white !important;
  font-family: Arial, sans-serif !important;
  font-size: 12pt !important;
  line-height: 1.4 !important;
  color: #333 !important;
  direction: rtl !important;
  overflow: visible !important;
  position: relative !important;
}

.pdf-optimized * {
  box-sizing: border-box !important;
}

.pdf-optimized img {
  max-width: 100% !important;
  height: auto !important;
  object-fit: contain !important;
}

.pdf-optimized table {
  width: 100% !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
}

.pdf-optimized th,
.pdf-optimized td {
  border: 1px solid #ddd !important;
  padding: 8px !important;
  text-align: center !important;
  vertical-align: middle !important;
  word-wrap: break-word !important;
}

/* Responsive Improvements */
@media (max-width: 640px) {
  .form-input-enhanced {
    @apply text-base; /* Prevent zoom on iOS */
  }

  .service-card-content {
    @apply p-3;
  }

  .contact-method-content {
    @apply p-3;
  }

  .invoice-container {
    @apply p-4;
  }

  .services-table {
    @apply text-xs;
  }

  .services-table th,
  .services-table td {
    @apply p-2;
  }
}
