/**
 * نظام إدارة التخزين الآمن والمحسن
 * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية
 */

import { Invoice, Client } from '@/types';

// إعدادات النظام
const STORAGE_CONFIG = {
  MAX_STORAGE_SIZE: 4 * 1024 * 1024, // 4MB (أقل من حد localStorage)
  BACKUP_INTERVAL: 5 * 60 * 1000, // 5 دقائق
  MAX_BACKUPS: 10, // عدد النسخ الاحتياطية المحفوظة
  ENCRYPTION_KEY: 'infapapp-secure-key-2024', // مفتاح التشفير الأساسي
};

// أنواع البيانات
interface StorageData {
  invoices: Invoice[];
  settings: any;
  counters: { [key: string]: number };
}

interface BackupData {
  data: StorageData;
  timestamp: string;
  version: string;
  checksum: string;
}

interface StorageHealth {
  isHealthy: boolean;
  issues: string[];
  recommendations: string[];
  storageUsed: number;
  storageLimit: number;
  backupsCount: number;
}

/**
 * تشفير أساسي للبيانات الحساسة
 */
const encryptData = (data: string): string => {
  try {
    // تشفير بسيط باستخدام Base64 مع تشويش
    const encoded = btoa(data);
    const scrambled = encoded.split('').reverse().join('');
    return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);
  } catch (error) {
    console.error('❌ Encryption failed:', error);
    return data; // إرجاع البيانات بدون تشفير في حالة الفشل
  }
};

/**
 * فك تشفير البيانات
 */
const decryptData = (encryptedData: string): string => {
  try {
    const decoded = atob(encryptedData);
    const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, '');
    const unscrambled = withoutKey.split('').reverse().join('');
    return atob(unscrambled);
  } catch (error) {
    console.error('❌ Decryption failed:', error);
    return encryptedData; // إرجاع البيانات كما هي في حالة الفشل
  }
};

/**
 * حساب checksum للبيانات
 */
const calculateChecksum = (data: string): string => {
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // تحويل إلى 32bit integer
  }
  return Math.abs(hash).toString(16);
};

/**
 * فحص حالة التخزين
 */
export const checkStorageHealth = (): StorageHealth => {
  const health: StorageHealth = {
    isHealthy: true,
    issues: [],
    recommendations: [],
    storageUsed: 0,
    storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,
    backupsCount: 0,
  };

  try {
    // حساب حجم التخزين المستخدم
    let totalSize = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length;
      }
    }
    health.storageUsed = totalSize;

    // فحص الحد الأقصى
    if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {
      health.isHealthy = false;
      health.issues.push('التخزين يقترب من الحد الأقصى');
      health.recommendations.push('تنظيف البيانات القديمة');
    }

    // فحص النسخ الاحتياطية
    const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('backup-'));
    health.backupsCount = backupKeys.length;

    if (health.backupsCount === 0) {
      health.issues.push('لا توجد نسخ احتياطية');
      health.recommendations.push('إنشاء نسخة احتياطية فورية');
    }

    // فحص البيانات الأساسية
    const invoicesData = localStorage.getItem('invoices');
    if (!invoicesData) {
      health.issues.push('لا توجد بيانات فواتير');
    } else {
      try {
        const invoices = JSON.parse(invoicesData);
        if (!Array.isArray(invoices)) {
          health.isHealthy = false;
          health.issues.push('بيانات الفواتير تالفة');
          health.recommendations.push('استرداد من النسخة الاحتياطية');
        }
      } catch (error) {
        health.isHealthy = false;
        health.issues.push('فشل في قراءة بيانات الفواتير');
        health.recommendations.push('استرداد من النسخة الاحتياطية');
      }
    }

  } catch (error) {
    health.isHealthy = false;
    health.issues.push('فشل في فحص حالة التخزين');
    health.recommendations.push('إعادة تشغيل التطبيق');
  }

  return health;
};

/**
 * إنشاء نسخة احتياطية
 */
export const createBackup = (): { success: boolean; message: string; backupId?: string } => {
  try {
    const timestamp = new Date().toISOString();
    const backupId = `backup-${Date.now()}`;

    // جمع البيانات
    const data: StorageData = {
      invoices: JSON.parse(localStorage.getItem('invoices') || '[]'),
      settings: JSON.parse(localStorage.getItem('companySettings') || '{}'),
      counters: JSON.parse(localStorage.getItem('invoiceCounters') || '{}'),
    };

    // إنشاء النسخة الاحتياطية
    const backup: BackupData = {
      data,
      timestamp,
      version: '1.0.0',
      checksum: calculateChecksum(JSON.stringify(data)),
    };

    // تشفير النسخة الاحتياطية
    const encryptedBackup = encryptData(JSON.stringify(backup));

    // حفظ النسخة الاحتياطية
    localStorage.setItem(backupId, encryptedBackup);

    // تنظيف النسخ القديمة
    cleanupOldBackups();

    console.log('✅ Backup created successfully:', backupId);
    return {
      success: true,
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      backupId,
    };

  } catch (error) {
    console.error('❌ Backup creation failed:', error);
    return {
      success: false,
      message: 'فشل في إنشاء النسخة الاحتياطية: ' + (error as Error).message,
    };
  }
};

/**
 * تنظيف النسخ الاحتياطية القديمة
 */
const cleanupOldBackups = (): void => {
  try {
    const backupKeys = Object.keys(localStorage)
      .filter(key => key.startsWith('backup-'))
      .sort((a, b) => {
        const timeA = parseInt(a.replace('backup-', ''));
        const timeB = parseInt(b.replace('backup-', ''));
        return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)
      });

    // حذف النسخ الزائدة
    if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {
      const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);
      toDelete.forEach(key => {
        localStorage.removeItem(key);
        console.log('🗑️ Removed old backup:', key);
      });
    }
  } catch (error) {
    console.error('❌ Failed to cleanup old backups:', error);
  }
};

/**
 * استرداد من النسخة الاحتياطية
 */
export const restoreFromBackup = (backupId?: string): { success: boolean; message: string } => {
  try {
    let targetBackupId = backupId;

    // إذا لم يتم تحديد النسخة، استخدم الأحدث
    if (!targetBackupId) {
      const backupKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('backup-'))
        .sort((a, b) => {
          const timeA = parseInt(a.replace('backup-', ''));
          const timeB = parseInt(b.replace('backup-', ''));
          return timeB - timeA;
        });

      if (backupKeys.length === 0) {
        return {
          success: false,
          message: 'لا توجد نسخ احتياطية متاحة',
        };
      }

      targetBackupId = backupKeys[0];
    }

    // قراءة النسخة الاحتياطية
    const encryptedBackup = localStorage.getItem(targetBackupId);
    if (!encryptedBackup) {
      return {
        success: false,
        message: 'النسخة الاحتياطية غير موجودة',
      };
    }

    // فك التشفير
    const decryptedData = decryptData(encryptedBackup);
    const backup: BackupData = JSON.parse(decryptedData);

    // التحقق من سلامة البيانات
    const currentChecksum = calculateChecksum(JSON.stringify(backup.data));
    if (currentChecksum !== backup.checksum) {
      return {
        success: false,
        message: 'النسخة الاحتياطية تالفة (checksum mismatch)',
      };
    }

    // استرداد البيانات
    localStorage.setItem('invoices', JSON.stringify(backup.data.invoices));
    localStorage.setItem('companySettings', JSON.stringify(backup.data.settings));
    localStorage.setItem('invoiceCounters', JSON.stringify(backup.data.counters));

    console.log('✅ Data restored successfully from:', targetBackupId);
    return {
      success: true,
      message: `تم استرداد البيانات بنجاح من النسخة: ${backup.timestamp}`,
    };

  } catch (error) {
    console.error('❌ Restore failed:', error);
    return {
      success: false,
      message: 'فشل في استرداد البيانات: ' + (error as Error).message,
    };
  }
};

/**
 * الحفظ الآمن للبيانات
 */
export const secureSetItem = (key: string, data: any): { success: boolean; message: string } => {
  try {
    // فحص حالة التخزين أولاً
    const health = checkStorageHealth();
    if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {
      return {
        success: false,
        message: 'التخزين ممتلئ - يرجى تنظيف البيانات',
      };
    }

    // تحويل البيانات إلى JSON
    const jsonData = JSON.stringify(data);

    // فحص حجم البيانات
    if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {
      console.warn('⚠️ Large data being saved:', Math.round(jsonData.length / 1024), 'KB');
    }

    // الحفظ
    localStorage.setItem(key, jsonData);

    // التحقق من الحفظ
    const saved = localStorage.getItem(key);
    if (saved !== jsonData) {
      throw new Error('فشل في التحقق من الحفظ');
    }

    // إنشاء نسخة احتياطية تلقائية للبيانات المهمة
    if (key === 'invoices' || key === 'companySettings') {
      setTimeout(() => createBackup(), 1000);
    }

    return {
      success: true,
      message: 'تم الحفظ بنجاح',
    };

  } catch (error) {
    console.error('❌ Secure save failed:', error);
    return {
      success: false,
      message: 'فشل في الحفظ: ' + (error as Error).message,
    };
  }
};

/**
 * القراءة الآمنة للبيانات
 */
export const secureGetItem = <T>(key: string, defaultValue: T): { success: boolean; data: T; message?: string } => {
  try {
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      return {
        success: true,
        data: defaultValue,
        message: 'لا توجد بيانات محفوظة، استخدام القيم الافتراضية',
      };
    }

    const parsed = JSON.parse(stored);
    
    // التحقق من نوع البيانات
    if (typeof parsed !== typeof defaultValue) {
      console.warn('⚠️ Data type mismatch for key:', key);
      return {
        success: false,
        data: defaultValue,
        message: 'نوع البيانات غير متطابق',
      };
    }

    return {
      success: true,
      data: parsed,
    };

  } catch (error) {
    console.error('❌ Secure read failed:', error);
    
    // محاولة الاسترداد من النسخة الاحتياطية
    if (key === 'invoices') {
      const restoreResult = restoreFromBackup();
      if (restoreResult.success) {
        // إعادة المحاولة بعد الاسترداد
        try {
          const stored = localStorage.getItem(key);
          if (stored) {
            return {
              success: true,
              data: JSON.parse(stored),
              message: 'تم الاسترداد من النسخة الاحتياطية',
            };
          }
        } catch (retryError) {
          console.error('❌ Retry after restore failed:', retryError);
        }
      }
    }

    return {
      success: false,
      data: defaultValue,
      message: 'فشل في قراءة البيانات: ' + (error as Error).message,
    };
  }
};

// تصدير الإعدادات للاستخدام الخارجي
export { STORAGE_CONFIG };
