"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicePreview.tsx":
/*!*******************************************!*\
  !*** ./src/components/InvoicePreview.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/settingsStore */ \"(app-pages-browser)/./src/store/settingsStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// دالة عرض رسائل التأكيد المحسنة\nconst showSuccessMessage = (message)=>{\n    // إنشاء عنصر toast مؤقت\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    // إزالة الرسالة بعد 3 ثوان\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 3000);\n};\nconst showErrorMessage = (message)=>{\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 4000);\n};\nconst InvoicePreview = (param)=>{\n    let { invoice, onEdit, onExportPDF } = param;\n    _s();\n    const [showCompanySettings, setShowCompanySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPOSInvoice, setShowPOSInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { settings, loadSettings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore)();\n    // Load company settings on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        loadSettings\n    ]);\n    // دالة تصدير PDF محسنة مع رسائل التأكيد\n    const handleExportPDF = async ()=>{\n        setIsExporting(true);\n        try {\n            // إضافة كلاس التحسين للعنصر\n            const element = document.getElementById(\"invoice-content\");\n            if (element) {\n                element.classList.add(\"pdf-optimized\");\n            }\n            // محاولة الطريقة المحسنة أولاً\n            await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n            showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            // إزالة كلاس التحسين\n            if (element) {\n                element.classList.remove(\"pdf-optimized\");\n            }\n        } catch (error) {\n            console.error(\"PDF Export Error:\", error);\n            // محاولة الطريقة البديلة\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDFFromHTML)(invoice);\n                showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            } catch (fallbackError) {\n                console.error(\"Fallback PDF Export Error:\", fallbackError);\n                showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n            }\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن/تعبئة\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getPaymentMethodName = (method)=>{\n        const methods = {\n            cash: \"نقدي\",\n            card: \"بطاقة\",\n            transfer: \"تحويل\",\n            installment: \"تقسيط\"\n        };\n        return methods[method] || method;\n    };\n    const getContactMethodName = (method)=>{\n        const methods = {\n            whatsapp: \"واتساب\",\n            meta: \"ميتا\",\n            meeting: \"مقابلة\",\n            phone: \"هاتف\"\n        };\n        return methods[method] || method;\n    };\n    // Generate client code based on name and phone\n    const generateClientCode = (name, phone)=>{\n        const nameCode = name.substring(0, 3).toUpperCase();\n        const phoneCode = phone.substring(-4);\n        return \"\".concat(nameCode, \"-\").concat(phoneCode);\n    };\n    // دالة فتح صفحة الطباعة في تبويب جديد محسنة\n    const openPrintView = (invoice)=>{\n        try {\n            // إنشاء HTML للفاتورة مع تنسيق الطباعة\n            const printHTML = createPrintableInvoiceHTML(invoice);\n            // فتح نافذة جديدة\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(printHTML);\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم فتح حوار الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        showSuccessMessage(\"تم فتح صفحة الطباعة بنجاح! \\uD83D\\uDDA8️\");\n                    }, 500);\n                };\n            } else {\n                showErrorMessage(\"فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.\");\n            }\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تحضير صفحة الطباعة.\");\n        }\n    };\n    // دالة إنشاء HTML للطباعة\n    const createPrintableInvoiceHTML = (invoice)=>{\n        const logoSrc = settings.companyLogo || \"\";\n        return '\\n<!DOCTYPE html>\\n<html lang=\"ar\" dir=\"rtl\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>فاتورة '.concat(invoice.id, \" - \").concat(invoice.client.name, '</title>\\n    <style>\\n        @page {\\n            size: A4;\\n            margin: 15mm;\\n        }\\n\\n        * {\\n            margin: 0;\\n            padding: 0;\\n            box-sizing: border-box;\\n        }\\n\\n        body {\\n            font-family: Arial, sans-serif;\\n            direction: rtl;\\n            line-height: 1.4;\\n            color: #333;\\n            background: white;\\n        }\\n\\n        .invoice-container {\\n            max-width: 100%;\\n            margin: 0 auto;\\n            background: white;\\n            padding: 20px;\\n        }\\n\\n        /* Header Section */\\n        .header {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background: linear-gradient(to left, #f0f8ff, white);\\n            border: 2px solid #2196f3;\\n            border-radius: 10px;\\n        }\\n\\n        .invoice-info {\\n            flex: 1;\\n            text-align: right;\\n        }\\n\\n        .invoice-info h2 {\\n            color: #2196f3;\\n            font-size: 24px;\\n            margin-bottom: 15px;\\n            font-weight: bold;\\n        }\\n\\n        .info-box {\\n            background: transparent;\\n            padding: 15px;\\n            border: none;\\n            box-shadow: none;\\n            max-width: 400px;\\n        }\\n\\n        .info-row {\\n            margin-bottom: 8px;\\n            font-size: 14px;\\n        }\\n\\n        .info-label {\\n            font-weight: bold;\\n            color: #555;\\n        }\\n\\n        .info-value {\\n            color: #333;\\n        }\\n\\n        .logo-section {\\n            flex-shrink: 0;\\n            text-align: center;\\n            margin-left: 30px;\\n        }\\n\\n        .logo-section img {\\n            max-width: 180px;\\n            max-height: 180px;\\n            object-fit: contain;\\n        }\\n\\n        .company-description {\\n            margin-top: 15px;\\n            font-size: 13px;\\n            color: #666;\\n            max-width: 200px;\\n            line-height: 1.5;\\n        }\\n\\n        /* Services Table */\\n        .services-section {\\n            margin-bottom: 30px;\\n        }\\n\\n        .section-title {\\n            font-size: 18px;\\n            font-weight: bold;\\n            color: #333;\\n            margin-bottom: 15px;\\n            padding-bottom: 8px;\\n            border-bottom: 2px solid #2196f3;\\n        }\\n\\n        .services-table {\\n            width: 100%;\\n            border-collapse: separate;\\n            border-spacing: 0;\\n            border: 1px solid #ddd;\\n            margin: 15px 0;\\n            table-layout: fixed;\\n        }\\n\\n        .services-table th {\\n            background-color: #2196f3;\\n            color: white;\\n            padding: 12px 8px;\\n            text-align: center;\\n            font-weight: bold;\\n            border: 1px solid #ddd;\\n            font-size: 13px;\\n        }\\n\\n        .services-table td {\\n            padding: 10px 8px;\\n            border: 1px solid #ddd;\\n            text-align: center;\\n            vertical-align: middle;\\n            word-wrap: break-word;\\n            font-size: 12px;\\n            line-height: 1.3;\\n        }\\n\\n        .services-table tr:nth-child(even) {\\n            background-color: #f9f9f9;\\n        }\\n\\n        .services-table tr:nth-child(odd) {\\n            background-color: white;\\n        }\\n\\n        .service-type {\\n            color: #2196f3;\\n            font-weight: bold;\\n        }\\n\\n        .service-details {\\n            text-align: right;\\n            white-space: normal;\\n        }\\n\\n        .service-price {\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .service-total {\\n            font-weight: bold;\\n            color: #4caf50;\\n        }\\n\\n        /* Total Summary */\\n        .total-section {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: center;\\n            background: linear-gradient(to right, #e8f5e8, #f0f8f0);\\n            padding: 20px;\\n            border-radius: 10px;\\n            border: 2px solid #4caf50;\\n            margin-bottom: 30px;\\n        }\\n\\n        .total-details {\\n            text-align: right;\\n        }\\n\\n        .total-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 8px;\\n            min-width: 300px;\\n        }\\n\\n        .total-label {\\n            font-weight: bold;\\n            color: #555;\\n            margin-left: 20px;\\n        }\\n\\n        .total-value {\\n            font-weight: bold;\\n        }\\n\\n        .grand-total {\\n            font-size: 20px;\\n            color: #4caf50;\\n        }\\n\\n        .paid-amount {\\n            font-size: 16px;\\n            color: #2196f3;\\n        }\\n\\n        .remaining-amount {\\n            font-size: 16px;\\n        }\\n\\n        .remaining-positive {\\n            color: #f44336;\\n        }\\n\\n        .remaining-zero {\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp {\\n            background: transparent;\\n            color: #333;\\n            padding: 15px;\\n            border: 3px solid #333;\\n            border-radius: 15px;\\n            text-align: center;\\n            transform: rotate(3deg);\\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\\n            min-width: 120px;\\n        }\\n\\n        .payment-stamp.paid-full {\\n            border-color: #4caf50;\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp.paid-partial {\\n            border-color: #ff9800;\\n            color: #ff9800;\\n        }\\n\\n        .payment-stamp.unpaid {\\n            border-color: #f44336;\\n            color: #f44336;\\n        }\\n\\n        .stamp-title {\\n            font-size: 14px;\\n            font-weight: bold;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-status {\\n            font-size: 12px;\\n            font-weight: bold;\\n        }\\n\\n        /* Footer */\\n        .footer {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-top: 40px;\\n            padding: 20px;\\n            background: white;\\n            border-top: 1px solid #e9ecef;\\n            min-height: 120px;\\n            direction: rtl;\\n        }\\n\\n        .footer-left {\\n            width: 45%;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 10px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .footer-right {\\n            width: 50%;\\n            text-align: right;\\n            direction: rtl;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: flex-end;\\n            justify-content: flex-start;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 15px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .company-stamp {\\n            position: relative;\\n            transform: none;\\n            opacity: 1;\\n            border-radius: 8px;\\n            padding: 10px;\\n            width: 160px;\\n            height: auto;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            margin-bottom: 15px;\\n            background: white;\\n            border: 1px solid #ddd;\\n            box-shadow: none;\\n        }\\n\\n        .company-stamp img {\\n            width: 140px;\\n            height: auto;\\n            max-height: 100px;\\n            object-fit: contain;\\n            border-radius: 4px;\\n        }\\n\\n        .company-stamp .stamp-content {\\n            background: rgba(33, 150, 243, 0.1);\\n            border: 2px solid #2196f3;\\n            border-radius: 8px;\\n            width: 100%;\\n            height: 100px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n\\n        .stamp-content {\\n            text-align: center;\\n            color: #2196f3;\\n            font-weight: bold;\\n            font-size: 14px;\\n            line-height: 1.2;\\n            direction: rtl;\\n        }\\n\\n        .stamp-title {\\n            font-size: 16px;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-subtitle {\\n            font-size: 12px;\\n            opacity: 0.8;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #555;\\n            text-align: right;\\n            line-height: 1.6;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .thank-you {\\n            text-align: center;\\n            margin-top: 20px;\\n            padding: 15px;\\n            background: #f8f9fa;\\n            border: 1px solid #e9ecef;\\n            border-radius: 8px;\\n            color: #2196f3;\\n            font-size: 14px;\\n            font-weight: bold;\\n            direction: rtl;\\n        }\\n\\n        /* Print specific styles */\\n        @media print {\\n            body {\\n                -webkit-print-color-adjust: exact;\\n                print-color-adjust: exact;\\n                margin: 0;\\n                padding: 0;\\n            }\\n\\n            .invoice-container {\\n                padding: 0;\\n                margin: 0;\\n                max-width: 100%;\\n            }\\n\\n            .header {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-table {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n            }\\n\\n            .total-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .footer {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                position: relative;\\n                clear: both;\\n                margin-top: 30px;\\n            }\\n\\n            .footer-left,\\n            .footer-right {\\n                float: none;\\n                display: inline-block;\\n                vertical-align: top;\\n            }\\n\\n            .company-stamp {\\n                transform: none;\\n            }\\n\\n            .thank-you {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-top: 15px;\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\"invoice-container\">\\n        <!-- Header Section -->\\n        <div class=\"header\">\\n            <div class=\"invoice-info\">\\n                <h2>فاتورة خدمات</h2>\\n                <div class=\"info-box\">\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الفاتورة: </span>\\n                        <span class=\"info-value\">').concat(invoice.id, '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">التاريخ: </span>\\n                        <span class=\"info-value\">').concat(formatDate(invoice.createdAt), '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">اسم العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.name, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.businessName ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">الاسم التجاري: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.businessName, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الهاتف: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.phone, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.province ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">المحافظة: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.province, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">كود العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone), '</span>\\n                    </div>\\n                    <div class=\"info-row\" style=\"border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;\">\\n                        <span class=\"info-label\">وسيلة التواصل: </span>\\n                        <span class=\"info-value\">').concat(getContactMethodName(invoice.client.contactMethod), '</span>\\n                    </div>\\n                </div>\\n            </div>\\n\\n            <div class=\"logo-section\">\\n                ').concat(logoSrc ? '<img src=\"'.concat(logoSrc, '\" alt=\"شعار الشركة\">') : '\\n                <div style=\"width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9;\">\\n                    <span style=\"color: #999;\">شعار الشركة</span>\\n                </div>\\n                ', '\\n                <div class=\"company-description\">\\n                    ').concat(settings.description || \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\", '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <!-- Services Section -->\\n        <div class=\"services-section\">\\n            <h3 class=\"section-title\">تفاصيل الخدمات والمنتجات</h3>\\n\\n            <table class=\"services-table\">\\n                <thead>\\n                    <tr>\\n                        <th style=\"width: 50px;\">م</th>\\n                        <th style=\"width: 120px;\">نوع الخدمة</th>\\n                        <th style=\"width: 200px;\">التفاصيل</th>\\n                        <th style=\"width: 100px;\">الكمية/المقاس</th>\\n                        <th style=\"width: 120px;\">السعر</th>\\n                        <th style=\"width: 120px;\">المجموع</th>\\n                    </tr>\\n                </thead>\\n                <tbody>\\n                    ').concat(invoice.services.map((service, index)=>{\n            // Calculate service details (same logic as in component)\n            let serviceTotal = 0;\n            let serviceDetails = \"\";\n            let quantity = \"\";\n            let unitPrice = 0;\n            if (service.serviceType === \"consultation\") {\n                const consultationService = service;\n                serviceTotal = consultationService.cost || 0;\n                serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                unitPrice = consultationService.cost || 0;\n            } else if (service.serviceType === \"pattern\") {\n                var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                const patternService = service;\n                serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n            } else if (service.serviceType === \"pattern_printing\") {\n                var _printingService_files, _printingService_files1, _printingService_files2;\n                const printingService = service;\n                serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n            } else if (service.serviceType === \"shipping\") {\n                const shippingService = service;\n                serviceTotal = shippingService.total || 0;\n                serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                quantity = \"\".concat(shippingService.quantity || 1);\n                unitPrice = shippingService.unitPrice || 0;\n            } else if (service.serviceType === \"manufacturing\") {\n                var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                const manufacturingService = service;\n                serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n            } else if (service.serviceType === \"products\") {\n                var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                const productsService = service;\n                let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                // طرح الخصم إن وجد\n                if (productsService.discountAmount) {\n                    productsSubtotal -= productsService.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n            } else {\n                serviceTotal = service.cost || service.total || 0;\n                serviceDetails = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n            }\n            return \"\\n                        <tr>\\n                            <td>\".concat(index + 1, '</td>\\n                            <td class=\"service-type\">').concat(getServiceName(service.serviceType), '</td>\\n                            <td class=\"service-details\">').concat(serviceDetails, \"</td>\\n                            <td>\").concat(quantity, '</td>\\n                            <td class=\"service-price\">').concat(formatCurrency(unitPrice), '</td>\\n                            <td class=\"service-total\">').concat(formatCurrency(serviceTotal), \"</td>\\n                        </tr>\\n                      \");\n        }).join(\"\"), '\\n                </tbody>\\n            </table>\\n        </div>\\n\\n        <!-- Total Summary -->\\n        <div class=\"total-section\">\\n            <div class=\"total-details\">\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المجموع الكلي:</span>\\n                    <span class=\"total-value grand-total\">').concat(formatCurrency(invoice.total), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المدفوع:</span>\\n                    <span class=\"total-value paid-amount\">').concat(formatCurrency(invoice.paidAmount || 0), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المتبقي:</span>\\n                    <span class=\"total-value remaining-amount ').concat((invoice.remainingAmount || 0) > 0 ? \"remaining-positive\" : \"remaining-zero\", '\">\\n                        ').concat(formatCurrency(invoice.remainingAmount || 0), '\\n                    </span>\\n                </div>\\n                <div class=\"total-row\" style=\"border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;\">\\n                    <span class=\"total-label\">طريقة الدفع:</span>\\n                    <span class=\"total-value\">').concat(getPaymentMethodName(invoice.paymentMethod), \"</span>\\n                </div>\\n                \").concat(invoice.services.some((service)=>{\n            if (service.serviceType === \"pattern\") {\n                var _service_models;\n                return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n            }\n            if (service.serviceType === \"manufacturing\") {\n                var _service_models1;\n                return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n            }\n            return false;\n        }) ? '\\n                <div class=\"total-row\" style=\"border-top: 1px solid #ff9800; padding-top: 8px; margin-top: 8px; color: #ff9800;\">\\n                    <span class=\"total-label\" style=\"font-size: 12px;\">ملاحظة:</span>\\n                    <span class=\"total-value\" style=\"font-size: 12px;\">يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر</span>\\n                </div>\\n                ' : \"\", '\\n            </div>\\n\\n            <div class=\"payment-stamp ').concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"paid-full\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"paid-partial\" : \"unpaid\", '\">\\n                <div class=\"stamp-title\">OKA Group</div>\\n                <div class=\"stamp-status\">').concat(invoice.paymentStatus || \"غير محدد\", '</div>\\n            </div>\\n        </div>\\n\\n        <!-- Footer -->\\n        <div class=\"footer\">\\n            <!-- Right side: Contact Information -->\\n            <div class=\"footer-left\">\\n                <div class=\"contact-info\">\\n                    <div><strong>معلومات التواصل:</strong></div>\\n                    <div>\\uD83D\\uDCE7 البريد الإلكتروني: <EMAIL></div>\\n                    <div>\\uD83D\\uDCDE للشكاوى والاقتراحات: 0114954118</div>\\n                </div>\\n            </div>\\n\\n            <!-- Left side: Headquarters + Company Stamp -->\\n            <div class=\"footer-right\">\\n                <div class=\"headquarters-info\">\\n                    <div><strong>المقر الإداري:</strong></div>\\n                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>\\n                </div>\\n\\n                <div class=\"company-stamp\">\\n                    ').concat(settings.companyStamp ? '\\n                    <img\\n                        src=\"'.concat(settings.companyStamp, '\"\\n                        alt=\"ختم الشركة\"\\n                    >\\n                    ') : '\\n                    <div class=\"stamp-content\">\\n                        <div class=\"stamp-title\">OKA</div>\\n                        <div class=\"stamp-subtitle\">GROUP</div>\\n                    </div>\\n                    ', '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div class=\"thank-you\">\\n            شكراً لتعاملكم معنا\\n        </div>\\n    </div>\\n</body>\\n</html>\\n    ');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl sm:text-3xl font-bold text-gray-800\",\n                        children: \"معاينة الفاتورة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompanySettings(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"إعدادات الشركة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"تعديل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportPDF,\n                                disabled: isExporting,\n                                className: \"btn-success \".concat(isExporting ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                children: isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 animate-spin\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"جاري التصدير...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"تصدير PDF\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>openPrintView(invoice),\n                                className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"طباعة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowPOSInvoice(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"فاتورة POS\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 963,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"invoice-content\",\n                className: \"bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden print-break-inside-avoid\",\n                style: {\n                    fontFamily: \"Arial, sans-serif\",\n                    lineHeight: \"1.4\",\n                    position: \"relative\",\n                    zIndex: 1,\n                    direction: \"rtl\",\n                    width: \"100%\",\n                    maxWidth: \"794px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-blue-50 via-white to-blue-50 p-8 border-b-4 border-blue-300 print-header print-break-inside-avoid relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -translate-y-32 translate-x-32\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 w-48 h-48 bg-blue-100 rounded-full translate-y-24 -translate-x-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: settings.companyLogo,\n                                                                alt: \"شعار الشركة\",\n                                                                className: \"w-56 h-56 object-contain mx-auto drop-shadow-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-48 h-4 bg-blue-200 rounded-full opacity-30 blur-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-56 h-56 border-3 border-dashed border-blue-300 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-50 to-white mx-auto shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-blue-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-24 h-24 mx-auto mb-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1016,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: \"شعار الشركة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1018,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl shadow-lg mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: \"OKA GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-100 text-sm\",\n                                                            children: \"مجموعة أوكا للخدمات المتكاملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1027,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-xs mx-auto bg-white p-4 rounded-lg shadow-sm border border-blue-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 leading-relaxed font-medium\",\n                                                        children: settings.description || \"نقدم خدمات متكاملة في مجال التصميم والتصنيع والاستشارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-right space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-4xl font-bold text-blue-600 mb-2\",\n                                                            children: \"فاتورة خدمات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-6 rounded-2xl shadow-lg border-2 border-blue-100 max-w-md ml-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-blue-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-blue-600\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            invoice.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"رقم الفاتورة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800 font-medium\",\n                                                                        children: formatDate(invoice.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"التاريخ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t-2 border-blue-100 my-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-bold text-gray-700 text-center mb-3 bg-blue-50 py-2 rounded-lg\",\n                                                                        children: \"بيانات العميل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1070,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1069,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.businessName\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1076,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم التجاري:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1077,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1075,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1082,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الهاتف:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1083,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1081,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.province && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.province\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1088,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"المحافظة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1089,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1087,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1094,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"كود العميل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1095,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1093,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between pt-2 border-t border-gray-200\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: getContactMethodName(invoice.client.contactMethod)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1099,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"وسيلة التواصل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1100,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1047,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-800 mb-4 border-b-2 border-blue-200 pb-2\",\n                                        children: \"تفاصيل الخدمات والمنتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto mb-6 print-break-inside-avoid\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full bg-white text-sm print-table\",\n                                            style: {\n                                                borderCollapse: \"separate\",\n                                                borderSpacing: \"0\",\n                                                border: \"1px solid #ddd\",\n                                                margin: \"20px 0\",\n                                                tableLayout: \"fixed\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        style: {\n                                                            backgroundColor: \"#2196f3\",\n                                                            color: \"white\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"50px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"م\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"120px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"نوع الخدمة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1141,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"200px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"التفاصيل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1151,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"100px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"الكمية/المقاس\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"120px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"السعر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"border border-gray-300 text-center font-semibold\",\n                                                                style: {\n                                                                    width: \"120px\",\n                                                                    padding: \"12px 8px\",\n                                                                    border: \"1px solid #ddd\"\n                                                                },\n                                                                children: \"المجموع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1181,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1129,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: invoice.services.map((service, index)=>{\n                                                        // Calculate service total based on service type\n                                                        let serviceTotal = 0;\n                                                        let serviceDetails = \"\";\n                                                        let quantity = \"\";\n                                                        let unitPrice = 0;\n                                                        if (service.serviceType === \"consultation\") {\n                                                            const consultationService = service;\n                                                            serviceTotal = consultationService.cost || 0;\n                                                            serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                                                            quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                                                            unitPrice = consultationService.cost || 0;\n                                                        } else if (service.serviceType === \"pattern\") {\n                                                            var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                                                            const patternService = service;\n                                                            serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                                                            quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                                                            unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"pattern_printing\") {\n                                                            var _printingService_files, _printingService_files1, _printingService_files2;\n                                                            const printingService = service;\n                                                            serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                                                            quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                                                            unitPrice = serviceTotal;\n                                                        } else if (service.serviceType === \"shipping\") {\n                                                            const shippingService = service;\n                                                            serviceTotal = shippingService.total || 0;\n                                                            serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                                                            quantity = \"\".concat(shippingService.quantity || 1);\n                                                            unitPrice = shippingService.unitPrice || 0;\n                                                        } else if (service.serviceType === \"manufacturing\") {\n                                                            var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                                                            const manufacturingService = service;\n                                                            serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                                                                return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                                                            }, 0)) || 0;\n                                                            serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                                                            quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"products\") {\n                                                            var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                                                            const productsService = service;\n                                                            let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                                                            // طرح الخصم إن وجد\n                                                            if (productsService.discountAmount) {\n                                                                productsSubtotal -= productsService.discountAmount || 0;\n                                                            }\n                                                            serviceTotal = Math.max(0, productsSubtotal);\n                                                            serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                                                            quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                                                        } else {\n                                                            // Default for other service types\n                                                            serviceTotal = service.cost || service.total || 0;\n                                                            serviceDetails = getServiceName(service.serviceType);\n                                                            quantity = \"1\";\n                                                            unitPrice = serviceTotal;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            style: {\n                                                                backgroundColor: index % 2 === 0 ? \"#f9f9f9\" : \"white\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\"\n                                                                    },\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1259,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\",\n                                                                        color: \"#2196f3\",\n                                                                        fontWeight: \"bold\"\n                                                                    },\n                                                                    children: getServiceName(service.serviceType)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"right\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        maxWidth: \"200px\"\n                                                                    },\n                                                                    children: serviceDetails\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1289,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        textOverflow: \"ellipsis\",\n                                                                        whiteSpace: \"nowrap\"\n                                                                    },\n                                                                    children: quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1303,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: \"#2196f3\"\n                                                                    },\n                                                                    children: formatCurrency(unitPrice)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    style: {\n                                                                        padding: \"10px 8px\",\n                                                                        border: \"1px solid #ddd\",\n                                                                        textAlign: \"center\",\n                                                                        verticalAlign: \"middle\",\n                                                                        wordWrap: \"break-word\",\n                                                                        overflow: \"hidden\",\n                                                                        whiteSpace: \"normal\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: \"#4caf50\"\n                                                                    },\n                                                                    children: formatCurrency(serviceTotal)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, service.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 flex justify-between items-center bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-lg border-2 border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المجموع الكلي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: formatCurrency(invoice.total)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1361,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1359,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المبلغ المدفوع:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1364,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatCurrency(invoice.paidAmount || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"المبلغ المتبقي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1368,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold \".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                        children: formatCurrency(invoice.remainingAmount || 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-2 border-t border-green-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-700\",\n                                                        children: \"طريقة الدفع: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-800\",\n                                                        children: getPaymentMethodName(invoice.paymentMethod)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1375,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            invoice.services.some((service)=>{\n                                                if (service.serviceType === \"pattern\") {\n                                                    var _service_models;\n                                                    return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n                                                }\n                                                if (service.serviceType === \"manufacturing\") {\n                                                    var _service_models1;\n                                                    return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n                                                }\n                                                return false;\n                                            }) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-2 border-t border-orange-300 mt-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-orange-600 font-semibold\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-orange-700\",\n                                                            children: \"ملاحظة: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1388,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1387,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1386,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 ml-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative rounded-2xl p-6 text-center shadow-xl transform rotate-3 border-4 bg-transparent \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"border-green-500 text-green-600\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"border-orange-500 text-orange-600\" : \"border-red-500 text-red-600\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-lg mb-1\",\n                                                        children: \"OKA Group\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1403,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-sm\",\n                                                        children: invoice.paymentStatus || \"غير محدد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1404,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1402,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1397,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1356,\n                                columnNumber: 11\n                            }, undefined),\n                            (invoice.requiresShipping || invoice.shippingAddress || invoice.notes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-yellow-50 to-yellow-100 p-6 rounded-lg border border-yellow-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-bold text-yellow-700 mb-4\",\n                                            children: \"معلومات إضافية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1416,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700\",\n                                                            children: \"يتطلب شحن:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1420,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600 font-semibold\",\n                                                            children: \"نعم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.shippingAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-2 border-t border-yellow-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700 block mb-1\",\n                                                            children: \"عنوان الشحن:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-800 text-sm\",\n                                                            children: invoice.shippingAddress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1428,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-2 border-t border-yellow-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-700 block mb-1\",\n                                                            children: \"ملاحظات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1434,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-800 text-sm\",\n                                                            children: invoice.notes\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1435,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1433,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1417,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1415,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1112,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-t border-gray-200 p-6 relative\",\n                        style: {\n                            direction: \"rtl\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-gray-700 mb-3\",\n                                                children: \"معلومات التواصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"البريد الإلكتروني: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1454,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600\",\n                                                                children: settings.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1457,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"للشكاوى والاقتراحات: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1466,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600 font-bold\",\n                                                                children: settings.complaintsPhone\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1467,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1465,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-green-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1470,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1469,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1464,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-semibold text-gray-700\",\n                                                        children: \"المقر الإداري: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1479,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: settings.address\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1480,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1478,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end mt-4\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 bg-white border border-gray-300 rounded-lg p-2 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: settings.companyStamp,\n                                                        alt: \"ختم الشركة\",\n                                                        className: \"max-w-full max-h-full object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1487,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1486,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 border-2 border-dashed border-gray-400 rounded-lg flex items-center justify-center bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 mx-auto mb-1\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1497,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1496,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs\",\n                                                                children: \"ختم الشركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1499,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1495,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1494,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1476,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1446,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center bg-blue-50 p-3 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 font-bold\",\n                                        children: \"شكراً لتعاملكم معنا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1510,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1509,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1508,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1445,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 976,\n                columnNumber: 7\n            }, undefined),\n            showCompanySettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"إعدادات الشركة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1523,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1529,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1528,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1524,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1522,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1521,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1540,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.companyName,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        companyName: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1539,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: settings.email,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        email: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1554,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"هاتف الشكاوى والاقتراحات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                value: settings.complaintsPhone,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        complaintsPhone: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رقم هاتف الشكاوى\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1585,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.address,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        address: e.target.value\n                                                    });\n                                                },\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1586,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1584,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1600,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.slogan,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        slogan: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1601,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1599,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1615,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.wishMessage,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        wishMessage: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1616,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1614,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1630,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.description,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        description: e.target.value\n                                                    });\n                                                },\n                                                rows: 2,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1631,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1645,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors\",\n                                                children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyLogo,\n                                                            alt: \"شعار الشركة\",\n                                                            className: \"w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1649,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1654,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1648,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1680,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1679,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع شعار الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1682,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1683,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1678,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1646,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1644,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"ختم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1712,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyStamp,\n                                                            alt: \"ختم الشركة\",\n                                                            className: \"w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1716,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1721,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1715,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-12 h-12 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1747,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1746,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع ختم الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1749,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1750,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1745,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1713,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1711,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1537,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1536,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-6 py-4 rounded-b-lg border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"سيتم حفظ الإعدادات تلقائياً عند التغيير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1782,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1785,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1781,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1780,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                    lineNumber: 1519,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1518,\n                columnNumber: 9\n            }, undefined),\n            showPOSInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                invoice: invoice,\n                onClose: ()=>setShowPOSInvoice(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1799,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n        lineNumber: 913,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicePreview, \"rxHFl2SkeEMTva1xN8F43PiUNL8=\", false, function() {\n    return [\n        _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore\n    ];\n});\n_c = InvoicePreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicePreview);\nvar _c;\n$RefreshReg$(_c, \"InvoicePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicePreview.tsx\n"));

/***/ })

});