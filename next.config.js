/** @type {import('next').NextConfig} */
const nextConfig = {
  // إزالة i18n config لأنه لا يتوافق مع App Router
  // سيتم التعامل مع اللغة العربية من خلال layout.tsx

  // إعدادات لإزالة تحذيرات Permissions-Policy
  async headers() {
    return [
      {
        // تطبيق على جميع الصفحات
        source: '/(.*)',
        headers: [
          {
            key: 'Permissions-Policy',
            value: [
              'camera=()',
              'microphone=()',
              'geolocation=()',
              'interest-cohort=()',
              'run-ad-auction=()',
              'browsing-topics=()',
              'private-state-token-redemption=()',
              'private-state-token-issuance=()',
              'join-ad-interest-group=()'
            ].join(', ')
          },
          // إضافة headers أمان إضافية
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ]
      }
    ]
  },

  // تحسين الأداء
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // إعدادات الإنتاج
  productionBrowserSourceMaps: false,

  // تحسين Bundle
  webpack: (config, { dev, isServer }) => {
    // تحسينات للإنتاج
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      }
    }

    return config
  }
}

module.exports = nextConfig
