'use client';

import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';

interface CompanyInfo {
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  taxNumber: string;
  commercialRegister: string;
}

const defaultCompanyInfo: CompanyInfo = {
  name: 'شركة التصميم والأزياء',
  logo: '',
  address: 'القاهرة، مصر',
  phone: '0*********0',
  email: '<EMAIL>',
  website: 'www.company.com',
  taxNumber: '*********',
  commercialRegister: '*********',
};

const validationSchema = Yup.object({
  name: Yup.string().required('اسم الشركة مطلوب'),
  phone: Yup.string().required('رقم الهاتف مطلوب'),
  email: Yup.string().email('بريد إلكتروني غير صحيح'),
});

interface CompanySettingsProps {
  onSave: (companyInfo: CompanyInfo) => void;
  onClose: () => void;
}

const CompanySettings: React.FC<CompanySettingsProps> = ({ onSave, onClose }) => {
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>(defaultCompanyInfo);

  useEffect(() => {
    // تحميل بيانات الشركة من localStorage
    const saved = localStorage.getItem('companyInfo');
    if (saved) {
      try {
        setCompanyInfo(JSON.parse(saved));
      } catch (error) {
        console.error('Error loading company info:', error);
      }
    }
  }, []);

  const handleSubmit = (values: CompanyInfo) => {
    // حفظ في localStorage
    localStorage.setItem('companyInfo', JSON.stringify(values));
    setCompanyInfo(values);
    onSave(values);
    onClose();
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>, setFieldValue: any) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFieldValue('logo', result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">إعدادات الشركة</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <Formik
          initialValues={companyInfo}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form className="space-y-6">
              {/* اسم الشركة */}
              <div className="form-field">
                <label className="form-label">اسم الشركة</label>
                <Field
                  name="name"
                  type="text"
                  className="form-input"
                  placeholder="أدخل اسم الشركة"
                />
                <ErrorMessage name="name" component="div" className="error-message" />
              </div>

              {/* شعار الشركة */}
              <div className="form-field">
                <label className="form-label">شعار الشركة</label>
                <div className="space-y-3">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleLogoUpload(e, setFieldValue)}
                    className="form-input"
                  />
                  {values.logo && (
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <img
                        src={values.logo}
                        alt="شعار الشركة"
                        className="w-20 h-20 object-contain border border-gray-300 rounded"
                      />
                      <button
                        type="button"
                        onClick={() => setFieldValue('logo', '')}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        حذف الشعار
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* العنوان */}
              <div className="form-field">
                <label className="form-label">العنوان</label>
                <Field
                  name="address"
                  as="textarea"
                  rows="3"
                  className="form-input"
                  placeholder="عنوان الشركة الكامل"
                />
              </div>

              {/* معلومات الاتصال */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-field">
                  <label className="form-label">رقم الهاتف</label>
                  <Field
                    name="phone"
                    type="tel"
                    className="form-input"
                    placeholder="رقم الهاتف"
                  />
                  <ErrorMessage name="phone" component="div" className="error-message" />
                </div>

                <div className="form-field">
                  <label className="form-label">البريد الإلكتروني</label>
                  <Field
                    name="email"
                    type="email"
                    className="form-input"
                    placeholder="البريد الإلكتروني"
                  />
                  <ErrorMessage name="email" component="div" className="error-message" />
                </div>
              </div>

              {/* الموقع الإلكتروني */}
              <div className="form-field">
                <label className="form-label">الموقع الإلكتروني</label>
                <Field
                  name="website"
                  type="url"
                  className="form-input"
                  placeholder="www.company.com"
                />
              </div>

              {/* معلومات قانونية */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-field">
                  <label className="form-label">الرقم الضريبي</label>
                  <Field
                    name="taxNumber"
                    type="text"
                    className="form-input"
                    placeholder="الرقم الضريبي"
                  />
                </div>

                <div className="form-field">
                  <label className="form-label">السجل التجاري</label>
                  <Field
                    name="commercialRegister"
                    type="text"
                    className="form-input"
                    placeholder="رقم السجل التجاري"
                  />
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex justify-end gap-4 pt-4 border-t">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn-secondary"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                >
                  حفظ الإعدادات
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CompanySettings;
