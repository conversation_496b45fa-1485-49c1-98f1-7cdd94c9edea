"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInvoiceStore: function() { return /* binding */ useInvoiceStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n\n\n// تحميل البيانات من التخزين الآمن\nconst loadInvoicesFromStorage = ()=>{\n    if (true) {\n        const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"invoices\", []);\n        if (!result.success) {\n            console.error(\"❌ Error loading invoices:\", result.message);\n        }\n        return result.data;\n    }\n    return [];\n};\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: loadInvoicesFromStorage(),\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            var _service_models;\n                            (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            var _service_files;\n                            (_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            var _service_models1;\n                            (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            var _service_items;\n                            let productsSubtotal = 0;\n                            (_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            var _service_materials;\n                            (_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            var _service_samples;\n                            (_service_samples = service.samples) === null || _service_samples === void 0 ? void 0 : _service_samples.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || \"INV-\".concat(Date.now()),\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من localStorage للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const stored = localStorage.getItem(\"invoices\");\n                    currentStoredInvoices = stored ? JSON.parse(stored) : [];\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices from localStorage:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في التخزين الآمن\n                const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", uniqueInvoices);\n                if (saveResult.success) {\n                    console.log(\"✅ Invoice saved securely. Total invoices:\", uniqueInvoices.length);\n                } else {\n                    console.error(\"❌ Error saving invoice:\", saveResult.message);\n                    // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                    if (true) {\n                        alert(\"تحذير: فشل في حفظ الفاتورة. \" + saveResult.message);\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (true) {\n                    alert(\"تحذير: فشل في حذف الفاتورة. \" + saveResult.message);\n                }\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return (currentInvoice === null || currentInvoice === void 0 ? void 0 : currentInvoice.services.filter((s)=>s.serviceType === type)) || [];\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/invoiceStore.ts\n"));

/***/ })

});