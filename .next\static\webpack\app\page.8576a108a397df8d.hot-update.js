"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InvoiceForm */ \"(app-pages-browser)/./src/components/InvoiceForm.tsx\");\n/* harmony import */ var _components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvoicesList */ \"(app-pages-browser)/./src/components/InvoicesList.tsx\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"client\");\n    const [currentClient, setCurrentClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentInvoice, setCurrentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { saveInvoice, currentClient: storeClient, clearCurrentClient } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore)();\n    const { addCustomer, customers, loadCustomers } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_8__.useCustomerStore)();\n    // تحميل العملاء عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadCustomers();\n    }, [\n        loadCustomers\n    ]);\n    // استخدام العميل من store إذا كان متوفراً\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (storeClient && !currentClient) {\n            setCurrentClient(storeClient);\n            setCurrentStep(\"invoice\");\n            // مسح العميل من store بعد الاستخدام\n            clearCurrentClient();\n        }\n    }, [\n        storeClient,\n        currentClient,\n        clearCurrentClient\n    ]);\n    const handleClientSubmit = (client)=>{\n        setCurrentClient(client);\n        setCurrentStep(\"invoice\");\n    };\n    // دالة لحفظ العميل تلقائياً في قاعدة بيانات العملاء\n    const saveCustomerAutomatically = async (client)=>{\n        try {\n            // التحقق من وجود العميل مسبقاً بناءً على كود العميل أو رقم الهاتف\n            const existingCustomer = customers.find((c)=>c.clientCode === client.clientCode || c.phone === client.phone && client.phone && client.phone.trim() !== \"\");\n            if (existingCustomer) {\n                console.log(\"✅ العميل موجود مسبقاً:\", existingCustomer.clientCode);\n                return {\n                    success: true,\n                    customer: existingCustomer\n                };\n            }\n            // إضافة العميل الجديد\n            console.log(\"\\uD83D\\uDD04 إضافة عميل جديد تلقائياً:\", client.clientCode);\n            const result = await addCustomer({\n                name: client.name,\n                phone: client.phone,\n                services: client.services || [],\n                contactMethod: client.contactMethod,\n                salesperson: client.salesperson,\n                initialCategory: client.initialCategory,\n                businessName: client.businessName,\n                province: client.province\n            });\n            if (result.success) {\n                var _result_customer;\n                console.log(\"✅ تم حفظ العميل تلقائياً:\", (_result_customer = result.customer) === null || _result_customer === void 0 ? void 0 : _result_customer.clientCode);\n                return result;\n            } else {\n                console.error(\"❌ فشل في حفظ العميل تلقائياً:\", result.error);\n                return result;\n            }\n        } catch (error) {\n            console.error(\"❌ خطأ في حفظ العميل تلقائياً:\", error);\n            return {\n                success: false,\n                error: \"حدث خطأ أثناء حفظ العميل\"\n            };\n        }\n    };\n    const handleInvoiceSubmit = async (invoice)=>{\n        // حفظ العميل تلقائياً في قاعدة بيانات العملاء\n        console.log(\"\\uD83D\\uDD04 بدء حفظ العميل تلقائياً...\");\n        await saveCustomerAutomatically(invoice.client);\n        // إنشاء ID فريد مع ترقيم تسلسلي\n        const generateSequentialId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const monthYearKey = \"\".concat(month, \"-\").concat(year);\n            // قراءة العدادات المحفوظة باستخدام النظام الآمن\n            const countersResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.secureGetItem)(\"invoiceCounters\", {});\n            let counters = {};\n            if (countersResult.success) {\n                counters = countersResult.data;\n            } else {\n                console.error(\"❌ Error reading invoice counters:\", countersResult.message);\n                counters = {};\n            }\n            // الحصول على العداد الحالي للشهر والسنة\n            const currentCounter = counters[monthYearKey] || 0;\n            const newCounter = currentCounter + 1;\n            // تحديث العداد\n            counters[monthYearKey] = newCounter;\n            // حفظ العدادات المحدثة باستخدام النظام الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.secureSetItem)(\"invoiceCounters\", counters);\n            if (!saveResult.success) {\n                console.error(\"❌ Error saving invoice counters:\", saveResult.message);\n            }\n            // إنشاء ID بالتنسيق المطلوب\n            const sequentialNumber = String(newCounter).padStart(3, \"0\");\n            const invoiceId = \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(sequentialNumber);\n            return invoiceId;\n        };\n        const invoiceWithId = {\n            ...invoice,\n            id: invoice.id || generateSequentialId(),\n            createdAt: invoice.createdAt || new Date().toISOString()\n        };\n        // تعيين الفاتورة الحالية\n        setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة في المتجر\n        const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore.getState();\n        store.setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)\n        store.saveInvoice();\n        // التحقق من النتيجة باستخدام النظام الآمن\n        setTimeout(()=>{\n            const invoicesResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_10__.secureGetItem)(\"invoices\", []);\n            if (invoicesResult.success) {\n                const savedInvoice = invoicesResult.data.find((inv)=>inv.id === invoiceWithId.id);\n                if (!savedInvoice) {\n                    console.error(\"❌ Invoice not found in storage after save attempt\");\n                }\n            } else {\n                console.error(\"❌ Error verifying save:\", invoicesResult.message);\n            }\n        }, 100);\n        setCurrentStep(\"preview\");\n    };\n    const handleEditInvoice = ()=>{\n        setCurrentStep(\"invoice\");\n    };\n    const handleExportPDF = async ()=>{\n        if (currentInvoice) {\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_9__.generateInvoicePDF)(currentInvoice);\n            } catch (error) {\n                alert(\"حدث خطأ أثناء تصدير PDF: \" + error.message);\n            }\n        }\n    };\n    const handleStartOver = ()=>{\n        console.log(\"\\uD83D\\uDD04 بداية جديدة - إعادة تعيين النظام\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n        setCurrentStep(\"client\");\n        setCurrentPage(\"home\");\n    };\n    const handleManageInvoices = ()=>{\n        console.log(\"\\uD83D\\uDCCB الانتقال لصفحة إدارة الفواتير\");\n        setCurrentPage(\"invoices\");\n    };\n    const handleCreateInvoice = ()=>{\n        console.log(\"➕ بدء إنشاء فاتورة جديدة\");\n        setCurrentPage(\"home\");\n        setCurrentStep(\"client\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n    };\n    const handleDataProtection = ()=>{\n        console.log(\"\\uD83D\\uDEE1️ الانتقال لصفحة حماية البيانات\");\n        router.push(\"/data-protection\");\n    };\n    const handleStatistics = ()=>{\n        console.log(\"\\uD83D\\uDCCA الانتقال لصفحة الإحصائيات الشاملة\");\n        router.push(\"/statistics\");\n    };\n    const handleCustomerManagement = ()=>{\n        console.log(\"\\uD83D\\uDC65 الانتقال لصفحة إدارة العملاء\");\n        router.push(\"/customers\");\n    };\n    const handleCustomerCodes = ()=>{\n        console.log(\"\\uD83D\\uDD22 الانتقال لصفحة إدارة أكواد العملاء\");\n        router.push(\"/customer-codes\");\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case \"client\":\n                return \"إدخال بيانات العميل\";\n            case \"invoice\":\n                return \"إنشاء الفاتورة\";\n            case \"preview\":\n                return \"معاينة الفاتورة\";\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"نظام إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm hidden sm:block\",\n                                                children: \"إدارة شاملة للفواتير والخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex gap-2 sm:gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateInvoice,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"home\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إنشاء فاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إنشاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleManageInvoices,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"invoices\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerManagement,\n                                        className: \"bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة العملاء وأكواد العملاء\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerCodes,\n                                        className: \"bg-teal-500 hover:bg-teal-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة أكواد العملاء والعدادات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"أكواد العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDD22\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDataProtection,\n                                        className: \"bg-purple-500 hover:bg-purple-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"لوحة تحكم حماية البيانات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"حماية البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDEE1️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStatistics,\n                                        className: \"bg-indigo-500 hover:bg-indigo-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"الإحصائيات الشاملة والتقارير\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"الإحصائيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartOver,\n                                        className: \"bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"بداية جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this),\n            currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 sm:space-x-12 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"client\" ? \"text-blue-600 scale-105\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"client\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"invoice\" || currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"بيانات العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"invoice\" ? \"text-blue-600 scale-105\" : currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"invoice\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"إنشاء الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"preview\" ? \"text-blue-600 scale-105\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"preview\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : \"bg-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"معاينة وتصدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري المعاينة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"التقدم: \",\n                                                currentStep === \"client\" ? \"33%\" : currentStep === \"invoice\" ? \"66%\" : \"100%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                    children: [\n                        currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 sm:mb-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full shadow-md \".concat(currentStep === \"client\" ? \"bg-blue-600\" : currentStep === \"invoice\" ? \"bg-green-600\" : \"bg-purple-600\"),\n                                                        children: [\n                                                            currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl sm:text-3xl font-bold text-gray-800 mb-2\",\n                                                                children: getStepTitle(currentStep)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm sm:text-base leading-relaxed\",\n                                                                children: [\n                                                                    currentStep === \"client\" && \"أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة\",\n                                                                    currentStep === \"invoice\" && \"اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة\",\n                                                                    currentStep === \"preview\" && \"راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 rounded-lg border-l-4 \".concat(currentStep === \"client\" ? \"bg-blue-50 border-blue-400\" : currentStep === \"invoice\" ? \"bg-green-50 border-green-400\" : \"bg-purple-50 border-purple-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mt-0.5 flex-shrink-0 \".concat(currentStep === \"client\" ? \"text-blue-600\" : currentStep === \"invoice\" ? \"text-green-600\" : \"text-purple-600\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-blue-800 mb-2\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-blue-700 space-y-1 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تأكد من صحة رقم الهاتف للتواصل\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• اختر الخدمات المطلوبة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن ترك الحقول الاختيارية فارغة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكنك اختيار عميل موجود من النموذج أدناه\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-blue-100 border border-blue-200 rounded-lg p-3 mb-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-800 font-medium text-sm\",\n                                                                                                children: \"هل تريد إدارة العملاء المسجلين؟\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 532,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-600 text-xs mt-1\",\n                                                                                                children: \"يمكنك عرض وإدارة جميع العملاء المسجلين\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 533,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 531,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCustomerManagement,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 540,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 539,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \"إدارة العملاء\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-teal-100 border border-teal-200 rounded-lg p-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-teal-800 font-medium text-sm\",\n                                                                                                children: \"إدارة أكواد العملاء والعدادات\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 550,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-teal-600 text-xs mt-1\",\n                                                                                                children: \"تتبع وإدارة أكواد العملاء وإحصائيات النظام\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 551,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 549,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCustomerCodes,\n                                                                                        className: \"bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    fillRule: \"evenodd\",\n                                                                                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                                                    clipRule: \"evenodd\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 558,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 557,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \"أكواد العملاء\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 553,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-green-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-green-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• أدخل تفاصيل كل خدمة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تحقق من الأسعار والكميات\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يتم حساب المجموع تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 572,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-purple-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-purple-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• راجع جميع البيانات قبل التصدير\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن طباعة الفاتورة مباشرة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• الفاتورة محفوظة تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 582,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInRight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: handleClientSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"invoice\" && currentClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInLeft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                client: currentClient,\n                                                onSubmit: handleInvoiceSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"preview\" && currentInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInUp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                invoice: currentInvoice,\n                                                onEdit: handleEditInvoice,\n                                                onExportPDF: handleExportPDF\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPage === \"invoices\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fadeIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 p-2 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"نظام إدارة الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm leading-relaxed\",\n                                            children: \"نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-blue-400\",\n                                            children: \"الخدمات المدعومة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 654,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الاستشارات والتصميم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الباترون والطباعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصنيع والعينات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الشحن والتسويق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصوير والمنتجات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-green-400\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"ترقيم تسلسلي للفواتير\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"تصدير PDF احترافي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"حساب المدفوعات التلقائي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"واجهة عربية متجاوبة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إدارة شاملة للعملاء\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"\\xa9 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-xs mt-2\",\n                                    children: \"تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"cg9PneFbjTU2hMlOvoNRCIOKn6I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_8__.useCustomerStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});