'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { PatternPrintingService, PrintingFile } from '@/types';

interface PatternPrintingServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const PatternPrintingServiceComponent: React.FC<PatternPrintingServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as PatternPrintingService;
  const isEnabled = serviceData?.enabled || false;

  // الحصول على بيانات العميل لاستخدام اسم السيلز
  const clientSalesperson = values.client?.salesperson || '';

  const defaultFile: PrintingFile = {
    meters: 0,
    fileCode: '',
    cost: 0,
    salesperson: clientSalesperson, // تعبئة تلقائية من بيانات العميل
  };

  // حساب المجموع الكلي للملفات
  const calculateTotal = () => {
    if (!serviceData?.files) return 0;
    return serviceData.files.reduce((total, file) => {
      return total + (Number(file.cost) || 0);
    }, 0);
  };

  // تحديث المجموع عند تغيير التكلفة
  React.useEffect(() => {
    if (isEnabled && serviceData?.files) {
      const total = calculateTotal();
      setFieldValue(`${namePrefix}.total`, total);
    }
  }, [serviceData?.files, isEnabled]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'pattern_printing');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              // تعبئة الملف الافتراضي مع اسم السيلز من العميل
              const fileWithSalesperson = { ...defaultFile, salesperson: clientSalesperson };
              setFieldValue(`${namePrefix}.files`, [fileWithSalesperson]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة طباعة الباترون</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-indigo-500">
          <FieldArray name={`${namePrefix}.files`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">ملفات الطباعة</h3>
                  <button
                    type="button"
                    onClick={() => {
                      const fileWithSalesperson = { ...defaultFile, salesperson: clientSalesperson };
                      push(fileWithSalesperson);
                    }}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة ملف جديد
                  </button>
                </div>

                {serviceData?.files?.map((file, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">الملف {index + 1}</h4>
                      {serviceData.files.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* عدد الأمتار */}
                      <div className="form-field">
                        <label className="form-label">عدد الأمتار</label>
                        <Field
                          name={`${namePrefix}.files.${index}.meters`}
                          type="number"
                          min="0"
                          step="0.1"
                          className="form-input"
                          placeholder="أدخل عدد الأمتار"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.files.${index}.meters`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* رمز الملف */}
                      <div className="form-field">
                        <label className="form-label">رمز الملف</label>
                        <Field
                          name={`${namePrefix}.files.${index}.fileCode`}
                          type="text"
                          className="form-input"
                          placeholder="مثل: PTN-001"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.files.${index}.fileCode`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* التكلفة */}
                      <div className="form-field">
                        <label className="form-label">التكلفة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.files.${index}.cost`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="تكلفة طباعة الملف"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.files.${index}.cost`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* اسم السيلز */}
                      <div className="form-field">
                        <label className="form-label">اسم السيلز</label>
                        <Field
                          name={`${namePrefix}.files.${index}.salesperson`}
                          type="text"
                          className="form-input bg-gray-100"
                          placeholder="اسم السيلز المسؤول"
                          value={clientSalesperson}
                          readOnly
                        />
                        <div className="text-xs text-gray-500 mt-1">
                          يتم تعبئة هذا الحقل تلقائياً من بيانات العميل
                        </div>
                      </div>
                    </div>

                    {/* وصف الملف */}
                    <div className="form-field mt-4">
                      <label className="form-label">وصف الملف</label>
                      <Field
                        name={`${namePrefix}.files.${index}.description`}
                        as="textarea"
                        rows="2"
                        className="form-input"
                        placeholder="وصف مختصر للملف (اختياري)"
                      />
                    </div>

                    {/* نوع الورق */}
                    <div className="form-field">
                      <label className="form-label">نوع الورق</label>
                      <Field as="select" name={`${namePrefix}.files.${index}.paperType`} className="form-input">
                        <option value="">اختر نوع الورق</option>
                        <option value="A0">A0</option>
                        <option value="A1">A1</option>
                        <option value="A2">A2</option>
                        <option value="A3">A3</option>
                        <option value="A4">A4</option>
                        <option value="custom">مقاس مخصص</option>
                      </Field>
                    </div>

                    {/* جودة الطباعة */}
                    <div className="form-field">
                      <label className="form-label">جودة الطباعة</label>
                      <div className="flex gap-4">
                        <label className="flex items-center">
                          <Field
                            type="radio"
                            name={`${namePrefix}.files.${index}.quality`}
                            value="standard"
                            className="form-checkbox"
                          />
                          <span className="text-sm">عادية</span>
                        </label>
                        <label className="flex items-center">
                          <Field
                            type="radio"
                            name={`${namePrefix}.files.${index}.quality`}
                            value="high"
                            className="form-checkbox"
                          />
                          <span className="text-sm">عالية</span>
                        </label>
                        <label className="flex items-center">
                          <Field
                            type="radio"
                            name={`${namePrefix}.files.${index}.quality`}
                            value="premium"
                            className="form-checkbox"
                          />
                          <span className="text-sm">ممتازة</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>

          {/* المجموع الكلي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-green-700">المجموع الكلي:</span>
              <span className="text-2xl font-bold text-green-800">{calculateTotal().toFixed(2)} جنيه</span>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات إضافية</h4>
            
            {/* تاريخ التسليم المطلوب */}
            <div className="form-field">
              <label className="form-label">تاريخ التسليم المطلوب</label>
              <Field
                name={`${namePrefix}.deliveryDate`}
                type="date"
                className="form-input"
              />
            </div>

            {/* ملاحظات خاصة */}
            <div className="form-field">
              <label className="form-label">ملاحظات خاصة</label>
              <Field
                name={`${namePrefix}.specialNotes`}
                as="textarea"
                rows="3"
                className="form-input"
                placeholder="أي ملاحظات خاصة بالطباعة"
              />
            </div>

            {/* خيارات إضافية */}
            <div className="space-y-2">
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.lamination`}
                  className="form-checkbox"
                />
                <span className="text-sm">تغليف (Lamination)</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.cutting`}
                  className="form-checkbox"
                />
                <span className="text-sm">قص حسب الخطوط</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.folding`}
                  className="form-checkbox"
                />
                <span className="text-sm">طي الباترون</span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatternPrintingServiceComponent;
