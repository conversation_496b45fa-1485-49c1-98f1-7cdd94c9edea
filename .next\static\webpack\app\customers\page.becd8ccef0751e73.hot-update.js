"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/store/customerStore.ts":
/*!************************************!*\
  !*** ./src/store/customerStore.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomerStore: function() { return /* binding */ useCustomerStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/**\n * متجر إدارة العملاء مع نظام الأكواد المتقدم\n */ \n\n\n\n/**\n * تحميل العملاء من النظام الموحد\n */ const loadCustomersFromStorage = async ()=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, []);\n        if (result.success) {\n            console.log(\"✅ تم تحميل العملاء من النظام الموحد:\", result.data.length);\n            return result.data;\n        } else {\n            console.error(\"❌ فشل في تحميل العملاء:\", result.message);\n            return [];\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في تحميل العملاء:\", error);\n        return [];\n    }\n};\n/**\n * حفظ العملاء في النظام الموحد\n */ const saveCustomersToStorage = async (customers)=>{\n    try {\n        const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_0__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEYS.CUSTOMERS, customers);\n        if (result.success) {\n            console.log(\"✅ تم حفظ العملاء في النظام الموحد:\", customers.length);\n            return true;\n        } else {\n            console.error(\"❌ فشل في حفظ العملاء:\", result.message);\n            return false;\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ حرج في حفظ العملاء:\", error);\n        return false;\n    }\n};\n/**\n * إنشاء معرف فريد للعميل\n */ const generateCustomerId = ()=>{\n    return \"customer-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n};\nconst useCustomerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        customers: [],\n        currentCustomer: null,\n        searchQuery: \"\",\n        suggestions: [],\n        isLoading: false,\n        error: null,\n        loadCustomers: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const customers = await loadCustomersFromStorage();\n                set({\n                    customers,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    error: \"فشل في تحميل بيانات العملاء\",\n                    isLoading: false\n                });\n            }\n        },\n        addCustomer: async (customerData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // إنشاء كود العميل\n                const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                // إنشاء العميل الجديد\n                const newCustomer = {\n                    ...customerData,\n                    id: generateCustomerId(),\n                    clientCode: codeData.code\n                };\n                // إضافة العميل للقائمة\n                const updatedCustomers = [\n                    ...customers,\n                    newCustomer\n                ];\n                // حفظ في التخزين\n                const saved = await saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ بيانات العميل\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true,\n                    customer: newCustomer\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ غير متوقع\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        // إضافة عملاء متعددين (للاستيراد)\n        addMultipleCustomers: async (customersData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                const newCustomers = [];\n                const errors = [];\n                for (const customerData of customersData){\n                    try {\n                        let clientCode = customerData.clientCode;\n                        // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد\n                        if (!clientCode) {\n                            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n                            clientCode = codeData.code;\n                        }\n                        // التحقق من عدم تكرار الكود\n                        const existingCustomer = [\n                            ...customers,\n                            ...newCustomers\n                        ].find((c)=>c.clientCode === clientCode);\n                        if (existingCustomer) {\n                            errors.push(\"كود العميل \".concat(clientCode, \" مستخدم بالفعل للعميل \").concat(customerData.name));\n                            continue;\n                        }\n                        const newCustomer = {\n                            ...customerData,\n                            id: generateCustomerId(),\n                            clientCode\n                        };\n                        newCustomers.push(newCustomer);\n                    } catch (error) {\n                        errors.push(\"خطأ في إضافة العميل \".concat(customerData.name, \": \").concat(error instanceof Error ? error.message : \"خطأ غير معروف\"));\n                    }\n                }\n                if (newCustomers.length > 0) {\n                    const updatedCustomers = [\n                        ...customers,\n                        ...newCustomers\n                    ];\n                    // حفظ في التخزين\n                    const saved = saveCustomersToStorage(updatedCustomers);\n                    if (!saved) {\n                        throw new Error(\"فشل في حفظ بيانات العملاء\");\n                    }\n                    set({\n                        customers: updatedCustomers,\n                        isLoading: false\n                    });\n                    return {\n                        success: true,\n                        imported: newCustomers.length,\n                        errors,\n                        customers: newCustomers\n                    };\n                } else {\n                    set({\n                        isLoading: false\n                    });\n                    return {\n                        success: false,\n                        error: \"لم يتم إضافة أي عميل\",\n                        imported: 0,\n                        errors\n                    };\n                }\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage,\n                    imported: 0,\n                    errors: []\n                };\n            }\n        },\n        updateCustomer: async (id, updates)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerIndex = customers.findIndex((c)=>c.id === id);\n                if (customerIndex === -1) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // التحقق من كود العميل إذا تم تحديثه\n                if (updates.clientCode) {\n                    const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(updates.clientCode);\n                    if (!validation.isValid) {\n                        throw new Error(validation.error || \"كود العميل غير صحيح\");\n                    }\n                    if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(updates.clientCode, id)) {\n                        throw new Error(\"كود العميل مستخدم بالفعل\");\n                    }\n                }\n                // تحديث العميل\n                const updatedCustomers = [\n                    ...customers\n                ];\n                updatedCustomers[customerIndex] = {\n                    ...updatedCustomers[customerIndex],\n                    ...updates\n                };\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التحديثات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في التحديث\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        deleteCustomer: async (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                var _get_currentCustomer;\n                const { customers } = get();\n                // التحقق من وجود العميل\n                const customerExists = customers.some((c)=>c.id === id);\n                if (!customerExists) {\n                    throw new Error(\"العميل غير موجود\");\n                }\n                // حذف العميل\n                const updatedCustomers = customers.filter((c)=>c.id !== id);\n                // حفظ في التخزين\n                const saved = saveCustomersToStorage(updatedCustomers);\n                if (!saved) {\n                    throw new Error(\"فشل في حفظ التغييرات\");\n                }\n                set({\n                    customers: updatedCustomers,\n                    currentCustomer: ((_get_currentCustomer = get().currentCustomer) === null || _get_currentCustomer === void 0 ? void 0 : _get_currentCustomer.id) === id ? null : get().currentCustomer,\n                    isLoading: false\n                });\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"حدث خطأ في الحذف\";\n                set({\n                    error: errorMessage,\n                    isLoading: false\n                });\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n        },\n        setCurrentCustomer: (customer)=>{\n            set({\n                currentCustomer: customer\n            });\n        },\n        setSearchQuery: (query)=>{\n            set({\n                searchQuery: query\n            });\n        },\n        searchCustomers: (query)=>{\n            const { customers } = get();\n            if (!query.trim()) {\n                return customers;\n            }\n            const searchTerm = query.toLowerCase().trim();\n            // البحث الأساسي المحسن\n            const basicResults = customers.filter((customer)=>customer.name.toLowerCase().includes(searchTerm) || customer.phone.includes(query) || customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm) || customer.businessName && customer.businessName.toLowerCase().includes(searchTerm) || customer.province && customer.province.toLowerCase().includes(searchTerm) || customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm) || customer.services.some((service)=>service.toLowerCase().includes(searchTerm)));\n            // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج\n            if (basicResults.length === 0 && searchTerm.length > 2) {\n                const fuzzyResults = customers.filter((customer)=>{\n                    const name = customer.name.toLowerCase();\n                    const words = searchTerm.split(\" \");\n                    return words.some((word)=>name.includes(word) && word.length > 1);\n                });\n                return fuzzyResults;\n            }\n            // ترتيب النتائج حسب الأولوية\n            return basicResults.sort((a, b)=>{\n                var _a_clientCode, _b_clientCode;\n                // أولوية للمطابقة الدقيقة في بداية الاسم\n                const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);\n                const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);\n                if (aNameMatch && !bNameMatch) return -1;\n                if (!aNameMatch && bNameMatch) return 1;\n                // أولوية للمطابقة في كود العميل\n                const aCodeMatch = (_a_clientCode = a.clientCode) === null || _a_clientCode === void 0 ? void 0 : _a_clientCode.toLowerCase().includes(searchTerm);\n                const bCodeMatch = (_b_clientCode = b.clientCode) === null || _b_clientCode === void 0 ? void 0 : _b_clientCode.toLowerCase().includes(searchTerm);\n                if (aCodeMatch && !bCodeMatch) return -1;\n                if (!aCodeMatch && bCodeMatch) return 1;\n                // ترتيب أبجدي كافتراضي\n                return a.name.localeCompare(b.name, \"ar\");\n            });\n        },\n        // الاقتراحات السريعة للبحث\n        getQuickSuggestions: function(query) {\n            let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n            const { customers } = get();\n            if (!query.trim()) return [];\n            const searchTerm = query.toLowerCase().trim();\n            const suggestions = customers.filter((customer)=>customer.name.toLowerCase().startsWith(searchTerm) || customer.phone.startsWith(query) || customer.clientCode && customer.clientCode.toLowerCase().startsWith(searchTerm)).slice(0, limit).map((customer)=>{\n                var _customer_clientCode, _customer_clientCode1;\n                return {\n                    id: customer.id,\n                    name: customer.name,\n                    phone: customer.phone,\n                    clientCode: customer.clientCode,\n                    businessName: customer.businessName,\n                    type: ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().startsWith(searchTerm)) ? \"code\" : customer.phone.startsWith(query) ? \"phone\" : \"name\",\n                    matchText: ((_customer_clientCode1 = customer.clientCode) === null || _customer_clientCode1 === void 0 ? void 0 : _customer_clientCode1.toLowerCase().startsWith(searchTerm)) ? customer.clientCode : customer.phone.startsWith(query) ? customer.phone : customer.name\n                };\n            });\n            return suggestions;\n        },\n        getSuggestions: (query)=>{\n            if (!query.trim()) {\n                set({\n                    suggestions: []\n                });\n                return;\n            }\n            const suggestions = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeSuggestions)(query, 10);\n            set({\n                suggestions\n            });\n        },\n        clearSuggestions: ()=>{\n            set({\n                suggestions: []\n            });\n        },\n        generateNewCustomerCode: ()=>{\n            const codeData = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            return codeData.code;\n        },\n        validateCode: (code, excludeId)=>{\n            const validation = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.validateCustomerCode)(code);\n            if (!validation.isValid) {\n                return validation;\n            }\n            if (!(0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.isCustomerCodeUnique)(code, excludeId)) {\n                return {\n                    isValid: false,\n                    error: \"كود العميل مستخدم بالفعل\"\n                };\n            }\n            return {\n                isValid: true\n            };\n        },\n        findByCode: (code)=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(code);\n        },\n        getStats: ()=>{\n            return (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n        },\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        refreshData: ()=>{\n            get().loadCustomers();\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9jdXN0b21lclN0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7O0NBRUMsR0FFZ0M7QUFHOEI7QUFDWjtBQVFaO0FBbUN2Qzs7Q0FFQyxHQUNELE1BQU1TLDJCQUEyQjtJQUMvQixJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNVCx3RUFBcUJBLENBQUNVLE9BQU8sQ0FBQ1QsNERBQVlBLENBQUNVLFNBQVMsRUFBRSxFQUFFO1FBQzdFLElBQUlGLE9BQU9HLE9BQU8sRUFBRTtZQUNsQkMsUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q0wsT0FBT00sSUFBSSxDQUFDQyxNQUFNO1lBQ3RFLE9BQU9QLE9BQU9NLElBQUk7UUFDcEIsT0FBTztZQUNMRixRQUFRSSxLQUFLLENBQUMsMkJBQTJCUixPQUFPUyxPQUFPO1lBQ3ZELE9BQU8sRUFBRTtRQUNYO0lBQ0YsRUFBRSxPQUFPRCxPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELE1BQU1FLHlCQUF5QixPQUFPQztJQUNwQyxJQUFJO1FBQ0YsTUFBTVgsU0FBUyxNQUFNVCx3RUFBcUJBLENBQUNxQixPQUFPLENBQUNwQiw0REFBWUEsQ0FBQ1UsU0FBUyxFQUFFUztRQUMzRSxJQUFJWCxPQUFPRyxPQUFPLEVBQUU7WUFDbEJDLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NNLFVBQVVKLE1BQU07WUFDbEUsT0FBTztRQUNULE9BQU87WUFDTEgsUUFBUUksS0FBSyxDQUFDLHlCQUF5QlIsT0FBT1MsT0FBTztZQUNyRCxPQUFPO1FBQ1Q7SUFDRixFQUFFLE9BQU9ELE9BQU87UUFDZEosUUFBUUksS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELE1BQU1LLHFCQUFxQjtJQUN6QixPQUFPLFlBQTBCQyxPQUFkQyxLQUFLQyxHQUFHLElBQUcsS0FBMkMsT0FBeENGLEtBQUtHLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHO0FBQ3hFO0FBRU8sTUFBTUMsbUJBQW1COUIsK0NBQU1BLENBQWdCLENBQUMrQixLQUFLQyxNQUFTO1FBQ25FWCxXQUFXLEVBQUU7UUFDYlksaUJBQWlCO1FBQ2pCQyxhQUFhO1FBQ2JDLGFBQWEsRUFBRTtRQUNmQyxXQUFXO1FBQ1hsQixPQUFPO1FBRVBtQixlQUFlO1lBQ2JOLElBQUk7Z0JBQUVLLFdBQVc7Z0JBQU1sQixPQUFPO1lBQUs7WUFDbkMsSUFBSTtnQkFDRixNQUFNRyxZQUFZLE1BQU1aO2dCQUN4QnNCLElBQUk7b0JBQUVWO29CQUFXZSxXQUFXO2dCQUFNO1lBQ3BDLEVBQUUsT0FBT2xCLE9BQU87Z0JBQ2RhLElBQUk7b0JBQ0ZiLE9BQU87b0JBQ1BrQixXQUFXO2dCQUNiO1lBQ0Y7UUFDRjtRQUVBRSxhQUFhLE9BQU9DO1lBQ2xCUixJQUFJO2dCQUFFSyxXQUFXO2dCQUFNbEIsT0FBTztZQUFLO1lBRW5DLElBQUk7Z0JBQ0YsTUFBTSxFQUFFRyxTQUFTLEVBQUUsR0FBR1c7Z0JBRXRCLG1CQUFtQjtnQkFDbkIsTUFBTVEsV0FBV3JDLGtGQUFvQkE7Z0JBRXJDLHNCQUFzQjtnQkFDdEIsTUFBTXNDLGNBQXNCO29CQUMxQixHQUFHRixZQUFZO29CQUNmRyxJQUFJbkI7b0JBQ0pvQixZQUFZSCxTQUFTSSxJQUFJO2dCQUMzQjtnQkFFQSx1QkFBdUI7Z0JBQ3ZCLE1BQU1DLG1CQUFtQjt1QkFBSXhCO29CQUFXb0I7aUJBQVk7Z0JBRXBELGlCQUFpQjtnQkFDakIsTUFBTUssUUFBUSxNQUFNMUIsdUJBQXVCeUI7Z0JBQzNDLElBQUksQ0FBQ0MsT0FBTztvQkFDVixNQUFNLElBQUlDLE1BQU07Z0JBQ2xCO2dCQUVBaEIsSUFBSTtvQkFDRlYsV0FBV3dCO29CQUNYVCxXQUFXO2dCQUNiO2dCQUVBLE9BQU87b0JBQUV2QixTQUFTO29CQUFNbUMsVUFBVVA7Z0JBQVk7WUFDaEQsRUFBRSxPQUFPdkIsT0FBTztnQkFDZCxNQUFNK0IsZUFBZS9CLGlCQUFpQjZCLFFBQVE3QixNQUFNQyxPQUFPLEdBQUc7Z0JBQzlEWSxJQUFJO29CQUNGYixPQUFPK0I7b0JBQ1BiLFdBQVc7Z0JBQ2I7Z0JBQ0EsT0FBTztvQkFBRXZCLFNBQVM7b0JBQU9LLE9BQU8rQjtnQkFBYTtZQUMvQztRQUNGO1FBRUEsa0NBQWtDO1FBQ2xDQyxzQkFBc0IsT0FBT0M7WUFDM0JwQixJQUFJO2dCQUFFSyxXQUFXO2dCQUFNbEIsT0FBTztZQUFLO1lBRW5DLElBQUk7Z0JBQ0YsTUFBTSxFQUFFRyxTQUFTLEVBQUUsR0FBR1c7Z0JBQ3RCLE1BQU1vQixlQUF5QixFQUFFO2dCQUNqQyxNQUFNQyxTQUFtQixFQUFFO2dCQUUzQixLQUFLLE1BQU1kLGdCQUFnQlksY0FBZTtvQkFDeEMsSUFBSTt3QkFDRixJQUFJUixhQUFhSixhQUFhSSxVQUFVO3dCQUV4Qyw0Q0FBNEM7d0JBQzVDLElBQUksQ0FBQ0EsWUFBWTs0QkFDZixNQUFNSCxXQUFXckMsa0ZBQW9CQTs0QkFDckN3QyxhQUFhSCxTQUFTSSxJQUFJO3dCQUM1Qjt3QkFFQSw0QkFBNEI7d0JBQzVCLE1BQU1VLG1CQUFtQjsrQkFBSWpDOytCQUFjK0I7eUJBQWEsQ0FBQ0csSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFYixVQUFVLEtBQUtBO3dCQUNwRixJQUFJVyxrQkFBa0I7NEJBQ3BCRCxPQUFPSSxJQUFJLENBQUMsY0FBaURsQixPQUFuQ0ksWUFBVywwQkFBMEMsT0FBbEJKLGFBQWFtQixJQUFJOzRCQUM5RTt3QkFDRjt3QkFFQSxNQUFNakIsY0FBc0I7NEJBQzFCLEdBQUdGLFlBQVk7NEJBQ2ZHLElBQUluQjs0QkFDSm9CO3dCQUNGO3dCQUVBUyxhQUFhSyxJQUFJLENBQUNoQjtvQkFDcEIsRUFBRSxPQUFPdkIsT0FBTzt3QkFDZG1DLE9BQU9JLElBQUksQ0FBQyx1QkFBNkN2QyxPQUF0QnFCLGFBQWFtQixJQUFJLEVBQUMsTUFBNkQsT0FBekR4QyxpQkFBaUI2QixRQUFRN0IsTUFBTUMsT0FBTyxHQUFHO29CQUNwRztnQkFDRjtnQkFFQSxJQUFJaUMsYUFBYW5DLE1BQU0sR0FBRyxHQUFHO29CQUMzQixNQUFNNEIsbUJBQW1COzJCQUFJeEI7MkJBQWMrQjtxQkFBYTtvQkFFeEQsaUJBQWlCO29CQUNqQixNQUFNTixRQUFRMUIsdUJBQXVCeUI7b0JBQ3JDLElBQUksQ0FBQ0MsT0FBTzt3QkFDVixNQUFNLElBQUlDLE1BQU07b0JBQ2xCO29CQUVBaEIsSUFBSTt3QkFDRlYsV0FBV3dCO3dCQUNYVCxXQUFXO29CQUNiO29CQUVBLE9BQU87d0JBQ0x2QixTQUFTO3dCQUNUOEMsVUFBVVAsYUFBYW5DLE1BQU07d0JBQzdCb0M7d0JBQ0FoQyxXQUFXK0I7b0JBQ2I7Z0JBQ0YsT0FBTztvQkFDTHJCLElBQUk7d0JBQUVLLFdBQVc7b0JBQU07b0JBQ3ZCLE9BQU87d0JBQUV2QixTQUFTO3dCQUFPSyxPQUFPO3dCQUF3QnlDLFVBQVU7d0JBQUdOO29CQUFPO2dCQUM5RTtZQUNGLEVBQUUsT0FBT25DLE9BQU87Z0JBQ2QsTUFBTStCLGVBQWUvQixpQkFBaUI2QixRQUFRN0IsTUFBTUMsT0FBTyxHQUFHO2dCQUM5RFksSUFBSTtvQkFDRmIsT0FBTytCO29CQUNQYixXQUFXO2dCQUNiO2dCQUNBLE9BQU87b0JBQUV2QixTQUFTO29CQUFPSyxPQUFPK0I7b0JBQWNVLFVBQVU7b0JBQUdOLFFBQVEsRUFBRTtnQkFBQztZQUN4RTtRQUNGO1FBRUFPLGdCQUFnQixPQUFPbEIsSUFBSW1CO1lBQ3pCOUIsSUFBSTtnQkFBRUssV0FBVztnQkFBTWxCLE9BQU87WUFBSztZQUVuQyxJQUFJO2dCQUNGLE1BQU0sRUFBRUcsU0FBUyxFQUFFLEdBQUdXO2dCQUV0Qix3QkFBd0I7Z0JBQ3hCLE1BQU04QixnQkFBZ0J6QyxVQUFVMEMsU0FBUyxDQUFDUCxDQUFBQSxJQUFLQSxFQUFFZCxFQUFFLEtBQUtBO2dCQUN4RCxJQUFJb0Isa0JBQWtCLENBQUMsR0FBRztvQkFDeEIsTUFBTSxJQUFJZixNQUFNO2dCQUNsQjtnQkFFQSxxQ0FBcUM7Z0JBQ3JDLElBQUljLFFBQVFsQixVQUFVLEVBQUU7b0JBQ3RCLE1BQU1xQixhQUFhNUQsa0ZBQW9CQSxDQUFDeUQsUUFBUWxCLFVBQVU7b0JBQzFELElBQUksQ0FBQ3FCLFdBQVdDLE9BQU8sRUFBRTt3QkFDdkIsTUFBTSxJQUFJbEIsTUFBTWlCLFdBQVc5QyxLQUFLLElBQUk7b0JBQ3RDO29CQUVBLElBQUksQ0FBQ2Isa0ZBQW9CQSxDQUFDd0QsUUFBUWxCLFVBQVUsRUFBRUQsS0FBSzt3QkFDakQsTUFBTSxJQUFJSyxNQUFNO29CQUNsQjtnQkFDRjtnQkFFQSxlQUFlO2dCQUNmLE1BQU1GLG1CQUFtQjt1QkFBSXhCO2lCQUFVO2dCQUN2Q3dCLGdCQUFnQixDQUFDaUIsY0FBYyxHQUFHO29CQUNoQyxHQUFHakIsZ0JBQWdCLENBQUNpQixjQUFjO29CQUNsQyxHQUFHRCxPQUFPO2dCQUNaO2dCQUVBLGlCQUFpQjtnQkFDakIsTUFBTWYsUUFBUTFCLHVCQUF1QnlCO2dCQUNyQyxJQUFJLENBQUNDLE9BQU87b0JBQ1YsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQjtnQkFFQWhCLElBQUk7b0JBQ0ZWLFdBQVd3QjtvQkFDWFQsV0FBVztnQkFDYjtnQkFFQSxPQUFPO29CQUFFdkIsU0FBUztnQkFBSztZQUN6QixFQUFFLE9BQU9LLE9BQU87Z0JBQ2QsTUFBTStCLGVBQWUvQixpQkFBaUI2QixRQUFRN0IsTUFBTUMsT0FBTyxHQUFHO2dCQUM5RFksSUFBSTtvQkFDRmIsT0FBTytCO29CQUNQYixXQUFXO2dCQUNiO2dCQUNBLE9BQU87b0JBQUV2QixTQUFTO29CQUFPSyxPQUFPK0I7Z0JBQWE7WUFDL0M7UUFDRjtRQUVBaUIsZ0JBQWdCLE9BQU94QjtZQUNyQlgsSUFBSTtnQkFBRUssV0FBVztnQkFBTWxCLE9BQU87WUFBSztZQUVuQyxJQUFJO29CQW9CaUJjO2dCQW5CbkIsTUFBTSxFQUFFWCxTQUFTLEVBQUUsR0FBR1c7Z0JBRXRCLHdCQUF3QjtnQkFDeEIsTUFBTW1DLGlCQUFpQjlDLFVBQVUrQyxJQUFJLENBQUNaLENBQUFBLElBQUtBLEVBQUVkLEVBQUUsS0FBS0E7Z0JBQ3BELElBQUksQ0FBQ3lCLGdCQUFnQjtvQkFDbkIsTUFBTSxJQUFJcEIsTUFBTTtnQkFDbEI7Z0JBRUEsYUFBYTtnQkFDYixNQUFNRixtQkFBbUJ4QixVQUFVZ0QsTUFBTSxDQUFDYixDQUFBQSxJQUFLQSxFQUFFZCxFQUFFLEtBQUtBO2dCQUV4RCxpQkFBaUI7Z0JBQ2pCLE1BQU1JLFFBQVExQix1QkFBdUJ5QjtnQkFDckMsSUFBSSxDQUFDQyxPQUFPO29CQUNWLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUFoQixJQUFJO29CQUNGVixXQUFXd0I7b0JBQ1haLGlCQUFpQkQsRUFBQUEsdUJBQUFBLE1BQU1DLGVBQWUsY0FBckJELDJDQUFBQSxxQkFBdUJVLEVBQUUsTUFBS0EsS0FBSyxPQUFPVixNQUFNQyxlQUFlO29CQUNoRkcsV0FBVztnQkFDYjtnQkFFQSxPQUFPO29CQUFFdkIsU0FBUztnQkFBSztZQUN6QixFQUFFLE9BQU9LLE9BQU87Z0JBQ2QsTUFBTStCLGVBQWUvQixpQkFBaUI2QixRQUFRN0IsTUFBTUMsT0FBTyxHQUFHO2dCQUM5RFksSUFBSTtvQkFDRmIsT0FBTytCO29CQUNQYixXQUFXO2dCQUNiO2dCQUNBLE9BQU87b0JBQUV2QixTQUFTO29CQUFPSyxPQUFPK0I7Z0JBQWE7WUFDL0M7UUFDRjtRQUVBcUIsb0JBQW9CLENBQUN0QjtZQUNuQmpCLElBQUk7Z0JBQUVFLGlCQUFpQmU7WUFBUztRQUNsQztRQUVBdUIsZ0JBQWdCLENBQUNDO1lBQ2Z6QyxJQUFJO2dCQUFFRyxhQUFhc0M7WUFBTTtRQUMzQjtRQUVBQyxpQkFBaUIsQ0FBQ0Q7WUFDaEIsTUFBTSxFQUFFbkQsU0FBUyxFQUFFLEdBQUdXO1lBQ3RCLElBQUksQ0FBQ3dDLE1BQU1FLElBQUksSUFBSTtnQkFDakIsT0FBT3JEO1lBQ1Q7WUFFQSxNQUFNc0QsYUFBYUgsTUFBTUksV0FBVyxHQUFHRixJQUFJO1lBRTNDLHVCQUF1QjtZQUN2QixNQUFNRyxlQUFleEQsVUFBVWdELE1BQU0sQ0FBQ3JCLENBQUFBLFdBQ3BDQSxTQUFTVSxJQUFJLENBQUNrQixXQUFXLEdBQUdFLFFBQVEsQ0FBQ0gsZUFDckMzQixTQUFTK0IsS0FBSyxDQUFDRCxRQUFRLENBQUNOLFVBQ3ZCeEIsU0FBU0wsVUFBVSxJQUFJSyxTQUFTTCxVQUFVLENBQUNpQyxXQUFXLEdBQUdFLFFBQVEsQ0FBQ0gsZUFDbEUzQixTQUFTZ0MsWUFBWSxJQUFJaEMsU0FBU2dDLFlBQVksQ0FBQ0osV0FBVyxHQUFHRSxRQUFRLENBQUNILGVBQ3RFM0IsU0FBU2lDLFFBQVEsSUFBSWpDLFNBQVNpQyxRQUFRLENBQUNMLFdBQVcsR0FBR0UsUUFBUSxDQUFDSCxlQUM5RDNCLFNBQVNrQyxXQUFXLElBQUlsQyxTQUFTa0MsV0FBVyxDQUFDTixXQUFXLEdBQUdFLFFBQVEsQ0FBQ0gsZUFDckUzQixTQUFTbUMsUUFBUSxDQUFDZixJQUFJLENBQUNnQixDQUFBQSxVQUFXQSxRQUFRUixXQUFXLEdBQUdFLFFBQVEsQ0FBQ0g7WUFHbkUsa0RBQWtEO1lBQ2xELElBQUlFLGFBQWE1RCxNQUFNLEtBQUssS0FBSzBELFdBQVcxRCxNQUFNLEdBQUcsR0FBRztnQkFDdEQsTUFBTW9FLGVBQWVoRSxVQUFVZ0QsTUFBTSxDQUFDckIsQ0FBQUE7b0JBQ3BDLE1BQU1VLE9BQU9WLFNBQVNVLElBQUksQ0FBQ2tCLFdBQVc7b0JBQ3RDLE1BQU1VLFFBQVFYLFdBQVdZLEtBQUssQ0FBQztvQkFDL0IsT0FBT0QsTUFBTWxCLElBQUksQ0FBQ29CLENBQUFBLE9BQVE5QixLQUFLb0IsUUFBUSxDQUFDVSxTQUFTQSxLQUFLdkUsTUFBTSxHQUFHO2dCQUNqRTtnQkFDQSxPQUFPb0U7WUFDVDtZQUVBLDZCQUE2QjtZQUM3QixPQUFPUixhQUFhWSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7b0JBUVJELGVBQ0FDO2dCQVJuQix5Q0FBeUM7Z0JBQ3pDLE1BQU1DLGFBQWFGLEVBQUVoQyxJQUFJLENBQUNrQixXQUFXLEdBQUdpQixVQUFVLENBQUNsQjtnQkFDbkQsTUFBTW1CLGFBQWFILEVBQUVqQyxJQUFJLENBQUNrQixXQUFXLEdBQUdpQixVQUFVLENBQUNsQjtnQkFDbkQsSUFBSWlCLGNBQWMsQ0FBQ0UsWUFBWSxPQUFPLENBQUM7Z0JBQ3ZDLElBQUksQ0FBQ0YsY0FBY0UsWUFBWSxPQUFPO2dCQUV0QyxnQ0FBZ0M7Z0JBQ2hDLE1BQU1DLGNBQWFMLGdCQUFBQSxFQUFFL0MsVUFBVSxjQUFaK0Msb0NBQUFBLGNBQWNkLFdBQVcsR0FBR0UsUUFBUSxDQUFDSDtnQkFDeEQsTUFBTXFCLGNBQWFMLGdCQUFBQSxFQUFFaEQsVUFBVSxjQUFaZ0Qsb0NBQUFBLGNBQWNmLFdBQVcsR0FBR0UsUUFBUSxDQUFDSDtnQkFDeEQsSUFBSW9CLGNBQWMsQ0FBQ0MsWUFBWSxPQUFPLENBQUM7Z0JBQ3ZDLElBQUksQ0FBQ0QsY0FBY0MsWUFBWSxPQUFPO2dCQUV0Qyx1QkFBdUI7Z0JBQ3ZCLE9BQU9OLEVBQUVoQyxJQUFJLENBQUN1QyxhQUFhLENBQUNOLEVBQUVqQyxJQUFJLEVBQUU7WUFDdEM7UUFDRjtRQUVBLDJCQUEyQjtRQUMzQndDLHFCQUFxQixTQUFDMUI7Z0JBQU8yQix5RUFBUTtZQUNuQyxNQUFNLEVBQUU5RSxTQUFTLEVBQUUsR0FBR1c7WUFDdEIsSUFBSSxDQUFDd0MsTUFBTUUsSUFBSSxJQUFJLE9BQU8sRUFBRTtZQUU1QixNQUFNQyxhQUFhSCxNQUFNSSxXQUFXLEdBQUdGLElBQUk7WUFDM0MsTUFBTXZDLGNBQWNkLFVBQ2pCZ0QsTUFBTSxDQUFDckIsQ0FBQUEsV0FDTkEsU0FBU1UsSUFBSSxDQUFDa0IsV0FBVyxHQUFHaUIsVUFBVSxDQUFDbEIsZUFDdkMzQixTQUFTK0IsS0FBSyxDQUFDYyxVQUFVLENBQUNyQixVQUN6QnhCLFNBQVNMLFVBQVUsSUFBSUssU0FBU0wsVUFBVSxDQUFDaUMsV0FBVyxHQUFHaUIsVUFBVSxDQUFDbEIsYUFFdEV5QixLQUFLLENBQUMsR0FBR0QsT0FDVEUsR0FBRyxDQUFDckQsQ0FBQUE7b0JBTUdBLHNCQUVLQTt1QkFSSztvQkFDaEJOLElBQUlNLFNBQVNOLEVBQUU7b0JBQ2ZnQixNQUFNVixTQUFTVSxJQUFJO29CQUNuQnFCLE9BQU8vQixTQUFTK0IsS0FBSztvQkFDckJwQyxZQUFZSyxTQUFTTCxVQUFVO29CQUMvQnFDLGNBQWNoQyxTQUFTZ0MsWUFBWTtvQkFDbkNzQixNQUFNdEQsRUFBQUEsdUJBQUFBLFNBQVNMLFVBQVUsY0FBbkJLLDJDQUFBQSxxQkFBcUI0QixXQUFXLEdBQUdpQixVQUFVLENBQUNsQixlQUFjLFNBQzVEM0IsU0FBUytCLEtBQUssQ0FBQ2MsVUFBVSxDQUFDckIsU0FBUyxVQUFVO29CQUNuRCtCLFdBQVd2RCxFQUFBQSx3QkFBQUEsU0FBU0wsVUFBVSxjQUFuQkssNENBQUFBLHNCQUFxQjRCLFdBQVcsR0FBR2lCLFVBQVUsQ0FBQ2xCLGVBQWMzQixTQUFTTCxVQUFVLEdBQ2hGSyxTQUFTK0IsS0FBSyxDQUFDYyxVQUFVLENBQUNyQixTQUFTeEIsU0FBUytCLEtBQUssR0FBRy9CLFNBQVNVLElBQUk7Z0JBQzdFOztZQUVGLE9BQU92QjtRQUNUO1FBRUFxRSxnQkFBZ0IsQ0FBQ2hDO1lBQ2YsSUFBSSxDQUFDQSxNQUFNRSxJQUFJLElBQUk7Z0JBQ2pCM0MsSUFBSTtvQkFBRUksYUFBYSxFQUFFO2dCQUFDO2dCQUN0QjtZQUNGO1lBRUEsTUFBTUEsY0FBYzNCLHdGQUEwQkEsQ0FBQ2dFLE9BQU87WUFDdER6QyxJQUFJO2dCQUFFSTtZQUFZO1FBQ3BCO1FBRUFzRSxrQkFBa0I7WUFDaEIxRSxJQUFJO2dCQUFFSSxhQUFhLEVBQUU7WUFBQztRQUN4QjtRQUVBdUUseUJBQXlCO1lBQ3ZCLE1BQU1sRSxXQUFXckMsa0ZBQW9CQTtZQUNyQyxPQUFPcUMsU0FBU0ksSUFBSTtRQUN0QjtRQUVBK0QsY0FBYyxDQUFDL0QsTUFBTWdFO1lBQ25CLE1BQU01QyxhQUFhNUQsa0ZBQW9CQSxDQUFDd0M7WUFDeEMsSUFBSSxDQUFDb0IsV0FBV0MsT0FBTyxFQUFFO2dCQUN2QixPQUFPRDtZQUNUO1lBRUEsSUFBSSxDQUFDM0Qsa0ZBQW9CQSxDQUFDdUMsTUFBTWdFLFlBQVk7Z0JBQzFDLE9BQU87b0JBQUUzQyxTQUFTO29CQUFPL0MsT0FBTztnQkFBMkI7WUFDN0Q7WUFFQSxPQUFPO2dCQUFFK0MsU0FBUztZQUFLO1FBQ3pCO1FBRUE0QyxZQUFZLENBQUNqRTtZQUNYLE9BQU9yQyxnRkFBa0JBLENBQUNxQztRQUM1QjtRQUVBa0UsVUFBVTtZQUNSLE9BQU94RyxrRkFBb0JBO1FBQzdCO1FBRUF5RyxZQUFZO1lBQ1ZoRixJQUFJO2dCQUFFYixPQUFPO1lBQUs7UUFDcEI7UUFFQThGLGFBQWE7WUFDWGhGLE1BQU1LLGFBQWE7UUFDckI7SUFDRixJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdG9yZS9jdXN0b21lclN0b3JlLnRzPzEyNWYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDZhdiq2KzYsSDYpdiv2KfYsdipINin2YTYudmF2YTYp9ihINmF2Lkg2YbYuNin2YUg2KfZhNij2YPZiNin2K8g2KfZhNmF2KrZgtiv2YVcbiAqL1xuXG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IENsaWVudCB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgc2VjdXJlR2V0SXRlbSwgc2VjdXJlU2V0SXRlbSB9IGZyb20gJ0AvdXRpbHMvc2VjdXJlU3RvcmFnZSc7XG5pbXBvcnQgeyBVbmlmaWVkU3RvcmFnZU1hbmFnZXIgfSBmcm9tICdAL3V0aWxzL3VuaWZpZWRTdG9yYWdlJztcbmltcG9ydCB7IFNUT1JBR0VfS0VZUyB9IGZyb20gJ0AvdXRpbHMvc3RvcmFnZUtleXMnO1xuaW1wb3J0IHtcbiAgZ2VuZXJhdGVDdXN0b21lckNvZGUsXG4gIHZhbGlkYXRlQ3VzdG9tZXJDb2RlLFxuICBpc0N1c3RvbWVyQ29kZVVuaXF1ZSxcbiAgZ2V0Q3VzdG9tZXJDb2RlU3RhdHMsXG4gIGZpbmRDdXN0b21lckJ5Q29kZSxcbiAgZ2V0Q3VzdG9tZXJDb2RlU3VnZ2VzdGlvbnNcbn0gZnJvbSAnQC91dGlscy9jdXN0b21lckNvZGVHZW5lcmF0b3InO1xuXG5pbnRlcmZhY2UgQ3VzdG9tZXJTdG9yZSB7XG4gIGN1c3RvbWVyczogQ2xpZW50W107XG4gIGN1cnJlbnRDdXN0b21lcjogQ2xpZW50IHwgbnVsbDtcbiAgc2VhcmNoUXVlcnk6IHN0cmluZztcbiAgc3VnZ2VzdGlvbnM6IENsaWVudFtdO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuXG4gIC8vIEFjdGlvbnNcbiAgbG9hZEN1c3RvbWVyczogKCkgPT4gdm9pZDtcbiAgYWRkQ3VzdG9tZXI6IChjdXN0b21lcjogT21pdDxDbGllbnQsICdpZCcgfCAnY2xpZW50Q29kZSc+KSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgY3VzdG9tZXI/OiBDbGllbnQ7IGVycm9yPzogc3RyaW5nIH0+O1xuICB1cGRhdGVDdXN0b21lcjogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8Q2xpZW50PikgPT4gUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+O1xuICBkZWxldGVDdXN0b21lcjogKGlkOiBzdHJpbmcpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PjtcbiAgc2V0Q3VycmVudEN1c3RvbWVyOiAoY3VzdG9tZXI6IENsaWVudCB8IG51bGwpID0+IHZvaWQ7XG4gIFxuICAvLyBTZWFyY2ggYW5kIHN1Z2dlc3Rpb25zXG4gIHNldFNlYXJjaFF1ZXJ5OiAocXVlcnk6IHN0cmluZykgPT4gdm9pZDtcbiAgc2VhcmNoQ3VzdG9tZXJzOiAocXVlcnk6IHN0cmluZykgPT4gQ2xpZW50W107XG4gIGdldFF1aWNrU3VnZ2VzdGlvbnM6IChxdWVyeTogc3RyaW5nLCBsaW1pdD86IG51bWJlcikgPT4gYW55W107XG4gIGdldFN1Z2dlc3Rpb25zOiAocXVlcnk6IHN0cmluZykgPT4gdm9pZDtcbiAgY2xlYXJTdWdnZXN0aW9uczogKCkgPT4gdm9pZDtcbiAgXG4gIC8vIEN1c3RvbWVyIGNvZGUgbWFuYWdlbWVudFxuICBnZW5lcmF0ZU5ld0N1c3RvbWVyQ29kZTogKCkgPT4gc3RyaW5nO1xuICB2YWxpZGF0ZUNvZGU6IChjb2RlOiBzdHJpbmcsIGV4Y2x1ZGVJZD86IHN0cmluZykgPT4geyBpc1ZhbGlkOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9O1xuICBmaW5kQnlDb2RlOiAoY29kZTogc3RyaW5nKSA9PiBDbGllbnQgfCBudWxsO1xuICBnZXRTdGF0czogKCkgPT4gYW55O1xuICBcbiAgLy8gVXRpbGl0eVxuICBjbGVhckVycm9yOiAoKSA9PiB2b2lkO1xuICByZWZyZXNoRGF0YTogKCkgPT4gdm9pZDtcbn1cblxuLyoqXG4gKiDYqtit2YXZitmEINin2YTYudmF2YTYp9ihINmF2YYg2KfZhNmG2LjYp9mFINin2YTZhdmI2K3Yr1xuICovXG5jb25zdCBsb2FkQ3VzdG9tZXJzRnJvbVN0b3JhZ2UgPSBhc3luYyAoKTogUHJvbWlzZTxDbGllbnRbXT4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFVuaWZpZWRTdG9yYWdlTWFuYWdlci5nZXRJdGVtKFNUT1JBR0VfS0VZUy5DVVNUT01FUlMsIFtdKTtcbiAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUg2KrZhSDYqtit2YXZitmEINin2YTYudmF2YTYp9ihINmF2YYg2KfZhNmG2LjYp9mFINin2YTZhdmI2K3YrzonLCByZXN1bHQuZGF0YS5sZW5ndGgpO1xuICAgICAgcmV0dXJuIHJlc3VsdC5kYXRhO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg2YHYtNmEINmB2Yog2KrYrdmF2YrZhCDYp9mE2LnZhdmE2KfYoTonLCByZXN1bHQubWVzc2FnZSk7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDYrti32KMg2K3YsdisINmB2Yog2KrYrdmF2YrZhCDYp9mE2LnZhdmE2KfYoTonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59O1xuXG4vKipcbiAqINit2YHYuCDYp9mE2LnZhdmE2KfYoSDZgdmKINin2YTZhti42KfZhSDYp9mE2YXZiNit2K9cbiAqL1xuY29uc3Qgc2F2ZUN1c3RvbWVyc1RvU3RvcmFnZSA9IGFzeW5jIChjdXN0b21lcnM6IENsaWVudFtdKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVW5pZmllZFN0b3JhZ2VNYW5hZ2VyLnNldEl0ZW0oU1RPUkFHRV9LRVlTLkNVU1RPTUVSUywgY3VzdG9tZXJzKTtcbiAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUg2KrZhSDYrdmB2Lgg2KfZhNi52YXZhNin2KEg2YHZiiDYp9mE2YbYuNin2YUg2KfZhNmF2YjYrdivOicsIGN1c3RvbWVycy5sZW5ndGgpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDZgdi02YQg2YHZiiDYrdmB2Lgg2KfZhNi52YXZhNin2KE6JywgcmVzdWx0Lm1lc3NhZ2UpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwg2K7Yt9ijINit2LHYrCDZgdmKINit2YHYuCDYp9mE2LnZhdmE2KfYoTonLCBlcnJvcik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG4vKipcbiAqINil2YbYtNin2KEg2YXYudix2YEg2YHYsdmK2K8g2YTZhNi52YXZitmEXG4gKi9cbmNvbnN0IGdlbmVyYXRlQ3VzdG9tZXJJZCA9ICgpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gYGN1c3RvbWVyLSR7RGF0ZS5ub3coKX0tJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VDdXN0b21lclN0b3JlID0gY3JlYXRlPEN1c3RvbWVyU3RvcmU+KChzZXQsIGdldCkgPT4gKHtcbiAgY3VzdG9tZXJzOiBbXSwgLy8g2LPZitiq2YUg2KrYrdmF2YrZhNmH2Kcg2YTYp9it2YLYp9mLXG4gIGN1cnJlbnRDdXN0b21lcjogbnVsbCxcbiAgc2VhcmNoUXVlcnk6ICcnLFxuICBzdWdnZXN0aW9uczogW10sXG4gIGlzTG9hZGluZzogZmFsc2UsXG4gIGVycm9yOiBudWxsLFxuXG4gIGxvYWRDdXN0b21lcnM6IGFzeW5jICgpID0+IHtcbiAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBjdXN0b21lcnMgPSBhd2FpdCBsb2FkQ3VzdG9tZXJzRnJvbVN0b3JhZ2UoKTtcbiAgICAgIHNldCh7IGN1c3RvbWVycywgaXNMb2FkaW5nOiBmYWxzZSB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0KHtcbiAgICAgICAgZXJyb3I6ICfZgdi02YQg2YHZiiDYqtit2YXZitmEINio2YrYp9mG2KfYqiDYp9mE2LnZhdmE2KfYoScsXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2VcbiAgICAgIH0pO1xuICAgIH1cbiAgfSxcblxuICBhZGRDdXN0b21lcjogYXN5bmMgKGN1c3RvbWVyRGF0YSkgPT4ge1xuICAgIHNldCh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgY3VzdG9tZXJzIH0gPSBnZXQoKTtcbiAgICAgIFxuICAgICAgLy8g2KXZhti02KfYoSDZg9mI2K8g2KfZhNi52YXZitmEXG4gICAgICBjb25zdCBjb2RlRGF0YSA9IGdlbmVyYXRlQ3VzdG9tZXJDb2RlKCk7XG4gICAgICBcbiAgICAgIC8vINil2YbYtNin2KEg2KfZhNi52YXZitmEINin2YTYrNiv2YrYr1xuICAgICAgY29uc3QgbmV3Q3VzdG9tZXI6IENsaWVudCA9IHtcbiAgICAgICAgLi4uY3VzdG9tZXJEYXRhLFxuICAgICAgICBpZDogZ2VuZXJhdGVDdXN0b21lcklkKCksXG4gICAgICAgIGNsaWVudENvZGU6IGNvZGVEYXRhLmNvZGUsXG4gICAgICB9O1xuICAgICAgXG4gICAgICAvLyDYpdi22KfZgdipINin2YTYudmF2YrZhCDZhNmE2YLYp9im2YXYqVxuICAgICAgY29uc3QgdXBkYXRlZEN1c3RvbWVycyA9IFsuLi5jdXN0b21lcnMsIG5ld0N1c3RvbWVyXTtcbiAgICAgIFxuICAgICAgLy8g2K3Zgdi4INmB2Yog2KfZhNiq2K7YstmK2YZcbiAgICAgIGNvbnN0IHNhdmVkID0gYXdhaXQgc2F2ZUN1c3RvbWVyc1RvU3RvcmFnZSh1cGRhdGVkQ3VzdG9tZXJzKTtcbiAgICAgIGlmICghc2F2ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfZgdi02YQg2YHZiiDYrdmB2Lgg2KjZitin2YbYp9iqINin2YTYudmF2YrZhCcpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBzZXQoeyBcbiAgICAgICAgY3VzdG9tZXJzOiB1cGRhdGVkQ3VzdG9tZXJzLCBcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBjdXN0b21lcjogbmV3Q3VzdG9tZXIgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K3Yr9irINiu2LfYoyDYutmK2LEg2YXYqtmI2YLYuSc7XG4gICAgICBzZXQoeyBcbiAgICAgICAgZXJyb3I6IGVycm9yTWVzc2FnZSwgXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2UgXG4gICAgICB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9LFxuXG4gIC8vINil2LbYp9mB2Kkg2LnZhdmE2KfYoSDZhdiq2LnYr9iv2YrZhiAo2YTZhNin2LPYqtmK2LHYp9ivKVxuICBhZGRNdWx0aXBsZUN1c3RvbWVyczogYXN5bmMgKGN1c3RvbWVyc0RhdGE6IE9taXQ8Q2xpZW50LCAnaWQnPltdKSA9PiB7XG4gICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGN1c3RvbWVycyB9ID0gZ2V0KCk7XG4gICAgICBjb25zdCBuZXdDdXN0b21lcnM6IENsaWVudFtdID0gW107XG4gICAgICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW107XG5cbiAgICAgIGZvciAoY29uc3QgY3VzdG9tZXJEYXRhIG9mIGN1c3RvbWVyc0RhdGEpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBsZXQgY2xpZW50Q29kZSA9IGN1c3RvbWVyRGF0YS5jbGllbnRDb2RlO1xuXG4gICAgICAgICAgLy8g2KXYsNinINmE2YUg2YrYqtmFINiq2YXYsdmK2LEg2YPZiNiv2Iwg2YLZhSDYqNil2YbYtNin2KEg2YjYp9it2K8g2KzYr9mK2K9cbiAgICAgICAgICBpZiAoIWNsaWVudENvZGUpIHtcbiAgICAgICAgICAgIGNvbnN0IGNvZGVEYXRhID0gZ2VuZXJhdGVDdXN0b21lckNvZGUoKTtcbiAgICAgICAgICAgIGNsaWVudENvZGUgPSBjb2RlRGF0YS5jb2RlO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINi52K/ZhSDYqtmD2LHYp9ixINin2YTZg9mI2K9cbiAgICAgICAgICBjb25zdCBleGlzdGluZ0N1c3RvbWVyID0gWy4uLmN1c3RvbWVycywgLi4ubmV3Q3VzdG9tZXJzXS5maW5kKGMgPT4gYy5jbGllbnRDb2RlID09PSBjbGllbnRDb2RlKTtcbiAgICAgICAgICBpZiAoZXhpc3RpbmdDdXN0b21lcikge1xuICAgICAgICAgICAgZXJyb3JzLnB1c2goYNmD2YjYryDYp9mE2LnZhdmK2YQgJHtjbGllbnRDb2RlfSDZhdiz2KrYrtiv2YUg2KjYp9mE2YHYudmEINmE2YTYudmF2YrZhCAke2N1c3RvbWVyRGF0YS5uYW1lfWApO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3QgbmV3Q3VzdG9tZXI6IENsaWVudCA9IHtcbiAgICAgICAgICAgIC4uLmN1c3RvbWVyRGF0YSxcbiAgICAgICAgICAgIGlkOiBnZW5lcmF0ZUN1c3RvbWVySWQoKSxcbiAgICAgICAgICAgIGNsaWVudENvZGUsXG4gICAgICAgICAgfTtcblxuICAgICAgICAgIG5ld0N1c3RvbWVycy5wdXNoKG5ld0N1c3RvbWVyKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBlcnJvcnMucHVzaChg2K7Yt9ijINmB2Yog2KXYttin2YHYqSDYp9mE2LnZhdmK2YQgJHtjdXN0b21lckRhdGEubmFtZX06ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K7Yt9ijINi62YrYsSDZhdi52LHZiNmBJ31gKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAobmV3Q3VzdG9tZXJzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEN1c3RvbWVycyA9IFsuLi5jdXN0b21lcnMsIC4uLm5ld0N1c3RvbWVyc107XG5cbiAgICAgICAgLy8g2K3Zgdi4INmB2Yog2KfZhNiq2K7YstmK2YZcbiAgICAgICAgY29uc3Qgc2F2ZWQgPSBzYXZlQ3VzdG9tZXJzVG9TdG9yYWdlKHVwZGF0ZWRDdXN0b21lcnMpO1xuICAgICAgICBpZiAoIXNhdmVkKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfZgdi02YQg2YHZiiDYrdmB2Lgg2KjZitin2YbYp9iqINin2YTYudmF2YTYp9ihJyk7XG4gICAgICAgIH1cblxuICAgICAgICBzZXQoe1xuICAgICAgICAgIGN1c3RvbWVyczogdXBkYXRlZEN1c3RvbWVycyxcbiAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlXG4gICAgICAgIH0pO1xuXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBpbXBvcnRlZDogbmV3Q3VzdG9tZXJzLmxlbmd0aCxcbiAgICAgICAgICBlcnJvcnMsXG4gICAgICAgICAgY3VzdG9tZXJzOiBuZXdDdXN0b21lcnNcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ9mE2YUg2YrYqtmFINil2LbYp9mB2Kkg2KPZiiDYudmF2YrZhCcsIGltcG9ydGVkOiAwLCBlcnJvcnMgfTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K7Yt9ijINi62YrYsSDZhdi52LHZiNmBJztcbiAgICAgIHNldCh7XG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UsXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2VcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UsIGltcG9ydGVkOiAwLCBlcnJvcnM6IFtdIH07XG4gICAgfVxuICB9LFxuXG4gIHVwZGF0ZUN1c3RvbWVyOiBhc3luYyAoaWQsIHVwZGF0ZXMpID0+IHtcbiAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGN1c3RvbWVycyB9ID0gZ2V0KCk7XG4gICAgICBcbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINmI2KzZiNivINin2YTYudmF2YrZhFxuICAgICAgY29uc3QgY3VzdG9tZXJJbmRleCA9IGN1c3RvbWVycy5maW5kSW5kZXgoYyA9PiBjLmlkID09PSBpZCk7XG4gICAgICBpZiAoY3VzdG9tZXJJbmRleCA9PT0gLTEpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfYp9mE2LnZhdmK2YQg2LrZitixINmF2YjYrNmI2K8nKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YPZiNivINin2YTYudmF2YrZhCDYpdiw2Kcg2KrZhSDYqtit2K/Zitir2YdcbiAgICAgIGlmICh1cGRhdGVzLmNsaWVudENvZGUpIHtcbiAgICAgICAgY29uc3QgdmFsaWRhdGlvbiA9IHZhbGlkYXRlQ3VzdG9tZXJDb2RlKHVwZGF0ZXMuY2xpZW50Q29kZSk7XG4gICAgICAgIGlmICghdmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHZhbGlkYXRpb24uZXJyb3IgfHwgJ9mD2YjYryDYp9mE2LnZhdmK2YQg2LrZitixINi12K3ZititJyk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGlmICghaXNDdXN0b21lckNvZGVVbmlxdWUodXBkYXRlcy5jbGllbnRDb2RlLCBpZCkpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ9mD2YjYryDYp9mE2LnZhdmK2YQg2YXYs9iq2K7Yr9mFINio2KfZhNmB2LnZhCcpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vINiq2K3Yr9mK2Ksg2KfZhNi52YXZitmEXG4gICAgICBjb25zdCB1cGRhdGVkQ3VzdG9tZXJzID0gWy4uLmN1c3RvbWVyc107XG4gICAgICB1cGRhdGVkQ3VzdG9tZXJzW2N1c3RvbWVySW5kZXhdID0ge1xuICAgICAgICAuLi51cGRhdGVkQ3VzdG9tZXJzW2N1c3RvbWVySW5kZXhdLFxuICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgfTtcbiAgICAgIFxuICAgICAgLy8g2K3Zgdi4INmB2Yog2KfZhNiq2K7YstmK2YZcbiAgICAgIGNvbnN0IHNhdmVkID0gc2F2ZUN1c3RvbWVyc1RvU3RvcmFnZSh1cGRhdGVkQ3VzdG9tZXJzKTtcbiAgICAgIGlmICghc2F2ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfZgdi02YQg2YHZiiDYrdmB2Lgg2KfZhNiq2K3Yr9mK2KvYp9iqJyk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHNldCh7IFxuICAgICAgICBjdXN0b21lcnM6IHVwZGF0ZWRDdXN0b21lcnMsIFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlIFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn2K3Yr9irINiu2LfYoyDZgdmKINin2YTYqtit2K/ZitirJztcbiAgICAgIHNldCh7IFxuICAgICAgICBlcnJvcjogZXJyb3JNZXNzYWdlLCBcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcbiAgICB9XG4gIH0sXG5cbiAgZGVsZXRlQ3VzdG9tZXI6IGFzeW5jIChpZCkgPT4ge1xuICAgIHNldCh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgY3VzdG9tZXJzIH0gPSBnZXQoKTtcbiAgICAgIFxuICAgICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YjYrNmI2K8g2KfZhNi52YXZitmEXG4gICAgICBjb25zdCBjdXN0b21lckV4aXN0cyA9IGN1c3RvbWVycy5zb21lKGMgPT4gYy5pZCA9PT0gaWQpO1xuICAgICAgaWYgKCFjdXN0b21lckV4aXN0cykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ9in2YTYudmF2YrZhCDYutmK2LEg2YXZiNis2YjYrycpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyDYrdiw2YEg2KfZhNi52YXZitmEXG4gICAgICBjb25zdCB1cGRhdGVkQ3VzdG9tZXJzID0gY3VzdG9tZXJzLmZpbHRlcihjID0+IGMuaWQgIT09IGlkKTtcbiAgICAgIFxuICAgICAgLy8g2K3Zgdi4INmB2Yog2KfZhNiq2K7YstmK2YZcbiAgICAgIGNvbnN0IHNhdmVkID0gc2F2ZUN1c3RvbWVyc1RvU3RvcmFnZSh1cGRhdGVkQ3VzdG9tZXJzKTtcbiAgICAgIGlmICghc2F2ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfZgdi02YQg2YHZiiDYrdmB2Lgg2KfZhNiq2LrZitmK2LHYp9iqJyk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHNldCh7IFxuICAgICAgICBjdXN0b21lcnM6IHVwZGF0ZWRDdXN0b21lcnMsIFxuICAgICAgICBjdXJyZW50Q3VzdG9tZXI6IGdldCgpLmN1cnJlbnRDdXN0b21lcj8uaWQgPT09IGlkID8gbnVsbCA6IGdldCgpLmN1cnJlbnRDdXN0b21lcixcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ9it2K/YqyDYrti32KMg2YHZiiDYp9mE2K3YsNmBJztcbiAgICAgIHNldCh7IFxuICAgICAgICBlcnJvcjogZXJyb3JNZXNzYWdlLCBcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSBcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcbiAgICB9XG4gIH0sXG5cbiAgc2V0Q3VycmVudEN1c3RvbWVyOiAoY3VzdG9tZXIpID0+IHtcbiAgICBzZXQoeyBjdXJyZW50Q3VzdG9tZXI6IGN1c3RvbWVyIH0pO1xuICB9LFxuXG4gIHNldFNlYXJjaFF1ZXJ5OiAocXVlcnkpID0+IHtcbiAgICBzZXQoeyBzZWFyY2hRdWVyeTogcXVlcnkgfSk7XG4gIH0sXG5cbiAgc2VhcmNoQ3VzdG9tZXJzOiAocXVlcnkpID0+IHtcbiAgICBjb25zdCB7IGN1c3RvbWVycyB9ID0gZ2V0KCk7XG4gICAgaWYgKCFxdWVyeS50cmltKCkpIHtcbiAgICAgIHJldHVybiBjdXN0b21lcnM7XG4gICAgfVxuXG4gICAgY29uc3Qgc2VhcmNoVGVybSA9IHF1ZXJ5LnRvTG93ZXJDYXNlKCkudHJpbSgpO1xuXG4gICAgLy8g2KfZhNio2K3YqyDYp9mE2KPYs9in2LPZiiDYp9mE2YXYrdiz2YZcbiAgICBjb25zdCBiYXNpY1Jlc3VsdHMgPSBjdXN0b21lcnMuZmlsdGVyKGN1c3RvbWVyID0+XG4gICAgICBjdXN0b21lci5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgIGN1c3RvbWVyLnBob25lLmluY2x1ZGVzKHF1ZXJ5KSB8fFxuICAgICAgKGN1c3RvbWVyLmNsaWVudENvZGUgJiYgY3VzdG9tZXIuY2xpZW50Q29kZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKSB8fFxuICAgICAgKGN1c3RvbWVyLmJ1c2luZXNzTmFtZSAmJiBjdXN0b21lci5idXNpbmVzc05hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtKSkgfHxcbiAgICAgIChjdXN0b21lci5wcm92aW5jZSAmJiBjdXN0b21lci5wcm92aW5jZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKSB8fFxuICAgICAgKGN1c3RvbWVyLnNhbGVzcGVyc29uICYmIGN1c3RvbWVyLnNhbGVzcGVyc29uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkpIHx8XG4gICAgICBjdXN0b21lci5zZXJ2aWNlcy5zb21lKHNlcnZpY2UgPT4gc2VydmljZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKVxuICAgICk7XG5cbiAgICAvLyDYp9mE2KjYrdirINin2YTYttio2KfYqNmKINmE2YTYo9iz2YXYp9ihINin2YTZhdi02KfYqNmH2Kkg2KXYsNinINmE2YUg2YbYrNivINmG2KrYp9im2KxcbiAgICBpZiAoYmFzaWNSZXN1bHRzLmxlbmd0aCA9PT0gMCAmJiBzZWFyY2hUZXJtLmxlbmd0aCA+IDIpIHtcbiAgICAgIGNvbnN0IGZ1enp5UmVzdWx0cyA9IGN1c3RvbWVycy5maWx0ZXIoY3VzdG9tZXIgPT4ge1xuICAgICAgICBjb25zdCBuYW1lID0gY3VzdG9tZXIubmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBjb25zdCB3b3JkcyA9IHNlYXJjaFRlcm0uc3BsaXQoJyAnKTtcbiAgICAgICAgcmV0dXJuIHdvcmRzLnNvbWUod29yZCA9PiBuYW1lLmluY2x1ZGVzKHdvcmQpICYmIHdvcmQubGVuZ3RoID4gMSk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBmdXp6eVJlc3VsdHM7XG4gICAgfVxuXG4gICAgLy8g2KrYsdiq2YrYqCDYp9mE2YbYqtin2KbYrCDYrdiz2Kgg2KfZhNij2YjZhNmI2YrYqVxuICAgIHJldHVybiBiYXNpY1Jlc3VsdHMuc29ydCgoYSwgYikgPT4ge1xuICAgICAgLy8g2KPZiNmE2YjZitipINmE2YTZhdi32KfYqNmC2Kkg2KfZhNiv2YLZitmC2Kkg2YHZiiDYqNiv2KfZitipINin2YTYp9iz2YVcbiAgICAgIGNvbnN0IGFOYW1lTWF0Y2ggPSBhLm5hbWUudG9Mb3dlckNhc2UoKS5zdGFydHNXaXRoKHNlYXJjaFRlcm0pO1xuICAgICAgY29uc3QgYk5hbWVNYXRjaCA9IGIubmFtZS50b0xvd2VyQ2FzZSgpLnN0YXJ0c1dpdGgoc2VhcmNoVGVybSk7XG4gICAgICBpZiAoYU5hbWVNYXRjaCAmJiAhYk5hbWVNYXRjaCkgcmV0dXJuIC0xO1xuICAgICAgaWYgKCFhTmFtZU1hdGNoICYmIGJOYW1lTWF0Y2gpIHJldHVybiAxO1xuXG4gICAgICAvLyDYo9mI2YTZiNmK2Kkg2YTZhNmF2LfYp9io2YLYqSDZgdmKINmD2YjYryDYp9mE2LnZhdmK2YRcbiAgICAgIGNvbnN0IGFDb2RlTWF0Y2ggPSBhLmNsaWVudENvZGU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSk7XG4gICAgICBjb25zdCBiQ29kZU1hdGNoID0gYi5jbGllbnRDb2RlPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pO1xuICAgICAgaWYgKGFDb2RlTWF0Y2ggJiYgIWJDb2RlTWF0Y2gpIHJldHVybiAtMTtcbiAgICAgIGlmICghYUNvZGVNYXRjaCAmJiBiQ29kZU1hdGNoKSByZXR1cm4gMTtcblxuICAgICAgLy8g2KrYsdiq2YrYqCDYo9io2KzYr9mKINmD2KfZgdiq2LHYp9i22YpcbiAgICAgIHJldHVybiBhLm5hbWUubG9jYWxlQ29tcGFyZShiLm5hbWUsICdhcicpO1xuICAgIH0pO1xuICB9LFxuXG4gIC8vINin2YTYp9mC2KrYsdin2K3Yp9iqINin2YTYs9ix2YrYudipINmE2YTYqNit2KtcbiAgZ2V0UXVpY2tTdWdnZXN0aW9uczogKHF1ZXJ5LCBsaW1pdCA9IDUpID0+IHtcbiAgICBjb25zdCB7IGN1c3RvbWVycyB9ID0gZ2V0KCk7XG4gICAgaWYgKCFxdWVyeS50cmltKCkpIHJldHVybiBbXTtcblxuICAgIGNvbnN0IHNlYXJjaFRlcm0gPSBxdWVyeS50b0xvd2VyQ2FzZSgpLnRyaW0oKTtcbiAgICBjb25zdCBzdWdnZXN0aW9ucyA9IGN1c3RvbWVyc1xuICAgICAgLmZpbHRlcihjdXN0b21lciA9PlxuICAgICAgICBjdXN0b21lci5uYW1lLnRvTG93ZXJDYXNlKCkuc3RhcnRzV2l0aChzZWFyY2hUZXJtKSB8fFxuICAgICAgICBjdXN0b21lci5waG9uZS5zdGFydHNXaXRoKHF1ZXJ5KSB8fFxuICAgICAgICAoY3VzdG9tZXIuY2xpZW50Q29kZSAmJiBjdXN0b21lci5jbGllbnRDb2RlLnRvTG93ZXJDYXNlKCkuc3RhcnRzV2l0aChzZWFyY2hUZXJtKSlcbiAgICAgIClcbiAgICAgIC5zbGljZSgwLCBsaW1pdClcbiAgICAgIC5tYXAoY3VzdG9tZXIgPT4gKHtcbiAgICAgICAgaWQ6IGN1c3RvbWVyLmlkLFxuICAgICAgICBuYW1lOiBjdXN0b21lci5uYW1lLFxuICAgICAgICBwaG9uZTogY3VzdG9tZXIucGhvbmUsXG4gICAgICAgIGNsaWVudENvZGU6IGN1c3RvbWVyLmNsaWVudENvZGUsXG4gICAgICAgIGJ1c2luZXNzTmFtZTogY3VzdG9tZXIuYnVzaW5lc3NOYW1lLFxuICAgICAgICB0eXBlOiBjdXN0b21lci5jbGllbnRDb2RlPy50b0xvd2VyQ2FzZSgpLnN0YXJ0c1dpdGgoc2VhcmNoVGVybSkgPyAnY29kZScgOlxuICAgICAgICAgICAgICBjdXN0b21lci5waG9uZS5zdGFydHNXaXRoKHF1ZXJ5KSA/ICdwaG9uZScgOiAnbmFtZScsXG4gICAgICAgIG1hdGNoVGV4dDogY3VzdG9tZXIuY2xpZW50Q29kZT8udG9Mb3dlckNhc2UoKS5zdGFydHNXaXRoKHNlYXJjaFRlcm0pID8gY3VzdG9tZXIuY2xpZW50Q29kZSA6XG4gICAgICAgICAgICAgICAgICBjdXN0b21lci5waG9uZS5zdGFydHNXaXRoKHF1ZXJ5KSA/IGN1c3RvbWVyLnBob25lIDogY3VzdG9tZXIubmFtZVxuICAgICAgfSkpO1xuXG4gICAgcmV0dXJuIHN1Z2dlc3Rpb25zO1xuICB9LFxuXG4gIGdldFN1Z2dlc3Rpb25zOiAocXVlcnkpID0+IHtcbiAgICBpZiAoIXF1ZXJ5LnRyaW0oKSkge1xuICAgICAgc2V0KHsgc3VnZ2VzdGlvbnM6IFtdIH0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBzdWdnZXN0aW9ucyA9IGdldEN1c3RvbWVyQ29kZVN1Z2dlc3Rpb25zKHF1ZXJ5LCAxMCk7XG4gICAgc2V0KHsgc3VnZ2VzdGlvbnMgfSk7XG4gIH0sXG5cbiAgY2xlYXJTdWdnZXN0aW9uczogKCkgPT4ge1xuICAgIHNldCh7IHN1Z2dlc3Rpb25zOiBbXSB9KTtcbiAgfSxcblxuICBnZW5lcmF0ZU5ld0N1c3RvbWVyQ29kZTogKCkgPT4ge1xuICAgIGNvbnN0IGNvZGVEYXRhID0gZ2VuZXJhdGVDdXN0b21lckNvZGUoKTtcbiAgICByZXR1cm4gY29kZURhdGEuY29kZTtcbiAgfSxcblxuICB2YWxpZGF0ZUNvZGU6IChjb2RlLCBleGNsdWRlSWQpID0+IHtcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVDdXN0b21lckNvZGUoY29kZSk7XG4gICAgaWYgKCF2YWxpZGF0aW9uLmlzVmFsaWQpIHtcbiAgICAgIHJldHVybiB2YWxpZGF0aW9uO1xuICAgIH1cbiAgICBcbiAgICBpZiAoIWlzQ3VzdG9tZXJDb2RlVW5pcXVlKGNvZGUsIGV4Y2x1ZGVJZCkpIHtcbiAgICAgIHJldHVybiB7IGlzVmFsaWQ6IGZhbHNlLCBlcnJvcjogJ9mD2YjYryDYp9mE2LnZhdmK2YQg2YXYs9iq2K7Yr9mFINio2KfZhNmB2LnZhCcgfTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHsgaXNWYWxpZDogdHJ1ZSB9O1xuICB9LFxuXG4gIGZpbmRCeUNvZGU6IChjb2RlKSA9PiB7XG4gICAgcmV0dXJuIGZpbmRDdXN0b21lckJ5Q29kZShjb2RlKTtcbiAgfSxcblxuICBnZXRTdGF0czogKCkgPT4ge1xuICAgIHJldHVybiBnZXRDdXN0b21lckNvZGVTdGF0cygpO1xuICB9LFxuXG4gIGNsZWFyRXJyb3I6ICgpID0+IHtcbiAgICBzZXQoeyBlcnJvcjogbnVsbCB9KTtcbiAgfSxcblxuICByZWZyZXNoRGF0YTogKCkgPT4ge1xuICAgIGdldCgpLmxvYWRDdXN0b21lcnMoKTtcbiAgfSxcbn0pKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJVbmlmaWVkU3RvcmFnZU1hbmFnZXIiLCJTVE9SQUdFX0tFWVMiLCJnZW5lcmF0ZUN1c3RvbWVyQ29kZSIsInZhbGlkYXRlQ3VzdG9tZXJDb2RlIiwiaXNDdXN0b21lckNvZGVVbmlxdWUiLCJnZXRDdXN0b21lckNvZGVTdGF0cyIsImZpbmRDdXN0b21lckJ5Q29kZSIsImdldEN1c3RvbWVyQ29kZVN1Z2dlc3Rpb25zIiwibG9hZEN1c3RvbWVyc0Zyb21TdG9yYWdlIiwicmVzdWx0IiwiZ2V0SXRlbSIsIkNVU1RPTUVSUyIsInN1Y2Nlc3MiLCJjb25zb2xlIiwibG9nIiwiZGF0YSIsImxlbmd0aCIsImVycm9yIiwibWVzc2FnZSIsInNhdmVDdXN0b21lcnNUb1N0b3JhZ2UiLCJjdXN0b21lcnMiLCJzZXRJdGVtIiwiZ2VuZXJhdGVDdXN0b21lcklkIiwiTWF0aCIsIkRhdGUiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInVzZUN1c3RvbWVyU3RvcmUiLCJzZXQiLCJnZXQiLCJjdXJyZW50Q3VzdG9tZXIiLCJzZWFyY2hRdWVyeSIsInN1Z2dlc3Rpb25zIiwiaXNMb2FkaW5nIiwibG9hZEN1c3RvbWVycyIsImFkZEN1c3RvbWVyIiwiY3VzdG9tZXJEYXRhIiwiY29kZURhdGEiLCJuZXdDdXN0b21lciIsImlkIiwiY2xpZW50Q29kZSIsImNvZGUiLCJ1cGRhdGVkQ3VzdG9tZXJzIiwic2F2ZWQiLCJFcnJvciIsImN1c3RvbWVyIiwiZXJyb3JNZXNzYWdlIiwiYWRkTXVsdGlwbGVDdXN0b21lcnMiLCJjdXN0b21lcnNEYXRhIiwibmV3Q3VzdG9tZXJzIiwiZXJyb3JzIiwiZXhpc3RpbmdDdXN0b21lciIsImZpbmQiLCJjIiwicHVzaCIsIm5hbWUiLCJpbXBvcnRlZCIsInVwZGF0ZUN1c3RvbWVyIiwidXBkYXRlcyIsImN1c3RvbWVySW5kZXgiLCJmaW5kSW5kZXgiLCJ2YWxpZGF0aW9uIiwiaXNWYWxpZCIsImRlbGV0ZUN1c3RvbWVyIiwiY3VzdG9tZXJFeGlzdHMiLCJzb21lIiwiZmlsdGVyIiwic2V0Q3VycmVudEN1c3RvbWVyIiwic2V0U2VhcmNoUXVlcnkiLCJxdWVyeSIsInNlYXJjaEN1c3RvbWVycyIsInRyaW0iLCJzZWFyY2hUZXJtIiwidG9Mb3dlckNhc2UiLCJiYXNpY1Jlc3VsdHMiLCJpbmNsdWRlcyIsInBob25lIiwiYnVzaW5lc3NOYW1lIiwicHJvdmluY2UiLCJzYWxlc3BlcnNvbiIsInNlcnZpY2VzIiwic2VydmljZSIsImZ1enp5UmVzdWx0cyIsIndvcmRzIiwic3BsaXQiLCJ3b3JkIiwic29ydCIsImEiLCJiIiwiYU5hbWVNYXRjaCIsInN0YXJ0c1dpdGgiLCJiTmFtZU1hdGNoIiwiYUNvZGVNYXRjaCIsImJDb2RlTWF0Y2giLCJsb2NhbGVDb21wYXJlIiwiZ2V0UXVpY2tTdWdnZXN0aW9ucyIsImxpbWl0Iiwic2xpY2UiLCJtYXAiLCJ0eXBlIiwibWF0Y2hUZXh0IiwiZ2V0U3VnZ2VzdGlvbnMiLCJjbGVhclN1Z2dlc3Rpb25zIiwiZ2VuZXJhdGVOZXdDdXN0b21lckNvZGUiLCJ2YWxpZGF0ZUNvZGUiLCJleGNsdWRlSWQiLCJmaW5kQnlDb2RlIiwiZ2V0U3RhdHMiLCJjbGVhckVycm9yIiwicmVmcmVzaERhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/customerStore.ts\n"));

/***/ })

});