// أنواع البيانات الأساسية للتطبيق

export interface Client {
  id?: string;
  name: string;
  phone: string;
  services: string[];
  contactMethod: 'whatsapp' | 'meta' | 'meeting' | 'phone';
  salesperson: string;
  initialCategory: 'A' | 'B' | 'C';
  businessName?: string;
  province?: string;
  clientCode?: string;
}

export interface ServiceBase {
  id: string;
  serviceType: ServiceType;
  enabled: boolean;
}

export type ServiceType = 
  | 'consultation'
  | 'pattern'
  | 'pattern_printing'
  | 'manufacturing'
  | 'samples'
  | 'shipping'
  | 'marketing'
  | 'photography'
  | 'products'
  | 'raw_purchases';

export interface ConsultationService extends ServiceBase {
  serviceType: 'consultation';
  hours: number;
  topic: string;
  dateTime: string;
  cost: number;
  paymentMethod: string;
}

export interface PatternService extends ServiceBase {
  serviceType: 'pattern';
  models: PatternModel[];
}

export interface PatternModel {
  modelName: string;
  sizesCount: number;
  cost: number;
  deposit: number;
  finalAmount: number;
  printOption: boolean;
  fileType: 'print' | 'plt';
  paymentMethod: string;
  salesperson: string;
  shippingInfo?: string;
}

export interface PatternPrintingService extends ServiceBase {
  serviceType: 'pattern_printing';
  files: PrintingFile[];
}

export interface PrintingFile {
  meters: number;
  fileCode: string;
  cost: number;
  salesperson: string;
}

export interface ManufacturingService extends ServiceBase {
  serviceType: 'manufacturing';
  models: ManufacturingModel[];
}

export interface ManufacturingModel {
  patternSize: string;
  quantity: number;
  patternPrice: number;
  samplePrice: number;
  manufacturingPrice: number;
  fileType?: 'print' | 'plt';
}

export interface ShippingService extends ServiceBase {
  serviceType: 'shipping';
  clientName: string;
  phone: string;
  contactMethod: string;
  salesperson: string;
  item: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface MarketingService extends ServiceBase {
  serviceType: 'marketing';
  description: string;
  cost: number;
  duration: string;
  marketingTypes?: string[];
  targetAudience?: string[];
  startDate?: string;
  endDate?: string;
  goals?: {
    brand_awareness?: boolean;
    lead_generation?: boolean;
    sales_increase?: boolean;
    website_traffic?: boolean;
    social_engagement?: boolean;
    customer_retention?: boolean;
  };
  content?: {
    graphics_design?: boolean;
    video_production?: boolean;
    copywriting?: boolean;
    photography?: boolean;
    animation?: boolean;
  };
  kpis?: string;
  specialNotes?: string;
  marketingManager?: string;
  contactNumber?: string;
}

export interface PhotographyService extends ServiceBase {
  serviceType: 'photography';
  location: 'indoor' | 'outdoor';
  withModel: boolean;
  withEditing: boolean;
  cost: number;
  notes?: string;
  sessionDuration?: string;
  numberOfPhotos?: number;
  deliveryDate?: string;
  photographyType?: string;
}

export interface ProductsService extends ServiceBase {
  serviceType: 'products';
  items: ProductItem[];
}

export interface ProductItem {
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface SamplesService extends ServiceBase {
  serviceType: 'samples';
  samples: SampleItem[];
  grandTotal?: number;
}

export interface SampleItem {
  sampleName: string;
  quantity: number;
  cost: number;
  fabricType?: string;
  color?: string;
  size?: string;
  deliveryDate?: string;
  notes?: string;
  sampleType?: string;
  urgencyLevel?: string;
}

export interface RawPurchasesService extends ServiceBase {
  serviceType: 'raw_purchases';
  materials: RawMaterial[];
}

export interface RawMaterial {
  materialName: string;
  quantity: number;
  unitPrice: number;
  supplier: string;
  total: number;
}

export type Service =
  | ConsultationService
  | PatternService
  | PatternPrintingService
  | ManufacturingService
  | SamplesService
  | ShippingService
  | MarketingService
  | PhotographyService
  | ProductsService
  | RawPurchasesService;

export interface Invoice {
  id?: string;
  client: Client;
  services: Service[];
  total: number;
  paidAmount: number;
  remainingAmount: number;
  paymentStatus: 'غير مدفوع' | 'مدفوع جزئياً' | 'مدفوع بالكامل';
  paymentMethod: string;
  requiresShipping: boolean;
  shippingAddress?: string;
  createdAt: string;
  notes?: string;
}

export interface CompanyInfo {
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  taxNumber: string;
  commercialRegister: string;
}
