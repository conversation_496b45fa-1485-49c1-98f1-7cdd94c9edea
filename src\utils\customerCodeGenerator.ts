/**
 * نظام إنشاء وإدارة أكواد العملاء
 * التنسيق: p013621 (p01 = رقم المعاملة، 3621 = الرقم المسلسل للعميل)
 */

import { secureGetItem, secureSetItem } from './secureStorage';
import { Client } from '@/types';

// إعدادات نظام الأكواد
const CODE_CONFIG = {
  PREFIX: 'p', // بادئة الكود
  TRANSACTION_DIGITS: 2, // عدد أرقام المعاملة (01, 02, 03...)
  CUSTOMER_DIGITS: 4, // عدد أرقام العميل (3621, 3622...)
  STARTING_TRANSACTION: 1, // رقم المعاملة الأول
  STARTING_CUSTOMER: 3621, // رقم العميل الأول
};

// واجهة بيانات العداد
interface CustomerCounters {
  lastTransactionNumber: number;
  lastCustomerNumber: number;
  totalCustomers: number;
}

// واجهة بيانات كود العميل
export interface CustomerCodeData {
  code: string;
  transactionNumber: number;
  customerNumber: number;
  generatedAt: string;
  isCustom: boolean;
}

/**
 * تحميل العدادات من التخزين الآمن
 */
const loadCounters = (): CustomerCounters => {
  const result = secureGetItem<CustomerCounters>('customerCounters');
  if (result.success && result.data) {
    return result.data;
  }
  
  // القيم الافتراضية
  return {
    lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,
    lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,
    totalCustomers: 0,
  };
};

/**
 * حفظ العدادات في التخزين الآمن
 */
const saveCounters = (counters: CustomerCounters): void => {
  const result = secureSetItem('customerCounters', counters);
  if (!result.success) {
    console.error('فشل في حفظ عدادات العملاء:', result.error);
    throw new Error('فشل في حفظ عدادات العملاء');
  }
};

/**
 * تنسيق رقم بعدد أرقام محدد
 */
const formatNumber = (num: number, digits: number): string => {
  return num.toString().padStart(digits, '0');
};

/**
 * إنشاء كود عميل جديد
 */
export const generateCustomerCode = (): CustomerCodeData => {
  const counters = loadCounters();
  
  // زيادة العدادات
  counters.lastCustomerNumber += 1;
  counters.totalCustomers += 1;
  
  // إنشاء الكود
  const transactionPart = formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS);
  const customerPart = formatNumber(counters.lastCustomerNumber, CODE_CONFIG.CUSTOMER_DIGITS);
  const code = `${CODE_CONFIG.PREFIX}${transactionPart}${customerPart}`;
  
  // حفظ العدادات المحدثة
  saveCounters(counters);
  
  return {
    code,
    transactionNumber: counters.lastTransactionNumber,
    customerNumber: counters.lastCustomerNumber,
    generatedAt: new Date().toISOString(),
    isCustom: false,
  };
};

/**
 * التحقق من صحة كود العميل
 */
export const validateCustomerCode = (code: string): { isValid: boolean; error?: string } => {
  if (!code || typeof code !== 'string') {
    return { isValid: false, error: 'الكود مطلوب' };
  }
  
  // التحقق من البادئة
  if (!code.startsWith(CODE_CONFIG.PREFIX)) {
    return { isValid: false, error: `يجب أن يبدأ الكود بـ "${CODE_CONFIG.PREFIX}"` };
  }
  
  // التحقق من الطول
  const expectedLength = 1 + CODE_CONFIG.TRANSACTION_DIGITS + CODE_CONFIG.CUSTOMER_DIGITS;
  if (code.length !== expectedLength) {
    return { isValid: false, error: `يجب أن يكون طول الكود ${expectedLength} أحرف` };
  }
  
  // التحقق من الأرقام
  const numberPart = code.slice(1);
  if (!/^\d+$/.test(numberPart)) {
    return { isValid: false, error: 'يجب أن يحتوي الكود على أرقام فقط بعد البادئة' };
  }
  
  return { isValid: true };
};

/**
 * تحليل كود العميل
 */
export const parseCustomerCode = (code: string): CustomerCodeData | null => {
  const validation = validateCustomerCode(code);
  if (!validation.isValid) {
    return null;
  }
  
  const numberPart = code.slice(1);
  const transactionNumber = parseInt(numberPart.slice(0, CODE_CONFIG.TRANSACTION_DIGITS), 10);
  const customerNumber = parseInt(numberPart.slice(CODE_CONFIG.TRANSACTION_DIGITS), 10);
  
  return {
    code,
    transactionNumber,
    customerNumber,
    generatedAt: new Date().toISOString(),
    isCustom: true,
  };
};

/**
 * التحقق من تفرد كود العميل
 */
export const isCustomerCodeUnique = (code: string, excludeClientId?: string): boolean => {
  const result = secureGetItem<Client[]>('customers');
  if (!result.success || !result.data) {
    return true; // إذا لم توجد عملاء، فالكود فريد
  }
  
  const customers = result.data;
  return !customers.some(customer => 
    customer.clientCode === code && customer.id !== excludeClientId
  );
};

/**
 * الحصول على إحصائيات أكواد العملاء
 */
export const getCustomerCodeStats = () => {
  const counters = loadCounters();
  const result = secureGetItem<Client[]>('customers');
  const customers = result.success && result.data ? result.data : [];
  
  const customCodes = customers.filter(c => c.clientCode && !c.clientCode.match(/^p\d+$/));
  const generatedCodes = customers.filter(c => c.clientCode && c.clientCode.match(/^p\d+$/));
  
  return {
    totalCustomers: customers.length,
    generatedCodes: generatedCodes.length,
    customCodes: customCodes.length,
    nextCode: `${CODE_CONFIG.PREFIX}${formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS)}${formatNumber(counters.lastCustomerNumber + 1, CODE_CONFIG.CUSTOMER_DIGITS)}`,
    lastTransactionNumber: counters.lastTransactionNumber,
    lastCustomerNumber: counters.lastCustomerNumber,
  };
};

/**
 * إعادة تعيين العدادات (للاستخدام في حالات الطوارئ)
 */
export const resetCustomerCounters = (): void => {
  const defaultCounters: CustomerCounters = {
    lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,
    lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,
    totalCustomers: 0,
  };
  
  saveCounters(defaultCounters);
};

/**
 * تحديث رقم المعاملة (للانتقال لمعاملة جديدة)
 */
export const incrementTransactionNumber = (): void => {
  const counters = loadCounters();
  counters.lastTransactionNumber += 1;
  saveCounters(counters);
};

/**
 * البحث عن عميل بالكود
 */
export const findCustomerByCode = (code: string): Client | null => {
  const result = secureGetItem<Client[]>('customers');
  if (!result.success || !result.data) {
    return null;
  }
  
  return result.data.find(customer => customer.clientCode === code) || null;
};

/**
 * اقتراحات أكواد العملاء (للبحث التلقائي)
 */
export const getCustomerCodeSuggestions = (query: string, limit: number = 10): Client[] => {
  const result = secureGetItem<Client[]>('customers');
  if (!result.success || !result.data) {
    return [];
  }
  
  const customers = result.data;
  const lowerQuery = query.toLowerCase();
  
  return customers
    .filter(customer =>
      customer.clientCode?.toLowerCase().includes(lowerQuery) ||
      customer.name.toLowerCase().includes(lowerQuery) ||
      customer.phone.includes(query)
    )
    .slice(0, limit);
};

/**
 * التحقق من صحة كود العميل
 */
export const validateCustomerCode = (code: string): { isValid: boolean; error?: string } => {
  if (!code || typeof code !== 'string') {
    return { isValid: false, error: 'كود العميل مطلوب' };
  }

  const trimmedCode = code.trim();
  if (trimmedCode.length === 0) {
    return { isValid: false, error: 'كود العميل لا يمكن أن يكون فارغاً' };
  }

  // التحقق من التنسيق الأساسي
  const pattern = new RegExp(`^${CODE_CONFIG.PREFIX}\\d{${CODE_CONFIG.TRANSACTION_DIGITS}}\\d{${CODE_CONFIG.CUSTOMER_DIGITS}}$`);
  if (!pattern.test(trimmedCode)) {
    return {
      isValid: false,
      error: `تنسيق الكود غير صحيح. يجب أن يكون بالتنسيق: ${CODE_CONFIG.PREFIX}${'0'.repeat(CODE_CONFIG.TRANSACTION_DIGITS)}${'0'.repeat(CODE_CONFIG.CUSTOMER_DIGITS)}`
    };
  }

  return { isValid: true };
};

/**
 * التحقق من تكرار كود العميل
 */
export const checkCodeDuplication = (code: string, excludeId?: string): { isDuplicate: boolean; existingCustomer?: Client } => {
  const result = secureGetItem<Client[]>('customers');
  if (!result.success || !result.data) {
    return { isDuplicate: false };
  }

  const customers = result.data;
  const existingCustomer = customers.find(customer =>
    customer.clientCode === code && customer.id !== excludeId
  );

  return {
    isDuplicate: !!existingCustomer,
    existingCustomer
  };
};

/**
 * التحقق من تكرار بيانات العميل (الاسم + الهاتف)
 */
export const checkCustomerDataDuplication = (
  name: string,
  phone: string,
  excludeId?: string
): {
  isDuplicate: boolean;
  duplicateType?: 'name' | 'phone' | 'both';
  existingCustomer?: Client
} => {
  const result = secureGetItem<Client[]>('customers');
  if (!result.success || !result.data) {
    return { isDuplicate: false };
  }

  const customers = result.data;
  const trimmedName = name.trim().toLowerCase();
  const trimmedPhone = phone.trim();

  // البحث عن تطابق في الاسم والهاتف
  const nameMatch = customers.find(customer =>
    customer.name.trim().toLowerCase() === trimmedName && customer.id !== excludeId
  );

  const phoneMatch = customers.find(customer =>
    customer.phone.trim() === trimmedPhone && customer.id !== excludeId
  );

  if (nameMatch && phoneMatch && nameMatch.id === phoneMatch.id) {
    return {
      isDuplicate: true,
      duplicateType: 'both',
      existingCustomer: nameMatch
    };
  } else if (nameMatch) {
    return {
      isDuplicate: true,
      duplicateType: 'name',
      existingCustomer: nameMatch
    };
  } else if (phoneMatch) {
    return {
      isDuplicate: true,
      duplicateType: 'phone',
      existingCustomer: phoneMatch
    };
  }

  return { isDuplicate: false };
};

/**
 * التحقق الشامل من التكرار قبل إضافة/تحديث العميل
 */
export const validateCustomerUniqueness = (
  customerData: { name: string; phone: string; clientCode?: string },
  excludeId?: string
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions?: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // التحقق من كود العميل إذا تم تمريره
  if (customerData.clientCode) {
    const codeValidation = validateCustomerCode(customerData.clientCode);
    if (!codeValidation.isValid) {
      errors.push(codeValidation.error!);
    } else {
      const codeDuplication = checkCodeDuplication(customerData.clientCode, excludeId);
      if (codeDuplication.isDuplicate) {
        errors.push(`كود العميل "${customerData.clientCode}" مستخدم بالفعل للعميل: ${codeDuplication.existingCustomer?.name}`);
        suggestions.push('يمكنك ترك حقل الكود فارغاً لإنشاء كود جديد تلقائياً');
      }
    }
  }

  // التحقق من تكرار بيانات العميل
  const dataDuplication = checkCustomerDataDuplication(
    customerData.name,
    customerData.phone,
    excludeId
  );

  if (dataDuplication.isDuplicate) {
    const existing = dataDuplication.existingCustomer!;

    switch (dataDuplication.duplicateType) {
      case 'both':
        errors.push(`عميل بنفس الاسم ورقم الهاتف موجود بالفعل: ${existing.name} (${existing.clientCode})`);
        break;
      case 'name':
        warnings.push(`يوجد عميل بنفس الاسم: ${existing.name} (${existing.clientCode}) - هاتف: ${existing.phone}`);
        suggestions.push('تأكد من أن هذا عميل مختلف أم نفس العميل');
        break;
      case 'phone':
        warnings.push(`يوجد عميل بنفس رقم الهاتف: ${existing.name} (${existing.clientCode})`);
        suggestions.push('تأكد من صحة رقم الهاتف أو أن هذا عميل مختلف');
        break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions: suggestions.length > 0 ? suggestions : undefined
  };
};
