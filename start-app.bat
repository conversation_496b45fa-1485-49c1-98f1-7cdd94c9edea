@echo off
chcp 65001 >nul
title تشغيل تطبيق إدارة الفواتير - Invoice Management System

echo.
echo ========================================
echo    تطبيق إدارة الفواتير - infapapp
echo    Invoice Management System
echo ========================================
echo.

:: التحقق من وجود Node.js
echo [1/5] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org/
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js مثبت - الإصدار: %NODE_VERSION%

:: التحقق من وجود npm
echo.
echo [2/5] فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm متوفر - الإصدار: %NPM_VERSION%

:: التحقق من وجود ملف package.json
echo.
echo [3/5] فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo تأكد من تشغيل الملف من مجلد المشروع الصحيح
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)
echo ✅ ملف package.json موجود

:: التحقق من وجود مجلد node_modules
if not exist "node_modules" (
    echo.
    echo [4/5] تثبيت التبعيات...
    echo 📦 جاري تثبيت التبعيات، قد يستغرق هذا بضع دقائق...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo.
        echo ❌ خطأ في تثبيت التبعيات
        echo جرب الأوامر التالية يدوياً:
        echo   npm cache clean --force
        echo   npm install
        echo.
        echo اضغط أي مفتاح للخروج...
        pause >nul
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات مثبتة مسبقاً
    
    :: فحص إذا كانت التبعيات محدثة
    echo.
    echo [4/5] فحص التبعيات...
    npm list --depth=0 >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️  قد تحتاج التبعيات إلى إعادة تثبيت
        echo هل تريد إعادة تثبيت التبعيات؟ (y/n)
        set /p REINSTALL="الإجابة: "
        if /i "%REINSTALL%"=="y" (
            echo 📦 جاري إعادة تثبيت التبعيات...
            rmdir /s /q node_modules 2>nul
            del package-lock.json 2>nul
            npm install
            if %errorlevel% neq 0 (
                echo ❌ خطأ في إعادة تثبيت التبعيات
                echo اضغط أي مفتاح للخروج...
                pause >nul
                exit /b 1
            )
        )
    )
)

:: بناء المشروع للتطوير
echo.
echo [5/5] تشغيل الخادم المحلي...
echo 🚀 جاري تشغيل خادم التطوير...
echo.
echo ملاحظات مهمة:
echo - سيتم فتح التطبيق على http://localhost:3000
echo - اتركه يعمل ولا تغلق هذه النافذة
echo - لإيقاف الخادم اضغط Ctrl+C
echo.
echo ========================================
echo.

:: تشغيل خادم التطوير
npm run dev

:: في حالة فشل التشغيل
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في تشغيل الخادم
    echo.
    echo حلول مقترحة:
    echo 1. تأكد من أن المنفذ 3000 غير مستخدم
    echo 2. جرب تشغيل: npm run build ثم npm start
    echo 3. احذف مجلد .next وأعد المحاولة
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

echo.
echo تم إغلاق الخادم
pause
