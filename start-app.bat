@echo off
chcp 65001 >nul 2>&1
title تشغيل تطبيق إدارة الفواتير - Invoice Management System
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    تطبيق إدارة الفواتير - infapapp
echo    Invoice Management System
echo ========================================
echo.

:: التحقق من وجود Node.js
echo [1/6] فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org/
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

:: الحصول على إصدار Node.js
for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
if "!NODE_VERSION!"=="" (
    echo ❌ خطأ: لا يمكن الحصول على إصدار Node.js
    pause >nul
    exit /b 1
)
echo ✅ Node.js مثبت - الإصدار: !NODE_VERSION!

:: التحقق من وجود npm
echo.
echo [2/6] فحص npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

:: الحصول على إصدار npm
for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
if "!NPM_VERSION!"=="" (
    echo ❌ خطأ: لا يمكن الحصول على إصدار npm
    pause >nul
    exit /b 1
)
echo ✅ npm متوفر - الإصدار: !NPM_VERSION!

:: التحقق من وجود ملف package.json
echo.
echo [3/6] فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo تأكد من تشغيل الملف من مجلد المشروع الصحيح
    echo المجلد الحالي: %CD%
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)
echo ✅ ملف package.json موجود

:: التحقق من وجود ملفات المشروع الأساسية
if not exist "src" (
    echo ❌ خطأ: مجلد src غير موجود
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)
echo ✅ مجلد src موجود

if not exist "next.config.js" (
    echo ⚠️  تحذير: ملف next.config.js غير موجود
) else (
    echo ✅ ملف next.config.js موجود
)

:: التحقق من وجود مجلد node_modules
echo.
echo [4/6] فحص وتثبيت التبعيات...
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات، قد يستغرق هذا بضع دقائق...
    echo.

    :: تنظيف cache أولاً
    echo تنظيف npm cache...
    npm cache clean --force >nul 2>&1

    :: تثبيت التبعيات
    echo تثبيت التبعيات...
    npm install --no-audit --no-fund
    if !errorlevel! neq 0 (
        echo.
        echo ❌ خطأ في تثبيت التبعيات
        echo.
        echo جرب الحلول التالية:
        echo 1. احذف مجلد node_modules وملف package-lock.json
        echo 2. شغل: npm cache clean --force
        echo 3. شغل: npm install
        echo.
        echo اضغط أي مفتاح للخروج...
        pause >nul
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ مجلد node_modules موجود

    :: فحص سلامة التبعيات
    echo فحص سلامة التبعيات...
    npm list --depth=0 >nul 2>&1
    if !errorlevel! neq 0 (
        echo ⚠️  توجد مشاكل في التبعيات
        echo هل تريد إعادة تثبيت التبعيات؟ (y/n)
        set /p REINSTALL="الإجابة: "
        if /i "!REINSTALL!"=="y" (
            echo 📦 جاري إعادة تثبيت التبعيات...
            if exist "node_modules" rmdir /s /q node_modules
            if exist "package-lock.json" del package-lock.json
            npm cache clean --force >nul 2>&1
            npm install --no-audit --no-fund
            if !errorlevel! neq 0 (
                echo ❌ خطأ في إعادة تثبيت التبعيات
                echo اضغط أي مفتاح للخروج...
                pause >nul
                exit /b 1
            )
            echo ✅ تم إعادة تثبيت التبعيات بنجاح
        )
    ) else (
        echo ✅ التبعيات سليمة
    )
)

:: فحص المنفذ 3000
echo.
echo [5/6] فحص المنفذ...
netstat -an | find "3000" >nul 2>&1
if !errorlevel! equ 0 (
    echo ⚠️  المنفذ 3000 مستخدم بالفعل
    echo هل تريد إيقاف العملية المستخدمة للمنفذ؟ (y/n)
    set /p KILL_PORT="الإجابة: "
    if /i "!KILL_PORT!"=="y" (
        echo إيقاف العمليات على المنفذ 3000...
        for /f "tokens=5" %%a in ('netstat -ano ^| find "3000" ^| find "LISTENING"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
    )
)

:: تنظيف ملفات البناء القديمة
echo.
echo [6/6] تنظيف وتشغيل الخادم...
if exist ".next" (
    echo تنظيف ملفات البناء القديمة...
    rmdir /s /q .next >nul 2>&1
)

echo 🚀 جاري تشغيل خادم التطوير...
echo.
echo ملاحظات مهمة:
echo - سيتم فتح التطبيق على http://localhost:3000
echo - انتظر حتى ترى "Ready - started server on 0.0.0.0:3000"
echo - اتركه يعمل ولا تغلق هذه النافذة
echo - لإيقاف الخادم اضغط Ctrl+C
echo.
echo ========================================
echo.

:: تشغيل خادم التطوير مع معالجة أفضل للأخطاء
call npm run dev
set DEV_EXIT_CODE=!errorlevel!

:: في حالة فشل التشغيل
if !DEV_EXIT_CODE! neq 0 (
    echo.
    echo ❌ خطأ في تشغيل الخادم (Exit Code: !DEV_EXIT_CODE!)
    echo.
    echo حلول مقترحة:
    echo 1. تأكد من أن المنفذ 3000 غير مستخدم
    echo 2. احذف مجلد .next ومجلد node_modules وأعد التثبيت
    echo 3. تأكد من عدم وجود أخطاء في الكود
    echo 4. جرب تشغيل: npm run build للتحقق من الأخطاء
    echo.
    echo هل تريد محاولة البناء للتحقق من الأخطاء؟ (y/n)
    set /p TRY_BUILD="الإجابة: "
    if /i "!TRY_BUILD!"=="y" (
        echo.
        echo جاري بناء المشروع للتحقق من الأخطاء...
        npm run build
        if !errorlevel! neq 0 (
            echo ❌ توجد أخطاء في الكود تمنع البناء
        ) else (
            echo ✅ البناء نجح، المشكلة قد تكون في خادم التطوير
        )
    )
    echo.
    echo اضغط أي مفتاح للخروج...
    pause >nul
    exit /b !DEV_EXIT_CODE!
)

echo.
echo تم إغلاق الخادم بنجاح
echo اضغط أي مفتاح للخروج...
pause >nul
