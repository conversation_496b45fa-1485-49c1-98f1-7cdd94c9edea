"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InvoiceForm */ \"(app-pages-browser)/./src/components/InvoiceForm.tsx\");\n/* harmony import */ var _components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvoicesList */ \"(app-pages-browser)/./src/components/InvoicesList.tsx\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"client\");\n    const [currentClient, setCurrentClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentInvoice, setCurrentInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { saveInvoice, currentClient: storeClient, clearCurrentClient } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore)();\n    // استخدام العميل من store إذا كان متوفراً\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (storeClient && !currentClient) {\n            setCurrentClient(storeClient);\n            setCurrentStep(\"invoice\");\n            // مسح العميل من store بعد الاستخدام\n            clearCurrentClient();\n        }\n    }, [\n        storeClient,\n        currentClient,\n        clearCurrentClient\n    ]);\n    const handleClientSubmit = (client)=>{\n        setCurrentClient(client);\n        setCurrentStep(\"invoice\");\n    };\n    const handleInvoiceSubmit = (invoice)=>{\n        // إنشاء ID فريد مع ترقيم تسلسلي\n        const generateSequentialId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const monthYearKey = \"\".concat(month, \"-\").concat(year);\n            // قراءة العدادات المحفوظة باستخدام النظام الآمن\n            const countersResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoiceCounters\", {});\n            let counters = {};\n            if (countersResult.success) {\n                counters = countersResult.data;\n            } else {\n                console.error(\"❌ Error reading invoice counters:\", countersResult.message);\n                counters = {};\n            }\n            // الحصول على العداد الحالي للشهر والسنة\n            const currentCounter = counters[monthYearKey] || 0;\n            const newCounter = currentCounter + 1;\n            // تحديث العداد\n            counters[monthYearKey] = newCounter;\n            // حفظ العدادات المحدثة باستخدام النظام الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureSetItem)(\"invoiceCounters\", counters);\n            if (!saveResult.success) {\n                console.error(\"❌ Error saving invoice counters:\", saveResult.message);\n            }\n            // إنشاء ID بالتنسيق المطلوب\n            const sequentialNumber = String(newCounter).padStart(3, \"0\");\n            const invoiceId = \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(sequentialNumber);\n            return invoiceId;\n        };\n        const invoiceWithId = {\n            ...invoice,\n            id: invoice.id || generateSequentialId(),\n            createdAt: invoice.createdAt || new Date().toISOString()\n        };\n        // تعيين الفاتورة الحالية\n        setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة في المتجر\n        const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore.getState();\n        store.setCurrentInvoice(invoiceWithId);\n        // حفظ الفاتورة (ستتعامل دالة saveInvoice مع التحقق من التكرار)\n        store.saveInvoice();\n        // التحقق من النتيجة باستخدام النظام الآمن\n        setTimeout(()=>{\n            const invoicesResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_9__.secureGetItem)(\"invoices\", []);\n            if (invoicesResult.success) {\n                const savedInvoice = invoicesResult.data.find((inv)=>inv.id === invoiceWithId.id);\n                if (!savedInvoice) {\n                    console.error(\"❌ Invoice not found in storage after save attempt\");\n                }\n            } else {\n                console.error(\"❌ Error verifying save:\", invoicesResult.message);\n            }\n        }, 100);\n        setCurrentStep(\"preview\");\n    };\n    const handleEditInvoice = ()=>{\n        setCurrentStep(\"invoice\");\n    };\n    const handleExportPDF = async ()=>{\n        if (currentInvoice) {\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_8__.generateInvoicePDF)(currentInvoice);\n            } catch (error) {\n                alert(\"حدث خطأ أثناء تصدير PDF: \" + error.message);\n            }\n        }\n    };\n    const handleStartOver = ()=>{\n        console.log(\"\\uD83D\\uDD04 بداية جديدة - إعادة تعيين النظام\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n        setCurrentStep(\"client\");\n        setCurrentPage(\"home\");\n    };\n    const handleManageInvoices = ()=>{\n        console.log(\"\\uD83D\\uDCCB الانتقال لصفحة إدارة الفواتير\");\n        setCurrentPage(\"invoices\");\n    };\n    const handleCreateInvoice = ()=>{\n        console.log(\"➕ بدء إنشاء فاتورة جديدة\");\n        setCurrentPage(\"home\");\n        setCurrentStep(\"client\");\n        setCurrentClient(null);\n        setCurrentInvoice(null);\n    };\n    const handleDataProtection = ()=>{\n        console.log(\"\\uD83D\\uDEE1️ الانتقال لصفحة حماية البيانات\");\n        router.push(\"/data-protection\");\n    };\n    const handleStatistics = ()=>{\n        console.log(\"\\uD83D\\uDCCA الانتقال لصفحة الإحصائيات الشاملة\");\n        router.push(\"/statistics\");\n    };\n    const handleCustomerManagement = ()=>{\n        console.log(\"\\uD83D\\uDC65 الانتقال لصفحة إدارة العملاء\");\n        router.push(\"/customers\");\n    };\n    const handleCustomerCodes = ()=>{\n        console.log(\"\\uD83D\\uDD22 الانتقال لصفحة إدارة أكواد العملاء\");\n        router.push(\"/customer-codes\");\n    };\n    const getStepTitle = (step)=>{\n        switch(step){\n            case \"client\":\n                return \"إدخال بيانات العميل\";\n            case \"invoice\":\n                return \"إنشاء الفاتورة\";\n            case \"preview\":\n                return \"معاينة الفاتورة\";\n            default:\n                return \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 p-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl sm:text-3xl font-bold\",\n                                                children: \"نظام إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-100 text-sm hidden sm:block\",\n                                                children: \"إدارة شاملة للفواتير والخدمات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex gap-2 sm:gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateInvoice,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"home\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10 2L3 9v9a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V9l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إنشاء فاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إنشاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleManageInvoices,\n                                        className: \"px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:scale-105 active:scale-95 \".concat(currentPage === \"invoices\" ? \"bg-white text-blue-600 shadow-md ring-2 ring-blue-200\" : \"bg-blue-500 hover:bg-blue-400 text-white hover:shadow-md\"),\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة الفواتير\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"إدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerManagement,\n                                        className: \"bg-orange-500 hover:bg-orange-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة العملاء وأكواد العملاء\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"إدارة العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDC65\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCustomerCodes,\n                                        className: \"bg-teal-500 hover:bg-teal-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"إدارة أكواد العملاء والعدادات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"أكواد العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDD22\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDataProtection,\n                                        className: \"bg-purple-500 hover:bg-purple-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"لوحة تحكم حماية البيانات\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"حماية البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDEE1️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStatistics,\n                                        className: \"bg-indigo-500 hover:bg-indigo-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        title: \"الإحصائيات الشاملة والتقارير\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"الإحصائيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartOver,\n                                        className: \"bg-green-500 hover:bg-green-400 px-3 sm:px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95\",\n                                        type: \"button\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"بداية جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sm:hidden\",\n                                                children: \"جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-gray-50 to-white shadow-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 sm:space-x-12 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"client\" ? \"text-blue-600 scale-105\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"client\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"invoice\" || currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"بيانات العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"invoice\" || currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"invoice\" ? \"text-blue-600 scale-105\" : currentStep === \"preview\" ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"invoice\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : currentStep === \"preview\" ? \"bg-gradient-to-br from-green-500 to-green-700 ring-2 ring-green-200\" : \"bg-gray-400\"),\n                                                    children: currentStep === \"preview\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 animate-bounceIn\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"إنشاء الفاتورة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري التعبئة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 max-w-24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-1 rounded-full transition-all duration-700 \".concat(currentStep === \"preview\" ? \"bg-gradient-to-l from-green-400 to-green-500\" : \"bg-gray-300\"),\n                                            children: currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-gradient-to-l from-blue-400 to-blue-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center transition-all duration-500 \".concat(currentStep === \"preview\" ? \"text-blue-600 scale-105\" : \"text-gray-400\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-500 shadow-lg hover:shadow-xl \".concat(currentStep === \"preview\" ? \"bg-gradient-to-br from-blue-500 to-blue-700 ring-4 ring-blue-200 animate-pulse\" : \"bg-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mt-3 font-semibold text-sm sm:text-base text-center\",\n                                                    children: \"معاينة وتصدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-xs text-blue-500 animate-pulse\",\n                                                    children: \"جاري المعاينة...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-full px-4 py-2 shadow-sm border border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"التقدم: \",\n                                                currentStep === \"client\" ? \"33%\" : currentStep === \"invoice\" ? \"66%\" : \"100%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                    children: [\n                        currentPage === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 sm:mb-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-white to-gray-50 rounded-xl shadow-sm p-6 sm:p-8 border border-gray-200 hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full shadow-md \".concat(currentStep === \"client\" ? \"bg-blue-600\" : currentStep === \"invoice\" ? \"bg-green-600\" : \"bg-purple-600\"),\n                                                        children: [\n                                                            currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-2xl sm:text-3xl font-bold text-gray-800 mb-2\",\n                                                                children: getStepTitle(currentStep)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm sm:text-base leading-relaxed\",\n                                                                children: [\n                                                                    currentStep === \"client\" && \"أدخل بيانات العميل الأساسية للبدء في إنشاء الفاتورة الجديدة\",\n                                                                    currentStep === \"invoice\" && \"اختر الخدمات المطلوبة وأدخل تفاصيل كل خدمة بدقة\",\n                                                                    currentStep === \"preview\" && \"راجع تفاصيل الفاتورة وقم بتصديرها أو تعديلها حسب الحاجة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-4 rounded-lg border-l-4 \".concat(currentStep === \"client\" ? \"bg-blue-50 border-blue-400\" : currentStep === \"invoice\" ? \"bg-green-50 border-green-400\" : \"bg-purple-50 border-purple-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mt-0.5 flex-shrink-0 \".concat(currentStep === \"client\" ? \"text-blue-600\" : currentStep === \"invoice\" ? \"text-green-600\" : \"text-purple-600\"),\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-blue-800 mb-2\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-blue-700 space-y-1 mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تأكد من صحة رقم الهاتف للتواصل\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 475,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• اختر الخدمات المطلوبة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن ترك الحقول الاختيارية فارغة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكنك اختيار عميل موجود من النموذج أدناه\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 478,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-blue-100 border border-blue-200 rounded-lg p-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-800 font-medium text-sm\",\n                                                                                                children: \"هل تريد إدارة العملاء المسجلين؟\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 483,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-blue-600 text-xs mt-1\",\n                                                                                                children: \"يمكنك عرض وإدارة جميع العملاء المسجلين\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 484,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 482,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: handleCustomerManagement,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                className: \"w-3 h-3\",\n                                                                                                fill: \"currentColor\",\n                                                                                                viewBox: \"0 0 20 20\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 491,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 490,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \"إدارة العملاء\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 481,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"invoice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-green-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-green-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• أدخل تفاصيل كل خدمة بدقة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 503,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• تحقق من الأسعار والكميات\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يتم حساب المجموع تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentStep === \"preview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-purple-800 mb-1\",\n                                                                            children: \"نصائح مفيدة:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"text-purple-700 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• راجع جميع البيانات قبل التصدير\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• يمكن طباعة الفاتورة مباشرة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"• الفاتورة محفوظة تلقائياً\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        currentStep === \"client\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInRight\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                onSubmit: handleClientSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"invoice\" && currentClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInLeft\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoiceForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                client: currentClient,\n                                                onSubmit: handleInvoiceSubmit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentStep === \"preview\" && currentInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-slideInUp\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicePreview__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                invoice: currentInvoice,\n                                                onEdit: handleEditInvoice,\n                                                onExportPDF: handleExportPDF\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPage === \"invoices\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-fadeIn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvoicesList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 p-2 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"نظام إدارة الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm leading-relaxed\",\n                                            children: \"نظام شامل ومتطور لإدارة فواتير الخدمات مع دعم كامل للغة العربية وتصدير PDF احترافي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-blue-400\",\n                                            children: \"الخدمات المدعومة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الاستشارات والتصميم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الباترون والطباعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصنيع والعينات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"الشحن والتسويق\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"التصوير والمنتجات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center md:text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-green-400\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"ترقيم تسلسلي للفواتير\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"تصدير PDF احترافي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"حساب المدفوعات التلقائي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"واجهة عربية متجاوبة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center justify-center md:justify-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-green-400\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إدارة شاملة للعملاء\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 mt-8 pt-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"\\xa9 2025 نظام إدارة الفواتير. جميع الحقوق محفوظة.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-xs mt-2\",\n                                    children: \"تم التطوير بأحدث التقنيات لضمان الأداء والموثوقية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 564,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SBp39x2T7Rlgt08WkQMecazsngQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_7__.useInvoiceStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});