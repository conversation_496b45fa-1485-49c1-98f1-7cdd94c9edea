"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/imageExporter.ts":
/*!************************************!*\
  !*** ./src/utils/imageExporter.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportAsImage: function() { return /* binding */ exportAsImage; },\n/* harmony export */   exportPOSInvoiceAsImage: function() { return /* binding */ exportPOSInvoiceAsImage; },\n/* harmony export */   previewAsImage: function() { return /* binding */ previewAsImage; },\n/* harmony export */   sharePOSInvoiceAsImage: function() { return /* binding */ sharePOSInvoiceAsImage; }\n/* harmony export */ });\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * انتظار تحميل جميع الصور في العنصر\n * @param element العنصر المراد فحصه\n */ const waitForImages = (element)=>{\n    return new Promise((resolve)=>{\n        const images = element.querySelectorAll(\"img\");\n        if (images.length === 0) {\n            resolve();\n            return;\n        }\n        let loadedCount = 0;\n        const totalImages = images.length;\n        const checkComplete = ()=>{\n            loadedCount++;\n            if (loadedCount === totalImages) {\n                resolve();\n            }\n        };\n        images.forEach((img)=>{\n            if (img.complete) {\n                checkComplete();\n            } else {\n                img.onload = checkComplete;\n                img.onerror = checkComplete; // حتى لو فشل التحميل، نكمل\n            }\n        });\n    });\n};\n/**\n * تصدير عنصر HTML كصورة JPG\n * @param elementId معرف العنصر المراد تصديره\n * @param filename اسم الملف (اختياري)\n * @param options خيارات التصدير\n */ const exportAsImage = async (elementId, filename, options)=>{\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        // إعدادات html2canvas المحسنة للطابعات الحرارية مع دعم أفضل للصور\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: (options === null || options === void 0 ? void 0 : options.scale) || 2,\n            backgroundColor: (options === null || options === void 0 ? void 0 : options.backgroundColor) || \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            width: (options === null || options === void 0 ? void 0 : options.width) || element.scrollWidth,\n            height: (options === null || options === void 0 ? void 0 : options.height) || element.scrollHeight,\n            scrollX: 0,\n            scrollY: 0,\n            // تحسينات للنصوص العربية\n            letterRendering: true,\n            logging: false,\n            // تحسين جودة الخطوط والصور\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    // تطبيق أنماط إضافية للطباعة\n                    clonedElement.style.fontFamily = \"Arial, sans-serif\";\n                    clonedElement.style.fontSize = \"14px\";\n                    clonedElement.style.lineHeight = \"1.4\";\n                    // التأكد من أن جميع الصور مرئية ومحسنة\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block !important\";\n                        img.style.visibility = \"visible !important\";\n                        img.style.opacity = \"1 !important\";\n                        img.style.maxWidth = \"100%\";\n                        img.style.height = \"auto\";\n                        img.style.objectFit = \"contain\";\n                        img.style.imageRendering = \"crisp-edges\";\n                        img.style.filter = \"contrast(1.2) brightness(1.1)\";\n                        // التأكد من تحميل الصورة\n                        if (img.src && !img.complete) {\n                            img.crossOrigin = \"anonymous\";\n                        }\n                    });\n                }\n            }\n        });\n        // تحويل إلى صورة JPG\n        const imageData = canvas.toDataURL(\"image/jpeg\", (options === null || options === void 0 ? void 0 : options.quality) || 0.95);\n        // إنشاء رابط التحميل\n        const link = document.createElement(\"a\");\n        link.download = filename || \"فاتورة-POS-\".concat(Date.now(), \".jpg\");\n        link.href = imageData;\n        // تحميل الصورة\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        console.log(\"تم تصدير الصورة بنجاح\");\n    } catch (error) {\n        console.error(\"خطأ في تصدير الصورة:\", error);\n        throw error;\n    }\n};\n/**\n * تصدير فاتورة POS كصورة مع إعدادات محسنة\n * @param invoiceId معرف الفاتورة\n * @param invoiceNumber رقم الفاتورة\n */ const exportPOSInvoiceAsImage = async (invoiceId, invoiceNumber)=>{\n    const filename = \"فاتورة-POS-\".concat(invoiceNumber || invoiceId, \"-\").concat(new Date().toISOString().split(\"T\")[0], \".jpg\");\n    await exportAsImage(\"pos-invoice-content\", filename, {\n        quality: 0.95,\n        scale: 3,\n        backgroundColor: \"#ffffff\",\n        width: 320\n    });\n};\n/**\n * مشاركة فاتورة POS كصورة (إذا كان المتصفح يدعم Web Share API)\n * @param elementId معرف العنصر\n * @param title عنوان المشاركة\n */ const sharePOSInvoiceAsImage = async function(elementId) {\n    let title = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"فاتورة POS\";\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: 2,\n            backgroundColor: \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block\";\n                        img.style.visibility = \"visible\";\n                        img.style.opacity = \"1\";\n                    });\n                }\n            }\n        });\n        // تحويل إلى blob\n        canvas.toBlob(async (blob)=>{\n            if (!blob) {\n                throw new Error(\"فشل في إنشاء الصورة\");\n            }\n            if (navigator.share && navigator.canShare) {\n                const file = new File([\n                    blob\n                ], \"\".concat(title, \".jpg\"), {\n                    type: \"image/jpeg\"\n                });\n                if (navigator.canShare({\n                    files: [\n                        file\n                    ]\n                })) {\n                    await navigator.share({\n                        title: title,\n                        text: \"فاتورة من OKA GROUP\",\n                        files: [\n                            file\n                        ]\n                    });\n                    return;\n                }\n            }\n            // إذا لم يكن Web Share API متاحاً، استخدم التحميل العادي\n            const imageData = canvas.toDataURL(\"image/jpeg\", 0.95);\n            const link = document.createElement(\"a\");\n            link.download = \"\".concat(title, \".jpg\");\n            link.href = imageData;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        }, \"image/jpeg\", 0.95);\n    } catch (error) {\n        console.error(\"خطأ في مشاركة الصورة:\", error);\n        throw error;\n    }\n};\n/**\n * معاينة الصورة قبل التصدير\n * @param elementId معرف العنصر\n * @returns Promise<string> رابط الصورة\n */ const previewAsImage = async (elementId)=>{\n    try {\n        const element = document.getElementById(elementId);\n        if (!element) {\n            throw new Error('Element with ID \"'.concat(elementId, '\" not found'));\n        }\n        // انتظار تحميل جميع الصور\n        await waitForImages(element);\n        const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_0___default()(element, {\n            scale: 2,\n            backgroundColor: \"#ffffff\",\n            useCORS: true,\n            allowTaint: true,\n            foreignObjectRendering: false,\n            imageTimeout: 15000,\n            onclone: (clonedDoc)=>{\n                const clonedElement = clonedDoc.getElementById(elementId);\n                if (clonedElement) {\n                    const images = clonedElement.querySelectorAll(\"img\");\n                    images.forEach((img)=>{\n                        img.style.display = \"block\";\n                        img.style.visibility = \"visible\";\n                        img.style.opacity = \"1\";\n                    });\n                }\n            }\n        });\n        return canvas.toDataURL(\"image/jpeg\", 0.95);\n    } catch (error) {\n        console.error(\"خطأ في معاينة الصورة:\", error);\n        throw error;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageExporter.ts\n"));

/***/ })

});