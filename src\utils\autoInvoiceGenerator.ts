/**
 * مولد الفواتير التلقائية عند استيراد العملاء
 * يدعم إنشاء فواتير متعددة للعميل الواحد بناءً على المعاملات
 */

import { Client, Invoice, Service } from '@/types';
import { UnifiedStorageManager } from './unifiedStorage';
import { STORAGE_KEYS } from './storageKeys';

// نوع بيانات العميل المستورد مع المعاملات
export interface ImportedCustomerData {
  clientCode: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  // بيانات المعاملة
  transactionId?: string; // P01, P02, P03, etc.
  services?: ImportedService[];
  totalAmount?: number;
  paidAmount?: number;
  notes?: string;
  dueDate?: string;
}

export interface ImportedService {
  serviceType: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

// نتيجة عملية الاستيراد
export interface ImportResult {
  success: boolean;
  message: string;
  stats: {
    totalRows: number;
    customersProcessed: number;
    invoicesCreated: number;
    errors: number;
  };
  details: {
    customers: Client[];
    invoices: Invoice[];
    errors: string[];
  };
}

/**
 * معالج استيراد العملاء مع إنشاء الفواتير التلقائية
 */
export class AutoInvoiceGenerator {
  
  /**
   * استيراد العملاء وإنشاء الفواتير التلقائية
   */
  static async importCustomersWithInvoices(
    importedData: ImportedCustomerData[]
  ): Promise<ImportResult> {
    
    const result: ImportResult = {
      success: false,
      message: '',
      stats: {
        totalRows: importedData.length,
        customersProcessed: 0,
        invoicesCreated: 0,
        errors: 0
      },
      details: {
        customers: [],
        invoices: [],
        errors: []
      }
    };

    try {
      console.log('🔄 بدء استيراد العملاء مع إنشاء الفواتير التلقائية...');
      
      // تحميل البيانات الحالية
      const existingCustomersResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
      const existingInvoicesResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
      
      if (!existingCustomersResult.success || !existingInvoicesResult.success) {
        throw new Error('فشل في تحميل البيانات الحالية');
      }

      const existingCustomers: Client[] = existingCustomersResult.data;
      const existingInvoices: Invoice[] = existingInvoicesResult.data;
      
      // تجميع البيانات حسب كود العميل
      const customerGroups = this.groupByCustomerCode(importedData);
      
      // معالجة كل مجموعة عملاء
      for (const [customerCode, transactions] of customerGroups.entries()) {
        try {
          const processResult = await this.processCustomerGroup(
            customerCode,
            transactions,
            existingCustomers,
            existingInvoices
          );
          
          result.details.customers.push(...processResult.customers);
          result.details.invoices.push(...processResult.invoices);
          result.stats.customersProcessed += processResult.customers.length;
          result.stats.invoicesCreated += processResult.invoices.length;
          
        } catch (error) {
          const errorMsg = `خطأ في معالجة العميل ${customerCode}: ${(error as Error).message}`;
          result.details.errors.push(errorMsg);
          result.stats.errors++;
          console.error('❌', errorMsg);
        }
      }
      
      // حفظ البيانات المحدثة
      const allCustomers = [...existingCustomers, ...result.details.customers];
      const allInvoices = [...existingInvoices, ...result.details.invoices];
      
      const saveCustomersResult = await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, allCustomers);
      const saveInvoicesResult = await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, allInvoices);
      
      if (!saveCustomersResult.success || !saveInvoicesResult.success) {
        throw new Error('فشل في حفظ البيانات المستوردة');
      }
      
      // تحديد نتيجة العملية
      const hasErrors = result.stats.errors > 0;
      result.success = !hasErrors || result.stats.customersProcessed > 0;
      result.message = hasErrors 
        ? `تم الاستيراد مع ${result.stats.errors} أخطاء`
        : `تم استيراد ${result.stats.customersProcessed} عميل و ${result.stats.invoicesCreated} فاتورة بنجاح`;
      
      console.log('✅ انتهى الاستيراد:', result.message);
      return result;
      
    } catch (error) {
      const errorMsg = `فشل في عملية الاستيراد: ${(error as Error).message}`;
      result.success = false;
      result.message = errorMsg;
      result.details.errors.push(errorMsg);
      console.error('❌', errorMsg);
      return result;
    }
  }
  
  /**
   * تجميع البيانات المستوردة حسب كود العميل
   */
  private static groupByCustomerCode(
    importedData: ImportedCustomerData[]
  ): Map<string, ImportedCustomerData[]> {
    
    const groups = new Map<string, ImportedCustomerData[]>();
    
    for (const data of importedData) {
      if (!data.clientCode) {
        console.warn('⚠️ تم تجاهل صف بدون كود عميل:', data);
        continue;
      }
      
      const code = data.clientCode.trim().toUpperCase();
      if (!groups.has(code)) {
        groups.set(code, []);
      }
      groups.get(code)!.push(data);
    }
    
    console.log(`📊 تم تجميع البيانات إلى ${groups.size} عميل`);
    return groups;
  }
  
  /**
   * معالجة مجموعة معاملات عميل واحد
   */
  private static async processCustomerGroup(
    customerCode: string,
    transactions: ImportedCustomerData[],
    existingCustomers: Client[],
    existingInvoices: Invoice[]
  ): Promise<{
    customers: Client[];
    invoices: Invoice[];
  }> {
    
    const result = {
      customers: [] as Client[],
      invoices: [] as Invoice[]
    };
    
    // البحث عن العميل الموجود
    let customer = existingCustomers.find(c => c.clientCode === customerCode);
    
    // إنشاء عميل جديد إذا لم يكن موجوداً
    if (!customer) {
      const firstTransaction = transactions[0];
      customer = this.createNewCustomer(customerCode, firstTransaction);
      result.customers.push(customer);
      console.log(`👤 تم إنشاء عميل جديد: ${customer.name} (${customerCode})`);
    } else {
      console.log(`👤 تم العثور على عميل موجود: ${customer.name} (${customerCode})`);
    }
    
    // إنشاء فاتورة لكل معاملة
    for (const transaction of transactions) {
      if (this.shouldCreateInvoice(transaction)) {
        const invoice = this.createInvoiceFromTransaction(customer, transaction, existingInvoices.length);
        result.invoices.push(invoice);
        console.log(`📋 تم إنشاء فاتورة: ${invoice.invoiceNumber} للعميل ${customer.name}`);
      }
    }
    
    return result;
  }
  
  /**
   * إنشاء عميل جديد من بيانات المعاملة
   */
  private static createNewCustomer(
    customerCode: string,
    transactionData: ImportedCustomerData
  ): Client {
    
    return {
      id: `customer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      clientCode: customerCode,
      name: transactionData.name || `عميل ${customerCode}`,
      email: transactionData.email || '',
      phone: transactionData.phone || '',
      address: transactionData.address || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }
  
  /**
   * تحديد ما إذا كان يجب إنشاء فاتورة للمعاملة
   */
  private static shouldCreateInvoice(transaction: ImportedCustomerData): boolean {
    return !!(
      transaction.services?.length || 
      transaction.totalAmount || 
      transaction.transactionId
    );
  }
  
  /**
   * إنشاء فاتورة من بيانات المعاملة
   */
  private static createInvoiceFromTransaction(
    customer: Client,
    transaction: ImportedCustomerData,
    existingInvoicesCount: number
  ): Invoice {
    
    // إنشاء الخدمات
    const services: Service[] = transaction.services?.map(s => ({
      id: `service-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      serviceType: s.serviceType,
      description: s.description,
      quantity: s.quantity,
      unitPrice: s.unitPrice,
      total: s.total,
      createdAt: new Date().toISOString()
    })) || [];
    
    // حساب المجموع
    const servicesTotal = services.reduce((sum, service) => sum + service.total, 0);
    const totalAmount = transaction.totalAmount || servicesTotal;
    const paidAmount = transaction.paidAmount || 0;
    const remainingAmount = totalAmount - paidAmount;
    
    // إنشاء رقم الفاتورة
    const invoiceNumber = this.generateInvoiceNumber(existingInvoicesCount + 1);
    
    return {
      id: `invoice-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      invoiceNumber,
      client: customer,
      services,
      totalAmount,
      paidAmount,
      remainingAmount,
      status: remainingAmount > 0 ? 'جزئي' : 'مدفوع',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dueDate: transaction.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      notes: transaction.notes || `فاتورة مستوردة للمعاملة ${transaction.transactionId || 'غير محدد'}`,
      transactionId: transaction.transactionId
    };
  }
  
  /**
   * إنشاء رقم فاتورة تسلسلي
   */
  private static generateInvoiceNumber(sequence: number): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    return `INV-${year}${month}-${String(sequence).padStart(4, '0')}`;
  }
  
  /**
   * التحقق من صحة بيانات الاستيراد
   */
  static validateImportData(data: ImportedCustomerData[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!Array.isArray(data) || data.length === 0) {
      errors.push('لا توجد بيانات للاستيراد');
      return { isValid: false, errors, warnings };
    }
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 1;
      
      // التحقق من الحقول المطلوبة
      if (!row.clientCode?.trim()) {
        errors.push(`الصف ${rowNum}: كود العميل مطلوب`);
      }
      
      if (!row.name?.trim()) {
        warnings.push(`الصف ${rowNum}: اسم العميل فارغ`);
      }
      
      // التحقق من صحة البيانات المالية
      if (row.totalAmount && row.totalAmount < 0) {
        errors.push(`الصف ${rowNum}: المبلغ الإجمالي لا يمكن أن يكون سالباً`);
      }
      
      if (row.paidAmount && row.paidAmount < 0) {
        errors.push(`الصف ${rowNum}: المبلغ المدفوع لا يمكن أن يكون سالباً`);
      }
      
      if (row.totalAmount && row.paidAmount && row.paidAmount > row.totalAmount) {
        warnings.push(`الصف ${rowNum}: المبلغ المدفوع أكبر من المبلغ الإجمالي`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
