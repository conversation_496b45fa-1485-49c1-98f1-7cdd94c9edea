'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { RawPurchasesService, RawMaterial } from '@/types';

interface RawPurchasesServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
  isPreSelected?: boolean;
}

const RawPurchasesServiceComponent: React.FC<RawPurchasesServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
  isPreSelected = false,
}) => {
  const serviceData = values[namePrefix] as RawPurchasesService;
  const isEnabled = serviceData?.enabled || false;

  // تفعيل الخدمة تلقائياً إذا كانت مختارة مسبقاً
  React.useEffect(() => {
    if (isPreSelected && !serviceData?.enabled) {
      setFieldValue(`${namePrefix}.enabled`, true);
      setFieldValue(`${namePrefix}.serviceType`, 'raw_purchases');
    }
  }, [isPreSelected, namePrefix, setFieldValue, serviceData?.enabled]);

  const defaultMaterial: RawMaterial = {
    materialName: '',
    quantity: 1,
    unitPrice: 0,
    supplier: '',
    total: 0,
  };

  const materialCategories = [
    { value: 'fabrics', label: 'أقمشة' },
    { value: 'threads', label: 'خيوط' },
    { value: 'buttons', label: 'أزرار' },
    { value: 'zippers', label: 'سحابات' },
    { value: 'accessories', label: 'إكسسوارات' },
    { value: 'trims', label: 'تشطيبات' },
    { value: 'interfacing', label: 'حشو' },
    { value: 'elastic', label: 'مطاط' },
    { value: 'labels', label: 'علامات' },
    { value: 'packaging', label: 'تغليف' },
    { value: 'tools', label: 'أدوات' },
    { value: 'chemicals', label: 'مواد كيميائية' },
  ];

  const units = [
    { value: 'meter', label: 'متر' },
    { value: 'yard', label: 'ياردة' },
    { value: 'piece', label: 'قطعة' },
    { value: 'roll', label: 'لفة' },
    { value: 'kg', label: 'كيلوجرام' },
    { value: 'gram', label: 'جرام' },
    { value: 'box', label: 'صندوق' },
    { value: 'pack', label: 'عبوة' },
    { value: 'dozen', label: 'دزينة' },
    { value: 'gross', label: 'جروس' },
  ];

  // حساب المجموع لكل مادة
  React.useEffect(() => {
    if (isEnabled && serviceData?.materials) {
      serviceData.materials.forEach((material, index) => {
        const quantity = material.quantity || 0;
        const unitPrice = material.unitPrice || 0;
        const total = quantity * unitPrice;
        setFieldValue(`${namePrefix}.materials.${index}.total`, total);
      });
    }
  }, [serviceData?.materials, isEnabled, namePrefix, setFieldValue]);

  // حساب المجموع الكلي
  const calculateGrandTotal = () => {
    if (!serviceData?.materials) return 0;
    return serviceData.materials.reduce((total, material) => {
      return total + (material.total || 0);
    }, 0);
  };

  // تحديث المجموع الكلي
  React.useEffect(() => {
    if (isEnabled) {
      const total = calculateGrandTotal();
      setFieldValue(`${namePrefix}.grandTotal`, total);
    }
  }, [serviceData?.materials, isEnabled]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'raw_purchases');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              setFieldValue(`${namePrefix}.materials`, [defaultMaterial]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة المشتريات الخام</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-amber-500">
          <FieldArray name={`${namePrefix}.materials`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">قائمة المواد الخام</h3>
                  <button
                    type="button"
                    onClick={() => push(defaultMaterial)}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة مادة جديدة
                  </button>
                </div>

                {serviceData?.materials?.map((material, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">المادة {index + 1}</h4>
                      {serviceData.materials.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* اسم المادة */}
                      <div className="form-field">
                        <label className="form-label">اسم المادة</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.materialName`}
                          type="text"
                          className="form-input"
                          placeholder="اسم المادة الخام"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.materials.${index}.materialName`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* فئة المادة */}
                      <div className="form-field">
                        <label className="form-label">فئة المادة</label>
                        <Field as="select" name={`${namePrefix}.materials.${index}.category`} className="form-input">
                          <option value="">اختر الفئة</option>
                          {materialCategories.map((category) => (
                            <option key={category.value} value={category.value}>
                              {category.label}
                            </option>
                          ))}
                        </Field>
                      </div>

                      {/* الكمية */}
                      <div className="form-field">
                        <label className="form-label">الكمية</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.quantity`}
                          type="number"
                          min="0"
                          step="0.1"
                          className="form-input"
                          placeholder="الكمية"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.materials.${index}.quantity`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* وحدة القياس */}
                      <div className="form-field">
                        <label className="form-label">وحدة القياس</label>
                        <Field as="select" name={`${namePrefix}.materials.${index}.unit`} className="form-input">
                          <option value="">اختر الوحدة</option>
                          {units.map((unit) => (
                            <option key={unit.value} value={unit.value}>
                              {unit.label}
                            </option>
                          ))}
                        </Field>
                      </div>

                      {/* سعر الوحدة */}
                      <div className="form-field">
                        <label className="form-label">سعر الوحدة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.unitPrice`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="سعر الوحدة"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.materials.${index}.unitPrice`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* المجموع */}
                      <div className="form-field">
                        <label className="form-label">المجموع (جنيه)</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.total`}
                          type="number"
                          className="form-input bg-green-50 font-semibold text-green-700"
                          readOnly
                          placeholder="المجموع"
                        />
                        <div className="text-xs text-gray-500 mt-1">
                          يتم الحساب تلقائياً
                        </div>
                      </div>

                      {/* المورد */}
                      <div className="form-field">
                        <label className="form-label">اسم المورد</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.supplier`}
                          type="text"
                          className="form-input"
                          placeholder="اسم المورد"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.materials.${index}.supplier`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* رقم المورد */}
                      <div className="form-field">
                        <label className="form-label">رقم المورد</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.supplierPhone`}
                          type="tel"
                          className="form-input"
                          placeholder="رقم هاتف المورد"
                        />
                      </div>

                      {/* رمز المادة */}
                      <div className="form-field">
                        <label className="form-label">رمز المادة</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.materialCode`}
                          type="text"
                          className="form-input"
                          placeholder="رمز أو كود المادة"
                        />
                      </div>
                    </div>

                    {/* تفاصيل إضافية */}
                    <div className="mt-4 space-y-3">
                      {/* وصف المادة */}
                      <div className="form-field">
                        <label className="form-label">وصف المادة</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.description`}
                          as="textarea"
                          rows="2"
                          className="form-input"
                          placeholder="وصف تفصيلي للمادة"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* اللون */}
                        <div className="form-field">
                          <label className="form-label">اللون</label>
                          <Field
                            name={`${namePrefix}.materials.${index}.color`}
                            type="text"
                            className="form-input"
                            placeholder="لون المادة"
                          />
                        </div>

                        {/* الجودة */}
                        <div className="form-field">
                          <label className="form-label">مستوى الجودة</label>
                          <Field as="select" name={`${namePrefix}.materials.${index}.quality`} className="form-input">
                            <option value="">اختر الجودة</option>
                            <option value="premium">ممتاز</option>
                            <option value="high">عالي</option>
                            <option value="standard">عادي</option>
                            <option value="economy">اقتصادي</option>
                          </Field>
                        </div>

                        {/* تاريخ التسليم */}
                        <div className="form-field">
                          <label className="form-label">تاريخ التسليم المطلوب</label>
                          <Field
                            name={`${namePrefix}.materials.${index}.deliveryDate`}
                            type="date"
                            className="form-input"
                          />
                        </div>
                      </div>

                      {/* خيارات إضافية */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.materials.${index}.urgent`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">مستعجل</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.materials.${index}.qualityCheck`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">فحص جودة</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.materials.${index}.sampleRequired`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">عينة مطلوبة</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.materials.${index}.bulkDiscount`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">خصم كمية</span>
                        </label>
                      </div>

                      {/* ملاحظات */}
                      <div className="form-field">
                        <label className="form-label">ملاحظات خاصة</label>
                        <Field
                          name={`${namePrefix}.materials.${index}.notes`}
                          as="textarea"
                          rows="2"
                          className="form-input"
                          placeholder="أي ملاحظات خاصة بهذه المادة"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>

          {/* المجموع الكلي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-green-700">المجموع الكلي لجميع المواد:</span>
              <span className="text-2xl font-bold text-green-800">{calculateGrandTotal().toFixed(2)} جنيه</span>
            </div>
            <div className="text-sm text-green-600 mt-1">
              شامل جميع المواد الخام المطلوبة
            </div>
          </div>

          {/* معلومات عامة */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات عامة للطلبية</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* رقم طلبية الشراء */}
              <div className="form-field">
                <label className="form-label">رقم طلبية الشراء</label>
                <Field
                  name={`${namePrefix}.purchaseOrderNumber`}
                  type="text"
                  className="form-input"
                  placeholder="رقم الطلبية"
                />
              </div>

              {/* مسؤول المشتريات */}
              <div className="form-field">
                <label className="form-label">مسؤول المشتريات</label>
                <Field
                  name={`${namePrefix}.purchaseManager`}
                  type="text"
                  className="form-input"
                  placeholder="اسم مسؤول المشتريات"
                />
              </div>

              {/* طريقة الدفع */}
              <div className="form-field">
                <label className="form-label">طريقة الدفع</label>
                <Field as="select" name={`${namePrefix}.paymentMethod`} className="form-input">
                  <option value="">اختر طريقة الدفع</option>
                  <option value="cash">نقدي</option>
                  <option value="transfer">تحويل بنكي</option>
                  <option value="check">شيك</option>
                  <option value="credit">آجل</option>
                  <option value="installment">تقسيط</option>
                </Field>
              </div>

              {/* تاريخ التسليم العام */}
              <div className="form-field">
                <label className="form-label">تاريخ التسليم العام</label>
                <Field
                  name={`${namePrefix}.generalDeliveryDate`}
                  type="date"
                  className="form-input"
                />
              </div>
            </div>

            {/* شروط خاصة */}
            <div className="form-field">
              <label className="form-label">شروط خاصة</label>
              <Field
                name={`${namePrefix}.specialConditions`}
                as="textarea"
                rows="3"
                className="form-input"
                placeholder="أي شروط خاصة بطلبية المشتريات"
              />
            </div>

            {/* عنوان التسليم */}
            <div className="form-field">
              <label className="form-label">عنوان التسليم</label>
              <Field
                name={`${namePrefix}.deliveryAddress`}
                as="textarea"
                rows="2"
                className="form-input"
                placeholder="العنوان الكامل لتسليم المواد"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RawPurchasesServiceComponent;
