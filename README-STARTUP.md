# دليل تشغيل تطبيق إدارة الفواتير
# Invoice Management System Startup Guide

## ملفات التشغيل المتوفرة

### 1. `run-app.bat` - الملف الرئيسي للتشغيل
**الاستخدام:** انقر مرتين على الملف أو شغله من Command Prompt
**الوظيفة:** 
- فحص Node.js و npm
- تنظيف وتثبيت التبعيات
- تشغيل خادم التطوير
- معالجة الأخطاء الشائعة

### 2. `fix-dependencies.bat` - إصلاح مشاكل التبعيات
**متى تستخدمه:** عندما تواجه مشاكل في تثبيت أو تشغيل التبعيات
**الوظيفة:**
- حذف ملفات التثبيت القديمة
- تنظيف npm cache
- إعادة تثبيت التبعيات بطرق متعددة
- حل تضارب التبعيات

### 3. `diagnose.bat` - تشخيص المشاكل
**متى تستخدمه:** لفهم سبب عدم عمل التطبيق
**الوظيفة:**
- فحص النظام والبيئة
- فحص ملفات المشروع
- فحص التبعيات والمنافذ
- اقتراح حلول للمشاكل

### 4. `start-simple.bat` - تشغيل مبسط
**متى تستخدمه:** للتشغيل السريع بدون فحوصات معقدة
**الوظيفة:** تشغيل أساسي مع فحوصات بسيطة

## خطوات التشغيل الموصى بها

### للمرة الأولى:
1. شغل `diagnose.bat` للتأكد من سلامة النظام
2. إذا وجدت مشاكل، شغل `fix-dependencies.bat`
3. شغل `run-app.bat` لتشغيل التطبيق

### للاستخدام العادي:
- شغل `run-app.bat` مباشرة

### عند مواجهة مشاكل:
1. شغل `diagnose.bat` لتحديد المشكلة
2. شغل `fix-dependencies.bat` لإصلاح مشاكل التبعيات
3. أعد تشغيل `run-app.bat`

## المشاكل الشائعة والحلول

### المشكلة: "Node.js غير مثبت"
**الحل:** 
- حمل وثبت Node.js من https://nodejs.org/
- اختر النسخة LTS (الموصى بها)

### المشكلة: "خطأ في تثبيت التبعيات"
**الحل:**
1. شغل `fix-dependencies.bat`
2. تأكد من اتصال الإنترنت
3. شغل Command Prompt كمدير
4. تحقق من إعدادات Firewall/Antivirus

### المشكلة: "المنفذ 3000 مستخدم"
**الحل:**
1. أغلق أي تطبيق يستخدم المنفذ 3000
2. أو استخدم الأوامر:
   ```
   netstat -ano | find "3000"
   taskkill /PID [رقم العملية] /F
   ```

### المشكلة: "ملف package.json غير موجود"
**الحل:**
- تأكد من تشغيل الملف من مجلد المشروع الصحيح
- المجلد يجب أن يحتوي على ملفات المشروع

## معلومات إضافية

### متطلبات النظام:
- Windows 10/11
- Node.js 18+ 
- npm 8+
- 2GB مساحة فارغة على القرص

### المنافذ المستخدمة:
- 3000: خادم التطوير الرئيسي
- 3001: منفذ بديل (تلقائي)

### ملفات مهمة:
- `package.json`: تعريف المشروع والتبعيات
- `next.config.js`: إعدادات Next.js
- `src/`: مجلد الكود المصدري
- `node_modules/`: مجلد التبعيات

## الدعم والمساعدة

إذا واجهت مشاكل لم تُحل بالطرق أعلاه:

1. شغل `diagnose.bat` واحفظ النتائج
2. تحقق من رسائل الخطأ في Command Prompt
3. تأكد من تحديث Node.js و npm
4. جرب إعادة تشغيل الكمبيوتر

---

**ملاحظة:** جميع الملفات تدعم اللغة العربية وتعرض الرسائل بوضوح.
