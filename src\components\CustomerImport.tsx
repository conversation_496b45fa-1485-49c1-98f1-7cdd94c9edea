/**
 * مكون استيراد العملاء من ملفات Excel/CSV
 */

import React, { useState, useRef } from 'react';
import { Client } from '@/types';
import { useCustomerStore } from '@/store/customerStore';
import { validateCustomerCode } from '@/utils/customerCodeGenerator';

interface ImportResult {
  success: boolean;
  imported: number;
  errors: string[];
  duplicates: string[];
  data?: Client[];
}

interface CustomerImportProps {
  onImportComplete?: (result: ImportResult) => void;
  onClose?: () => void;
}

const CustomerImport: React.FC<CustomerImportProps> = ({ onImportComplete, onClose }) => {
  const { addMultipleCustomers, customers } = useCustomerStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // قالب البيانات المطلوبة
  const requiredFields = [
    'اسم العميل',
    'رقم الهاتف',
    'كود العميل',
    'اسم المصنع/البراند',
    'الموديل',
    'عدد المقاسات',
    'المبالغ المدفوعة',
    'المبالغ المتبقية',
    'مصاريف الطباعة',
    'وسيلة التواصل',
    'اسم السيلز',
    'التصنيف الأولي',
    'المحافظة',
    'الخدمات'
  ];

  const downloadTemplate = () => {
    const csvContent = [
      requiredFields.join(','),
      'أحمد محمد,01234567890,p013621,براند تجريبي,موديل 1,5,1000,500,100,whatsapp,سيلز 1,A,القاهرة,"استشارات,باترون"',
      'فاطمة علي,01987654321,p013622,براند آخر,موديل 2,3,2000,0,0,meta,سيلز 2,B,الجيزة,"تصنيع,شحن"'
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'قالب_استيراد_العملاء.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const parseCSV = (text: string): any[] => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = [];
      let current = '';
      let inQuotes = false;

      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      if (values.length === headers.length) {
        const row: any = {};
        headers.forEach((header, index) => {
          row[header] = values[index]?.replace(/"/g, '') || '';
        });
        data.push(row);
      }
    }

    return data;
  };

  const validateImportData = (data: any[]): { valid: Client[]; errors: string[] } => {
    const valid: Client[] = [];
    const errors: string[] = [];

    data.forEach((row, index) => {
      const rowNumber = index + 2; // +2 because of header and 0-based index
      
      // التحقق من الحقول المطلوبة
      if (!row['اسم العميل']?.trim()) {
        errors.push(`الصف ${rowNumber}: اسم العميل مطلوب`);
        return;
      }
      
      if (!row['رقم الهاتف']?.trim()) {
        errors.push(`الصف ${rowNumber}: رقم الهاتف مطلوب`);
        return;
      }

      // التحقق من كود العميل
      const clientCode = row['كود العميل']?.trim();
      if (clientCode) {
        const validation = validateCustomerCode(clientCode);
        if (!validation.isValid) {
          errors.push(`الصف ${rowNumber}: ${validation.error}`);
          return;
        }
        
        // التحقق من عدم التكرار
        const existingCustomer = customers.find(c => c.clientCode === clientCode);
        if (existingCustomer) {
          errors.push(`الصف ${rowNumber}: كود العميل ${clientCode} مستخدم بالفعل`);
          return;
        }
      }

      // تحويل البيانات لتنسيق Client
      const services = row['الخدمات']?.split(',').map((s: string) => s.trim()).filter(Boolean) || [];
      const contactMethod = ['whatsapp', 'meta', 'meeting', 'phone'].includes(row['وسيلة التواصل']) 
        ? row['وسيلة التواصل'] 
        : 'whatsapp';
      const initialCategory = ['A', 'B', 'C'].includes(row['التصنيف الأولي']) 
        ? row['التصنيف الأولي'] 
        : 'A';

      const client: Client = {
        name: row['اسم العميل'].trim(),
        phone: row['رقم الهاتف'].trim(),
        clientCode: clientCode || undefined,
        businessName: row['اسم المصنع/البراند']?.trim() || '',
        province: row['المحافظة']?.trim() || '',
        services,
        contactMethod: contactMethod as any,
        salesperson: row['اسم السيلز']?.trim() || '',
        initialCategory: initialCategory as any,
      };

      valid.push(client);
    });

    return { valid, errors };
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setImportResult(null);
    setPreviewData([]);
    setShowPreview(false);

    try {
      const text = await file.text();
      const data = parseCSV(text);
      
      if (data.length === 0) {
        throw new Error('الملف فارغ أو تنسيقه غير صحيح');
      }

      setPreviewData(data);
      setShowPreview(true);
    } catch (error) {
      setImportResult({
        success: false,
        imported: 0,
        errors: [error instanceof Error ? error.message : 'خطأ في قراءة الملف'],
        duplicates: []
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const processImport = async () => {
    if (previewData.length === 0) return;

    setIsProcessing(true);
    const { valid, errors } = validateImportData(previewData);
    
    let imported = 0;
    const importErrors: string[] = [...errors];
    const duplicates: string[] = [];

    // استيراد العملاء الصحيحين
    if (valid.length > 0) {
      try {
        const result = await addMultipleCustomers(valid);
        imported = result.imported || 0;
        if (result.errors) {
          importErrors.push(...result.errors);
        }
        if (!result.success && result.error) {
          importErrors.push(result.error);
        }
      } catch (error) {
        importErrors.push(`خطأ في استيراد العملاء: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }

    const result: ImportResult = {
      success: imported > 0,
      imported,
      errors: importErrors,
      duplicates,
      data: valid
    };

    setImportResult(result);
    setShowPreview(false);
    setIsProcessing(false);
    
    onImportComplete?.(result);
  };

  const resetImport = () => {
    setImportResult(null);
    setPreviewData([]);
    setShowPreview(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">استيراد العملاء</h3>
            <p className="text-sm text-gray-600">استيراد بيانات العملاء من ملف CSV</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
            </svg>
          </button>
        )}
      </div>

      {!showPreview && !importResult && (
        <div className="space-y-6">
          {/* تحميل القالب */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
              </svg>
              <div className="flex-1">
                <h4 className="font-medium text-blue-800 mb-2">تحميل القالب</h4>
                <p className="text-sm text-blue-700 mb-3">
                  قم بتحميل قالب CSV لمعرفة التنسيق المطلوب للبيانات
                </p>
                <button
                  onClick={downloadTemplate}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                  تحميل القالب
                </button>
              </div>
            </div>
          </div>

          {/* رفع الملف */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
            </svg>
            <h4 className="text-lg font-medium text-gray-800 mb-2">رفع ملف CSV</h4>
            <p className="text-gray-600 mb-4">اختر ملف CSV يحتوي على بيانات العملاء</p>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isProcessing ? 'جاري المعالجة...' : 'اختيار ملف'}
            </button>
          </div>
        </div>
      )}

      {showPreview && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-800">معاينة البيانات ({previewData.length} صف)</h4>
            <div className="flex gap-2">
              <button
                onClick={resetImport}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={processImport}
                disabled={isProcessing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isProcessing ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}
              </button>
            </div>
          </div>
          
          <div className="max-h-64 overflow-auto border border-gray-200 rounded-lg">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(previewData[0] || {}).slice(0, 5).map((key) => (
                    <th key={key} className="px-3 py-2 text-right font-medium text-gray-700">
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {previewData.slice(0, 10).map((row, index) => (
                  <tr key={index} className="border-t border-gray-200">
                    {Object.values(row).slice(0, 5).map((value: any, cellIndex) => (
                      <td key={cellIndex} className="px-3 py-2 text-gray-900">
                        {String(value).substring(0, 30)}
                        {String(value).length > 30 && '...'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {importResult && (
        <div className="space-y-4">
          <div className={`p-4 rounded-lg ${importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center gap-2 mb-2">
              <svg className={`w-5 h-5 ${importResult.success ? 'text-green-600' : 'text-red-600'}`} fill="currentColor" viewBox="0 0 20 20">
                {importResult.success ? (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                ) : (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
                )}
              </svg>
              <h4 className={`font-medium ${importResult.success ? 'text-green-800' : 'text-red-800'}`}>
                نتيجة الاستيراد
              </h4>
            </div>
            <p className={`text-sm ${importResult.success ? 'text-green-700' : 'text-red-700'}`}>
              تم استيراد {importResult.imported} عميل بنجاح
              {importResult.errors.length > 0 && ` مع ${importResult.errors.length} خطأ`}
            </p>
          </div>

          {importResult.errors.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 className="font-medium text-yellow-800 mb-2">الأخطاء:</h5>
              <ul className="text-sm text-yellow-700 space-y-1">
                {importResult.errors.slice(0, 10).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
                {importResult.errors.length > 10 && (
                  <li>... و {importResult.errors.length - 10} أخطاء أخرى</li>
                )}
              </ul>
            </div>
          )}

          <div className="flex gap-2">
            <button
              onClick={resetImport}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              استيراد ملف آخر
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إغلاق
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerImport;
