/**
 * مكون استيراد العملاء من ملفات Excel/CSV
 */

import React, { useState, useRef } from 'react';
import { Client } from '@/types';
import { useCustomerStore } from '@/store/customerStore';
import { validateCustomerCode } from '@/utils/customerCodeGenerator';

interface ImportResult {
  success: boolean;
  imported: number;
  errors: string[];
  duplicates: string[];
  data?: Client[];
}

interface CustomerImportProps {
  onImportComplete?: (result: ImportResult) => void;
  onClose?: () => void;
}

const CustomerImport: React.FC<CustomerImportProps> = ({ onImportComplete, onClose }) => {
  const { addMultipleCustomers, customers } = useCustomerStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // قالب البيانات المطلوبة
  const requiredFields = [
    'اسم العميل',
    'رقم الهاتف',
    'كود العميل',
    'اسم المصنع/البراند',
    'الموديل',
    'عدد المقاسات',
    'المبالغ المدفوعة',
    'المبالغ المتبقية',
    'مصاريف الطباعة',
    'وسيلة التواصل',
    'اسم السيلز',
    'التصنيف الأولي',
    'المحافظة',
    'الخدمات'
  ];

  const downloadTemplate = () => {
    // إنشاء CSV بتنسيق صحيح للعربية - استخدام فواصل منقوطة للتوافق مع Excel العربي
    const separator = ';'; // فاصل منقوط للتوافق مع Excel العربي
    const headers = requiredFields.join(separator);

    // إنشاء صفوف العينة بدون اقتباس إضافي لتجنب مشاكل التنسيق
    const sampleRows = [
      [
        'أحمد محمد',
        '01234567890',
        'p013621',
        'براند تجريبي',
        'موديل 1',
        '5',
        '1000',
        '500',
        '100',
        'whatsapp',
        'سيلز 1',
        'A',
        'القاهرة',
        'استشارات|باترون' // استخدام | بدلاً من , لتجنب تضارب الفواصل
      ].join(separator),
      [
        'فاطمة علي',
        '01987654321',
        'p013622',
        'براند آخر',
        'موديل 2',
        '3',
        '2000',
        '0',
        '0',
        'meta',
        'سيلز 2',
        'B',
        'الجيزة',
        'تصنيع|شحن' // استخدام | بدلاً من , لتجنب تضارب الفواصل
      ].join(separator)
    ];

    const csvContent = [headers, ...sampleRows].join('\r\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'قالب_استيراد_العملاء.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const parseCSV = (text: string): any[] => {
    try {
      // تنظيف النص وتقسيمه إلى أسطر
      const lines = text
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .split('\n')
        .filter(line => line.trim());

      if (lines.length < 2) {
        console.error('ملف CSV يجب أن يحتوي على سطر العناوين وسطر واحد على الأقل من البيانات');
        return [];
      }

      // تحليل سطر العناوين
      const headers = parseCSVLine(lines[0]);
      if (headers.length === 0) {
        console.error('لا يمكن تحليل سطر العناوين');
        return [];
      }

      const data = [];

      // تحليل بقية الأسطر
      for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]);

        if (values.length > 0) {
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          data.push(row);
        }
      }

      console.log('تم تحليل CSV بنجاح:', { headers, rowCount: data.length });
      return data;
    } catch (error) {
      console.error('خطأ في تحليل CSV:', error);
      return [];
    }
  };

  // دالة مساعدة لتحليل سطر CSV واحد - تدعم الفواصل العادية والمنقوطة
  const parseCSVLine = (line: string): string[] => {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    // تحديد نوع الفاصل المستخدم في السطر
    const separator = line.includes(';') ? ';' : ',';

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // علامة اقتباس مضاعفة داخل النص
          current += '"';
          i += 2;
        } else {
          // بداية أو نهاية النص المقتبس
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === separator && !inQuotes) {
        // فاصل خارج النص المقتبس
        values.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    // إضافة القيمة الأخيرة
    values.push(current.trim());

    return values;
  };

  const validateImportData = (data: any[]): { valid: Client[]; errors: string[] } => {
    const valid: Client[] = [];
    const errors: string[] = [];
    const seenCodes = new Set<string>();
    const seenPhones = new Set<string>();

    console.log('بدء التحقق من صحة البيانات، عدد الصفوف:', data.length);

    data.forEach((row, index) => {
      const rowNumber = index + 1; // +1 لأن البيانات تبدأ من الصف الأول (بدون العناوين)

      try {
        // التحقق من الحقول المطلوبة
        const name = row['اسم العميل']?.trim();
        if (!name) {
          errors.push(`الصف ${rowNumber}: اسم العميل مطلوب`);
          return;
        }

        const phone = row['رقم الهاتف']?.trim();
        if (!phone) {
          errors.push(`الصف ${rowNumber}: رقم الهاتف مطلوب`);
          return;
        }

        // التحقق من صحة رقم الهاتف
        const phoneRegex = /^[0-9+\-\s()]{10,15}$/;
        if (!phoneRegex.test(phone)) {
          errors.push(`الصف ${rowNumber}: رقم الهاتف غير صحيح (${phone})`);
          return;
        }

        // التحقق من عدم تكرار رقم الهاتف في الملف
        if (seenPhones.has(phone)) {
          errors.push(`الصف ${rowNumber}: رقم الهاتف ${phone} مكرر في الملف`);
          return;
        }
        seenPhones.add(phone);

        // التحقق من عدم تكرار رقم الهاتف في قاعدة البيانات
        const existingCustomerByPhone = customers.find(c => c.phone === phone);
        if (existingCustomerByPhone) {
          errors.push(`الصف ${rowNumber}: رقم الهاتف ${phone} مستخدم بالفعل للعميل ${existingCustomerByPhone.name}`);
          return;
        }

        // التحقق من كود العميل
        const clientCode = row['كود العميل']?.trim();
        if (clientCode) {
          const validation = validateCustomerCode(clientCode);
          if (!validation.isValid) {
            errors.push(`الصف ${rowNumber}: ${validation.error}`);
            return;
          }

          // التحقق من عدم تكرار الكود في الملف
          if (seenCodes.has(clientCode)) {
            errors.push(`الصف ${rowNumber}: كود العميل ${clientCode} مكرر في الملف`);
            return;
          }
          seenCodes.add(clientCode);

          // التحقق من عدم التكرار في قاعدة البيانات
          const existingCustomer = customers.find(c => c.clientCode === clientCode);
          if (existingCustomer) {
            errors.push(`الصف ${rowNumber}: كود العميل ${clientCode} مستخدم بالفعل للعميل ${existingCustomer.name}`);
            return;
          }
        }

        // تحويل البيانات لتنسيق Client
        const servicesText = row['الخدمات']?.trim() || '';
        // دعم كل من الفاصلة والرمز | لفصل الخدمات
        const serviceSeparator = servicesText.includes('|') ? '|' : ',';
        const services = servicesText ? servicesText.split(serviceSeparator).map((s: string) => s.trim()).filter(Boolean) : [];

        const contactMethodValue = row['وسيلة التواصل']?.trim().toLowerCase();
        const contactMethod = ['whatsapp', 'meta', 'meeting', 'phone'].includes(contactMethodValue)
          ? contactMethodValue
          : 'whatsapp';

        const initialCategoryValue = row['التصنيف الأولي']?.trim().toUpperCase();
        const initialCategory = ['A', 'B', 'C'].includes(initialCategoryValue)
          ? initialCategoryValue
          : 'A';

        const client: Client = {
          name: name,
          phone: phone,
          clientCode: clientCode || undefined,
          businessName: row['اسم المصنع/البراند']?.trim() || '',
          province: row['المحافظة']?.trim() || '',
          services,
          contactMethod: contactMethod as any,
          salesperson: row['اسم السيلز']?.trim() || '',
          initialCategory: initialCategory as any,
        };

        valid.push(client);
        console.log(`الصف ${rowNumber}: تم التحقق بنجاح من العميل ${name}`);
      } catch (error) {
        errors.push(`الصف ${rowNumber}: خطأ في معالجة البيانات - ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    });

    console.log('انتهى التحقق من البيانات:', { valid: valid.length, errors: errors.length });
    return { valid, errors };
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setImportResult({
        success: false,
        imported: 0,
        errors: ['يرجى اختيار ملف CSV فقط'],
        duplicates: []
      });
      return;
    }

    setIsProcessing(true);
    setImportResult(null);
    setPreviewData([]);
    setShowPreview(false);

    try {
      console.log('بدء قراءة الملف:', file.name, 'الحجم:', file.size, 'bytes');

      const text = await file.text();
      console.log('تم قراءة الملف، طول النص:', text.length);

      if (!text.trim()) {
        throw new Error('الملف فارغ');
      }

      const data = parseCSV(text);
      console.log('نتيجة تحليل CSV:', data);

      if (data.length === 0) {
        throw new Error('لا يمكن تحليل الملف. تأكد من أن الملف بتنسيق CSV صحيح ويحتوي على البيانات المطلوبة.');
      }

      // التحقق من وجود العناوين المطلوبة
      const firstRow = data[0];
      const missingFields = requiredFields.filter(field => !(field in firstRow));

      if (missingFields.length > 0) {
        throw new Error(`الحقول التالية مفقودة في الملف: ${missingFields.join(', ')}`);
      }

      console.log('تم تحليل البيانات بنجاح، عدد الصفوف:', data.length);
      setPreviewData(data);
      setShowPreview(true);
    } catch (error) {
      console.error('خطأ في معالجة الملف:', error);
      setImportResult({
        success: false,
        imported: 0,
        errors: [error instanceof Error ? error.message : 'خطأ غير معروف في قراءة الملف'],
        duplicates: []
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const processImport = async () => {
    if (previewData.length === 0) return;

    setIsProcessing(true);
    const { valid, errors } = validateImportData(previewData);
    
    let imported = 0;
    const importErrors: string[] = [...errors];
    const duplicates: string[] = [];

    // استيراد العملاء الصحيحين
    if (valid.length > 0) {
      try {
        const result = await addMultipleCustomers(valid);
        imported = result.imported || 0;
        if (result.errors) {
          importErrors.push(...result.errors);
        }
        if (!result.success && result.error) {
          importErrors.push(result.error);
        }
      } catch (error) {
        importErrors.push(`خطأ في استيراد العملاء: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }

    const result: ImportResult = {
      success: imported > 0,
      imported,
      errors: importErrors,
      duplicates,
      data: valid
    };

    setImportResult(result);
    setShowPreview(false);
    setIsProcessing(false);
    
    onImportComplete?.(result);
  };

  const resetImport = () => {
    setImportResult(null);
    setPreviewData([]);
    setShowPreview(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">استيراد العملاء</h3>
            <p className="text-sm text-gray-600">استيراد بيانات العملاء من ملف CSV</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
            </svg>
          </button>
        )}
      </div>

      {!showPreview && !importResult && (
        <div className="space-y-6">
          {/* تحميل القالب */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
              </svg>
              <div className="flex-1">
                <h4 className="font-medium text-blue-800 mb-2">تحميل القالب</h4>
                <p className="text-sm text-blue-700 mb-3">
                  قم بتحميل قالب CSV لمعرفة التنسيق المطلوب للبيانات
                </p>
                <button
                  onClick={downloadTemplate}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                  </svg>
                  تحميل القالب
                </button>
              </div>
            </div>
          </div>

          {/* رفع الملف */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
            </svg>
            <h4 className="text-lg font-medium text-gray-800 mb-2">رفع ملف CSV</h4>
            <p className="text-gray-600 mb-4">اختر ملف CSV يحتوي على بيانات العملاء</p>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isProcessing ? 'جاري المعالجة...' : 'اختيار ملف'}
            </button>
          </div>
        </div>
      )}

      {showPreview && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-800">معاينة البيانات ({previewData.length} صف)</h4>
            <div className="flex gap-2">
              <button
                onClick={resetImport}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={processImport}
                disabled={isProcessing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isProcessing ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}
              </button>
            </div>
          </div>
          
          <div className="max-h-64 overflow-auto border border-gray-200 rounded-lg">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(previewData[0] || {}).slice(0, 5).map((key) => (
                    <th key={key} className="px-3 py-2 text-right font-medium text-gray-700">
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {previewData.slice(0, 10).map((row, index) => (
                  <tr key={index} className="border-t border-gray-200">
                    {Object.values(row).slice(0, 5).map((value: any, cellIndex) => (
                      <td key={cellIndex} className="px-3 py-2 text-gray-900">
                        {String(value).substring(0, 30)}
                        {String(value).length > 30 && '...'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {importResult && (
        <div className="space-y-4">
          <div className={`p-4 rounded-lg ${importResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center gap-2 mb-2">
              <svg className={`w-5 h-5 ${importResult.success ? 'text-green-600' : 'text-red-600'}`} fill="currentColor" viewBox="0 0 20 20">
                {importResult.success ? (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                ) : (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"/>
                )}
              </svg>
              <h4 className={`font-medium ${importResult.success ? 'text-green-800' : 'text-red-800'}`}>
                نتيجة الاستيراد
              </h4>
            </div>
            <p className={`text-sm ${importResult.success ? 'text-green-700' : 'text-red-700'}`}>
              تم استيراد {importResult.imported} عميل بنجاح
              {importResult.errors.length > 0 && ` مع ${importResult.errors.length} خطأ`}
            </p>
          </div>

          {importResult.errors.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 className="font-medium text-yellow-800 mb-2">الأخطاء:</h5>
              <ul className="text-sm text-yellow-700 space-y-1">
                {importResult.errors.slice(0, 10).map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
                {importResult.errors.length > 10 && (
                  <li>... و {importResult.errors.length - 10} أخطاء أخرى</li>
                )}
              </ul>
            </div>
          )}

          <div className="flex gap-2">
            <button
              onClick={resetImport}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              استيراد ملف آخر
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إغلاق
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerImport;
