"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicePreview.tsx":
/*!*******************************************!*\
  !*** ./src/components/InvoicePreview.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/settingsStore */ \"(app-pages-browser)/./src/store/settingsStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// دالة عرض رسائل التأكيد المحسنة\nconst showSuccessMessage = (message)=>{\n    // إنشاء عنصر toast مؤقت\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    // إزالة الرسالة بعد 3 ثوان\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 3000);\n};\nconst showErrorMessage = (message)=>{\n    const toast = document.createElement(\"div\");\n    toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n    toast.innerHTML = '\\n    <div class=\"flex items-center gap-2\">\\n      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n      </svg>\\n      <span>'.concat(message, \"</span>\\n    </div>\\n  \");\n    document.body.appendChild(toast);\n    setTimeout(()=>{\n        toast.style.transform = \"translateX(100%)\";\n        setTimeout(()=>{\n            if (document.body.contains(toast)) {\n                document.body.removeChild(toast);\n            }\n        }, 300);\n    }, 4000);\n};\nconst InvoicePreview = (param)=>{\n    let { invoice, onEdit, onExportPDF } = param;\n    _s();\n    const [showCompanySettings, setShowCompanySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPOSInvoice, setShowPOSInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { settings, loadSettings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore)();\n    // Load company settings on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        loadSettings\n    ]);\n    // دالة تصدير PDF محسنة مع رسائل التأكيد\n    const handleExportPDF = async ()=>{\n        setIsExporting(true);\n        try {\n            // إضافة كلاس التحسين للعنصر\n            const element = document.getElementById(\"invoice-content\");\n            if (element) {\n                element.classList.add(\"pdf-optimized\");\n            }\n            // محاولة الطريقة المحسنة أولاً\n            await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n            showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            // إزالة كلاس التحسين\n            if (element) {\n                element.classList.remove(\"pdf-optimized\");\n            }\n        } catch (error) {\n            console.error(\"PDF Export Error:\", error);\n            // محاولة الطريقة البديلة\n            try {\n                await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDFFromHTML)(invoice);\n                showSuccessMessage(\"تم تصدير PDF بنجاح! \\uD83C\\uDF89\");\n            } catch (fallbackError) {\n                console.error(\"Fallback PDF Export Error:\", fallbackError);\n                showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n            }\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن/تعبئة\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getPaymentMethodName = (method)=>{\n        const methods = {\n            cash: \"نقدي\",\n            card: \"بطاقة\",\n            transfer: \"تحويل\",\n            installment: \"تقسيط\"\n        };\n        return methods[method] || method;\n    };\n    const getContactMethodName = (method)=>{\n        const methods = {\n            whatsapp: \"واتساب\",\n            meta: \"ميتا\",\n            meeting: \"مقابلة\",\n            phone: \"هاتف\"\n        };\n        return methods[method] || method;\n    };\n    // Generate client code based on name and phone\n    const generateClientCode = (name, phone)=>{\n        const nameCode = name.substring(0, 3).toUpperCase();\n        const phoneCode = phone.substring(-4);\n        return \"\".concat(nameCode, \"-\").concat(phoneCode);\n    };\n    // دالة فتح صفحة الطباعة في تبويب جديد محسنة\n    const openPrintView = (invoice)=>{\n        try {\n            // إنشاء HTML للفاتورة مع تنسيق الطباعة\n            const printHTML = createPrintableInvoiceHTML(invoice);\n            // فتح نافذة جديدة\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(printHTML);\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم فتح حوار الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        showSuccessMessage(\"تم فتح صفحة الطباعة بنجاح! \\uD83D\\uDDA8️\");\n                    }, 500);\n                };\n            } else {\n                showErrorMessage(\"فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.\");\n            }\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تحضير صفحة الطباعة.\");\n        }\n    };\n    // دالة إنشاء HTML للطباعة\n    const createPrintableInvoiceHTML = (invoice)=>{\n        const logoSrc = settings.companyLogo || \"\";\n        return '\\n<!DOCTYPE html>\\n<html lang=\"ar\" dir=\"rtl\">\\n<head>\\n    <meta charset=\"UTF-8\">\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n    <title>فاتورة '.concat(invoice.id, \" - \").concat(invoice.client.name, '</title>\\n    <style>\\n        @page {\\n            size: A4;\\n            margin: 15mm;\\n        }\\n\\n        * {\\n            margin: 0;\\n            padding: 0;\\n            box-sizing: border-box;\\n        }\\n\\n        body {\\n            font-family: Arial, sans-serif;\\n            direction: rtl;\\n            line-height: 1.4;\\n            color: #333;\\n            background: white;\\n        }\\n\\n        .invoice-container {\\n            max-width: 100%;\\n            margin: 0 auto;\\n            background: white;\\n            padding: 20px;\\n        }\\n\\n        /* Header Section */\\n        .header {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-bottom: 30px;\\n            padding: 20px;\\n            background: linear-gradient(to left, #f0f8ff, white);\\n            border: 2px solid #2196f3;\\n            border-radius: 10px;\\n        }\\n\\n        .invoice-info {\\n            flex: 1;\\n            text-align: right;\\n        }\\n\\n        .invoice-info h2 {\\n            color: #2196f3;\\n            font-size: 24px;\\n            margin-bottom: 15px;\\n            font-weight: bold;\\n        }\\n\\n        .info-box {\\n            background: transparent;\\n            padding: 15px;\\n            border: none;\\n            box-shadow: none;\\n            max-width: 400px;\\n        }\\n\\n        .info-row {\\n            margin-bottom: 8px;\\n            font-size: 14px;\\n        }\\n\\n        .info-label {\\n            font-weight: bold;\\n            color: #555;\\n        }\\n\\n        .info-value {\\n            color: #333;\\n        }\\n\\n        .logo-section {\\n            flex-shrink: 0;\\n            text-align: center;\\n            margin-left: 30px;\\n        }\\n\\n        .logo-section img {\\n            max-width: 180px;\\n            max-height: 180px;\\n            object-fit: contain;\\n        }\\n\\n        .company-description {\\n            margin-top: 15px;\\n            font-size: 13px;\\n            color: #666;\\n            max-width: 200px;\\n            line-height: 1.5;\\n        }\\n\\n        /* Services Table */\\n        .services-section {\\n            margin-bottom: 30px;\\n        }\\n\\n        .section-title {\\n            font-size: 18px;\\n            font-weight: bold;\\n            color: #333;\\n            margin-bottom: 15px;\\n            padding-bottom: 8px;\\n            border-bottom: 2px solid #2196f3;\\n        }\\n\\n        .services-table {\\n            width: 100%;\\n            border-collapse: separate;\\n            border-spacing: 0;\\n            border: 1px solid #ddd;\\n            margin: 15px 0;\\n            table-layout: fixed;\\n        }\\n\\n        .services-table th {\\n            background-color: #2196f3;\\n            color: white;\\n            padding: 12px 8px;\\n            text-align: center;\\n            font-weight: bold;\\n            border: 1px solid #ddd;\\n            font-size: 13px;\\n        }\\n\\n        .services-table td {\\n            padding: 10px 8px;\\n            border: 1px solid #ddd;\\n            text-align: center;\\n            vertical-align: middle;\\n            word-wrap: break-word;\\n            font-size: 12px;\\n            line-height: 1.3;\\n        }\\n\\n        .services-table tr:nth-child(even) {\\n            background-color: #f9f9f9;\\n        }\\n\\n        .services-table tr:nth-child(odd) {\\n            background-color: white;\\n        }\\n\\n        .service-type {\\n            color: #2196f3;\\n            font-weight: bold;\\n        }\\n\\n        .service-details {\\n            text-align: right;\\n            white-space: normal;\\n        }\\n\\n        .service-price {\\n            font-weight: bold;\\n            color: #2196f3;\\n        }\\n\\n        .service-total {\\n            font-weight: bold;\\n            color: #4caf50;\\n        }\\n\\n        /* Total Summary */\\n        .total-section {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: center;\\n            background: linear-gradient(to right, #e8f5e8, #f0f8f0);\\n            padding: 20px;\\n            border-radius: 10px;\\n            border: 2px solid #4caf50;\\n            margin-bottom: 30px;\\n        }\\n\\n        .total-details {\\n            text-align: right;\\n        }\\n\\n        .total-row {\\n            display: flex;\\n            justify-content: space-between;\\n            margin-bottom: 8px;\\n            min-width: 300px;\\n        }\\n\\n        .total-label {\\n            font-weight: bold;\\n            color: #555;\\n            margin-left: 20px;\\n        }\\n\\n        .total-value {\\n            font-weight: bold;\\n        }\\n\\n        .grand-total {\\n            font-size: 20px;\\n            color: #4caf50;\\n        }\\n\\n        .paid-amount {\\n            font-size: 16px;\\n            color: #2196f3;\\n        }\\n\\n        .remaining-amount {\\n            font-size: 16px;\\n        }\\n\\n        .remaining-positive {\\n            color: #f44336;\\n        }\\n\\n        .remaining-zero {\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp {\\n            background: transparent;\\n            color: #333;\\n            padding: 15px;\\n            border: 3px solid #333;\\n            border-radius: 15px;\\n            text-align: center;\\n            transform: rotate(3deg);\\n            box-shadow: 0 4px 8px rgba(0,0,0,0.1);\\n            min-width: 120px;\\n        }\\n\\n        .payment-stamp.paid-full {\\n            border-color: #4caf50;\\n            color: #4caf50;\\n        }\\n\\n        .payment-stamp.paid-partial {\\n            border-color: #ff9800;\\n            color: #ff9800;\\n        }\\n\\n        .payment-stamp.unpaid {\\n            border-color: #f44336;\\n            color: #f44336;\\n        }\\n\\n        .stamp-title {\\n            font-size: 14px;\\n            font-weight: bold;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-status {\\n            font-size: 12px;\\n            font-weight: bold;\\n        }\\n\\n        /* Footer */\\n        .footer {\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: flex-start;\\n            margin-top: 40px;\\n            padding: 20px;\\n            background: white;\\n            border-top: 1px solid #e9ecef;\\n            min-height: 120px;\\n            direction: rtl;\\n        }\\n\\n        .footer-left {\\n            width: 45%;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 10px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .contact-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .footer-right {\\n            width: 50%;\\n            text-align: right;\\n            direction: rtl;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: flex-end;\\n            justify-content: flex-start;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #666;\\n            line-height: 1.8;\\n            margin-bottom: 15px;\\n            text-align: right;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .company-stamp {\\n            position: relative;\\n            transform: none;\\n            opacity: 1;\\n            border-radius: 8px;\\n            padding: 10px;\\n            width: 160px;\\n            height: auto;\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            margin-bottom: 15px;\\n            background: white;\\n            border: 1px solid #ddd;\\n            box-shadow: none;\\n        }\\n\\n        .company-stamp img {\\n            width: 140px;\\n            height: auto;\\n            max-height: 100px;\\n            object-fit: contain;\\n            border-radius: 4px;\\n        }\\n\\n        .company-stamp .stamp-content {\\n            background: rgba(33, 150, 243, 0.1);\\n            border: 2px solid #2196f3;\\n            border-radius: 8px;\\n            width: 100%;\\n            height: 100px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: center;\\n            justify-content: center;\\n        }\\n\\n        .stamp-content {\\n            text-align: center;\\n            color: #2196f3;\\n            font-weight: bold;\\n            font-size: 14px;\\n            line-height: 1.2;\\n            direction: rtl;\\n        }\\n\\n        .stamp-title {\\n            font-size: 16px;\\n            margin-bottom: 5px;\\n        }\\n\\n        .stamp-subtitle {\\n            font-size: 12px;\\n            opacity: 0.8;\\n        }\\n\\n        .headquarters-info {\\n            font-size: 12px;\\n            color: #555;\\n            text-align: right;\\n            line-height: 1.6;\\n            direction: rtl;\\n        }\\n\\n        .headquarters-info strong {\\n            color: #333;\\n            font-size: 13px;\\n        }\\n\\n        .thank-you {\\n            text-align: center;\\n            margin-top: 20px;\\n            padding: 15px;\\n            background: #f8f9fa;\\n            border: 1px solid #e9ecef;\\n            border-radius: 8px;\\n            color: #2196f3;\\n            font-size: 14px;\\n            font-weight: bold;\\n            direction: rtl;\\n        }\\n\\n        /* Print specific styles */\\n        @media print {\\n            body {\\n                -webkit-print-color-adjust: exact;\\n                print-color-adjust: exact;\\n                margin: 0;\\n                padding: 0;\\n            }\\n\\n            .invoice-container {\\n                padding: 0;\\n                margin: 0;\\n                max-width: 100%;\\n            }\\n\\n            .header {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .services-table {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n            }\\n\\n            .total-section {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-bottom: 20px;\\n            }\\n\\n            .footer {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                position: relative;\\n                clear: both;\\n                margin-top: 30px;\\n            }\\n\\n            .footer-left,\\n            .footer-right {\\n                float: none;\\n                display: inline-block;\\n                vertical-align: top;\\n            }\\n\\n            .company-stamp {\\n                transform: none;\\n            }\\n\\n            .thank-you {\\n                break-inside: avoid;\\n                page-break-inside: avoid;\\n                margin-top: 15px;\\n            }\\n        }\\n    </style>\\n</head>\\n<body>\\n    <div class=\"invoice-container\">\\n        <!-- Header Section -->\\n        <div class=\"header\">\\n            <div class=\"invoice-info\">\\n                <h2>فاتورة خدمات</h2>\\n                <div class=\"info-box\">\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الفاتورة: </span>\\n                        <span class=\"info-value\">').concat(invoice.id, '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">التاريخ: </span>\\n                        <span class=\"info-value\">').concat(formatDate(invoice.createdAt), '</span>\\n                    </div>\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">اسم العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.name, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.businessName ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">الاسم التجاري: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.businessName, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">رقم الهاتف: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.phone, \"</span>\\n                    </div>\\n                    \").concat(invoice.client.province ? '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">المحافظة: </span>\\n                        <span class=\"info-value\">'.concat(invoice.client.province, \"</span>\\n                    </div>\\n                    \") : \"\", '\\n                    <div class=\"info-row\">\\n                        <span class=\"info-label\">كود العميل: </span>\\n                        <span class=\"info-value\">').concat(invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone), '</span>\\n                    </div>\\n                    <div class=\"info-row\" style=\"border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;\">\\n                        <span class=\"info-label\">وسيلة التواصل: </span>\\n                        <span class=\"info-value\">').concat(getContactMethodName(invoice.client.contactMethod), '</span>\\n                    </div>\\n                </div>\\n            </div>\\n\\n            <div class=\"logo-section\">\\n                ').concat(logoSrc ? '<img src=\"'.concat(logoSrc, '\" alt=\"شعار الشركة\">') : '\\n                <div style=\"width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9;\">\\n                    <span style=\"color: #999;\">شعار الشركة</span>\\n                </div>\\n                ', '\\n                <div class=\"company-description\">\\n                    ').concat(settings.description || \"شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة\", '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <!-- Services Section -->\\n        <div class=\"services-section\">\\n            <h3 class=\"section-title\">تفاصيل الخدمات والمنتجات</h3>\\n\\n            <table class=\"services-table\">\\n                <thead>\\n                    <tr>\\n                        <th style=\"width: 50px;\">م</th>\\n                        <th style=\"width: 120px;\">نوع الخدمة</th>\\n                        <th style=\"width: 200px;\">التفاصيل</th>\\n                        <th style=\"width: 100px;\">الكمية/المقاس</th>\\n                        <th style=\"width: 120px;\">السعر</th>\\n                        <th style=\"width: 120px;\">المجموع</th>\\n                    </tr>\\n                </thead>\\n                <tbody>\\n                    ').concat(invoice.services.map((service, index)=>{\n            // Calculate service details (same logic as in component)\n            let serviceTotal = 0;\n            let serviceDetails = \"\";\n            let quantity = \"\";\n            let unitPrice = 0;\n            if (service.serviceType === \"consultation\") {\n                const consultationService = service;\n                serviceTotal = consultationService.cost || 0;\n                serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                unitPrice = consultationService.cost || 0;\n            } else if (service.serviceType === \"pattern\") {\n                var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                const patternService = service;\n                serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n            } else if (service.serviceType === \"pattern_printing\") {\n                var _printingService_files, _printingService_files1, _printingService_files2;\n                const printingService = service;\n                serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n            } else if (service.serviceType === \"shipping\") {\n                const shippingService = service;\n                serviceTotal = shippingService.total || 0;\n                serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                quantity = \"\".concat(shippingService.quantity || 1);\n                unitPrice = shippingService.unitPrice || 0;\n            } else if (service.serviceType === \"manufacturing\") {\n                var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                const manufacturingService = service;\n                serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n            } else if (service.serviceType === \"products\") {\n                var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                const productsService = service;\n                let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                // طرح الخصم إن وجد\n                if (productsService.discountAmount) {\n                    productsSubtotal -= productsService.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n            } else {\n                serviceTotal = service.cost || service.total || 0;\n                serviceDetails = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n            }\n            return \"\\n                        <tr>\\n                            <td>\".concat(index + 1, '</td>\\n                            <td class=\"service-type\">').concat(getServiceName(service.serviceType), '</td>\\n                            <td class=\"service-details\">').concat(serviceDetails, \"</td>\\n                            <td>\").concat(quantity, '</td>\\n                            <td class=\"service-price\">').concat(formatCurrency(unitPrice), '</td>\\n                            <td class=\"service-total\">').concat(formatCurrency(serviceTotal), \"</td>\\n                        </tr>\\n                      \");\n        }).join(\"\"), '\\n                </tbody>\\n            </table>\\n        </div>\\n\\n        <!-- Total Summary -->\\n        <div class=\"total-section\">\\n            <div class=\"total-details\">\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المجموع الكلي:</span>\\n                    <span class=\"total-value grand-total\">').concat(formatCurrency(invoice.total), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المدفوع:</span>\\n                    <span class=\"total-value paid-amount\">').concat(formatCurrency(invoice.paidAmount || 0), '</span>\\n                </div>\\n                <div class=\"total-row\">\\n                    <span class=\"total-label\">المبلغ المتبقي:</span>\\n                    <span class=\"total-value remaining-amount ').concat((invoice.remainingAmount || 0) > 0 ? \"remaining-positive\" : \"remaining-zero\", '\">\\n                        ').concat(formatCurrency(invoice.remainingAmount || 0), '\\n                    </span>\\n                </div>\\n                <div class=\"total-row\" style=\"border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;\">\\n                    <span class=\"total-label\">طريقة الدفع:</span>\\n                    <span class=\"total-value\">').concat(getPaymentMethodName(invoice.paymentMethod), \"</span>\\n                </div>\\n                \").concat(invoice.services.some((service)=>{\n            if (service.serviceType === \"pattern\") {\n                var _service_models;\n                return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n            }\n            if (service.serviceType === \"manufacturing\") {\n                var _service_models1;\n                return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n            }\n            return false;\n        }) ? '\\n                <div class=\"total-row\" style=\"border-top: 1px solid #ff9800; padding-top: 8px; margin-top: 8px; color: #ff9800;\">\\n                    <span class=\"total-label\" style=\"font-size: 12px;\">ملاحظة:</span>\\n                    <span class=\"total-value\" style=\"font-size: 12px;\">يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر</span>\\n                </div>\\n                ' : \"\", '\\n            </div>\\n\\n            <div class=\"payment-stamp ').concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"paid-full\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"paid-partial\" : \"unpaid\", '\">\\n                <div class=\"stamp-title\">OKA Group</div>\\n                <div class=\"stamp-status\">').concat(invoice.paymentStatus || \"غير محدد\", '</div>\\n            </div>\\n        </div>\\n\\n        <!-- Footer -->\\n        <div class=\"footer\">\\n            <!-- Right side: Contact Information -->\\n            <div class=\"footer-left\">\\n                <div class=\"contact-info\">\\n                    <div><strong>معلومات التواصل:</strong></div>\\n                    <div>\\uD83D\\uDCE7 البريد الإلكتروني: <EMAIL></div>\\n                    <div>\\uD83D\\uDCDE للشكاوى والاقتراحات: 0114954118</div>\\n                </div>\\n            </div>\\n\\n            <!-- Left side: Headquarters + Company Stamp -->\\n            <div class=\"footer-right\">\\n                <div class=\"headquarters-info\">\\n                    <div><strong>المقر الإداري:</strong></div>\\n                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>\\n                </div>\\n\\n                <div class=\"company-stamp\">\\n                    ').concat(settings.companyStamp ? '\\n                    <img\\n                        src=\"'.concat(settings.companyStamp, '\"\\n                        alt=\"ختم الشركة\"\\n                    >\\n                    ') : '\\n                    <div class=\"stamp-content\">\\n                        <div class=\"stamp-title\">OKA</div>\\n                        <div class=\"stamp-subtitle\">GROUP</div>\\n                    </div>\\n                    ', '\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div class=\"thank-you\">\\n            شكراً لتعاملكم معنا\\n        </div>\\n    </div>\\n</body>\\n</html>\\n    ');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl sm:text-3xl font-bold text-gray-800\",\n                        children: \"معاينة الفاتورة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompanySettings(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"إعدادات الشركة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"تعديل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExportPDF,\n                                disabled: isExporting,\n                                className: \"btn-success \".concat(isExporting ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                children: isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 animate-spin\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"جاري التصدير...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 948,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"تصدير PDF\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>openPrintView(invoice),\n                                className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"طباعة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowPOSInvoice(true),\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"فاتورة POS\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 963,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"invoice-content\",\n                className: \"bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden print-break-inside-avoid\",\n                style: {\n                    fontFamily: \"Arial, sans-serif\",\n                    lineHeight: \"1.4\",\n                    position: \"relative\",\n                    zIndex: 1,\n                    direction: \"rtl\",\n                    width: \"100%\",\n                    maxWidth: \"794px\",\n                    margin: \"0 auto\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-blue-50 via-white to-blue-50 p-8 border-b-4 border-blue-300 print-header print-break-inside-avoid relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -translate-y-32 translate-x-32\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 w-48 h-48 bg-blue-100 rounded-full translate-y-24 -translate-x-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: settings.companyLogo,\n                                                                alt: \"شعار الشركة\",\n                                                                className: \"w-56 h-56 object-contain mx-auto drop-shadow-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-48 h-4 bg-blue-200 rounded-full opacity-30 blur-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-56 h-56 border-3 border-dashed border-blue-300 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-50 to-white mx-auto shadow-inner\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-blue-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-24 h-24 mx-auto mb-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1016,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold\",\n                                                                    children: \"شعار الشركة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1018,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl shadow-lg mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: \"OKA GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-100 text-sm\",\n                                                            children: \"مجموعة أوكا للخدمات المتكاملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1027,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-xs mx-auto bg-white p-4 rounded-lg shadow-sm border border-blue-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 leading-relaxed font-medium\",\n                                                        children: settings.description || \"نقدم خدمات متكاملة في مجال التصميم والتصنيع والاستشارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 text-right space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-4xl font-bold text-blue-600 mb-2\",\n                                                            children: \"فاتورة خدمات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white p-6 rounded-2xl shadow-lg border-2 border-blue-100 max-w-md ml-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-blue-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold text-blue-600\",\n                                                                        children: [\n                                                                            \"#\",\n                                                                            invoice.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"رقم الفاتورة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center p-3 bg-gray-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800 font-medium\",\n                                                                        children: formatDate(invoice.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1057,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-semibold text-gray-700\",\n                                                                        children: \"التاريخ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t-2 border-blue-100 my-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-bold text-gray-700 text-center mb-3 bg-blue-50 py-2 rounded-lg\",\n                                                                        children: \"بيانات العميل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1070,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1069,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.businessName\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1076,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الاسم التجاري:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1077,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1075,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.phone\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1082,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"الهاتف:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1083,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1081,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            invoice.client.province && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-800 font-medium\",\n                                                                                        children: invoice.client.province\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1088,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"المحافظة:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1089,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1087,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1094,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"كود العميل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1095,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1093,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between pt-2 border-t border-gray-200\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-blue-600 font-bold\",\n                                                                                        children: getContactMethodName(invoice.client.contactMethod)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1099,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-600\",\n                                                                                        children: \"وسيلة التواصل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1100,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1068,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1047,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-t-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-center flex items-center justify-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"تفاصيل الخدمات والمنتجات\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto print-break-inside-avoid bg-white rounded-b-xl shadow-lg border-2 border-blue-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-16\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-white text-blue-600 rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm\",\n                                                                        children: \"م\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1138,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1137,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"نوع الخدمة\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1135,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-48\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1146,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1145,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"التفاصيل\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-28\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1154,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"الكمية\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1151,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold border-r border-blue-400 w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1162,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1163,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1161,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"السعر\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1160,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-4 text-center font-bold w-32\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1170,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"المجموع\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: invoice.services.map((service, index)=>{\n                                                        // Calculate service total based on service type\n                                                        let serviceTotal = 0;\n                                                        let serviceDetails = \"\";\n                                                        let quantity = \"\";\n                                                        let unitPrice = 0;\n                                                        if (service.serviceType === \"consultation\") {\n                                                            const consultationService = service;\n                                                            serviceTotal = consultationService.cost || 0;\n                                                            serviceDetails = \"\".concat(consultationService.topic || \"استشارة\", \" - \").concat(consultationService.hours || 0, \" ساعة\");\n                                                            quantity = \"\".concat(consultationService.hours || 0, \" ساعة\");\n                                                            unitPrice = consultationService.cost || 0;\n                                                        } else if (service.serviceType === \"pattern\") {\n                                                            var _patternService_models, _patternService_models1, _patternService_models2, _patternService_models3;\n                                                            const patternService = service;\n                                                            serviceTotal = ((_patternService_models = patternService.models) === null || _patternService_models === void 0 ? void 0 : _patternService_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"\".concat(((_patternService_models1 = patternService.models) === null || _patternService_models1 === void 0 ? void 0 : _patternService_models1.length) || 0, \" نموذج باترون\");\n                                                            quantity = \"\".concat(((_patternService_models2 = patternService.models) === null || _patternService_models2 === void 0 ? void 0 : _patternService_models2.length) || 0, \" نموذج\");\n                                                            unitPrice = serviceTotal / (((_patternService_models3 = patternService.models) === null || _patternService_models3 === void 0 ? void 0 : _patternService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"pattern_printing\") {\n                                                            var _printingService_files, _printingService_files1, _printingService_files2;\n                                                            const printingService = service;\n                                                            serviceTotal = ((_printingService_files = printingService.files) === null || _printingService_files === void 0 ? void 0 : _printingService_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                                                            serviceDetails = \"طباعة \".concat(((_printingService_files1 = printingService.files) === null || _printingService_files1 === void 0 ? void 0 : _printingService_files1.length) || 0, \" ملف\");\n                                                            quantity = \"\".concat(((_printingService_files2 = printingService.files) === null || _printingService_files2 === void 0 ? void 0 : _printingService_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                                                            unitPrice = serviceTotal;\n                                                        } else if (service.serviceType === \"shipping\") {\n                                                            const shippingService = service;\n                                                            serviceTotal = shippingService.total || 0;\n                                                            serviceDetails = \"شحن \".concat(shippingService.item || \"منتج\");\n                                                            quantity = \"\".concat(shippingService.quantity || 1);\n                                                            unitPrice = shippingService.unitPrice || 0;\n                                                        } else if (service.serviceType === \"manufacturing\") {\n                                                            var _manufacturingService_models, _manufacturingService_models1, _manufacturingService_models2, _manufacturingService_models3;\n                                                            const manufacturingService = service;\n                                                            serviceTotal = ((_manufacturingService_models = manufacturingService.models) === null || _manufacturingService_models === void 0 ? void 0 : _manufacturingService_models.reduce((sum, model)=>{\n                                                                return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                                                            }, 0)) || 0;\n                                                            serviceDetails = \"تصنيع \".concat(((_manufacturingService_models1 = manufacturingService.models) === null || _manufacturingService_models1 === void 0 ? void 0 : _manufacturingService_models1.length) || 0, \" نموذج\");\n                                                            quantity = \"\".concat(((_manufacturingService_models2 = manufacturingService.models) === null || _manufacturingService_models2 === void 0 ? void 0 : _manufacturingService_models2.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_manufacturingService_models3 = manufacturingService.models) === null || _manufacturingService_models3 === void 0 ? void 0 : _manufacturingService_models3.length) || 1);\n                                                        } else if (service.serviceType === \"products\") {\n                                                            var _productsService_items, _productsService_items1, _productsService_items2, _productsService_items3;\n                                                            const productsService = service;\n                                                            let productsSubtotal = ((_productsService_items = productsService.items) === null || _productsService_items === void 0 ? void 0 : _productsService_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                                                            // طرح الخصم إن وجد\n                                                            if (productsService.discountAmount) {\n                                                                productsSubtotal -= productsService.discountAmount || 0;\n                                                            }\n                                                            serviceTotal = Math.max(0, productsSubtotal);\n                                                            serviceDetails = \"\".concat(((_productsService_items1 = productsService.items) === null || _productsService_items1 === void 0 ? void 0 : _productsService_items1.length) || 0, \" منتج\");\n                                                            quantity = \"\".concat(((_productsService_items2 = productsService.items) === null || _productsService_items2 === void 0 ? void 0 : _productsService_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                                                            unitPrice = serviceTotal / (((_productsService_items3 = productsService.items) === null || _productsService_items3 === void 0 ? void 0 : _productsService_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                                                        } else {\n                                                            // Default for other service types\n                                                            serviceTotal = service.cost || service.total || 0;\n                                                            serviceDetails = getServiceName(service.serviceType);\n                                                            quantity = \"1\";\n                                                            unitPrice = serviceTotal;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"\".concat(index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\", \" hover:bg-blue-50 transition-colors duration-200 border-b border-gray-200\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-center border-r border-gray-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold text-sm mx-auto\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1243,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-center border-r border-gray-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-100 text-blue-700 px-3 py-2 rounded-lg font-bold text-sm inline-block\",\n                                                                        children: getServiceName(service.serviceType)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1251,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-right border-r border-gray-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-700 font-medium leading-relaxed\",\n                                                                        children: serviceDetails\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1258,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-center border-r border-gray-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-100 text-gray-700 px-3 py-2 rounded-lg font-semibold text-sm inline-block\",\n                                                                        children: quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1265,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-center border-r border-gray-200\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-blue-600 font-bold text-lg\",\n                                                                        children: formatCurrency(unitPrice)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1271,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-green-100 text-green-700 px-4 py-2 rounded-lg font-bold text-lg inline-block shadow-sm\",\n                                                                        children: formatCurrency(serviceTotal)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1279,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1278,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, service.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 bg-gradient-to-br from-slate-50 via-white to-slate-50 rounded-2xl shadow-xl border-2 border-slate-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-slate-600 to-slate-700 text-white p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-center flex items-center justify-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"ملخص المبالغ المالية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1295,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1294,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start gap-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-50 border-2 border-green-200 rounded-xl p-4 flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1312,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1311,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1310,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl font-bold text-green-700\",\n                                                                            children: \"المجموع الكلي\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1315,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1309,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: formatCurrency(invoice.total)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1308,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-blue-50 border-2 border-blue-200 rounded-xl p-4 flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1325,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1324,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1323,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl font-bold text-blue-700\",\n                                                                            children: \"المبلغ المدفوع\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1328,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: formatCurrency(invoice.paidAmount || 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1321,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"bg-red-50 border-red-200\" : \"bg-green-50 border-green-200\", \" border-2 rounded-xl p-4 flex justify-between items-center\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 \".concat((invoice.remainingAmount || 0) > 0 ? \"bg-red-500\" : \"bg-green-500\", \" rounded-full flex items-center justify-center\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                    lineNumber: 1338,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1337,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1336,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl font-bold \".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-700\" : \"text-green-700\"),\n                                                                            children: \"المبلغ المتبقي\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1341,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1335,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold \".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                                    children: formatCurrency(invoice.remainingAmount || 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1343,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1334,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 border-2 border-gray-200 rounded-xl p-4 flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-6 h-6 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1353,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        fillRule: \"evenodd\",\n                                                                                        d: \"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z\",\n                                                                                        clipRule: \"evenodd\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                        lineNumber: 1354,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1352,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1351,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl font-bold text-gray-700\",\n                                                                            children: \"طريقة الدفع\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1357,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-bold text-gray-800\",\n                                                                    children: getPaymentMethodName(invoice.paymentMethod)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1359,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        invoice.services.some((service)=>{\n                                                            if (service.serviceType === \"pattern\") {\n                                                                var _service_models;\n                                                                return (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.some((model)=>model.fileType !== \"plt\");\n                                                            }\n                                                            if (service.serviceType === \"manufacturing\") {\n                                                                var _service_models1;\n                                                                return (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.some((model)=>model.fileType !== \"plt\");\n                                                            }\n                                                            return false;\n                                                        }) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-orange-50 border-2 border-orange-200 rounded-xl p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1376,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                            lineNumber: 1375,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1374,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-orange-700 font-bold text-lg mb-1\",\n                                                                                children: \"ملاحظة مهمة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1380,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-orange-600 font-semibold\",\n                                                                                children: \"يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1381,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1306,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative rounded-2xl p-6 text-center shadow-2xl transform rotate-3 border-4 bg-white \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"border-green-500 text-green-600\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"border-orange-500 text-orange-600\" : \"border-red-500 text-red-600\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-2xl mb-2\",\n                                                                        children: \"OKA GROUP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-1 bg-current mx-auto mb-3 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1399,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-lg\",\n                                                                        children: invoice.paymentStatus || \"غير محدد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1400,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1397,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-2 right-2 w-3 h-3 bg-current rounded-full opacity-20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute bottom-2 left-2 w-2 h-2 bg-current rounded-full opacity-30\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1406,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1391,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1303,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1292,\n                                columnNumber: 11\n                            }, undefined),\n                            (invoice.requiresShipping || invoice.shippingAddress || invoice.notes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-amber-50 via-yellow-50 to-amber-50 rounded-2xl shadow-lg border-2 border-amber-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-amber-500 to-yellow-500 text-white p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-bold text-center flex items-center justify-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1420,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"معلومات إضافية\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1419,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1418,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 border-2 border-green-200 rounded-xl p-4 flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1433,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                                lineNumber: 1434,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1431,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-green-700 text-lg\",\n                                                                    children: \"يتطلب شحن\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1437,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1430,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600 font-bold text-xl\",\n                                                            children: \"نعم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1429,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.shippingAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 border-2 border-blue-200 rounded-xl p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-white\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1448,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1447,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1446,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-blue-700 text-lg block mb-2\",\n                                                                        children: \"عنوان الشحن\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1452,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 font-medium leading-relaxed\",\n                                                                        children: invoice.shippingAddress\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1453,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1451,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1445,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1444,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                invoice.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-50 border-2 border-purple-200 rounded-xl p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-white\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1464,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1463,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1462,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-purple-700 text-lg block mb-2\",\n                                                                        children: \"ملاحظات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1468,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-700 font-medium leading-relaxed\",\n                                                                        children: invoice.notes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                        lineNumber: 1469,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1467,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1461,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1427,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1416,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1415,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1112,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-t border-gray-200 p-6 relative\",\n                        style: {\n                            direction: \"rtl\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-bold text-gray-700 mb-3\",\n                                                children: \"معلومات التواصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"البريد الإلكتروني: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1490,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-blue-600\",\n                                                                children: settings.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1491,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1489,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1494,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1495,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1493,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1488,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-2 justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-semibold text-gray-700\",\n                                                                children: \"للشكاوى والاقتراحات: \"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600 font-bold\",\n                                                                children: settings.complaintsPhone\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1503,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1501,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-green-600 flex-shrink-0 mt-0.5\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1505,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1500,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1484,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/2 text-right space-y-4\",\n                                        style: {\n                                            direction: \"rtl\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-semibold text-gray-700\",\n                                                        children: \"المقر الإداري: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1515,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: settings.address\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1516,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1514,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end mt-4\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 bg-white border border-gray-300 rounded-lg p-2 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: settings.companyStamp,\n                                                        alt: \"ختم الشركة\",\n                                                        className: \"max-w-full max-h-full object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1523,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1522,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-40 h-24 border-2 border-dashed border-gray-400 rounded-lg flex items-center justify-center bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 mx-auto mb-1\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                    lineNumber: 1533,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs\",\n                                                                children: \"ختم الشركة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1535,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1530,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1520,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1512,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1482,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 pt-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center bg-blue-50 p-3 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 font-bold\",\n                                        children: \"شكراً لتعاملكم معنا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                    lineNumber: 1545,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                        lineNumber: 1481,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 976,\n                columnNumber: 7\n            }, undefined),\n            showCompanySettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"إعدادات الشركة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                            lineNumber: 1564,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1560,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1557,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1576,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.companyName,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        companyName: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل اسم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1577,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1575,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1591,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: settings.email,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        email: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1592,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"هاتف الشكاوى والاقتراحات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1606,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"tel\",\n                                                value: settings.complaintsPhone,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        complaintsPhone: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رقم هاتف الشكاوى\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1607,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1605,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1621,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.address,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        address: e.target.value\n                                                    });\n                                                },\n                                                rows: 3,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل عنوان المقر الإداري\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1622,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1620,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.slogan,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        slogan: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1637,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1635,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1651,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: settings.wishMessage,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        wishMessage: e.target.value\n                                                    });\n                                                },\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                                                placeholder: \"أدخل رسالة التمنيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1650,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                                children: \"وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1666,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: settings.description,\n                                                onChange: (e)=>{\n                                                    const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                    updateSettings({\n                                                        description: e.target.value\n                                                    });\n                                                },\n                                                rows: 2,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                                                placeholder: \"أدخل وصف الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1667,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1665,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"شعار الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1681,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors\",\n                                                children: settings.companyLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyLogo,\n                                                            alt: \"شعار الشركة\",\n                                                            className: \"w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1685,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1690,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1684,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1716,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1715,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع شعار الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1718,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyLogo: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الشعار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1719,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1714,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1682,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1680,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-semibold text-gray-700 mb-3\",\n                                                children: \"ختم الشركة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1748,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors\",\n                                                children: settings.companyStamp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: settings.companyStamp,\n                                                            alt: \"ختم الشركة\",\n                                                            className: \"w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1752,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"تغيير الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1757,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1751,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-12 h-12 text-gray-400 mx-auto\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                                lineNumber: 1783,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1782,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"اضغط لرفع ختم الشركة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1785,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const input = document.createElement(\"input\");\n                                                                input.type = \"file\";\n                                                                input.accept = \"image/*\";\n                                                                input.onchange = (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (e)=>{\n                                                                            var _e_target;\n                                                                            const { updateSettings } = _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore.getState();\n                                                                            updateSettings({\n                                                                                companyStamp: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                                                                            });\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                };\n                                                                input.click();\n                                                            },\n                                                            className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\",\n                                                            children: \"رفع الختم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                            lineNumber: 1786,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                    lineNumber: 1781,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                                lineNumber: 1749,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1747,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1573,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1572,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-6 py-4 rounded-b-lg border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"سيتم حفظ الإعدادات تلقائياً عند التغيير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1818,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCompanySettings(false),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                        lineNumber: 1821,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                                lineNumber: 1817,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                            lineNumber: 1816,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                    lineNumber: 1555,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1554,\n                columnNumber: 9\n            }, undefined),\n            showPOSInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                invoice: invoice,\n                onClose: ()=>setShowPOSInvoice(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n                lineNumber: 1835,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicePreview.tsx\",\n        lineNumber: 913,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicePreview, \"rxHFl2SkeEMTva1xN8F43PiUNL8=\", false, function() {\n    return [\n        _store_settingsStore__WEBPACK_IMPORTED_MODULE_2__.useSettingsStore\n    ];\n});\n_c = InvoicePreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicePreview);\nvar _c;\n$RefreshReg$(_c, \"InvoicePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicePreview.tsx\n"));

/***/ })

});