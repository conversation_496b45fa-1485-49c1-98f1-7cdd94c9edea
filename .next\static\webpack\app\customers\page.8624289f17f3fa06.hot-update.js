"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/app/customers/page.tsx":
/*!************************************!*\
  !*** ./src/app/customers/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _components_CustomerList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CustomerList */ \"(app-pages-browser)/./src/components/CustomerList.tsx\");\n/* harmony import */ var _components_ClientForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ClientForm */ \"(app-pages-browser)/./src/components/ClientForm.tsx\");\n/* harmony import */ var _components_CustomerImport__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/CustomerImport */ \"(app-pages-browser)/./src/components/CustomerImport.tsx\");\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * صفحة إدارة العملاء الشاملة\n */ \n\n\n\n\n\n\n\nconst CustomersPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { customers, currentCustomer, setCurrentCustomer, addCustomer, updateCustomer, deleteCustomer, getStats, isLoading, error, clearError } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_3__.useCustomerStore)();\n    const { setCurrentClient } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_4__.useInvoiceStore)();\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"list\");\n    const [editingCustomer, setEditingCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const customerStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_8__.getCustomerCodeStats)();\n        setStats(customerStats);\n    }, [\n        customers\n    ]);\n    const handleAddCustomer = async (customerData)=>{\n        const result = await addCustomer(customerData);\n        if (result.success) {\n            setViewMode(\"list\");\n            // تحديث الإحصائيات\n            const newStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_8__.getCustomerCodeStats)();\n            setStats(newStats);\n        }\n    };\n    const handleEditCustomer = (customer)=>{\n        setEditingCustomer(customer);\n        setViewMode(\"edit\");\n    };\n    const handleUpdateCustomer = async (customerData)=>{\n        if (editingCustomer === null || editingCustomer === void 0 ? void 0 : editingCustomer.id) {\n            const result = await updateCustomer(editingCustomer.id, customerData);\n            if (result.success) {\n                setViewMode(\"list\");\n                setEditingCustomer(null);\n            }\n        }\n    };\n    const handleDeleteCustomer = async (customerId)=>{\n        const result = await deleteCustomer(customerId);\n        if (result.success) {\n            // تحديث الإحصائيات\n            const newStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_8__.getCustomerCodeStats)();\n            setStats(newStats);\n        }\n    };\n    const handleBackToHome = ()=>{\n        router.push(\"/\");\n    };\n    const handleCreateInvoice = (customer)=>{\n        // تعيين العميل الحالي في store الفواتير\n        setCurrentClient(customer);\n        // الانتقال إلى الصفحة الرئيسية لإنشاء الفاتورة\n        router.push(\"/\");\n    };\n    const handleViewDetails = (customer)=>{\n        // الانتقال إلى صفحة تفاصيل العميل\n        router.push(\"/customers/\".concat(customer.id));\n    };\n    const renderHeader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"إدارة العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            viewMode === \"list\" && \"عرض وإدارة جميع العملاء\",\n                                            viewMode === \"add\" && \"إضافة عميل جديد\",\n                                            viewMode === \"edit\" && \"تعديل بيانات العميل\",\n                                            viewMode === \"stats\" && \"إحصائيات العملاء\",\n                                            viewMode === \"import\" && \"استيراد العملاء من ملف CSV\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/customer-codes\"),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"إدارة الأكواد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBackToHome,\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"العودة للرئيسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 5\n        }, undefined);\n    const renderNavigation = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setViewMode(\"list\"),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg transition-colors \".concat(viewMode === \"list\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            \"قائمة العملاء\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setViewMode(\"add\"),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg transition-colors \".concat(viewMode === \"add\" ? \"bg-green-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            \"إضافة عميل\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setViewMode(\"stats\"),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg transition-colors \".concat(viewMode === \"stats\" ? \"bg-purple-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, undefined),\n                            \"الإحصائيات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setViewMode(\"import\"),\n                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg transition-colors \".concat(viewMode === \"import\" ? \"bg-indigo-600 text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, undefined),\n                            \"استيراد العملاء\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, undefined);\n    const renderStats = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 p-3 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-blue-600\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي العملاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.totalCustomers) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-100 p-3 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-green-600\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"أكواد مُنشأة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.generatedCodes) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-100 p-3 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-orange-600\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"أكواد مخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.customCodes) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-100 p-3 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-purple-600\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"الكود التالي\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.nextCode) || \"p013621\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"معلومات النظام\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-700 mb-2\",\n                                            children: \"إعدادات الأكواد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• تنسيق الكود: p + رقم المعاملة + رقم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• رقم المعاملة الحالي: \",\n                                                        (stats === null || stats === void 0 ? void 0 : stats.lastTransactionNumber) || 1\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• آخر رقم عميل: \",\n                                                        (stats === null || stats === void 0 ? void 0 : stats.lastCustomerNumber) || 3621\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-700 mb-2\",\n                                            children: \"إحصائيات إضافية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-1 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• نسبة الأكواد المُنشأة: \",\n                                                        (stats === null || stats === void 0 ? void 0 : stats.totalCustomers) > 0 ? Math.round(stats.generatedCodes / stats.totalCustomers * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        \"• نسبة الأكواد المخصصة: \",\n                                                        (stats === null || stats === void 0 ? void 0 : stats.totalCustomers) > 0 ? Math.round(stats.customCodes / stats.totalCustomers * 100) : 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 5\n        }, undefined);\n    const handleImportComplete = (result)=>{\n        if (result.success) {\n            // تحديث الإحصائيات\n            const newStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_8__.getCustomerCodeStats)();\n            setStats(newStats);\n            // العودة لقائمة العملاء\n            setTimeout(()=>{\n                setViewMode(\"list\");\n            }, 2000);\n        }\n    };\n    const renderContent = ()=>{\n        switch(viewMode){\n            case \"add\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        onSubmit: handleAddCustomer\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, undefined);\n            case \"edit\":\n                return editingCustomer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        onSubmit: handleUpdateCustomer,\n                        initialValues: editingCustomer\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, undefined) : null;\n            case \"stats\":\n                return renderStats();\n            case \"import\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerImport__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        onImportComplete: handleImportComplete,\n                        onClose: ()=>setViewMode(\"list\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onEditCustomer: handleEditCustomer,\n                    onDeleteCustomer: handleDeleteCustomer,\n                    onCreateInvoice: handleCreateInvoice,\n                    onViewDetails: handleViewDetails,\n                    showActions: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                renderHeader(),\n                renderNavigation(),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-red-500 ml-2\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-800\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearError,\n                                className: \"text-red-600 hover:text-red-800\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, undefined),\n                renderContent()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n            lineNumber: 347,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customers\\\\page.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomersPage, \"v2/jZt8qOu1KLAufNDDhbufEw4Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_3__.useCustomerStore,\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_4__.useInvoiceStore\n    ];\n});\n_c = CustomersPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomersPage);\nvar _c;\n$RefreshReg$(_c, \"CustomersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customers/page.tsx\n"));

/***/ })

});