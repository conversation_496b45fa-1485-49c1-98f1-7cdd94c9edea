/**
 * متجر إدارة العملاء مع نظام الأكواد المتقدم
 */

import { create } from 'zustand';
import { Client } from '@/types';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';
import { 
  generateCustomerCode, 
  validateCustomerCode, 
  isCustomerCodeUnique,
  getCustomerCodeStats,
  findCustomerByCode,
  getCustomerCodeSuggestions
} from '@/utils/customerCodeGenerator';

interface CustomerStore {
  customers: Client[];
  currentCustomer: Client | null;
  searchQuery: string;
  suggestions: Client[];
  isLoading: boolean;
  error: string | null;

  // Actions
  loadCustomers: () => void;
  addCustomer: (customer: Omit<Client, 'id' | 'clientCode'>) => Promise<{ success: boolean; customer?: Client; error?: string }>;
  updateCustomer: (id: string, updates: Partial<Client>) => Promise<{ success: boolean; error?: string }>;
  deleteCustomer: (id: string) => Promise<{ success: boolean; error?: string }>;
  setCurrentCustomer: (customer: Client | null) => void;
  
  // Search and suggestions
  setSearchQuery: (query: string) => void;
  searchCustomers: (query: string) => Client[];
  getSuggestions: (query: string) => void;
  clearSuggestions: () => void;
  
  // Customer code management
  generateNewCustomerCode: () => string;
  validateCode: (code: string, excludeId?: string) => { isValid: boolean; error?: string };
  findByCode: (code: string) => Client | null;
  getStats: () => any;
  
  // Utility
  clearError: () => void;
  refreshData: () => void;
}

/**
 * تحميل العملاء من التخزين الآمن
 */
const loadCustomersFromStorage = (): Client[] => {
  const result = secureGetItem<Client[]>('customers');
  if (result.success && result.data) {
    return result.data;
  }
  return [];
};

/**
 * حفظ العملاء في التخزين الآمن
 */
const saveCustomersToStorage = (customers: Client[]): boolean => {
  const result = secureSetItem('customers', customers);
  return result.success;
};

/**
 * إنشاء معرف فريد للعميل
 */
const generateCustomerId = (): string => {
  return `customer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const useCustomerStore = create<CustomerStore>((set, get) => ({
  customers: loadCustomersFromStorage(),
  currentCustomer: null,
  searchQuery: '',
  suggestions: [],
  isLoading: false,
  error: null,

  loadCustomers: () => {
    set({ isLoading: true, error: null });
    try {
      const customers = loadCustomersFromStorage();
      set({ customers, isLoading: false });
    } catch (error) {
      set({ 
        error: 'فشل في تحميل بيانات العملاء', 
        isLoading: false 
      });
    }
  },

  addCustomer: async (customerData) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // إنشاء كود العميل
      const codeData = generateCustomerCode();
      
      // إنشاء العميل الجديد
      const newCustomer: Client = {
        ...customerData,
        id: generateCustomerId(),
        clientCode: codeData.code,
      };
      
      // إضافة العميل للقائمة
      const updatedCustomers = [...customers, newCustomer];
      
      // حفظ في التخزين
      const saved = saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ بيانات العميل');
      }
      
      set({ 
        customers: updatedCustomers, 
        isLoading: false 
      });
      
      return { success: true, customer: newCustomer };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  // إضافة عملاء متعددين (للاستيراد)
  addMultipleCustomers: async (customersData: Omit<Client, 'id'>[]) => {
    set({ isLoading: true, error: null });

    try {
      const { customers } = get();
      const newCustomers: Client[] = [];
      const errors: string[] = [];

      for (const customerData of customersData) {
        try {
          let clientCode = customerData.clientCode;

          // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد
          if (!clientCode) {
            const codeData = generateCustomerCode();
            clientCode = codeData.code;
          }

          // التحقق من عدم تكرار الكود
          const existingCustomer = [...customers, ...newCustomers].find(c => c.clientCode === clientCode);
          if (existingCustomer) {
            errors.push(`كود العميل ${clientCode} مستخدم بالفعل للعميل ${customerData.name}`);
            continue;
          }

          const newCustomer: Client = {
            ...customerData,
            id: generateCustomerId(),
            clientCode,
          };

          newCustomers.push(newCustomer);
        } catch (error) {
          errors.push(`خطأ في إضافة العميل ${customerData.name}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
        }
      }

      if (newCustomers.length > 0) {
        const updatedCustomers = [...customers, ...newCustomers];

        // حفظ في التخزين
        const saved = saveCustomersToStorage(updatedCustomers);
        if (!saved) {
          throw new Error('فشل في حفظ بيانات العملاء');
        }

        set({
          customers: updatedCustomers,
          isLoading: false
        });

        return {
          success: true,
          imported: newCustomers.length,
          errors,
          customers: newCustomers
        };
      } else {
        set({ isLoading: false });
        return { success: false, error: 'لم يتم إضافة أي عميل', imported: 0, errors };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      set({
        error: errorMessage,
        isLoading: false
      });
      return { success: false, error: errorMessage, imported: 0, errors: [] };
    }
  },

  updateCustomer: async (id, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // التحقق من وجود العميل
      const customerIndex = customers.findIndex(c => c.id === id);
      if (customerIndex === -1) {
        throw new Error('العميل غير موجود');
      }
      
      // التحقق من كود العميل إذا تم تحديثه
      if (updates.clientCode) {
        const validation = validateCustomerCode(updates.clientCode);
        if (!validation.isValid) {
          throw new Error(validation.error || 'كود العميل غير صحيح');
        }
        
        if (!isCustomerCodeUnique(updates.clientCode, id)) {
          throw new Error('كود العميل مستخدم بالفعل');
        }
      }
      
      // تحديث العميل
      const updatedCustomers = [...customers];
      updatedCustomers[customerIndex] = {
        ...updatedCustomers[customerIndex],
        ...updates,
      };
      
      // حفظ في التخزين
      const saved = saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ التحديثات');
      }
      
      set({ 
        customers: updatedCustomers, 
        isLoading: false 
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في التحديث';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  deleteCustomer: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // التحقق من وجود العميل
      const customerExists = customers.some(c => c.id === id);
      if (!customerExists) {
        throw new Error('العميل غير موجود');
      }
      
      // حذف العميل
      const updatedCustomers = customers.filter(c => c.id !== id);
      
      // حفظ في التخزين
      const saved = saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ التغييرات');
      }
      
      set({ 
        customers: updatedCustomers, 
        currentCustomer: get().currentCustomer?.id === id ? null : get().currentCustomer,
        isLoading: false 
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في الحذف';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  setCurrentCustomer: (customer) => {
    set({ currentCustomer: customer });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  searchCustomers: (query) => {
    const { customers } = get();
    if (!query.trim()) {
      return customers;
    }
    
    const lowerQuery = query.toLowerCase();
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(lowerQuery) ||
      customer.phone.includes(query) ||
      customer.clientCode?.toLowerCase().includes(lowerQuery) ||
      customer.businessName?.toLowerCase().includes(lowerQuery)
    );
  },

  getSuggestions: (query) => {
    if (!query.trim()) {
      set({ suggestions: [] });
      return;
    }
    
    const suggestions = getCustomerCodeSuggestions(query, 10);
    set({ suggestions });
  },

  clearSuggestions: () => {
    set({ suggestions: [] });
  },

  generateNewCustomerCode: () => {
    const codeData = generateCustomerCode();
    return codeData.code;
  },

  validateCode: (code, excludeId) => {
    const validation = validateCustomerCode(code);
    if (!validation.isValid) {
      return validation;
    }
    
    if (!isCustomerCodeUnique(code, excludeId)) {
      return { isValid: false, error: 'كود العميل مستخدم بالفعل' };
    }
    
    return { isValid: true };
  },

  findByCode: (code) => {
    return findCustomerByCode(code);
  },

  getStats: () => {
    return getCustomerCodeStats();
  },

  clearError: () => {
    set({ error: null });
  },

  refreshData: () => {
    get().loadCustomers();
  },
}));
