/**
 * متجر إدارة العملاء مع نظام الأكواد المتقدم
 */

import { create } from 'zustand';
import { Client } from '@/types';
import { secureGetItem, secureSetItem } from '@/utils/secureStorage';
import { UnifiedStorageManager } from '@/utils/unifiedStorage';
import { STORAGE_KEYS } from '@/utils/storageKeys';
import {
  generateCustomerCode,
  validateCustomerCode,
  isCustomerCodeUnique,
  getCustomerCodeStats,
  findCustomerByCode,
  getCustomerCodeSuggestions
} from '@/utils/customerCodeGenerator';

interface CustomerStore {
  customers: Client[];
  currentCustomer: Client | null;
  searchQuery: string;
  suggestions: Client[];
  isLoading: boolean;
  error: string | null;

  // Actions
  loadCustomers: () => void;
  addCustomer: (customer: Omit<Client, 'id' | 'clientCode'>) => Promise<{ success: boolean; customer?: Client; error?: string }>;
  updateCustomer: (id: string, updates: Partial<Client>) => Promise<{ success: boolean; error?: string }>;
  deleteCustomer: (id: string) => Promise<{ success: boolean; error?: string }>;
  setCurrentCustomer: (customer: Client | null) => void;
  
  // Search and suggestions
  setSearchQuery: (query: string) => void;
  searchCustomers: (query: string) => Client[];
  getQuickSuggestions: (query: string, limit?: number) => any[];
  getSuggestions: (query: string) => void;
  clearSuggestions: () => void;
  
  // Customer code management
  generateNewCustomerCode: () => string;
  validateCode: (code: string, excludeId?: string) => { isValid: boolean; error?: string };
  findByCode: (code: string) => Client | null;
  getStats: () => any;
  
  // Utility
  clearError: () => void;
  refreshData: () => void;
}

/**
 * تحميل العملاء من النظام الموحد
 */
const loadCustomersFromStorage = async (): Promise<Client[]> => {
  try {
    const result = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
    if (result.success) {
      console.log('✅ تم تحميل العملاء من النظام الموحد:', result.data.length);
      return result.data;
    } else {
      console.error('❌ فشل في تحميل العملاء:', result.message);
      return [];
    }
  } catch (error) {
    console.error('❌ خطأ حرج في تحميل العملاء:', error);
    return [];
  }
};

/**
 * حفظ العملاء في النظام الموحد
 */
const saveCustomersToStorage = async (customers: Client[]): Promise<boolean> => {
  try {
    const result = await UnifiedStorageManager.setItem(STORAGE_KEYS.CUSTOMERS, customers);
    if (result.success) {
      console.log('✅ تم حفظ العملاء في النظام الموحد:', customers.length);
      return true;
    } else {
      console.error('❌ فشل في حفظ العملاء:', result.message);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ حرج في حفظ العملاء:', error);
    return false;
  }
};

/**
 * إنشاء معرف فريد للعميل
 */
const generateCustomerId = (): string => {
  return `customer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const useCustomerStore = create<CustomerStore>((set, get) => ({
  customers: [], // سيتم تحميلها لاحقاً
  currentCustomer: null,
  searchQuery: '',
  suggestions: [],
  isLoading: false,
  error: null,

  loadCustomers: async () => {
    set({ isLoading: true, error: null });
    try {
      const customers = await loadCustomersFromStorage();
      set({ customers, isLoading: false });
    } catch (error) {
      set({
        error: 'فشل في تحميل بيانات العملاء',
        isLoading: false
      });
    }
  },

  addCustomer: async (customerData) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // إنشاء كود العميل
      const codeData = generateCustomerCode();
      
      // إنشاء العميل الجديد
      const newCustomer: Client = {
        ...customerData,
        id: generateCustomerId(),
        clientCode: codeData.code,
      };
      
      // إضافة العميل للقائمة
      const updatedCustomers = [...customers, newCustomer];
      
      // حفظ في التخزين
      const saved = await saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ بيانات العميل');
      }
      
      set({ 
        customers: updatedCustomers, 
        isLoading: false 
      });
      
      return { success: true, customer: newCustomer };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  // إضافة عملاء متعددين (للاستيراد)
  addMultipleCustomers: async (customersData: Omit<Client, 'id'>[]) => {
    set({ isLoading: true, error: null });

    try {
      const { customers } = get();
      const newCustomers: Client[] = [];
      const errors: string[] = [];

      for (const customerData of customersData) {
        try {
          let clientCode = customerData.clientCode;

          // إذا لم يتم تمرير كود، قم بإنشاء واحد جديد
          if (!clientCode) {
            const codeData = generateCustomerCode();
            clientCode = codeData.code;
          }

          // التحقق من عدم تكرار الكود
          const existingCustomer = [...customers, ...newCustomers].find(c => c.clientCode === clientCode);
          if (existingCustomer) {
            errors.push(`كود العميل ${clientCode} مستخدم بالفعل للعميل ${customerData.name}`);
            continue;
          }

          const newCustomer: Client = {
            ...customerData,
            id: generateCustomerId(),
            clientCode,
          };

          newCustomers.push(newCustomer);
        } catch (error) {
          errors.push(`خطأ في إضافة العميل ${customerData.name}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
        }
      }

      if (newCustomers.length > 0) {
        const updatedCustomers = [...customers, ...newCustomers];

        // حفظ في التخزين
        const saved = saveCustomersToStorage(updatedCustomers);
        if (!saved) {
          throw new Error('فشل في حفظ بيانات العملاء');
        }

        set({
          customers: updatedCustomers,
          isLoading: false
        });

        return {
          success: true,
          imported: newCustomers.length,
          errors,
          customers: newCustomers
        };
      } else {
        set({ isLoading: false });
        return { success: false, error: 'لم يتم إضافة أي عميل', imported: 0, errors };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      set({
        error: errorMessage,
        isLoading: false
      });
      return { success: false, error: errorMessage, imported: 0, errors: [] };
    }
  },

  updateCustomer: async (id, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // التحقق من وجود العميل
      const customerIndex = customers.findIndex(c => c.id === id);
      if (customerIndex === -1) {
        throw new Error('العميل غير موجود');
      }
      
      // التحقق من كود العميل إذا تم تحديثه
      if (updates.clientCode) {
        const validation = validateCustomerCode(updates.clientCode);
        if (!validation.isValid) {
          throw new Error(validation.error || 'كود العميل غير صحيح');
        }
        
        if (!isCustomerCodeUnique(updates.clientCode, id)) {
          throw new Error('كود العميل مستخدم بالفعل');
        }
      }
      
      // تحديث العميل
      const updatedCustomers = [...customers];
      updatedCustomers[customerIndex] = {
        ...updatedCustomers[customerIndex],
        ...updates,
      };
      
      // حفظ في التخزين
      const saved = saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ التحديثات');
      }
      
      set({ 
        customers: updatedCustomers, 
        isLoading: false 
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في التحديث';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  deleteCustomer: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      const { customers } = get();
      
      // التحقق من وجود العميل
      const customerExists = customers.some(c => c.id === id);
      if (!customerExists) {
        throw new Error('العميل غير موجود');
      }
      
      // حذف العميل
      const updatedCustomers = customers.filter(c => c.id !== id);
      
      // حفظ في التخزين
      const saved = saveCustomersToStorage(updatedCustomers);
      if (!saved) {
        throw new Error('فشل في حفظ التغييرات');
      }
      
      set({ 
        customers: updatedCustomers, 
        currentCustomer: get().currentCustomer?.id === id ? null : get().currentCustomer,
        isLoading: false 
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في الحذف';
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
      return { success: false, error: errorMessage };
    }
  },

  setCurrentCustomer: (customer) => {
    set({ currentCustomer: customer });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  searchCustomers: (query) => {
    const { customers } = get();
    if (!query.trim()) {
      return customers;
    }

    const searchTerm = query.toLowerCase().trim();

    // البحث الأساسي المحسن
    const basicResults = customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm) ||
      customer.phone.includes(query) ||
      (customer.clientCode && customer.clientCode.toLowerCase().includes(searchTerm)) ||
      (customer.businessName && customer.businessName.toLowerCase().includes(searchTerm)) ||
      (customer.province && customer.province.toLowerCase().includes(searchTerm)) ||
      (customer.salesperson && customer.salesperson.toLowerCase().includes(searchTerm)) ||
      customer.services.some(service => service.toLowerCase().includes(searchTerm))
    );

    // البحث الضبابي للأسماء المشابهة إذا لم نجد نتائج
    if (basicResults.length === 0 && searchTerm.length > 2) {
      const fuzzyResults = customers.filter(customer => {
        const name = customer.name.toLowerCase();
        const words = searchTerm.split(' ');
        return words.some(word => name.includes(word) && word.length > 1);
      });
      return fuzzyResults;
    }

    // ترتيب النتائج حسب الأولوية
    return basicResults.sort((a, b) => {
      // أولوية للمطابقة الدقيقة في بداية الاسم
      const aNameMatch = a.name.toLowerCase().startsWith(searchTerm);
      const bNameMatch = b.name.toLowerCase().startsWith(searchTerm);
      if (aNameMatch && !bNameMatch) return -1;
      if (!aNameMatch && bNameMatch) return 1;

      // أولوية للمطابقة في كود العميل
      const aCodeMatch = a.clientCode?.toLowerCase().includes(searchTerm);
      const bCodeMatch = b.clientCode?.toLowerCase().includes(searchTerm);
      if (aCodeMatch && !bCodeMatch) return -1;
      if (!aCodeMatch && bCodeMatch) return 1;

      // ترتيب أبجدي كافتراضي
      return a.name.localeCompare(b.name, 'ar');
    });
  },

  // الاقتراحات السريعة للبحث
  getQuickSuggestions: (query, limit = 5) => {
    const { customers } = get();
    if (!query.trim()) return [];

    const searchTerm = query.toLowerCase().trim();
    const suggestions = customers
      .filter(customer =>
        customer.name.toLowerCase().startsWith(searchTerm) ||
        customer.phone.startsWith(query) ||
        (customer.clientCode && customer.clientCode.toLowerCase().startsWith(searchTerm))
      )
      .slice(0, limit)
      .map(customer => ({
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        clientCode: customer.clientCode,
        businessName: customer.businessName,
        type: customer.clientCode?.toLowerCase().startsWith(searchTerm) ? 'code' :
              customer.phone.startsWith(query) ? 'phone' : 'name',
        matchText: customer.clientCode?.toLowerCase().startsWith(searchTerm) ? customer.clientCode :
                  customer.phone.startsWith(query) ? customer.phone : customer.name
      }));

    return suggestions;
  },

  getSuggestions: (query) => {
    if (!query.trim()) {
      set({ suggestions: [] });
      return;
    }
    
    const suggestions = getCustomerCodeSuggestions(query, 10);
    set({ suggestions });
  },

  clearSuggestions: () => {
    set({ suggestions: [] });
  },

  generateNewCustomerCode: () => {
    const codeData = generateCustomerCode();
    return codeData.code;
  },

  validateCode: (code, excludeId) => {
    const validation = validateCustomerCode(code);
    if (!validation.isValid) {
      return validation;
    }
    
    if (!isCustomerCodeUnique(code, excludeId)) {
      return { isValid: false, error: 'كود العميل مستخدم بالفعل' };
    }
    
    return { isValid: true };
  },

  findByCode: (code) => {
    return findCustomerByCode(code);
  },

  getStats: () => {
    return getCustomerCodeStats();
  },

  clearError: () => {
    set({ error: null });
  },

  refreshData: () => {
    get().loadCustomers();
  },
}));

// دالة تهيئة متجر العملاء
export const initializeCustomerStore = async () => {
  try {
    console.log('🔄 تهيئة متجر العملاء...');
    const customers = await loadCustomersFromStorage();
    useCustomerStore.setState({ customers });
    console.log('✅ تم تهيئة متجر العملاء بنجاح مع', customers.length, 'عميل');
  } catch (error) {
    console.error('❌ فشل في تهيئة متجر العملاء:', error);
    useCustomerStore.setState({ customers: [] });
  }
};

// دالة تهيئة متجر العملاء
export const initializeCustomerStore = async () => {
  try {
    console.log('🔄 تهيئة متجر العملاء...');
    const customers = await loadCustomersFromStorage();
    useCustomerStore.setState({ customers });
    console.log('✅ تم تهيئة متجر العملاء بنجاح مع', customers.length, 'عميل');
  } catch (error) {
    console.error('❌ فشل في تهيئة متجر العملاء:', error);
    useCustomerStore.setState({ customers: [] });
  }
};
