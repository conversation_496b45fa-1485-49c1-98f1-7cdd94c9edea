import { UnifiedStorageManager } from './unifiedStorage';
import { STORAGE_KEYS } from './storageKeys';

// أداة تنظيف التخزين المحلي المحدثة للنظام الموحد
export const cleanupLocalStorage = async () => {
  try {
    console.log('🧹 بدء تنظيف التخزين باستخدام النظام الموحد...');

    // تنظيف البيانات التجريبية والمكررة للفواتير
    const invoicesResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
    if (invoicesResult.success) {
      const invoices = invoicesResult.data;

      // إزالة الفواتير المكررة
      const uniqueInvoices = invoices.filter((invoice: any, index: number, self: any[]) =>
        index === self.findIndex(inv => inv.id === invoice.id)
      );

      if (uniqueInvoices.length !== invoices.length) {
        console.log(`🔄 إزالة ${invoices.length - uniqueInvoices.length} فاتورة مكررة`);
        await UnifiedStorageManager.setItem(STORAGE_KEYS.INVOICES, uniqueInvoices);
      }
    }

    // استخدام دالة دمج البيانات المكررة من النظام الموحد
    const mergeResult = await UnifiedStorageManager.mergeDuplicateKeys();
    console.log('🔄 نتيجة دمج البيانات:', mergeResult);

    // فحص حالة النظام بعد التنظيف
    const healthCheck = await UnifiedStorageManager.checkUnifiedSystemHealth();

    return {
      success: true,
      message: `تم تنظيف التخزين بنجاح باستخدام النظام الموحد`,
      mergeStats: mergeResult,
      systemHealth: healthCheck,
      invoicesCount: invoicesResult.success ? invoicesResult.data.length : 0
    };
  } catch (error) {
    console.error('❌ فشل في تنظيف التخزين:', error);
    return {
      success: false,
      message: 'فشل في تنظيف التخزين باستخدام النظام الموحد',
      error: error
    };
  }
};

// دالة إعادة تعيين كامل للتخزين (للاستخدام في حالات الطوارئ)
export const resetLocalStorage = async () => {
  try {
    console.log('⚠️ بدء إعادة تعيين التخزين الكامل...');

    // حفظ نسخة احتياطية شاملة قبل الحذف
    const invoicesResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
    const customersResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.CUSTOMERS, []);
    const settingsResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.COMPANY_SETTINGS, {});

    const backup = {
      invoices: invoicesResult.data,
      customers: customersResult.data,
      settings: settingsResult.data,
      timestamp: new Date().toISOString(),
      version: '2.0-unified'
    };

    // حفظ النسخة الاحتياطية
    const backupKey = `${STORAGE_KEYS.BACKUP_PREFIX}${Date.now()}`;
    await UnifiedStorageManager.setItem(backupKey, backup);

    // مسح جميع البيانات
    localStorage.clear();
    
    return {
      success: true,
      message: 'تم إعادة تعيين التخزين مع حفظ نسخة احتياطية',
      backup: backup
    };
  } catch (error) {
    return {
      success: false,
      message: 'فشل في إعادة تعيين التخزين',
      error: error
    };
  }
};

// دالة فحص حالة التخزين
export const checkStorageHealth = () => {
  try {
    const invoices = JSON.parse(localStorage.getItem('invoices') || '[]');
    const settings = JSON.parse(localStorage.getItem('company-settings') || '{}');

    // فحص الفواتير المكررة
    const duplicates = invoices.length - new Set(invoices.map((inv: any) => inv.id)).size;

    // فحص حجم التخزين
    const storageSize = new Blob([JSON.stringify(invoices)]).size;

    return {
      invoicesCount: invoices.length,
      duplicatesCount: duplicates,
      storageSize: Math.round(storageSize / 1024) + ' KB',
      hasSettings: Object.keys(settings).length > 0,
      health: duplicates === 0 ? 'جيد' : 'يحتاج تنظيف'
    };
  } catch (error) {
    return {
      error: 'فشل في فحص حالة التخزين',
      health: 'خطأ'
    };
  }
};

// دالة تشخيص مبسطة لمشكلة localStorage
export const diagnoseStorageIssue = () => {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [] as string[],
    recommendations: [] as string[],
    data: {} as any
  };

  try {
    if (typeof Storage === "undefined") {
      diagnosis.issues.push("localStorage غير مدعوم في هذا المتصفح");
      return diagnosis;
    }

    // استخدام النظام الموحد لقراءة الفواتير
    const invoicesResult = await UnifiedStorageManager.getItem(STORAGE_KEYS.INVOICES, []);
    const invoices = invoicesResult.success ? invoicesResult.data : [];
    const uniqueIds = new Set(invoices.map((inv: any) => inv.id));
    const duplicatesCount = invoices.length - uniqueIds.size;

    diagnosis.data.invoicesCount = invoices.length;
    diagnosis.data.totalStorageSize = Math.round(JSON.stringify(localStorage).length / 1024) + ' KB';

    if (duplicatesCount > 0) {
      diagnosis.issues.push(`يوجد ${duplicatesCount} فاتورة مكررة`);
      diagnosis.recommendations.push("تشغيل عملية تنظيف البيانات");
    }

    if (diagnosis.issues.length === 0) {
      diagnosis.recommendations.push("البيانات تبدو سليمة");
    }

    return diagnosis;
  } catch (error) {
    diagnosis.issues.push("خطأ في عملية التشخيص");
    return diagnosis;
  }
};

// دالة إصلاح البيانات التالفة
export const repairStorageData = () => {
  try {
    const diagnosis = diagnoseStorageIssue();
    const repairs = [];

    // إصلاح بيانات الفواتير
    let invoices = [];
    try {
      const invoicesRaw = localStorage.getItem('invoices');
      if (invoicesRaw) {
        invoices = JSON.parse(invoicesRaw);
        if (!Array.isArray(invoices)) {
          invoices = [];
          repairs.push("تم إعادة تعيين بيانات الفواتير إلى مصفوفة فارغة");
        }
      }
    } catch (error) {
      invoices = [];
      repairs.push("تم إنشاء مصفوفة فواتير جديدة بسبب تلف البيانات");
    }

    // إزالة الفواتير المكررة
    const uniqueInvoices = invoices.filter((invoice: any, index: number, self: any[]) =>
      index === self.findIndex(inv => inv.id === invoice.id)
    );

    if (uniqueInvoices.length !== invoices.length) {
      repairs.push(`تم إزالة ${invoices.length - uniqueInvoices.length} فاتورة مكررة`);
      invoices = uniqueInvoices;
    }

    // إصلاح الفواتير التالفة
    const validInvoices = invoices.filter((inv: any) => {
      if (!inv.id || !inv.client || !inv.services || !Array.isArray(inv.services)) {
        return false;
      }
      return true;
    });

    if (validInvoices.length !== invoices.length) {
      repairs.push(`تم إزالة ${invoices.length - validInvoices.length} فاتورة تالفة`);
      invoices = validInvoices;
    }

    // حفظ البيانات المصلحة
    localStorage.setItem('invoices', JSON.stringify(invoices));

    // إصلاح الإعدادات
    let settings = {};
    try {
      const settingsRaw = localStorage.getItem('company-settings');
      if (settingsRaw) {
        settings = JSON.parse(settingsRaw);
        if (typeof settings !== 'object') {
          settings = {};
          repairs.push("تم إعادة تعيين إعدادات الشركة");
        }
      }
    } catch (error) {
      settings = {};
      repairs.push("تم إنشاء إعدادات شركة جديدة");
    }

    localStorage.setItem('company-settings', JSON.stringify(settings));

    return {
      success: true,
      message: "تم إصلاح البيانات بنجاح",
      repairs: repairs,
      finalInvoicesCount: invoices.length
    };

  } catch (error) {
    return {
      success: false,
      message: "فشل في إصلاح البيانات",
      error: error
    };
  }
};
