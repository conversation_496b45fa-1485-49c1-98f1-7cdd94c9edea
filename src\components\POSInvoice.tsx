'use client';

import React, { useState } from 'react';
import { Invoice } from '@/types';
import { exportPOSInvoiceAsImage, sharePOSInvoiceAsImage } from '@/utils/imageExporter';
import { useSettingsStore } from '@/store/settingsStore';

interface POSInvoiceProps {
  invoice: Invoice;
  onClose: () => void;
}

const POSInvoice: React.FC<POSInvoiceProps> = ({ invoice, onClose }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const { settings } = useSettingsStore();

  // دالة تصدير الفاتورة كصورة
  const handleExportAsImage = async () => {
    try {
      setIsExporting(true);
      await exportPOSInvoiceAsImage(invoice.id || 'unknown', invoice.id);
      alert('تم تصدير الفاتورة كصورة بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير الصورة:', error);
      alert('حدث خطأ أثناء تصدير الصورة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsExporting(false);
    }
  };

  // دالة مشاركة الفاتورة كصورة
  const handleShareAsImage = async () => {
    try {
      setIsSharing(true);
      await sharePOSInvoiceAsImage('pos-invoice-content', `فاتورة-${invoice.id || 'unknown'}`);
    } catch (error) {
      console.error('خطأ في مشاركة الصورة:', error);
      alert('حدث خطأ أثناء مشاركة الصورة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsSharing(false);
    }
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getServiceName = (serviceType: string) => {
    const serviceNames: { [key: string]: string } = {
      consultation: 'استشارات',
      pattern: 'باترون',
      pattern_printing: 'طباعة باترون',
      manufacturing: 'تصنيع',
      shipping: 'شحن',
      marketing: 'تسويق',
      photography: 'تصوير',
      products: 'منتجات',
      raw_purchases: 'مشتريات خام',
      samples: 'عينات'
    };
    return serviceNames[serviceType] || serviceType;
  };

  const getServiceDetails = (service: any) => {
    let details = '';
    let quantity = '';
    let unitPrice = 0;
    let serviceTotal = 0;

    switch (service.serviceType) {
      case 'consultation':
        serviceTotal = service.cost || 0;
        details = `${service.topic || 'استشارة'} - ${service.hours || 0} ساعة`;
        quantity = `${service.hours || 0} ساعة`;
        unitPrice = service.cost || 0;
        break;
      
      case 'pattern':
        serviceTotal = service.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || model.cost || 0), 0) || 0;
        details = `${service.models?.length || 0} نموذج باترون`;
        quantity = `${service.models?.length || 0} نموذج`;
        unitPrice = serviceTotal / (service.models?.length || 1);
        break;
      
      case 'pattern_printing':
        serviceTotal = service.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
        details = `طباعة ${service.files?.length || 0} ملف`;
        quantity = `${service.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
        unitPrice = serviceTotal;
        break;
      
      case 'manufacturing':
        serviceTotal = service.models?.reduce((sum: number, model: any) => {
          return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
        }, 0) || 0;
        details = `تصنيع ${service.models?.length || 0} نموذج`;
        quantity = `${service.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 0}`;
        unitPrice = serviceTotal / (service.models?.length || 1);
        break;
      
      case 'shipping':
        serviceTotal = service.total || 0;
        details = `شحن ${service.item || 'منتج'}`;
        quantity = `${service.quantity || 1}`;
        unitPrice = service.unitPrice || 0;
        break;
      
      case 'products':
        let productsSubtotal = service.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
        if (service.discountAmount) {
          productsSubtotal -= service.discountAmount || 0;
        }
        serviceTotal = Math.max(0, productsSubtotal);
        details = `${service.items?.length || 0} منتج`;
        quantity = `${service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
        unitPrice = serviceTotal / (service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
        break;
      
      default:
        serviceTotal = service.cost || service.total || 0;
        details = getServiceName(service.serviceType);
        quantity = '1';
        unitPrice = serviceTotal;
    }

    return { details, quantity, unitPrice, serviceTotal };
  };

  const handlePrint = () => {
    // إنشاء نافذة طباعة منفصلة
    const printWindow = window.open('', '_blank', 'width=300,height=600');

    if (printWindow) {
      // الحصول على محتوى الفاتورة مع تحديث الشعار
      const posContent = document.querySelector('.pos-invoice-content')?.innerHTML;

      if (posContent) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة POS - ${invoice.id}</title>
            <style>
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }

              body {
                font-family: 'Courier New', monospace;
                font-size: 10px;
                line-height: 1.2;
                color: black;
                background: white;
                width: 80mm;
                margin: 0 auto;
                padding: 2mm;
              }

              @page {
                size: 80mm auto;
                margin: 2mm;
              }

              @media print {
                body {
                  width: 80mm !important;
                  font-size: 9px !important;
                }
              }

              .text-center { text-align: center; }
              .text-sm { font-size: 9px; }
              .text-xs { font-size: 8px; }
              .text-lg { font-size: 12px; }
              .font-bold { font-weight: bold; }
              .font-semibold { font-weight: 600; }
              .mb-1 { margin-bottom: 2px; }
              .mb-2 { margin-bottom: 4px; }
              .mb-3 { margin-bottom: 6px; }
              .pb-3 { padding-bottom: 6px; }
              .pt-3 { padding-top: 6px; }
              .mr-3 { margin-right: 6px; }
              .mt-2 { margin-top: 4px; }

              .border-b { border-bottom: 1px solid #ccc; }
              .border-dashed { border-style: dashed; }
              .border-gray-400 { border-color: #999; }

              .flex { display: flex; }
              .justify-between { justify-content: space-between; }

              /* Thermal Printer Colors - Black and White Only */
              .text-green-600 { color: #000; font-weight: bold; }
              .text-orange-600 { color: #000; font-weight: bold; }
              .text-red-600 { color: #000; font-weight: bold; }
              .text-gray-600 { color: #666; }
              .text-gray-700 { color: #333; }

              /* Enhanced Logo Styles for Print - Thermal Printer Optimized */
              .inline-block { display: inline-block; }
              .bg-gradient-to-br { background: #000; } /* Black background for thermal printing */
              .rounded-xl { border-radius: 8px; }
              .p-3 { padding: 6px; }
              .p-1 { padding: 2px; }
              .w-20 { width: 40px; }
              .h-20 { height: 40px; }
              .w-full { width: 100%; }
              .h-full { height: 100%; }
              .mx-auto { margin-left: auto; margin-right: auto; }
              .flex { display: flex; }
              .items-center { align-items: center; }
              .justify-center { justify-content: center; }
              .text-white { color: white; }
              .bg-white { background-color: white; }
              .font-extrabold { font-weight: 800; }
              .font-semibold { font-weight: 600; }
              .leading-tight { line-height: 1.1; }
              .tracking-wide { letter-spacing: 0.025em; }
              .tracking-widest { letter-spacing: 0.1em; }
              .opacity-90 { opacity: 0.9; }
              .space-y-1 > * + * { margin-top: 2px; }
              .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
              .border { border: 1px solid #e5e7eb; }
              .border-gray-200 { border-color: #e5e7eb; }
              .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
              .relative { position: relative; }
              .absolute { position: absolute; }
              .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
              .top-1 { top: 2px; }
              .right-1 { right: 2px; }
              .bottom-2 { bottom: 4px; }
              .left-2 { left: 4px; }
              .w-3 { width: 6px; }
              .h-3 { height: 6px; }
              .w-2 { width: 4px; }
              .h-2 { height: 4px; }
              .w-8 { width: 16px; }
              .h-0\.5 { height: 1px; }
              .mt-1 { margin-top: 2px; }
              .rounded-full { border-radius: 50%; }
              .z-10 { z-index: 10; }
              .overflow-hidden { overflow: hidden; }

              /* Image Styles for Uploaded Logo */
              .object-contain {
                object-fit: contain;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
              }

              /* Enhanced Layout Styles for Thermal Print - Black and White */
              .bg-gray-50 { background-color: #f8f8f8; }
              .bg-blue-50 { background-color: #f0f0f0; }
              .text-blue-600 { color: #000; font-weight: bold; }
              .text-blue-800 { color: #000; font-weight: bold; }
              .text-green-600 { color: #000; font-weight: bold; }
              .text-green-800 { color: #000; font-weight: bold; }
              .text-orange-800 { color: #000; font-weight: bold; }
              .text-red-800 { color: #000; font-weight: bold; }
              .bg-green-100 { background-color: #f0f0f0; }
              .bg-orange-100 { background-color: #f0f0f0; }
              .bg-red-100 { background-color: #f0f0f0; }
              .grid { display: grid; }
              .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
              .gap-2 { gap: 8px; }
              .items-center { align-items: center; }
              .border-b { border-bottom: 1px solid #e5e7eb; }
              .pb-1 { padding-bottom: 2px; }
              .font-medium { font-weight: 500; }
              .space-y-2 > * + * { margin-top: 8px; }
              .px-2 { padding-left: 4px; padding-right: 4px; }
              .py-1 { padding-top: 2px; padding-bottom: 2px; }
              .px-4 { padding-left: 8px; padding-right: 8px; }
              .py-2 { padding-top: 4px; padding-bottom: 4px; }
              .rounded-full { border-radius: 9999px; }
              .border-2 { border-width: 2px; }
              .border-t-2 { border-top-width: 2px; }
              .pt-4 { padding-top: 8px; }
              .mb-4 { margin-bottom: 8px; }
              .mb-3 { margin-bottom: 6px; }
              .mb-2 { margin-bottom: 4px; }
              .mb-1 { margin-bottom: 2px; }
              .mt-1 { margin-top: 2px; }
              .mt-2 { margin-top: 4px; }
              .mr-2 { margin-right: 4px; }
              .mr-3 { margin-right: 6px; }
              .tracking-wide { letter-spacing: 0.025em; }
              .leading-relaxed { line-height: 1.625; }
            </style>
          </head>
          <body>
            ${posContent}
          </body>
          </html>
        `);

        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 250);
        };
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header with action buttons */}
        <div className="p-4 border-b border-gray-200 print:hidden">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-xl font-bold">فاتورة POS</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 flex-wrap">
            {/* Print Button */}
            <button
              onClick={handlePrint}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              طباعة
            </button>

            {/* Export as Image Button */}
            <button
              onClick={handleExportAsImage}
              disabled={isExporting}
              className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {isExporting ? 'جاري التصدير...' : 'تصدير كصورة'}
            </button>

            {/* Share as Image Button */}
            <button
              onClick={handleShareAsImage}
              disabled={isSharing}
              className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              {isSharing ? 'جاري المشاركة...' : 'مشاركة'}
            </button>
          </div>
        </div>

        {/* POS Invoice Content - Enhanced for 80mm Thermal Printers */}
        <div
          id="pos-invoice-content"
          className="pos-invoice-content p-4"
          style={{
            fontFamily: 'Arial, sans-serif',
            fontSize: '13px',
            lineHeight: '1.5',
            maxWidth: '320px',
            margin: '0 auto',
            color: '#000'
          }}
        >
          {/* Company Header with Logo */}
          <div className="text-center border-b border-dashed border-gray-400 pb-3 mb-3">
            {/* Company Logo Section - Thermal Printer Optimized */}
            <div className="mb-3">
              {settings.companyLogo ? (
                /* Uploaded Company Logo */
                <div className="w-16 h-16 mx-auto flex items-center justify-center">
                  <img
                    src={settings.companyLogo}
                    alt="شعار الشركة"
                    className="w-full h-full object-contain"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      imageRendering: 'crisp-edges',
                      filter: 'contrast(1.2) brightness(1.1)',
                      display: 'block'
                    }}
                    crossOrigin="anonymous"
                  />
                </div>
              ) : (
                /* Fallback CSS Logo - Thermal Optimized */
                <div className="w-16 h-16 mx-auto bg-black rounded-lg flex items-center justify-center">
                  {/* Logo Content */}
                  <div className="text-white font-bold text-center">
                    <div className="text-lg leading-tight font-extrabold tracking-wide">OKA</div>
                    <div className="text-xs leading-tight font-semibold tracking-widest">GROUP</div>
                    <div className="w-6 h-0.5 bg-white mx-auto mt-1"></div>
                  </div>
                </div>
              )}
            </div>

            {/* Company Name - Thermal Printer Optimized */}
            <h1 className="text-xl font-bold mb-1 tracking-wide" style={{ color: '#000' }}>
              {settings.companyName || 'OKA GROUP'}
            </h1>
            <p className="text-sm font-semibold mb-2" style={{ color: '#333' }}>
              {settings.description || 'مجموعة أوكا للخدمات المتكاملة'}
            </p>

            {/* Contact Information - Using Settings Data */}
            <div className="mt-3 space-y-1 text-xs leading-relaxed">
              <div className="flex items-center justify-center gap-1">
                <span>📧</span>
                <span className="font-medium">{settings.email || '<EMAIL>'}</span>
              </div>
              <div className="flex items-center justify-center gap-1">
                <span>📞</span>
                <span className="font-medium">{settings.complaintsPhone || '01148637552'}</span>
              </div>
              <div className="flex items-center justify-center gap-1 text-center">
                <span>📍</span>
                <span className="font-medium">{settings.address || '73 ش 6 اكتوبر الجراش جسر السويس'}</span>
              </div>
            </div>
          </div>

          {/* Invoice Info - Thermal Printer Optimized */}
          <div className="mb-4 text-sm bg-white p-3 rounded-lg border border-gray-400">
            <div className="grid grid-cols-1 gap-2">
              <div className="flex justify-between items-center border-b border-gray-400 pb-1">
                <span style={{ color: '#666' }}>رقم الفاتورة:</span>
                <span className="font-bold" style={{ color: '#000' }}>#{invoice.id}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-400 pb-1">
                <span style={{ color: '#666' }}>التاريخ:</span>
                <span className="font-medium" style={{ color: '#000' }}>{formatDate(invoice.createdAt)}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-400 pb-1">
                <span style={{ color: '#666' }}>العميل:</span>
                <span className="font-bold" style={{ color: '#000' }}>{invoice.client.name}</span>
              </div>
              <div className="flex justify-between items-center">
                <span style={{ color: '#666' }}>الهاتف:</span>
                <span className="font-medium" style={{ color: '#000' }}>{invoice.client.phone}</span>
              </div>
            </div>
          </div>

          {/* Services Table - Thermal Printer Optimized */}
          <div className="border-t-2 border-dashed border-gray-400 pt-4 mb-4">
            <div className="text-center font-bold mb-3 text-sm bg-white py-2 rounded-lg border border-gray-400">
              📋 تفاصيل الطلب
            </div>

            {invoice.services.map((service, index) => {
              const serviceInfo = getServiceDetails(service);
              return (
                <div key={service.id} className="mb-3 p-2 bg-white rounded-lg border border-gray-400">
                  <div className="flex justify-between items-center font-bold text-sm mb-1">
                    <span style={{ color: '#000' }}>{index + 1}. {getServiceName(service.serviceType)}</span>
                    <span style={{ color: '#000', fontWeight: 'bold' }}>{formatCurrency(serviceInfo.serviceTotal)}</span>
                  </div>
                  <div className="text-xs mr-3 space-y-1" style={{ color: '#666' }}>
                    <div className="font-medium">{serviceInfo.details}</div>
                    <div className="flex justify-between">
                      <span>الكمية: <span className="font-semibold">{serviceInfo.quantity}</span></span>
                      <span>السعر: <span className="font-semibold">{formatCurrency(Number(serviceInfo.quantity) > 0 ? serviceInfo.serviceTotal / Number(serviceInfo.quantity) : serviceInfo.serviceTotal)}</span></span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Totals - Thermal Printer Optimized */}
          <div className="border-t-2 border-dashed border-gray-400 pt-4 mb-4 bg-white p-3 rounded-lg border border-gray-400">
            <div className="space-y-2">
              <div className="flex justify-between text-sm font-bold border-b border-gray-400 pb-1">
                <span style={{ color: '#000' }}>المجموع الكلي:</span>
                <span style={{ color: '#000', fontWeight: 'bold' }}>{formatCurrency(invoice.total)}</span>
              </div>
              <div className="flex justify-between text-sm border-b border-gray-400 pb-1">
                <span style={{ color: '#000' }}>المبلغ المدفوع:</span>
                <span style={{ color: '#000', fontWeight: 'bold' }}>{formatCurrency(invoice.paidAmount || 0)}</span>
              </div>
              <div className="flex justify-between text-sm font-bold">
                <span style={{ color: '#000' }}>المبلغ المتبقي:</span>
                <span style={{ color: '#000', fontWeight: 'bold' }}>{formatCurrency(invoice.total - (invoice.paidAmount || 0))}</span>
              </div>
            </div>
          </div>

          {/* Payment Status - Thermal Printer Optimized */}
          <div className="text-center border-t border-dashed border-gray-400 pt-3 mb-4">
            <div className="inline-block bg-white border-2 border-gray-400 rounded-lg px-4 py-2">
              <div className="text-sm font-bold" style={{ color: '#000' }}>
                حالة الدفع:
                <span className="mr-2 px-2 py-1 rounded-full text-xs font-semibold bg-white border border-gray-400" style={{ color: '#000' }}>
                  {invoice.paymentStatus}
                </span>
              </div>
            </div>
          </div>

          {/* Footer - Thermal Printer Optimized */}
          <div className="text-center border-t-2 border-dashed border-gray-400 pt-4 text-xs">
            <div className="bg-white p-3 rounded-lg border border-gray-400">
              <p className="font-bold mb-1" style={{ color: '#000' }}>🙏 شكراً لتعاملكم معنا</p>
              <p className="mb-2" style={{ color: '#000' }}>Thank you</p>
              <div className="border-t border-gray-400 pt-2 mt-2">
                <p style={{ color: '#666' }}>تاريخ الطباعة: {new Date().toLocaleDateString('ar-EG')}</p>
                <p className="text-xs mt-1" style={{ color: '#666' }}>Printed: {new Date().toLocaleString('en-US')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Print Button */}
        <div className="p-4 border-t border-gray-200 print:hidden">
          <div className="flex gap-2">
            <button
              onClick={handlePrint}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors"
            >
              🖨️ طباعة
            </button>
            <button
              onClick={onClose}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }

          body {
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
          }

          .pos-print-content {
            width: 80mm !important;
            max-width: 80mm !important;
            font-family: 'Courier New', monospace !important;
            font-size: 10px !important;
            line-height: 1.2 !important;
            color: black !important;
            background: white !important;
            margin: 0 !important;
            padding: 2mm !important;
          }

          .pos-invoice-content {
            width: 100% !important;
            max-width: 100% !important;
            font-size: 10px !important;
            line-height: 1.2 !important;
            padding: 0 !important;
            margin: 0 !important;
          }

          /* Logo styles for print */
          .pos-invoice-content .inline-block {
            display: inline-block !important;
          }

          .pos-invoice-content .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }

          .pos-invoice-content .rounded-lg {
            border-radius: 4px !important;
          }

          .pos-invoice-content .w-16 {
            width: 32px !important;
            height: 32px !important;
          }

          .pos-invoice-content .bg-gradient-to-br {
            background: #000 !important;
          }

          .pos-invoice-content .text-white {
            color: white !important;
          }

          /* Force black and white for thermal printing */
          .pos-invoice-content .text-blue-600,
          .pos-invoice-content .text-green-600,
          .pos-invoice-content .text-red-600,
          .pos-invoice-content .text-orange-600 {
            color: #000 !important;
            font-weight: bold !important;
          }

          .pos-invoice-content .bg-blue-50,
          .pos-invoice-content .bg-green-100,
          .pos-invoice-content .bg-orange-100,
          .pos-invoice-content .bg-red-100 {
            background-color: #f8f8f8 !important;
          }

          .pos-invoice-content .font-semibold {
            font-weight: 600 !important;
          }

          @page {
            size: 80mm auto;
            margin: 2mm;
          }

          /* إخفاء العناصر غير المرغوب فيها */
          .print\\:hidden {
            display: none !important;
          }
        }

        /* تحسين العرض العادي */
        .pos-invoice-content {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
        }
      `}</style>
    </div>
  );
};

export default POSInvoice;
