'use client';

import React from 'react';
import { Invoice } from '@/types';

interface POSInvoiceProps {
  invoice: Invoice;
  onClose: () => void;
}

const POSInvoice: React.FC<POSInvoiceProps> = ({ invoice, onClose }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getServiceName = (serviceType: string) => {
    const serviceNames: { [key: string]: string } = {
      consultation: 'استشارات',
      pattern: 'باترون',
      pattern_printing: 'طباعة باترون',
      manufacturing: 'تصنيع',
      shipping: 'شحن',
      marketing: 'تسويق',
      photography: 'تصوير',
      products: 'منتجات',
      raw_purchases: 'مشتريات خام',
      samples: 'عينات'
    };
    return serviceNames[serviceType] || serviceType;
  };

  const getServiceDetails = (service: any) => {
    let details = '';
    let quantity = '';
    let unitPrice = 0;
    let serviceTotal = 0;

    switch (service.serviceType) {
      case 'consultation':
        serviceTotal = service.cost || 0;
        details = `${service.topic || 'استشارة'} - ${service.hours || 0} ساعة`;
        quantity = `${service.hours || 0} ساعة`;
        unitPrice = service.cost || 0;
        break;
      
      case 'pattern':
        serviceTotal = service.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || model.cost || 0), 0) || 0;
        details = `${service.models?.length || 0} نموذج باترون`;
        quantity = `${service.models?.length || 0} نموذج`;
        unitPrice = serviceTotal / (service.models?.length || 1);
        break;
      
      case 'pattern_printing':
        serviceTotal = service.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
        details = `طباعة ${service.files?.length || 0} ملف`;
        quantity = `${service.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
        unitPrice = serviceTotal;
        break;
      
      case 'manufacturing':
        serviceTotal = service.models?.reduce((sum: number, model: any) => {
          return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
        }, 0) || 0;
        details = `تصنيع ${service.models?.length || 0} نموذج`;
        quantity = `${service.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 0}`;
        unitPrice = serviceTotal / (service.models?.length || 1);
        break;
      
      case 'shipping':
        serviceTotal = service.total || 0;
        details = `شحن ${service.item || 'منتج'}`;
        quantity = `${service.quantity || 1}`;
        unitPrice = service.unitPrice || 0;
        break;
      
      case 'products':
        let productsSubtotal = service.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
        if (service.discountAmount) {
          productsSubtotal -= service.discountAmount || 0;
        }
        serviceTotal = Math.max(0, productsSubtotal);
        details = `${service.items?.length || 0} منتج`;
        quantity = `${service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
        unitPrice = serviceTotal / (service.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
        break;
      
      default:
        serviceTotal = service.cost || service.total || 0;
        details = getServiceName(service.serviceType);
        quantity = '1';
        unitPrice = serviceTotal;
    }

    return { details, quantity, unitPrice, serviceTotal };
  };

  const handlePrint = () => {
    // إنشاء نافذة طباعة منفصلة
    const printWindow = window.open('', '_blank', 'width=300,height=600');

    if (printWindow) {
      const posContent = document.querySelector('.pos-invoice-content')?.innerHTML;

      if (posContent) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة POS - ${invoice.id}</title>
            <style>
              * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
              }

              body {
                font-family: 'Courier New', monospace;
                font-size: 10px;
                line-height: 1.2;
                color: black;
                background: white;
                width: 80mm;
                margin: 0 auto;
                padding: 2mm;
              }

              @page {
                size: 80mm auto;
                margin: 2mm;
              }

              @media print {
                body {
                  width: 80mm !important;
                  font-size: 9px !important;
                }
              }

              .text-center { text-align: center; }
              .text-sm { font-size: 9px; }
              .text-xs { font-size: 8px; }
              .text-lg { font-size: 12px; }
              .font-bold { font-weight: bold; }
              .font-semibold { font-weight: 600; }
              .mb-1 { margin-bottom: 2px; }
              .mb-2 { margin-bottom: 4px; }
              .mb-3 { margin-bottom: 6px; }
              .pb-3 { padding-bottom: 6px; }
              .pt-3 { padding-top: 6px; }
              .mr-3 { margin-right: 6px; }
              .mt-2 { margin-top: 4px; }

              .border-b { border-bottom: 1px solid #ccc; }
              .border-dashed { border-style: dashed; }
              .border-gray-400 { border-color: #999; }

              .flex { display: flex; }
              .justify-between { justify-content: space-between; }

              .text-green-600 { color: #059669; }
              .text-orange-600 { color: #ea580c; }
              .text-red-600 { color: #dc2626; }
              .text-gray-600 { color: #4b5563; }
              .text-gray-700 { color: #374151; }

              /* Logo Styles for Print */
              .inline-block { display: inline-block; }
              .bg-gray-100 { background-color: #f3f4f6; }
              .rounded-lg { border-radius: 6px; }
              .p-2 { padding: 4px; }
              .w-16 { width: 32px; }
              .h-16 { height: 32px; }
              .mx-auto { margin-left: auto; margin-right: auto; }
              .bg-gradient-to-br { background: linear-gradient(135deg, #2563eb, #1d4ed8); }
              .flex { display: flex; }
              .items-center { align-items: center; }
              .justify-center { justify-content: center; }
              .text-white { color: white; }
              .leading-tight { line-height: 1.1; }
              .space-y-1 > * + * { margin-top: 2px; }
            </style>
          </head>
          <body>
            ${posContent}
          </body>
          </html>
        `);

        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 250);
        };
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header with close button */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200 print:hidden">
          <h2 className="text-xl font-bold">فاتورة POS</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
          >
            ×
          </button>
        </div>

        {/* POS Invoice Content */}
        <div className="pos-invoice-content p-4" style={{ fontFamily: 'monospace', fontSize: '12px', lineHeight: '1.4' }}>
          {/* Company Header with Logo */}
          <div className="text-center border-b border-dashed border-gray-400 pb-3 mb-3">
            {/* Company Logo Section */}
            <div className="mb-2">
              <div className="inline-block bg-gray-100 rounded-lg p-2 mb-2">
                <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg">
                  <div className="text-white font-bold text-center">
                    <div className="text-lg leading-tight">OKA</div>
                    <div className="text-xs leading-tight">GROUP</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Company Name */}
            <h1 className="text-lg font-bold mb-1">OKA GROUP</h1>
            <p className="text-sm font-semibold text-gray-700">مجموعة أوكا للخدمات المتكاملة</p>

            {/* Contact Information */}
            <div className="mt-2 space-y-1">
              <p className="text-xs">📧 البريد: <EMAIL></p>
              <p className="text-xs">📞 الهاتف: 0114954118</p>
              <p className="text-xs">📍 المقر: 73 ش 6 اكتوبر الجراش جسر السويس</p>
            </div>
          </div>

          {/* Invoice Info */}
          <div className="mb-3 text-sm">
            <div className="flex justify-between">
              <span>رقم الفاتورة:</span>
              <span className="font-bold">#{invoice.id}</span>
            </div>
            <div className="flex justify-between">
              <span>التاريخ:</span>
              <span>{formatDate(invoice.createdAt)}</span>
            </div>
            <div className="flex justify-between">
              <span>العميل:</span>
              <span className="font-bold">{invoice.client.name}</span>
            </div>
            <div className="flex justify-between">
              <span>الهاتف:</span>
              <span>{invoice.client.phone}</span>
            </div>
          </div>

          {/* Services Table */}
          <div className="border-t border-dashed border-gray-400 pt-3 mb-3">
            <div className="text-center font-bold mb-2 text-sm">تفاصيل الطلب</div>
            
            {invoice.services.map((service, index) => {
              const serviceInfo = getServiceDetails(service);
              return (
                <div key={service.id} className="mb-2 text-xs">
                  <div className="flex justify-between font-bold">
                    <span>{index + 1}. {getServiceName(service.serviceType)}</span>
                    <span>{formatCurrency(serviceInfo.serviceTotal)}</span>
                  </div>
                  <div className="text-gray-600 mr-3">
                    <div>{serviceInfo.details}</div>
                    <div>الكمية: {serviceInfo.quantity}</div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Totals */}
          <div className="border-t border-dashed border-gray-400 pt-3 mb-3">
            <div className="flex justify-between text-sm font-bold">
              <span>المجموع الكلي:</span>
              <span>{formatCurrency(invoice.total)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>المبلغ المدفوع:</span>
              <span>{formatCurrency(invoice.paidAmount || 0)}</span>
            </div>
            <div className="flex justify-between text-sm font-bold">
              <span>المبلغ المتبقي:</span>
              <span>{formatCurrency(invoice.total - (invoice.paidAmount || 0))}</span>
            </div>
          </div>

          {/* Payment Status */}
          <div className="text-center border-t border-dashed border-gray-400 pt-3 mb-3">
            <div className="text-sm font-bold">
              حالة الدفع: 
              <span className={`mr-2 ${
                invoice.paymentStatus === 'مدفوع بالكامل' ? 'text-green-600' :
                invoice.paymentStatus === 'مدفوع جزئياً' ? 'text-orange-600' :
                'text-red-600'
              }`}>
                {invoice.paymentStatus}
              </span>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center border-t border-dashed border-gray-400 pt-3 text-xs">
            <p>شكراً لتعاملكم معنا</p>
            <p>Thank you</p>
            <p className="mt-2">تاريخ الطباعة: {new Date().toLocaleDateString('ar-EG')}</p>
          </div>
        </div>

        {/* Print Button */}
        <div className="p-4 border-t border-gray-200 print:hidden">
          <div className="flex gap-2">
            <button
              onClick={handlePrint}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors"
            >
              🖨️ طباعة
            </button>
            <button
              onClick={onClose}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }

          body {
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
          }

          .pos-print-content {
            width: 80mm !important;
            max-width: 80mm !important;
            font-family: 'Courier New', monospace !important;
            font-size: 10px !important;
            line-height: 1.2 !important;
            color: black !important;
            background: white !important;
            margin: 0 !important;
            padding: 2mm !important;
          }

          .pos-invoice-content {
            width: 100% !important;
            max-width: 100% !important;
            font-size: 10px !important;
            line-height: 1.2 !important;
            padding: 0 !important;
            margin: 0 !important;
          }

          /* Logo styles for print */
          .pos-invoice-content .inline-block {
            display: inline-block !important;
          }

          .pos-invoice-content .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }

          .pos-invoice-content .rounded-lg {
            border-radius: 4px !important;
          }

          .pos-invoice-content .w-16 {
            width: 32px !important;
            height: 32px !important;
          }

          .pos-invoice-content .bg-gradient-to-br {
            background: linear-gradient(135deg, #2563eb, #1d4ed8) !important;
          }

          .pos-invoice-content .text-white {
            color: white !important;
          }

          .pos-invoice-content .font-semibold {
            font-weight: 600 !important;
          }

          @page {
            size: 80mm auto;
            margin: 2mm;
          }

          /* إخفاء العناصر غير المرغوب فيها */
          .print\\:hidden {
            display: none !important;
          }
        }

        /* تحسين العرض العادي */
        .pos-invoice-content {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
        }
      `}</style>
    </div>
  );
};

export default POSInvoice;
