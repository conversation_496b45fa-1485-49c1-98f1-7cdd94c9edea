"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicesList.tsx":
/*!*****************************************!*\
  !*** ./src/components/InvoicesList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst InvoicesList = ()=>{\n    _s();\n    const { invoices } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date_desc\");\n    const [filteredInvoices, setFilteredInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previewInvoice, setPreviewInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [posInvoice, setPosInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // إعادة تحميل البيانات من التخزين الآمن عند تحميل المكون\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInvoices = ()=>{\n            // استخدام النظام الآمن لتحميل البيانات\n            const result = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState().invoices;\n            // التحقق من عدم وجود تكرار في البيانات\n            const uniqueInvoices = result.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n            // تحديث الحالة المحلية\n            setFilteredInvoices(uniqueInvoices);\n        };\n        // تحميل فوري\n        loadInvoices();\n        // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري\n        const handleStorageChange = (e)=>{\n            if (e.key === \"invoices\") {\n                loadInvoices();\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = [\n            ...invoices\n        ];\n        // البحث\n        if (searchTerm) {\n            filtered = filtered.filter((invoice)=>{\n                var _invoice_id;\n                return invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.client.phone.includes(searchTerm) || ((_invoice_id = invoice.id) === null || _invoice_id === void 0 ? void 0 : _invoice_id.includes(searchTerm));\n            });\n        }\n        // الفلترة\n        if (filterBy !== \"all\") {\n            filtered = filtered.filter((invoice)=>{\n                switch(filterBy){\n                    case \"consultation\":\n                        return invoice.services.some((s)=>s.serviceType === \"consultation\");\n                    case \"pattern\":\n                        return invoice.services.some((s)=>s.serviceType === \"pattern\");\n                    case \"photography\":\n                        return invoice.services.some((s)=>s.serviceType === \"photography\");\n                    case \"shipping\":\n                        return invoice.services.some((s)=>s.serviceType === \"shipping\");\n                    case \"high_value\":\n                        return invoice.total > 1000;\n                    case \"recent\":\n                        const oneWeekAgo = new Date();\n                        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n                        return new Date(invoice.createdAt) > oneWeekAgo;\n                    case \"paid_full\":\n                        return invoice.paymentStatus === \"مدفوع بالكامل\";\n                    case \"paid_partial\":\n                        return invoice.paymentStatus === \"مدفوع جزئياً\";\n                    case \"unpaid\":\n                        return invoice.paymentStatus === \"غير مدفوع\" || !invoice.paymentStatus;\n                    default:\n                        return true;\n                }\n            });\n        }\n        // الترتيب\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"date_desc\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"date_asc\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"amount_desc\":\n                    return b.total - a.total;\n                case \"amount_asc\":\n                    return a.total - b.total;\n                case \"client_name\":\n                    return a.client.name.localeCompare(b.client.name, \"ar\");\n                default:\n                    return 0;\n            }\n        });\n        setFilteredInvoices(filtered);\n    }, [\n        invoices,\n        searchTerm,\n        filterBy,\n        sortBy\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceNames = (services)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return services.map((s)=>serviceNames[s.serviceType] || s.serviceType).join(\"، \");\n    };\n    // دالة عرض رسائل التأكيد المحسنة\n    const showSuccessMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 3000);\n    };\n    const showErrorMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 4000);\n    };\n    const handleExportPDF = async (invoice)=>{\n        try {\n            // فتح المعاينة أولاً للحصول على نفس التنسيق\n            setPreviewInvoice(invoice);\n            // انتظار قصير للتأكد من عرض المعاينة\n            setTimeout(async ()=>{\n                try {\n                    await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n                    showSuccessMessage(\"تم تصدير PDF للفاتورة \".concat(invoice.id, \" بنجاح! \\uD83C\\uDF89\"));\n                    // إغلاق المعاينة بعد التصدير بثانية واحدة\n                    setTimeout(()=>{\n                        setPreviewInvoice(null);\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"❌ Error exporting PDF:\", error);\n                    showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n                    setPreviewInvoice(null);\n                }\n            }, 500);\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تصدير PDF.\");\n        }\n    };\n    const handlePreviewInvoice = (invoice)=>{\n        setPreviewInvoice(invoice);\n    };\n    const handlePOSInvoice = (invoice)=>{\n        setPosInvoice(invoice);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewInvoice(null);\n    };\n    const handleEditFromPreview = ()=>{\n        // For now, just close the preview\n        // In a full implementation, this would navigate to edit mode\n        setPreviewInvoice(null);\n        alert(\"وظيفة التعديل ستكون متاحة قريباً\");\n    };\n    const handleExportFromPreview = async ()=>{\n        if (previewInvoice) {\n            await handleExportPDF(previewInvoice);\n        }\n    };\n    // دالة حذف الفاتورة مع تأكيد\n    const handleDeleteInvoice = (invoice)=>{\n        const confirmMessage = \"هل أنت متأكد من حذف هذه الفاتورة؟\\n\\nالفاتورة: \".concat(invoice.id, \"\\nالعميل: \").concat(invoice.client.name, \"\\nالمبلغ: \").concat(formatCurrency(invoice.total), \"\\n\\n⚠️ هذا الإجراء لا يمكن التراجع عنه!\");\n        if (window.confirm(confirmMessage)) {\n            const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState();\n            store.deleteInvoice(invoice.id);\n            if (previewInvoice && previewInvoice.id === invoice.id) {\n                setPreviewInvoice(null);\n            }\n            setTimeout(()=>{\n                const updatedInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                setFilteredInvoices(updatedInvoices);\n            }, 100);\n            showSuccessMessage(\"تم حذف الفاتورة \".concat(invoice.id, \" بنجاح! \\uD83D\\uDDD1️\"));\n        }\n    };\n    const getTotalStats = ()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const count = filteredInvoices.length;\n        const avgAmount = count > 0 ? total / count : 0;\n        // حساب المبالغ المدفوعة والمتبقية\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const totalRemaining = total - totalPaid;\n        // حساب عدد الفواتير حسب حالة الدفع\n        const paidFullCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const paidPartialCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        // حساب نسبة التحصيل\n        const collectionRate = total > 0 ? totalPaid / total * 100 : 0;\n        return {\n            total,\n            count,\n            avgAmount,\n            totalPaid,\n            totalRemaining,\n            paidFullCount,\n            paidPartialCount,\n            unpaidCount,\n            collectionRate\n        };\n    };\n    const stats = getTotalStats();\n    // حساب إحصائيات اليوم الحالي مع مقارنة بالأمس\n    const getTodayStats = ()=>{\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const todayStr = today.toDateString();\n        const yesterdayStr = yesterday.toDateString();\n        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === todayStr);\n        const yesterdayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === yesterdayStr);\n        const todayTotal = todayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const todayPaid = todayInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const todayRemaining = todayTotal - todayPaid;\n        const yesterdayTotal = yesterdayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const yesterdayCount = yesterdayInvoices.length;\n        const todayPaidFullCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const todayPaidPartialCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const todayUnpaidCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const todayCollectionRate = todayTotal > 0 ? todayPaid / todayTotal * 100 : 0;\n        // حساب التغيير مقارنة بالأمس\n        const countChange = todayInvoices.length - yesterdayCount;\n        const revenueChange = todayTotal - yesterdayTotal;\n        const countChangePercent = yesterdayCount > 0 ? countChange / yesterdayCount * 100 : 0;\n        const revenueChangePercent = yesterdayTotal > 0 ? revenueChange / yesterdayTotal * 100 : 0;\n        return {\n            count: todayInvoices.length,\n            total: todayTotal,\n            paid: todayPaid,\n            remaining: todayRemaining,\n            paidFullCount: todayPaidFullCount,\n            paidPartialCount: todayPaidPartialCount,\n            unpaidCount: todayUnpaidCount,\n            collectionRate: todayCollectionRate,\n            countChange,\n            revenueChange,\n            countChangePercent,\n            revenueChangePercent,\n            yesterdayCount,\n            yesterdayTotal\n        };\n    };\n    const todayStats = getTodayStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"إدارة الفواتير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-indigo-500 to-purple-600 text-white rounded-xl p-6 mb-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"\\uD83D\\uDCCA إحصائيات اليوم الحالي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-sm\",\n                                            children: new Date().toLocaleDateString(\"ar-EG\", {\n                                                weekday: \"long\",\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: todayStats.count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"فواتير اليوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"إجمالي المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.paid)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"المبلغ المحصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    todayStats.collectionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"نسبة التحصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-indigo-400 border-opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-green-200\",\n                                                    children: todayStats.paidFullCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-yellow-200\",\n                                                    children: todayStats.paidPartialCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-red-200\",\n                                                    children: todayStats.unpaidCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-700\",\n                                        children: \"إجمالي الفواتير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: stats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg border-r-4 border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"إجمالي المبلغ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatCurrency(stats.total)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 p-4 rounded-lg border-r-4 border-emerald-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-emerald-700\",\n                                        children: \"المبلغ المدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-emerald-800\",\n                                        children: formatCurrency(stats.totalPaid)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg border-r-4 border-red-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-red-700\",\n                                        children: \"المبلغ المتبقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-800\",\n                                        children: formatCurrency(stats.totalRemaining)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-teal-50 p-4 rounded-lg border-r-4 border-teal-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-teal-700\",\n                                        children: \"مدفوع بالكامل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-teal-800\",\n                                        children: stats.paidFullCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-teal-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-orange-700\",\n                                        children: \"مدفوع جزئياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: stats.paidPartialCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg border-r-4 border-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-700\",\n                                        children: \"غير مدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: stats.unpaidCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-700\",\n                                        children: \"نسبة التحصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: [\n                                            stats.collectionRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: [\n                                            \"متوسط الفاتورة: \",\n                                            formatCurrency(stats.avgAmount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البحث\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث بالاسم، الهاتف، أو رقم الفاتورة\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"فلترة حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterBy,\n                                            onChange: (e)=>setFilterBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"consultation\",\n                                                    children: \"استشارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pattern\",\n                                                    children: \"باترون\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"photography\",\n                                                    children: \"تصوير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shipping\",\n                                                    children: \"شحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high_value\",\n                                                    children: \"قيمة عالية (+1000 جنيه)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"recent\",\n                                                    children: \"الأسبوع الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_full\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_partial\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"unpaid\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ترتيب حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_desc\",\n                                                    children: \"التاريخ (الأحدث أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_asc\",\n                                                    children: \"التاريخ (الأقدم أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_desc\",\n                                                    children: \"المبلغ (الأعلى أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_asc\",\n                                                    children: \"المبلغ (الأقل أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"client_name\",\n                                                    children: \"اسم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined),\n            filteredInvoices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-lg mb-4\",\n                        children: invoices.length === 0 ? \"لا توجد فواتير محفوظة\" : \"لا توجد نتائج للبحث\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, undefined),\n                    invoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"قم بإنشاء فاتورة جديدة لتظهر هنا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 537,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: [\n                                                        \"فاتورة #\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"السيلز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.salesperson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الخدمات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getServiceNames(invoice.services)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"طريقة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.paymentMethod\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"حالة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: invoice.paymentStatus || \"غير محدد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المدفوع / المتبقي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatCurrency(invoice.paidAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" / \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                            children: formatCurrency(invoice.remainingAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-2 rounded text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-700 font-medium\",\n                                                children: \"\\uD83D\\uDE9A يتطلب شحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleExportPDF(invoice),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDCC4 PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePreviewInvoice(invoice),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDC41️ معاينة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(invoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83E\\uDDFE POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteInvoice(invoice),\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2\",\n                                        children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, invoice.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 546,\n                columnNumber: 9\n            }, undefined),\n            previewInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"معاينة الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClosePreview,\n                                    className: \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    invoice: previewInvoice,\n                                    onEdit: handleEditFromPreview,\n                                    onExportPDF: handleExportFromPreview\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(previewInvoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83E\\uDDFE فاتورة POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteInvoice(previewInvoice),\n                                                className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 656,\n                columnNumber: 9\n            }, undefined),\n            posInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                invoice: posInvoice,\n                onClose: ()=>setPosInvoice(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 698,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicesList, \"vNSHrZ6bGj05wXDBkzjUPPk++oo=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore\n    ];\n});\n_c = InvoicesList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicesList);\nvar _c;\n$RefreshReg$(_c, \"InvoicesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicesList.tsx\n"));

/***/ })

});