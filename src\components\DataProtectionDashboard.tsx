'use client';

import React, { useState, useEffect } from 'react';
import { dataProtection } from '@/utils/dataProtection';
import { checkStorageHealth } from '@/utils/secureStorage';

interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface SystemStatus {
  isMonitoring: boolean;
  lastBackup: string | null;
  lastHealthCheck: string | null;
  alertsCount: number;
  autoBackupEnabled: boolean;
  alerts: Alert[];
}

const DataProtectionDashboard: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [storageHealth, setStorageHealth] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // تحديث البيانات
  const refreshData = () => {
    const currentStatus = dataProtection.getStatus();
    const health = checkStorageHealth();
    setStatus(currentStatus);
    setStorageHealth(health);
  };

  useEffect(() => {
    refreshData();
    // تحديث كل 30 ثانية
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, []);

  // إنشاء نسخة احتياطية يدوية
  const handleManualBackup = async () => {
    setIsLoading(true);
    try {
      const result = dataProtection.createManualBackup();
      if (result.success) {
        alert('✅ تم إنشاء النسخة الاحتياطية بنجاح');
      } else {
        alert('❌ فشل في إنشاء النسخة الاحتياطية: ' + result.message);
      }
      refreshData();
    } catch (error) {
      alert('❌ خطأ غير متوقع');
    } finally {
      setIsLoading(false);
    }
  };

  // استرداد البيانات
  const handleRestore = async () => {
    if (!confirm('هل أنت متأكد من استرداد البيانات من النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
      return;
    }

    setIsLoading(true);
    try {
      const result = dataProtection.restoreData();
      if (result.success) {
        alert('✅ تم استرداد البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
      } else {
        alert('❌ فشل في استرداد البيانات: ' + result.message);
      }
    } catch (error) {
      alert('❌ خطأ غير متوقع');
    } finally {
      setIsLoading(false);
    }
  };

  // تصدير البيانات
  const handleExport = () => {
    const result = dataProtection.exportData();
    if (result.success && result.data) {
      const blob = new Blob([result.data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `infapapp-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      alert('✅ تم تصدير البيانات بنجاح');
    } else {
      alert('❌ فشل في تصدير البيانات: ' + result.message);
    }
  };

  // استيراد البيانات
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = e.target?.result as string;
            const result = dataProtection.importData(data);
            if (result.success) {
              alert('✅ تم استيراد البيانات بنجاح');
              refreshData();
            } else {
              alert('❌ فشل في استيراد البيانات: ' + result.message);
            }
          } catch (error) {
            alert('❌ ملف غير صحيح');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // تبديل النسخ الاحتياطي التلقائي
  const toggleAutoBackup = () => {
    const newState = !status?.autoBackupEnabled;
    dataProtection.toggleAutoBackup(newState);
    refreshData();
  };

  // حل التنبيه
  const resolveAlert = (alertId: string) => {
    dataProtection.resolveAlert(alertId);
    refreshData();
  };

  if (!status || !storageHealth) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري تحميل بيانات النظام...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* عنوان الصفحة */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h1 className="text-2xl font-bold mb-2">🛡️ لوحة تحكم حماية البيانات</h1>
        <p className="opacity-90">مراقبة وحماية بيانات التطبيق مع نسخ احتياطية تلقائية</p>
      </div>

      {/* حالة النظام */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border-r-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">حالة المراقبة</p>
              <p className="text-lg font-semibold text-green-600">
                {status.isMonitoring ? '🟢 نشط' : '🔴 متوقف'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border-r-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">النسخ الاحتياطي</p>
              <p className="text-lg font-semibold text-blue-600">
                {status.autoBackupEnabled ? '🟢 تلقائي' : '🟡 يدوي'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border-r-4 border-purple-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">التنبيهات</p>
              <p className="text-lg font-semibold text-purple-600">
                {status.alertsCount} تنبيه
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border-r-4 border-orange-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">حالة التخزين</p>
              <p className="text-lg font-semibold text-orange-600">
                {storageHealth.isHealthy ? '🟢 سليم' : '🟡 يحتاج انتباه'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* معلومات التخزين */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">📊 معلومات التخزين</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600">المساحة المستخدمة</p>
            <p className="text-lg font-semibold">
              {Math.round(storageHealth.storageUsed / 1024)} KB
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ 
                  width: `${(storageHealth.storageUsed / storageHealth.storageLimit) * 100}%` 
                }}
              ></div>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-600">النسخ الاحتياطية</p>
            <p className="text-lg font-semibold">{storageHealth.backupsCount} نسخة</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">آخر نسخة احتياطية</p>
            <p className="text-sm">
              {status.lastBackup 
                ? new Date(status.lastBackup).toLocaleString('ar-EG')
                : 'لا توجد'
              }
            </p>
          </div>
        </div>
      </div>

      {/* أزرار التحكم */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4">⚙️ أدوات التحكم</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={handleManualBackup}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            💾 نسخة احتياطية
          </button>
          
          <button
            onClick={handleRestore}
            disabled={isLoading}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            🔄 استرداد البيانات
          </button>
          
          <button
            onClick={handleExport}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
          >
            📤 تصدير البيانات
          </button>
          
          <button
            onClick={handleImport}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700"
          >
            📥 استيراد البيانات
          </button>
        </div>
        
        <div className="mt-4">
          <label className="flex items-center space-x-2 space-x-reverse">
            <input
              type="checkbox"
              checked={status.autoBackupEnabled}
              onChange={toggleAutoBackup}
              className="rounded"
            />
            <span>تفعيل النسخ الاحتياطي التلقائي (كل 5 دقائق)</span>
          </label>
        </div>
      </div>

      {/* التنبيهات */}
      {status.alerts.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">🚨 التنبيهات</h2>
          <div className="space-y-2">
            {status.alerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border-r-4 ${
                  alert.type === 'error' ? 'bg-red-50 border-red-500' :
                  alert.type === 'warning' ? 'bg-yellow-50 border-yellow-500' :
                  'bg-blue-50 border-blue-500'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{alert.message}</p>
                    <p className="text-sm text-gray-500">
                      {new Date(alert.timestamp).toLocaleString('ar-EG')}
                    </p>
                  </div>
                  <button
                    onClick={() => resolveAlert(alert.id)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* مشاكل التخزين */}
      {storageHealth.issues.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold mb-4">⚠️ مشاكل التخزين</h2>
          <div className="space-y-2">
            {storageHealth.issues.map((issue: string, index: number) => (
              <div key={index} className="p-3 bg-yellow-50 border-r-4 border-yellow-500 rounded-lg">
                <p className="font-medium text-yellow-800">{issue}</p>
              </div>
            ))}
          </div>
          
          {storageHealth.recommendations.length > 0 && (
            <div className="mt-4">
              <h3 className="font-bold mb-2">💡 التوصيات:</h3>
              <ul className="list-disc list-inside space-y-1">
                {storageHealth.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-gray-700">{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DataProtectionDashboard;
