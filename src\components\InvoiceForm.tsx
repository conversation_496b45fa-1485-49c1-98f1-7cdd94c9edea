'use client';

import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Invoice, Client } from '@/types';
import { useInvoiceStore } from '@/store/invoiceStore';
import ConsultationService from './services/ConsultationService';
import PatternService from './services/PatternService';
import PhotographyService from './services/PhotographyService';
import ShippingService from './services/ShippingService';
import PatternPrintingService from './services/PatternPrintingService';
import ManufacturingService from './services/ManufacturingService';
import ProductsService from './services/ProductsService';
import MarketingService from './services/MarketingService';
import SamplesService from './services/SamplesService';
import RawPurchasesService from './services/RawPurchasesService';
import PaymentCalculator from './PaymentCalculator';

interface InvoiceFormProps {
  client: Client;
  onSubmit: (invoice: Invoice) => void;
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ client, onSubmit }) => {
  const { setCurrentInvoice, calculateTotal } = useInvoiceStore();
  const [totalAmount, setTotalAmount] = React.useState(0);
  const [paidAmount, setPaidAmount] = React.useState(0);

  const initialValues = {
    client,
    services: {},
    total: 0,
    paymentMethod: '',
    requiresShipping: false,
    shippingAddress: '',
    notes: '',
    createdAt: new Date().toISOString(),
    
    // Initialize service objects
    consultation: {
      id: '',
      serviceType: 'consultation' as const,
      enabled: false,
      hours: 0,
      topic: '',
      dateTime: '',
      cost: 0,
      paymentMethod: '',
      notes: '',
    },
    pattern: {
      id: '',
      serviceType: 'pattern' as const,
      enabled: false,
      models: [],
    },
    pattern_printing: {
      id: '',
      serviceType: 'pattern_printing' as const,
      enabled: false,
      files: [],
    },
    manufacturing: {
      id: '',
      serviceType: 'manufacturing' as const,
      enabled: false,
      models: [],
    },
    shipping: {
      id: '',
      serviceType: 'shipping' as const,
      enabled: false,
      clientName: client.name,
      phone: client.phone,
      contactMethod: client.contactMethod,
      salesperson: client.salesperson,
      item: '',
      quantity: 0,
      unitPrice: 0,
      total: 0,
    },
    marketing: {
      id: '',
      serviceType: 'marketing' as const,
      enabled: false,
      description: '',
      cost: 0,
      duration: '',
    },
    photography: {
      id: '',
      serviceType: 'photography' as const,
      enabled: false,
      location: 'indoor' as const,
      withModel: false,
      withEditing: false,
      cost: 0,
    },
    products: {
      id: '',
      serviceType: 'products' as const,
      enabled: false,
      items: [],
    },
    raw_purchases: {
      id: '',
      serviceType: 'raw_purchases' as const,
      enabled: false,
      materials: [],
    },
  };

  // تم تبسيط التحقق لتجنب المراجع الدائرية
  const validationSchema = Yup.object({
    paymentMethod: Yup.string().required('طريقة الدفع مطلوبة'),
    notes: Yup.string(),
    requiresShipping: Yup.boolean(),
    shippingAddress: Yup.string(),
  });

  const handleSubmit = (values: any) => {
    // Collect enabled services
    const enabledServices = [];

    // قائمة الخدمات المحتملة
    const serviceKeys = [
      'consultation', 'pattern', 'pattern_printing', 'manufacturing',
      'samples', 'shipping', 'marketing', 'photography', 'products', 'raw_purchases'
    ];

    serviceKeys.forEach(key => {
      if (values[key]?.enabled && values[key]?.serviceType) {
        enabledServices.push(values[key]);
      }
    });

    // إنشاء معرف فريد مبسط للفاتورة
    const generateUniqueInvoiceId = () => {
      const now = new Date();
      const month = now.getMonth() + 1;
      const year = now.getFullYear();
      const counter = Date.now() % 1000; // آخر 3 أرقام من timestamp
      return `INV-${month}-${year}-${String(counter).padStart(3, '0')}`;
    };

    const invoice: Invoice = {
      id: generateUniqueInvoiceId(),
      client: client,
      services: enabledServices,
      total: totalAmount,
      paidAmount: paidAmount,
      remainingAmount: totalAmount - paidAmount,
      paymentStatus: paidAmount === 0 ? 'غير مدفوع' : paidAmount >= totalAmount ? 'مدفوع بالكامل' : 'مدفوع جزئياً',
      paymentMethod: values.paymentMethod,
      requiresShipping: values.requiresShipping || false,
      shippingAddress: values.shippingAddress || '',
      createdAt: new Date().toISOString(),
      notes: values.notes || '',
    };

    setCurrentInvoice(invoice);
    onSubmit(invoice);
  };

  const paymentMethods = [
    { value: 'cash', label: 'نقدي' },
    { value: 'card', label: 'بطاقة' },
    { value: 'transfer', label: 'تحويل' },
    { value: 'installment', label: 'تقسيط' },
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm">
        <div className="flex items-center gap-4 mb-4">
          <div className="bg-blue-600 p-3 rounded-full shadow-md">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">إنشاء فاتورة جديدة</h2>
            <p className="text-gray-600 text-sm">اختر الخدمات وأدخل التفاصيل المطلوبة</p>
          </div>
        </div>

        {/* Client Info Card */}
        <div className="bg-white rounded-lg p-4 border border-blue-200">
          <div className="flex items-center gap-2 mb-3">
            <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
            </svg>
            <span className="font-semibold text-gray-800">معلومات العميل</span>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-gray-500">العميل:</span>
              <span className="font-medium text-gray-800">{client.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-500">الهاتف:</span>
              <span className="font-medium text-gray-800">{client.phone}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-500">السيلز:</span>
              <span className="font-medium text-gray-800">{client.salesperson}</span>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center gap-2 text-sm text-blue-600 mt-4">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <span>الخطوة 2 من 3 - تفاصيل الخدمات</span>
        </div>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue }) => {
          // تعبئة اسم السيلز تلقائياً في جميع الخدمات
          React.useEffect(() => {
            const clientSalesperson = client.salesperson;
            if (clientSalesperson) {
              // تحديث اسم السيلز في جميع الخدمات المفعلة
              const serviceKeys = [
                'consultation', 'pattern', 'pattern_printing', 'manufacturing',
                'samples', 'shipping', 'marketing', 'photography', 'products', 'raw_purchases'
              ];

              serviceKeys.forEach(key => {
                const service = values[key];
                if (service?.enabled) {
                  // للخدمات التي تحتوي على salesperson مباشرة
                  if (key === 'shipping' || key === 'consultation') {
                    setFieldValue(`${key}.salesperson`, clientSalesperson);
                  }

                  // للخدمات التي تحتوي على models مع salesperson
                  if ((key === 'pattern' || key === 'manufacturing') && service.models) {
                    service.models.forEach((model: any, index: number) => {
                      setFieldValue(`${key}.models.${index}.salesperson`, clientSalesperson);
                    });
                  }

                  // للخدمات التي تحتوي على files مع salesperson
                  if (key === 'pattern_printing' && service.files) {
                    service.files.forEach((file: any, index: number) => {
                      setFieldValue(`${key}.files.${index}.salesperson`, clientSalesperson);
                    });
                  }
                }
              });
            }
          }, [values, client.salesperson, setFieldValue]);

          // حساب المجموع تلقائياً
          React.useEffect(() => {
            let calculatedTotal = 0;

            const serviceKeys = [
              'consultation', 'pattern', 'pattern_printing', 'manufacturing',
              'samples', 'shipping', 'marketing', 'photography', 'products', 'raw_purchases'
            ];

            serviceKeys.forEach(key => {
              const service = values[key];
              if (service?.enabled) {
                // خدمة الاستشارات
                if (key === 'consultation' && service.cost) {
                  calculatedTotal += Number(service.cost) || 0;
                }

                // خدمة الباترون
                if (key === 'pattern' && service.models) {
                  service.models.forEach((model: any) => {
                    calculatedTotal += Number(model.finalAmount) || Number(model.cost) || 0;
                  });
                }

                // خدمة طباعة الباترون
                if (key === 'pattern_printing' && service.files) {
                  service.files.forEach((file: any) => {
                    calculatedTotal += Number(file.cost) || 0;
                  });
                }

                // خدمة التصنيع
                if (key === 'manufacturing' && service.models) {
                  service.models.forEach((model: any) => {
                    const patternPrice = Number(model.patternPrice) || 0;
                    const samplePrice = Number(model.samplePrice) || 0;
                    const manufacturingPrice = Number(model.manufacturingPrice) || 0;
                    calculatedTotal += patternPrice + samplePrice + manufacturingPrice;
                  });
                }

                // خدمة العينات
                if (key === 'samples' && service.samples) {
                  service.samples.forEach((sample: any) => {
                    calculatedTotal += Number(sample.cost) || 0;
                  });
                }

                // خدمة الشحن
                if (key === 'shipping' && service.total) {
                  calculatedTotal += Number(service.total) || 0;
                }

                // خدمة التسويق
                if (key === 'marketing' && service.cost) {
                  calculatedTotal += Number(service.cost) || 0;
                }

                // خدمة التصوير
                if (key === 'photography' && service.cost) {
                  calculatedTotal += Number(service.cost) || 0;
                }

                // خدمة المنتجات
                if (key === 'products' && service.items) {
                  service.items.forEach((item: any) => {
                    calculatedTotal += Number(item.total) || 0;
                  });
                  // طرح الخصم إن وجد
                  if (service.discountAmount) {
                    calculatedTotal -= Number(service.discountAmount) || 0;
                  }
                }

                // خدمة المشتريات الخام
                if (key === 'raw_purchases' && service.materials) {
                  service.materials.forEach((material: any) => {
                    calculatedTotal += Number(material.total) || 0;
                  });
                }
              }
            });

            setTotalAmount(calculatedTotal);
          }, [values]);

          return (
          <Form className="space-y-8">
            {/* Services Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  تفاصيل الخدمات المطلوبة
                </h3>
                <p className="text-sm text-gray-600 mt-1">أدخل تفاصيل كل خدمة مطلوبة</p>
              </div>

              <div className="p-6 space-y-8">
                {/* خدمة الاستشارات */}
                {client.services.includes('consultation') && (
                  <div className="service-section animate-fadeIn">
                    <ConsultationService
                      namePrefix="consultation"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة الباترون */}
                {client.services.includes('pattern') && (
                  <div className="service-section animate-fadeIn">
                    <PatternService
                      namePrefix="pattern"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة التصوير */}
                {client.services.includes('photography') && (
                  <div className="service-section animate-fadeIn">
                    <PhotographyService
                      namePrefix="photography"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة الشحن */}
                {client.services.includes('shipping') && (
                  <div className="service-section animate-fadeIn">
                    <ShippingService
                      namePrefix="shipping"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة طباعة الباترون */}
                {client.services.includes('pattern_printing') && (
                  <div className="service-section animate-fadeIn">
                    <PatternPrintingService
                      namePrefix="pattern_printing"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة التصنيع */}
                {client.services.includes('manufacturing') && (
                  <div className="service-section animate-fadeIn">
                    <ManufacturingService
                      namePrefix="manufacturing"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة التسويق */}
                {client.services.includes('marketing') && (
                  <div className="service-section animate-fadeIn">
                    <MarketingService
                      namePrefix="marketing"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة المنتجات */}
                {client.services.includes('products') && (
                  <div className="service-section animate-fadeIn">
                    <ProductsService
                      namePrefix="products"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة العينات */}
                {client.services.includes('samples') && (
                  <div className="service-section animate-fadeIn">
                    <SamplesService
                      namePrefix="samples"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}

                {/* خدمة المشتريات الخام */}
                {client.services.includes('raw_purchases') && (
                  <div className="service-section animate-fadeIn">
                    <RawPurchasesService
                      namePrefix="raw_purchases"
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Payment Calculator Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-yellow-50 to-amber-50 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                  </svg>
                  حاسبة المدفوعات
                </h3>
                <p className="text-sm text-gray-600 mt-1">احسب المبالغ المدفوعة والمتبقية</p>
              </div>

              <div className="p-6">
                <PaymentCalculator
                  totalAmount={totalAmount}
                  paidAmount={paidAmount}
                  onPaidAmountChange={setPaidAmount}
                />
              </div>
            </div>

            {/* Payment & Shipping Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                    <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"/>
                  </svg>
                  معلومات الدفع والشحن
                </h3>
              </div>

              <div className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-field group">
                    <label className="form-label-enhanced">
                      <span className="text-red-500">*</span> طريقة الدفع الرئيسية
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                          <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <select
                        name="paymentMethod"
                        value={values.paymentMethod}
                        onChange={(e) => setFieldValue('paymentMethod', e.target.value)}
                        className="form-input-enhanced pr-10"
                      >
                        <option value="">اختر طريقة الدفع</option>
                        {paymentMethods.map((method) => (
                          <option key={method.value} value={method.value}>
                            {method.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="form-field">
                    <label className="form-label-enhanced">خيارات الشحن</label>
                    <label className="flex items-center gap-3 p-4 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer">
                      <input
                        type="checkbox"
                        checked={values.requiresShipping}
                        onChange={(e) => setFieldValue('requiresShipping', e.target.checked)}
                        className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <div className="flex items-center gap-2">
                        <svg className="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                        </svg>
                        <span className="text-sm font-medium text-gray-700">يتطلب شحن</span>
                      </div>
                    </label>
                  </div>
                </div>

                {values.requiresShipping && (
                  <div className="form-field group animate-fadeIn">
                    <label className="form-label-enhanced">عنوان الشحن</label>
                    <div className="relative">
                      <div className="absolute top-3 right-3 pointer-events-none">
                        <svg className="w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <textarea
                        name="shippingAddress"
                        value={values.shippingAddress}
                        onChange={(e) => setFieldValue('shippingAddress', e.target.value)}
                        rows={3}
                        className="form-input-enhanced pr-10"
                        placeholder="أدخل عنوان الشحن التفصيلي"
                      />
                    </div>
                  </div>
                )}

                <div className="form-field group">
                  <label className="form-label-enhanced">ملاحظات إضافية</label>
                  <div className="relative">
                    <div className="absolute top-3 right-3 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                      </svg>
                    </div>
                    <textarea
                      name="notes"
                      value={values.notes}
                      onChange={(e) => setFieldValue('notes', e.target.value)}
                      rows={3}
                      className="form-input-enhanced pr-10"
                      placeholder="أي ملاحظات أو تعليمات خاصة"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 pt-6">
              <button
                type="submit"
                className="btn-primary-enhanced group w-full sm:w-auto"
              >
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
                إنشاء الفاتورة ومعاينتها
              </button>

              <button
                type="button"
                className="btn-secondary px-6 py-3 w-full sm:w-auto"
                onClick={() => window.history.back()}
              >
                <svg className="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd"/>
                </svg>
                العودة للخطوة السابقة
              </button>
            </div>
          </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default InvoiceForm;
