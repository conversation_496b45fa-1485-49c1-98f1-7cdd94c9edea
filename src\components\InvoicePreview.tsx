'use client';

import React, { useState, useEffect } from 'react';
import { Invoice } from '@/types';
import { useSettingsStore } from '@/store/settingsStore';
import { generateInvoicePDF, generateInvoicePDFFromHTML } from '@/utils/pdfGenerator';
import POSInvoice from './POSInvoice';

interface InvoicePreviewProps {
  invoice: Invoice;
  onEdit: () => void;
  onExportPDF: () => void;
}

// دالة عرض رسائل التأكيد المحسنة
const showSuccessMessage = (message: string) => {
  // إنشاء عنصر toast مؤقت
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
  toast.innerHTML = `
    <div class="flex items-center gap-2">
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
      </svg>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(toast);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 3000);
};

const showErrorMessage = (message: string) => {
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
  toast.innerHTML = `
    <div class="flex items-center gap-2">
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
      </svg>
      <span>${message}</span>
    </div>
  `;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 4000);
};

const InvoicePreview: React.FC<InvoicePreviewProps> = ({
  invoice,
  onEdit,
  onExportPDF
}) => {
  const [showCompanySettings, setShowCompanySettings] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [showPOSInvoice, setShowPOSInvoice] = useState(false);
  const { settings, loadSettings } = useSettingsStore();

  // Load company settings on component mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // دالة تصدير PDF محسنة مع رسائل التأكيد
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      // إضافة كلاس التحسين للعنصر
      const element = document.getElementById('invoice-content');
      if (element) {
        element.classList.add('pdf-optimized');
      }

      // محاولة الطريقة المحسنة أولاً
      await generateInvoicePDF(invoice);
      showSuccessMessage('تم تصدير PDF بنجاح! 🎉');

      // إزالة كلاس التحسين
      if (element) {
        element.classList.remove('pdf-optimized');
      }
    } catch (error) {
      console.error('PDF Export Error:', error);

      // محاولة الطريقة البديلة
      try {
        await generateInvoicePDFFromHTML(invoice);
        showSuccessMessage('تم تصدير PDF بنجاح! 🎉');
      } catch (fallbackError) {
        console.error('Fallback PDF Export Error:', fallbackError);
        showErrorMessage('فشل في تصدير PDF. يرجى المحاولة مرة أخرى.');
      }
    } finally {
      setIsExporting(false);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory', // التقويم الميلادي
    });
  };

  const formatCurrency = (amount: number | string | undefined | null) => {
    // التحقق من صحة القيمة وتحويلها لرقم
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));

    // التحقق من أن النتيجة رقم صحيح
    if (isNaN(numericAmount)) {
      return '0.00 جنيه';
    }

    return `${numericAmount.toFixed(2)} جنيه`;
  };

  const getServiceName = (serviceType: string) => {
    const serviceNames: { [key: string]: string } = {
      consultation: 'استشارات',
      pattern: 'باترون',
      pattern_printing: 'طباعة باترون',
      manufacturing: 'تصنيع',
      samples: 'عينات',
      shipping: 'شحن/تعبئة',
      marketing: 'تسويق',
      photography: 'تصوير',
      products: 'منتجات',
      raw_purchases: 'مشتريات خام',
    };
    return serviceNames[serviceType] || serviceType;
  };

  const getPaymentMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      cash: 'نقدي',
      card: 'بطاقة',
      transfer: 'تحويل',
      installment: 'تقسيط',
    };
    return methods[method] || method;
  };

  const getContactMethodName = (method: string) => {
    const methods: { [key: string]: string } = {
      whatsapp: 'واتساب',
      meta: 'ميتا',
      meeting: 'مقابلة',
      phone: 'هاتف',
    };
    return methods[method] || method;
  };

  // Generate client code based on name and phone
  const generateClientCode = (name: string, phone: string) => {
    const nameCode = name.substring(0, 3).toUpperCase();
    const phoneCode = phone.substring(-4);
    return `${nameCode}-${phoneCode}`;
  };

  // دالة فتح صفحة الطباعة في تبويب جديد محسنة
  const openPrintView = (invoice: Invoice) => {
    try {
      // إنشاء HTML للفاتورة مع تنسيق الطباعة
      const printHTML = createPrintableInvoiceHTML(invoice);

      // فتح نافذة جديدة
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(printHTML);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم فتح حوار الطباعة
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            showSuccessMessage('تم فتح صفحة الطباعة بنجاح! 🖨️');
          }, 500);
        };
      } else {
        showErrorMessage('فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
      }
    } catch (error) {
      showErrorMessage('حدث خطأ أثناء تحضير صفحة الطباعة.');
    }
  };

  // دالة إنشاء HTML للطباعة
  const createPrintableInvoiceHTML = (invoice: Invoice): string => {
    const logoSrc = settings.companyLogo || '';

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة ${invoice.id} - ${invoice.client.name}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .invoice-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            padding: 20px;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(to left, #f0f8ff, white);
            border: 2px solid #2196f3;
            border-radius: 10px;
        }

        .invoice-info {
            flex: 1;
            text-align: right;
        }

        .invoice-info h2 {
            color: #2196f3;
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .info-box {
            background: transparent;
            padding: 15px;
            border: none;
            box-shadow: none;
            max-width: 400px;
        }

        .info-row {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .logo-section {
            flex-shrink: 0;
            text-align: center;
            margin-left: 30px;
        }

        .logo-section img {
            max-width: 180px;
            max-height: 180px;
            object-fit: contain;
        }

        .company-description {
            margin-top: 15px;
            font-size: 13px;
            color: #666;
            max-width: 200px;
            line-height: 1.5;
        }

        /* Services Table */
        .services-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #2196f3;
        }

        .services-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: 1px solid #ddd;
            margin: 15px 0;
            table-layout: fixed;
        }

        .services-table th {
            background-color: #2196f3;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #ddd;
            font-size: 13px;
        }

        .services-table td {
            padding: 10px 8px;
            border: 1px solid #ddd;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.3;
        }

        .services-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .services-table tr:nth-child(odd) {
            background-color: white;
        }

        .service-type {
            color: #2196f3;
            font-weight: bold;
        }

        .service-details {
            text-align: right;
            white-space: normal;
        }

        .service-price {
            font-weight: bold;
            color: #2196f3;
        }

        .service-total {
            font-weight: bold;
            color: #4caf50;
        }

        /* Total Summary */
        .total-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to right, #e8f5e8, #f0f8f0);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #4caf50;
            margin-bottom: 30px;
        }

        .total-details {
            text-align: right;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            min-width: 300px;
        }

        .total-label {
            font-weight: bold;
            color: #555;
            margin-left: 20px;
        }

        .total-value {
            font-weight: bold;
        }

        .grand-total {
            font-size: 20px;
            color: #4caf50;
        }

        .paid-amount {
            font-size: 16px;
            color: #2196f3;
        }

        .remaining-amount {
            font-size: 16px;
        }

        .remaining-positive {
            color: #f44336;
        }

        .remaining-zero {
            color: #4caf50;
        }

        .payment-stamp {
            background: transparent;
            color: #333;
            padding: 15px;
            border: 3px solid #333;
            border-radius: 15px;
            text-align: center;
            transform: rotate(3deg);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            min-width: 120px;
        }

        .payment-stamp.paid-full {
            border-color: #4caf50;
            color: #4caf50;
        }

        .payment-stamp.paid-partial {
            border-color: #ff9800;
            color: #ff9800;
        }

        .payment-stamp.unpaid {
            border-color: #f44336;
            color: #f44336;
        }

        .stamp-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stamp-status {
            font-size: 12px;
            font-weight: bold;
        }

        /* Footer */
        .footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-top: 40px;
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            min-height: 120px;
            direction: rtl;
        }

        .footer-left {
            width: 45%;
            text-align: right;
            direction: rtl;
        }

        .contact-info {
            font-size: 12px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 10px;
            text-align: right;
            direction: rtl;
        }

        .contact-info strong {
            color: #333;
            font-size: 13px;
        }

        .footer-right {
            width: 50%;
            text-align: right;
            direction: rtl;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-start;
        }

        .headquarters-info {
            font-size: 12px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
            text-align: right;
            direction: rtl;
        }

        .headquarters-info strong {
            color: #333;
            font-size: 13px;
        }

        .company-stamp {
            position: relative;
            transform: none;
            opacity: 1;
            border-radius: 8px;
            padding: 10px;
            width: 160px;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            background: white;
            border: 1px solid #ddd;
            box-shadow: none;
        }

        .company-stamp img {
            width: 140px;
            height: auto;
            max-height: 100px;
            object-fit: contain;
            border-radius: 4px;
        }

        .company-stamp .stamp-content {
            background: rgba(33, 150, 243, 0.1);
            border: 2px solid #2196f3;
            border-radius: 8px;
            width: 100%;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .stamp-content {
            text-align: center;
            color: #2196f3;
            font-weight: bold;
            font-size: 14px;
            line-height: 1.2;
            direction: rtl;
        }

        .stamp-title {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .stamp-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .headquarters-info {
            font-size: 12px;
            color: #555;
            text-align: right;
            line-height: 1.6;
            direction: rtl;
        }

        .headquarters-info strong {
            color: #333;
            font-size: 13px;
        }

        .thank-you {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            color: #2196f3;
            font-size: 14px;
            font-weight: bold;
            direction: rtl;
        }

        /* Print specific styles */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }

            .invoice-container {
                padding: 0;
                margin: 0;
                max-width: 100%;
            }

            .header {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .services-section {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .services-table {
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .total-section {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 20px;
            }

            .footer {
                break-inside: avoid;
                page-break-inside: avoid;
                position: relative;
                clear: both;
                margin-top: 30px;
            }

            .footer-left,
            .footer-right {
                float: none;
                display: inline-block;
                vertical-align: top;
            }

            .company-stamp {
                transform: none;
            }

            .thank-you {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="invoice-info">
                <h2>فاتورة خدمات</h2>
                <div class="info-box">
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة: </span>
                        <span class="info-value">${invoice.id}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">التاريخ: </span>
                        <span class="info-value">${formatDate(invoice.createdAt)}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">اسم العميل: </span>
                        <span class="info-value">${invoice.client.name}</span>
                    </div>
                    ${invoice.client.businessName ? `
                    <div class="info-row">
                        <span class="info-label">الاسم التجاري: </span>
                        <span class="info-value">${invoice.client.businessName}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">رقم الهاتف: </span>
                        <span class="info-value">${invoice.client.phone}</span>
                    </div>
                    ${invoice.client.province ? `
                    <div class="info-row">
                        <span class="info-label">المحافظة: </span>
                        <span class="info-value">${invoice.client.province}</span>
                    </div>
                    ` : ''}
                    <div class="info-row">
                        <span class="info-label">كود العميل: </span>
                        <span class="info-value">${invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)}</span>
                    </div>
                    <div class="info-row" style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
                        <span class="info-label">وسيلة التواصل: </span>
                        <span class="info-value">${getContactMethodName(invoice.client.contactMethod)}</span>
                    </div>
                </div>
            </div>

            <div class="logo-section">
                ${logoSrc ? `<img src="${logoSrc}" alt="شعار الشركة">` : `
                <div style="width: 180px; height: 180px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9f9;">
                    <span style="color: #999;">شعار الشركة</span>
                </div>
                `}
                <div class="company-description">
                    ${settings.description || 'شركة متخصصة في جميع خدمات صناعة الملابس الجاهزة'}
                </div>
            </div>
        </div>

        <!-- Services Section -->
        <div class="services-section">
            <h3 class="section-title">تفاصيل الخدمات والمنتجات</h3>

            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">م</th>
                        <th style="width: 120px;">نوع الخدمة</th>
                        <th style="width: 200px;">التفاصيل</th>
                        <th style="width: 100px;">الكمية/المقاس</th>
                        <th style="width: 120px;">السعر</th>
                        <th style="width: 120px;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.services.map((service, index) => {
                      // Calculate service details (same logic as in component)
                      let serviceTotal = 0;
                      let serviceDetails = '';
                      let quantity = '';
                      let unitPrice = 0;

                      if (service.serviceType === 'consultation') {
                        const consultationService = service as any;
                        serviceTotal = consultationService.cost || 0;
                        serviceDetails = `${consultationService.topic || 'استشارة'} - ${consultationService.hours || 0} ساعة`;
                        quantity = `${consultationService.hours || 0} ساعة`;
                        unitPrice = consultationService.cost || 0;
                      } else if (service.serviceType === 'pattern') {
                        const patternService = service as any;
                        serviceTotal = patternService.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || model.cost || 0), 0) || 0;
                        serviceDetails = `${patternService.models?.length || 0} نموذج باترون`;
                        quantity = `${patternService.models?.length || 0} نموذج`;
                        unitPrice = serviceTotal / (patternService.models?.length || 1);
                      } else if (service.serviceType === 'pattern_printing') {
                        const printingService = service as any;
                        serviceTotal = printingService.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
                        serviceDetails = `طباعة ${printingService.files?.length || 0} ملف`;
                        quantity = `${printingService.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
                        unitPrice = serviceTotal;
                      } else if (service.serviceType === 'shipping') {
                        const shippingService = service as any;
                        serviceTotal = shippingService.total || 0;
                        serviceDetails = `شحن ${shippingService.item || 'منتج'}`;
                        quantity = `${shippingService.quantity || 1}`;
                        unitPrice = shippingService.unitPrice || 0;
                      } else if (service.serviceType === 'manufacturing') {
                        const manufacturingService = service as any;
                        serviceTotal = manufacturingService.models?.reduce((sum: number, model: any) => {
                          return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
                        }, 0) || 0;
                        serviceDetails = `تصنيع ${manufacturingService.models?.length || 0} نموذج`;
                        quantity = `${manufacturingService.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 0}`;
                        unitPrice = serviceTotal / (manufacturingService.models?.length || 1);
                      } else if (service.serviceType === 'products') {
                        const productsService = service as any;
                        let productsSubtotal = productsService.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
                        // طرح الخصم إن وجد
                        if (productsService.discountAmount) {
                          productsSubtotal -= productsService.discountAmount || 0;
                        }
                        serviceTotal = Math.max(0, productsSubtotal);
                        serviceDetails = `${productsService.items?.length || 0} منتج`;
                        quantity = `${productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
                        unitPrice = serviceTotal / (productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
                      } else {
                        serviceTotal = (service as any).cost || (service as any).total || 0;
                        serviceDetails = getServiceName(service.serviceType);
                        quantity = '1';
                        unitPrice = serviceTotal;
                      }

                      return `
                        <tr>
                            <td>${index + 1}</td>
                            <td class="service-type">${getServiceName(service.serviceType)}</td>
                            <td class="service-details">${serviceDetails}</td>
                            <td>${quantity}</td>
                            <td class="service-price">${formatCurrency(unitPrice)}</td>
                            <td class="service-total">${formatCurrency(serviceTotal)}</td>
                        </tr>
                      `;
                    }).join('')}
                </tbody>
            </table>
        </div>

        <!-- Total Summary -->
        <div class="total-section">
            <div class="total-details">
                <div class="total-row">
                    <span class="total-label">المجموع الكلي:</span>
                    <span class="total-value grand-total">${formatCurrency(invoice.total)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المدفوع:</span>
                    <span class="total-value paid-amount">${formatCurrency(invoice.paidAmount || 0)}</span>
                </div>
                <div class="total-row">
                    <span class="total-label">المبلغ المتبقي:</span>
                    <span class="total-value remaining-amount ${(invoice.remainingAmount || 0) > 0 ? 'remaining-positive' : 'remaining-zero'}">
                        ${formatCurrency(invoice.remainingAmount || 0)}
                    </span>
                </div>
                <div class="total-row" style="border-top: 1px solid #4caf50; padding-top: 8px; margin-top: 8px;">
                    <span class="total-label">طريقة الدفع:</span>
                    <span class="total-value">${getPaymentMethodName(invoice.paymentMethod)}</span>
                </div>
                ${invoice.services.some(service => {
                  if (service.serviceType === 'pattern') {
                    return (service as any).models?.some((model: any) => model.fileType !== 'plt');
                  }
                  if (service.serviceType === 'manufacturing') {
                    return (service as any).models?.some((model: any) => model.fileType !== 'plt');
                  }
                  return false;
                }) ? `
                <div class="total-row" style="border-top: 1px solid #ff9800; padding-top: 8px; margin-top: 8px; color: #ff9800;">
                    <span class="total-label" style="font-size: 12px;">ملاحظة:</span>
                    <span class="total-value" style="font-size: 12px;">يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر</span>
                </div>
                ` : ''}
            </div>

            <div class="payment-stamp ${
              invoice.paymentStatus === 'مدفوع بالكامل' ? 'paid-full' :
              invoice.paymentStatus === 'مدفوع جزئياً' ? 'paid-partial' : 'unpaid'
            }">
                <div class="stamp-title">OKA Group</div>
                <div class="stamp-status">${invoice.paymentStatus || 'غير محدد'}</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <!-- Right side: Contact Information -->
            <div class="footer-left">
                <div class="contact-info">
                    <div><strong>معلومات التواصل:</strong></div>
                    <div>📧 البريد الإلكتروني: <EMAIL></div>
                    <div>📞 للشكاوى والاقتراحات: 0114954118</div>
                </div>
            </div>

            <!-- Left side: Headquarters + Company Stamp -->
            <div class="footer-right">
                <div class="headquarters-info">
                    <div><strong>المقر الإداري:</strong></div>
                    <div>73 ش 6 اكتوبر الجراش جسر السويس</div>
                </div>

                <div class="company-stamp">
                    ${settings.companyStamp ? `
                    <img
                        src="${settings.companyStamp}"
                        alt="ختم الشركة"
                    >
                    ` : `
                    <div class="stamp-content">
                        <div class="stamp-title">OKA</div>
                        <div class="stamp-subtitle">GROUP</div>
                    </div>
                    `}
                </div>
            </div>
        </div>

        <div class="thank-you">
            شكراً لتعاملكم معنا
        </div>
    </div>
</body>
</html>
    `;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* أزرار التحكم */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">معاينة الفاتورة</h1>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setShowCompanySettings(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
            </svg>
            إعدادات الشركة
          </button>
          <button onClick={onEdit} className="btn-secondary">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
            تعديل
          </button>
          <button
            onClick={handleExportPDF}
            disabled={isExporting}
            className={`btn-success ${isExporting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isExporting ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd"/>
                </svg>
                جاري التصدير...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd"/>
                </svg>
                تصدير PDF
              </>
            )}
          </button>
          <button
            onClick={() => openPrintView(invoice)}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd"/>
            </svg>
            طباعة
          </button>
          <button
            onClick={() => setShowPOSInvoice(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"/>
            </svg>
            فاتورة POS
          </button>
        </div>
      </div>

      {/* محتوى الفاتورة */}
      <div
        id="invoice-content"
        className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden print-break-inside-avoid"
        style={{
          fontFamily: 'Arial, sans-serif',
          lineHeight: '1.4',
          position: 'relative',
          zIndex: 1,
          direction: 'rtl',
          width: '100%',
          maxWidth: '794px',
          margin: '0 auto'
        }}
      >
        {/* Header Section - Enhanced Professional Layout */}
        <div className="bg-gradient-to-l from-blue-50 via-white to-blue-50 p-8 border-b-4 border-blue-300 print-header print-break-inside-avoid relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -translate-y-32 translate-x-32"></div>
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-blue-100 rounded-full translate-y-24 -translate-x-24"></div>
          </div>

          <div className="relative z-10">
            <div className="flex justify-between items-start gap-8">
              {/* Right side - Company Logo + Brand */}
              <div className="flex-shrink-0 text-center">
                <div className="mb-6">
                  {settings.companyLogo ? (
                    <div className="relative">
                      <img
                        src={settings.companyLogo}
                        alt="شعار الشركة"
                        className="w-56 h-56 object-contain mx-auto drop-shadow-lg"
                      />
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-48 h-4 bg-blue-200 rounded-full opacity-30 blur-sm"></div>
                    </div>
                  ) : (
                    <div className="w-56 h-56 border-3 border-dashed border-blue-300 rounded-2xl flex items-center justify-center bg-gradient-to-br from-blue-50 to-white mx-auto shadow-inner">
                      <div className="text-center text-blue-400">
                        <svg className="w-24 h-24 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-lg font-semibold">شعار الشركة</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Company Brand Name */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl shadow-lg mb-4">
                  <h1 className="text-2xl font-bold">OKA GROUP</h1>
                  <p className="text-blue-100 text-sm">مجموعة أوكا للخدمات المتكاملة</p>
                </div>

                {/* Company Description */}
                <div className="max-w-xs mx-auto bg-white p-4 rounded-lg shadow-sm border border-blue-100">
                  <p className="text-sm text-gray-600 leading-relaxed font-medium">
                    {settings.description || "نقدم خدمات متكاملة في مجال التصميم والتصنيع والاستشارات"}
                  </p>
                </div>
              </div>

              {/* Left side - Invoice Information */}
              <div className="flex-1 text-right space-y-4">
                {/* Invoice Title */}
                <div className="text-center mb-6">
                  <h2 className="text-4xl font-bold text-blue-600 mb-2">فاتورة خدمات</h2>
                  <div className="w-32 h-1 bg-gradient-to-r from-blue-400 to-blue-600 mx-auto rounded-full"></div>
                </div>

                {/* Invoice Details Card */}
                <div className="bg-white p-6 rounded-2xl shadow-lg border-2 border-blue-100 max-w-md ml-auto">
                  <div className="space-y-3">
                    {/* Invoice Number */}
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                      <span className="text-lg font-bold text-blue-600">#{invoice.id}</span>
                      <span className="font-semibold text-gray-700">رقم الفاتورة</span>
                    </div>

                    {/* Date */}
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-800 font-medium">{formatDate(invoice.createdAt)}</span>
                      <span className="font-semibold text-gray-700">التاريخ</span>
                    </div>

                    {/* Divider */}
                    <div className="border-t-2 border-blue-100 my-4"></div>

                    {/* Client Information */}
                    <div className="space-y-2">
                      <h4 className="font-bold text-gray-700 text-center mb-3 bg-blue-50 py-2 rounded-lg">بيانات العميل</h4>

                      <div className="text-right space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-800 font-medium">{invoice.client.name}</span>
                          <span className="font-semibold text-gray-600">الاسم:</span>
                        </div>

                        {invoice.client.businessName && (
                          <div className="flex justify-between">
                            <span className="text-gray-800 font-medium">{invoice.client.businessName}</span>
                            <span className="font-semibold text-gray-600">الاسم التجاري:</span>
                          </div>
                        )}

                        <div className="flex justify-between">
                          <span className="text-gray-800 font-medium">{invoice.client.phone}</span>
                          <span className="font-semibold text-gray-600">الهاتف:</span>
                        </div>

                        {invoice.client.province && (
                          <div className="flex justify-between">
                            <span className="text-gray-800 font-medium">{invoice.client.province}</span>
                            <span className="font-semibold text-gray-600">المحافظة:</span>
                          </div>
                        )}

                        <div className="flex justify-between">
                          <span className="text-blue-600 font-bold">{invoice.client.clientCode || generateClientCode(invoice.client.name, invoice.client.phone)}</span>
                          <span className="font-semibold text-gray-600">كود العميل:</span>
                        </div>

                        <div className="flex justify-between pt-2 border-t border-gray-200">
                          <span className="text-blue-600 font-bold">{getContactMethodName(invoice.client.contactMethod)}</span>
                          <span className="font-semibold text-gray-600">وسيلة التواصل:</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section - Table Layout */}
        <div className="p-6">

          {/* Services Table - Enhanced Design */}
          <div className="mb-8">
            {/* Section Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-t-xl">
              <h3 className="text-2xl font-bold text-center flex items-center justify-center gap-3">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                </svg>
                تفاصيل الخدمات والمنتجات
              </h3>
            </div>

            <div className="overflow-x-auto print-break-inside-avoid bg-white rounded-b-xl shadow-lg border-2 border-blue-100">
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                    <th className="px-4 py-4 text-center font-bold border-r border-blue-400 w-16">
                      <div className="flex items-center justify-center">
                        <span className="bg-white text-blue-600 rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">م</span>
                      </div>
                    </th>
                    <th className="px-4 py-4 text-center font-bold border-r border-blue-400 w-32">
                      <div className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9z" clipRule="evenodd"/>
                        </svg>
                        نوع الخدمة
                      </div>
                    </th>
                    <th className="px-4 py-4 text-center font-bold border-r border-blue-400 w-48">
                      <div className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd"/>
                        </svg>
                        التفاصيل
                      </div>
                    </th>
                    <th className="px-4 py-4 text-center font-bold border-r border-blue-400 w-28">
                      <div className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                        </svg>
                        الكمية
                      </div>
                    </th>
                    <th className="px-4 py-4 text-center font-bold border-r border-blue-400 w-32">
                      <div className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                        </svg>
                        السعر
                      </div>
                    </th>
                    <th className="px-4 py-4 text-center font-bold w-32">
                      <div className="flex items-center justify-center gap-2">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                        </svg>
                        المجموع
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.services.map((service, index) => {
                    // Calculate service total based on service type
                    let serviceTotal = 0;
                    let serviceDetails = '';
                    let quantity = '';
                    let unitPrice = 0;

                    if (service.serviceType === 'consultation') {
                      const consultationService = service as any;
                      serviceTotal = consultationService.cost || 0;
                      serviceDetails = `${consultationService.topic || 'استشارة'} - ${consultationService.hours || 0} ساعة`;
                      quantity = `${consultationService.hours || 0} ساعة`;
                      unitPrice = consultationService.cost || 0;
                    } else if (service.serviceType === 'pattern') {
                      const patternService = service as any;
                      serviceTotal = patternService.models?.reduce((sum: number, model: any) => sum + (model.finalAmount || model.cost || 0), 0) || 0;
                      serviceDetails = `${patternService.models?.length || 0} نموذج باترون`;
                      quantity = `${patternService.models?.length || 0} نموذج`;
                      unitPrice = serviceTotal / (patternService.models?.length || 1);
                    } else if (service.serviceType === 'pattern_printing') {
                      const printingService = service as any;
                      serviceTotal = printingService.files?.reduce((sum: number, file: any) => sum + (file.cost || 0), 0) || 0;
                      serviceDetails = `طباعة ${printingService.files?.length || 0} ملف`;
                      quantity = `${printingService.files?.reduce((sum: number, file: any) => sum + (file.meters || 0), 0) || 0} متر`;
                      unitPrice = serviceTotal;
                    } else if (service.serviceType === 'shipping') {
                      const shippingService = service as any;
                      serviceTotal = shippingService.total || 0;
                      serviceDetails = `شحن ${shippingService.item || 'منتج'}`;
                      quantity = `${shippingService.quantity || 1}`;
                      unitPrice = shippingService.unitPrice || 0;
                    } else if (service.serviceType === 'manufacturing') {
                      const manufacturingService = service as any;
                      serviceTotal = manufacturingService.models?.reduce((sum: number, model: any) => {
                        return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);
                      }, 0) || 0;
                      serviceDetails = `تصنيع ${manufacturingService.models?.length || 0} نموذج`;
                      quantity = `${manufacturingService.models?.reduce((sum: number, model: any) => sum + (model.quantity || 0), 0) || 0}`;
                      unitPrice = serviceTotal / (manufacturingService.models?.length || 1);
                    } else if (service.serviceType === 'products') {
                      const productsService = service as any;
                      let productsSubtotal = productsService.items?.reduce((sum: number, item: any) => sum + (item.total || 0), 0) || 0;
                      // طرح الخصم إن وجد
                      if (productsService.discountAmount) {
                        productsSubtotal -= productsService.discountAmount || 0;
                      }
                      serviceTotal = Math.max(0, productsSubtotal);
                      serviceDetails = `${productsService.items?.length || 0} منتج`;
                      quantity = `${productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 0}`;
                      unitPrice = serviceTotal / (productsService.items?.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0) || 1);
                    } else {
                      // Default for other service types
                      serviceTotal = (service as any).cost || (service as any).total || 0;
                      serviceDetails = getServiceName(service.serviceType);
                      quantity = '1';
                      unitPrice = serviceTotal;
                    }

                    return (
                      <tr
                        key={service.id}
                        className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} hover:bg-blue-50 transition-colors duration-200 border-b border-gray-200`}
                      >
                        {/* Row Number */}
                        <td className="px-4 py-4 text-center border-r border-gray-200">
                          <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold text-sm mx-auto">
                            {index + 1}
                          </div>
                        </td>

                        {/* Service Type */}
                        <td className="px-4 py-4 text-center border-r border-gray-200">
                          <div className="bg-blue-100 text-blue-700 px-3 py-2 rounded-lg font-bold text-sm inline-block">
                            {getServiceName(service.serviceType)}
                          </div>
                        </td>

                        {/* Service Details */}
                        <td className="px-4 py-4 text-right border-r border-gray-200">
                          <div className="text-gray-700 font-medium leading-relaxed">
                            {serviceDetails}
                          </div>
                        </td>

                        {/* Quantity */}
                        <td className="px-4 py-4 text-center border-r border-gray-200">
                          <div className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg font-semibold text-sm inline-block">
                            {quantity}
                          </div>
                        </td>

                        {/* Unit Price */}
                        <td className="px-4 py-4 text-center border-r border-gray-200">
                          <div className="text-blue-600 font-bold text-lg">
                            {formatCurrency(unitPrice)}
                          </div>
                        </td>

                        {/* Total */}
                        <td className="px-4 py-4 text-center">
                          <div className="bg-green-100 text-green-700 px-4 py-2 rounded-lg font-bold text-lg inline-block shadow-sm">
                            {formatCurrency(serviceTotal)}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Total Summary Section - Enhanced Professional Design */}
          <div className="mb-8 bg-gradient-to-br from-slate-50 via-white to-slate-50 rounded-2xl shadow-xl border-2 border-slate-200 overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-slate-600 to-slate-700 text-white p-4">
              <h3 className="text-xl font-bold text-center flex items-center justify-center gap-3">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                </svg>
                ملخص المبالغ المالية
              </h3>
            </div>

            <div className="p-6">
              <div className="flex justify-between items-start gap-8">
                {/* Financial Details - Right Side */}
                <div className="flex-1 space-y-4">
                  {/* Grand Total */}
                  <div className="bg-green-50 border-2 border-green-200 rounded-xl p-4 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <span className="text-xl font-bold text-green-700">المجموع الكلي</span>
                    </div>
                    <span className="text-3xl font-bold text-green-600">{formatCurrency(invoice.total)}</span>
                  </div>

                  {/* Paid Amount */}
                  <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-4 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <span className="text-xl font-bold text-blue-700">المبلغ المدفوع</span>
                    </div>
                    <span className="text-2xl font-bold text-blue-600">{formatCurrency(invoice.paidAmount || 0)}</span>
                  </div>

                  {/* Remaining Amount */}
                  <div className={`${(invoice.remainingAmount || 0) > 0 ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'} border-2 rounded-xl p-4 flex justify-between items-center`}>
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 ${(invoice.remainingAmount || 0) > 0 ? 'bg-red-500' : 'bg-green-500'} rounded-full flex items-center justify-center`}>
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <span className={`text-xl font-bold ${(invoice.remainingAmount || 0) > 0 ? 'text-red-700' : 'text-green-700'}`}>المبلغ المتبقي</span>
                    </div>
                    <span className={`text-2xl font-bold ${(invoice.remainingAmount || 0) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {formatCurrency(invoice.remainingAmount || 0)}
                    </span>
                  </div>

                  {/* Payment Method */}
                  <div className="bg-gray-50 border-2 border-gray-200 rounded-xl p-4 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-500 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                          <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"/>
                        </svg>
                      </div>
                      <span className="text-xl font-bold text-gray-700">طريقة الدفع</span>
                    </div>
                    <span className="text-xl font-bold text-gray-800">{getPaymentMethodName(invoice.paymentMethod)}</span>
                  </div>

                  {/* Printing Cost Notice */}
                  {invoice.services.some(service => {
                    if (service.serviceType === 'pattern') {
                      return (service as any).models?.some((model: any) => model.fileType !== 'plt');
                    }
                    if (service.serviceType === 'manufacturing') {
                      return (service as any).models?.some((model: any) => model.fileType !== 'plt');
                    }
                    return false;
                  }) && (
                    <div className="bg-orange-50 border-2 border-orange-200 rounded-xl p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                          </svg>
                        </div>
                        <div>
                          <div className="text-orange-700 font-bold text-lg mb-1">ملاحظة مهمة</div>
                          <div className="text-orange-600 font-semibold">
                            يرجي العلم ان السعر غير شامل مصاريف الطباعة 30 جنية للمتر
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Payment Status Badge - Left Side */}
                <div className="flex-shrink-0">
                  <div className={`relative rounded-2xl p-6 text-center shadow-2xl transform rotate-3 border-4 bg-white ${
                    invoice.paymentStatus === 'مدفوع بالكامل' ? 'border-green-500 text-green-600' :
                    invoice.paymentStatus === 'مدفوع جزئياً' ? 'border-orange-500 text-orange-600' :
                    'border-red-500 text-red-600'
                  }`}>
                    <div className="relative z-10">
                      <div className="font-bold text-2xl mb-2">OKA GROUP</div>
                      <div className="w-16 h-1 bg-current mx-auto mb-3 rounded-full"></div>
                      <div className="font-bold text-lg">
                        {invoice.paymentStatus || 'غير محدد'}
                      </div>
                    </div>
                    {/* Decorative elements */}
                    <div className="absolute top-2 right-2 w-3 h-3 bg-current rounded-full opacity-20"></div>
                    <div className="absolute bottom-2 left-2 w-2 h-2 bg-current rounded-full opacity-30"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information - Enhanced Design */}
          {(invoice.requiresShipping || invoice.shippingAddress || invoice.notes) && (
            <div className="mb-8">
              <div className="bg-gradient-to-br from-amber-50 via-yellow-50 to-amber-50 rounded-2xl shadow-lg border-2 border-amber-200 overflow-hidden">
                {/* Header */}
                <div className="bg-gradient-to-r from-amber-500 to-yellow-500 text-white p-4">
                  <h4 className="text-xl font-bold text-center flex items-center justify-center gap-3">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                    </svg>
                    معلومات إضافية
                  </h4>
                </div>

                <div className="p-6 space-y-4">
                  {invoice.requiresShipping && (
                    <div className="bg-green-50 border-2 border-green-200 rounded-xl p-4 flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
                            <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
                          </svg>
                        </div>
                        <span className="font-bold text-green-700 text-lg">يتطلب شحن</span>
                      </div>
                      <span className="text-green-600 font-bold text-xl">نعم</span>
                    </div>
                  )}

                  {invoice.shippingAddress && (
                    <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                          </svg>
                        </div>
                        <div className="flex-1">
                          <span className="font-bold text-blue-700 text-lg block mb-2">عنوان الشحن</span>
                          <span className="text-gray-700 font-medium leading-relaxed">{invoice.shippingAddress}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {invoice.notes && (
                    <div className="bg-purple-50 border-2 border-purple-200 rounded-xl p-4">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                          </svg>
                        </div>
                        <div className="flex-1">
                          <span className="font-bold text-purple-700 text-lg block mb-2">ملاحظات</span>
                          <span className="text-gray-700 font-medium leading-relaxed">{invoice.notes}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer Section - Professional Enhanced Design */}
        <div className="bg-gradient-to-br from-slate-100 via-white to-slate-100 border-t-4 border-slate-300 relative overflow-hidden" style={{direction: 'rtl'}}>
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 left-0 w-32 h-32 bg-slate-300 rounded-full -translate-y-16 -translate-x-16"></div>
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-slate-200 rounded-full translate-y-12 translate-x-12"></div>
          </div>

          <div className="relative z-10 p-8">
            {/* Header */}
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-slate-700 mb-2">OKA GROUP</h3>
              <div className="w-32 h-1 bg-gradient-to-r from-blue-500 to-green-500 mx-auto rounded-full"></div>
              <p className="text-slate-600 font-medium mt-2">مجموعة أوكا للخدمات المتكاملة</p>
            </div>

            <div className="flex justify-between items-start gap-8">
              {/* Right side - Contact Information */}
              <div className="flex-1 text-right space-y-4" style={{direction: 'rtl'}}>
                <div className="bg-white rounded-2xl p-6 shadow-lg border-2 border-slate-200">
                  <h4 className="text-xl font-bold text-slate-700 mb-4 flex items-center justify-end gap-3">
                    <span>معلومات التواصل</span>
                    <svg className="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd"/>
                    </svg>
                  </h4>

                  <div className="space-y-3">
                    {/* Email */}
                    <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-3 flex items-center justify-end gap-3">
                      <div className="text-right">
                        <div className="text-blue-700 font-bold">{settings.email}</div>
                        <div className="text-blue-600 text-sm">البريد الإلكتروني</div>
                      </div>
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                        </svg>
                      </div>
                    </div>

                    {/* Phone */}
                    <div className="bg-green-50 border-2 border-green-200 rounded-xl p-3 flex items-center justify-end gap-3">
                      <div className="text-right">
                        <div className="text-green-700 font-bold">{settings.complaintsPhone}</div>
                        <div className="text-green-600 text-sm">للشكاوى والاقتراحات</div>
                      </div>
                      <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                        </svg>
                      </div>
                    </div>

                    {/* Address */}
                    <div className="bg-purple-50 border-2 border-purple-200 rounded-xl p-3 flex items-center justify-end gap-3">
                      <div className="text-right">
                        <div className="text-purple-700 font-bold">{settings.address}</div>
                        <div className="text-purple-600 text-sm">المقر الإداري</div>
                      </div>
                      <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Left side - Company Stamp */}
              <div className="flex-shrink-0">
                <div className="bg-white rounded-2xl p-6 shadow-lg border-2 border-slate-200 text-center">
                  <h4 className="text-lg font-bold text-slate-700 mb-4">ختم الشركة</h4>

                  {settings.companyStamp ? (
                    <div className="w-48 h-32 bg-gray-50 border-2 border-gray-200 rounded-xl p-3 flex items-center justify-center">
                      <img
                        src={settings.companyStamp}
                        alt="ختم الشركة"
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                  ) : (
                    <div className="w-48 h-32 border-3 border-dashed border-slate-300 rounded-xl flex items-center justify-center bg-slate-50">
                      <div className="text-center text-slate-400">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-sm font-semibold">ختم الشركة</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Bottom Decorative Section */}
            <div className="mt-8 pt-6 border-t-2 border-slate-200">
              <div className="text-center">
                <div className="bg-gradient-to-r from-blue-500 to-green-500 text-white px-8 py-3 rounded-full inline-block shadow-lg">
                  <div className="flex items-center justify-center gap-4 text-lg font-bold">
                    <span>شكراً لثقتكم بنا</span>
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span>نتطلع لخدمتكم مرة أخرى</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Gradient Border */}
          <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-blue-500 via-green-500 via-purple-500 to-blue-500"></div>
        </div>
      </div>

      {/* Company Settings Modal */}
      {showCompanySettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-lg">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">إعدادات الشركة</h2>
                <button
                  onClick={() => setShowCompanySettings(false)}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="space-y-6">
                {/* Company Name */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">اسم الشركة</label>
                  <input
                    type="text"
                    value={settings.companyName}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ companyName: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل اسم الشركة"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={settings.email}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ email: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>

                {/* Complaints Phone */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">هاتف الشكاوى والاقتراحات</label>
                  <input
                    type="tel"
                    value={settings.complaintsPhone}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ complaintsPhone: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل رقم هاتف الشكاوى"
                  />
                </div>

                {/* Address */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">عنوان المقر الإداري</label>
                  <textarea
                    value={settings.address}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ address: e.target.value });
                    }}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    placeholder="أدخل عنوان المقر الإداري"
                  />
                </div>

                {/* Slogan */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">شعار الشركة</label>
                  <input
                    type="text"
                    value={settings.slogan}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ slogan: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل شعار الشركة"
                  />
                </div>

                {/* Wish Message */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">رسالة التمنيات</label>
                  <input
                    type="text"
                    value={settings.wishMessage}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ wishMessage: e.target.value });
                    }}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="أدخل رسالة التمنيات"
                  />
                </div>

                {/* Company Description */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">وصف الشركة</label>
                  <textarea
                    value={settings.description}
                    onChange={(e) => {
                      const { updateSettings } = useSettingsStore.getState();
                      updateSettings({ description: e.target.value });
                    }}
                    rows={2}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    placeholder="أدخل وصف الشركة"
                  />
                </div>

                {/* Company Logo Upload */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">شعار الشركة</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    {settings.companyLogo ? (
                      <div className="space-y-4">
                        <img
                          src={settings.companyLogo}
                          alt="شعار الشركة"
                          className="w-32 h-32 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                        />
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyLogo: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          تغيير الشعار
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <svg className="w-16 h-16 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-gray-600">اضغط لرفع شعار الشركة</p>
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyLogo: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          رفع الشعار
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Company Stamp Upload */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">ختم الشركة</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                    {settings.companyStamp ? (
                      <div className="space-y-4">
                        <img
                          src={settings.companyStamp}
                          alt="ختم الشركة"
                          className="w-24 h-24 object-contain mx-auto border border-gray-200 rounded-lg bg-white p-2"
                        />
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyStamp: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          تغيير الختم
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <svg className="w-12 h-12 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-gray-600">اضغط لرفع ختم الشركة</p>
                        <button
                          onClick={() => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'image/*';
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  const { updateSettings } = useSettingsStore.getState();
                                  updateSettings({ companyStamp: e.target?.result as string });
                                };
                                reader.readAsDataURL(file);
                              }
                            };
                            input.click();
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                        >
                          رفع الختم
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">
                  سيتم حفظ الإعدادات تلقائياً عند التغيير
                </p>
                <button
                  onClick={() => setShowCompanySettings(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* POS Invoice Modal */}
      {showPOSInvoice && (
        <POSInvoice
          invoice={invoice}
          onClose={() => setShowPOSInvoice(false)}
        />
      )}
    </div>
  );
};

export default InvoicePreview;
