/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/customer-codes/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=false!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/customer-codes/page.tsx */ \"(app-pages-browser)/./src/app/customer-codes/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNDQUQlMjBVU0VSJTIwWDY0JTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1QzIzMSU1Q3NyYyU1Q2FwcCU1Q2N1c3RvbWVyLWNvZGVzJTVDcGFnZS50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzVlZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcY3VzdG9tZXItY29kZXNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/customer-codes/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/customer-codes/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CustomerCodeManager */ \"(app-pages-browser)/./src/components/CustomerCodeManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CustomerCodesPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"إدارة أكواد العملاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة وتتبع أكواد العملاء والعدادات الخاصة بالنظام\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/customers\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"إدارة العملاء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"الصفحة الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerCodeManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 bg-blue-50 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-blue-800 mb-4\",\n                            children: \"معلومات مهمة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"تنسيق أكواد العملاء:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• التنسيق: p + رقم المعاملة (2 رقم) + رقم العميل (4 أرقام)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• مثال: p013621 (معاملة 01، عميل 3621)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• الأكواد تتزايد تلقائياً مع كل عميل جديد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-700 mb-2\",\n                                            children: \"إدارة العدادات:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• يمكن تعديل العدادات يدوياً عند الحاجة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• رقم المعاملة يزيد عند بدء معاملة جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• إعادة التعيين تحذف جميع العدادات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-yellow-100 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"تنبيه:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" تأكد من عمل نسخة احتياطية قبل إجراء أي تعديلات على العدادات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\app\\\\customer-codes\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerCodesPage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CustomerCodesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomerCodesPage);\nvar _c;\n$RefreshReg$(_c, \"CustomerCodesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customer-codes/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CustomerCodeManager.tsx":
/*!************************************************!*\
  !*** ./src/components/CustomerCodeManager.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/customerCodeGenerator */ \"(app-pages-browser)/./src/utils/customerCodeGenerator.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CustomerCodeManager = ()=>{\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editCounters, setEditCounters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        lastTransactionNumber: 1,\n        lastCustomerNumber: 0,\n        totalCustomers: 0\n    });\n    const [searchCode, setSearchCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResult, setSearchResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loadStats = ()=>{\n        try {\n            const codeStats = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.getCustomerCodeStats)();\n            setStats(codeStats);\n            // تحميل العدادات الحالية\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureGetItem)(\"customerCounters\");\n            if (result.success && result.data) {\n                setEditCounters(result.data);\n            }\n        } catch (error) {\n            console.error(\"خطأ في تحميل إحصائيات الأكواد:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadStats();\n    }, []);\n    const handleGenerateNewCode = ()=>{\n        try {\n            const newCode = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.generateCustomerCode)();\n            console.log(\"تم إنشاء كود جديد:\", newCode);\n            loadStats(); // إعادة تحميل الإحصائيات\n        } catch (error) {\n            console.error(\"خطأ في إنشاء كود جديد:\", error);\n            alert(\"حدث خطأ في إنشاء كود جديد\");\n        }\n    };\n    const handleIncrementTransaction = ()=>{\n        try {\n            (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.incrementTransactionNumber)();\n            loadStats();\n            alert(\"تم الانتقال للمعاملة التالية بنجاح\");\n        } catch (error) {\n            console.error(\"خطأ في تحديث رقم المعاملة:\", error);\n            alert(\"حدث خطأ في تحديث رقم المعاملة\");\n        }\n    };\n    const handleSaveCounters = ()=>{\n        try {\n            const result = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.secureSetItem)(\"customerCounters\", editCounters);\n            if (result.success) {\n                loadStats();\n                setShowEditModal(false);\n                alert(\"تم حفظ العدادات بنجاح\");\n            } else {\n                throw new Error(result.error);\n            }\n        } catch (error) {\n            console.error(\"خطأ في حفظ العدادات:\", error);\n            alert(\"حدث خطأ في حفظ العدادات\");\n        }\n    };\n    const handleResetCounters = ()=>{\n        if (confirm(\"هل أنت متأكد من إعادة تعيين جميع العدادات؟ هذا الإجراء لا يمكن التراجع عنه.\")) {\n            try {\n                (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.resetCustomerCounters)();\n                loadStats();\n                alert(\"تم إعادة تعيين العدادات بنجاح\");\n            } catch (error) {\n                console.error(\"خطأ في إعادة تعيين العدادات:\", error);\n                alert(\"حدث خطأ في إعادة تعيين العدادات\");\n            }\n        }\n    };\n    const handleSearchCode = ()=>{\n        if (!searchCode.trim()) {\n            setSearchResult(null);\n            return;\n        }\n        try {\n            const customer = (0,_utils_customerCodeGenerator__WEBPACK_IMPORTED_MODULE_2__.findCustomerByCode)(searchCode.trim());\n            setSearchResult(customer);\n        } catch (error) {\n            console.error(\"خطأ في البحث:\", error);\n            setSearchResult(null);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-gray-600\",\n                    children: \"جاري التحميل...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"إدارة أكواد العملاء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowEditModal(true),\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"تعديل العدادات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadStats,\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"تحديث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-800 mb-2\",\n                                children: \"إجمالي العملاء\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-blue-600\",\n                                children: stats.totalCustomers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-2\",\n                                children: \"الأكواد المولدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-green-600\",\n                                children: stats.generatedCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                                children: \"الأكواد المخصصة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl font-bold text-yellow-600\",\n                                children: stats.customCodes\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-purple-800 mb-2\",\n                                children: \"آخر كود\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl font-bold text-purple-600\",\n                                children: stats.lastGeneratedCode || \"لا يوجد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"رقم المعاملة الحالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastTransactionNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"آخر رقم عميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.lastCustomerNumber\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2\",\n                                children: \"الكود التالي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-600\",\n                                children: stats.nextCode\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-green-800 mb-3\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-green-700 mb-3\",\n                                children: \"إنشاء كود عميل جديد للاختبار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGenerateNewCode,\n                                className: \"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                children: \"إنشاء كود جديد\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 p-4 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-orange-800 mb-3\",\n                                children: \"الانتقال للمعاملة التالية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-orange-700 mb-3\",\n                                children: \"زيادة رقم المعاملة للانتقال لمعاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleIncrementTransaction,\n                                className: \"w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors\",\n                                children: \"معاملة جديدة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-blue-800 mb-3\",\n                        children: \"البحث عن كود عميل\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchCode,\n                                onChange: (e)=>setSearchCode(e.target.value),\n                                placeholder: \"أدخل كود العميل (مثل: p013621)\",\n                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSearchCode,\n                                className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"بحث\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-white rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-800\",\n                                children: \"نتيجة البحث:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الاسم:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الهاتف:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.phone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"الكود:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 16\n                                    }, undefined),\n                                    \" \",\n                                    searchResult.clientCode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, undefined),\n                    searchCode && !searchResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 rounded-lg border border-red-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: \"لم يتم العثور على عميل بهذا الكود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-red-800 mb-3\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-700 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"تحذير:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            \" هذا الإجراء سيعيد تعيين جميع العدادات إلى القيم الافتراضية\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleResetCounters,\n                        className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                        children: \"إعادة تعيين العدادات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            showEditModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-800 mb-4\",\n                            children: \"تعديل العدادات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"رقم المعاملة الحالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastTransactionNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastTransactionNumber: parseInt(e.target.value) || 1\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"آخر رقم عميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.lastCustomerNumber,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    lastCustomerNumber: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"إجمالي العملاء\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: editCounters.totalCustomers,\n                                            onChange: (e)=>setEditCounters({\n                                                    ...editCounters,\n                                                    totalCustomers: parseInt(e.target.value) || 0\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveCounters,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"حفظ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowEditModal(false),\n                                    className: \"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerCodeManager.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerCodeManager, \"CHRWvQp1fkSwgE4wgiU7Ep3Shek=\");\n_c = CustomerCodeManager;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomerCodeManager);\nvar _c;\n$RefreshReg$(_c, \"CustomerCodeManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CustomerCodeManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/customerCodeGenerator.ts":
/*!********************************************!*\
  !*** ./src/utils/customerCodeGenerator.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCodeDuplication: function() { return /* binding */ checkCodeDuplication; },\n/* harmony export */   checkCustomerDataDuplication: function() { return /* binding */ checkCustomerDataDuplication; },\n/* harmony export */   findCustomerByCode: function() { return /* binding */ findCustomerByCode; },\n/* harmony export */   generateCustomerCode: function() { return /* binding */ generateCustomerCode; },\n/* harmony export */   getCustomerCodeStats: function() { return /* binding */ getCustomerCodeStats; },\n/* harmony export */   getCustomerCodeSuggestions: function() { return /* binding */ getCustomerCodeSuggestions; },\n/* harmony export */   incrementTransactionNumber: function() { return /* binding */ incrementTransactionNumber; },\n/* harmony export */   isCustomerCodeUnique: function() { return /* binding */ isCustomerCodeUnique; },\n/* harmony export */   parseCustomerCode: function() { return /* binding */ parseCustomerCode; },\n/* harmony export */   resetCustomerCounters: function() { return /* binding */ resetCustomerCounters; },\n/* harmony export */   validateCustomerCode: function() { return /* binding */ validateCustomerCode; },\n/* harmony export */   validateCustomerUniqueness: function() { return /* binding */ validateCustomerUniqueness; }\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/**\n * نظام إنشاء وإدارة أكواد العملاء\n * التنسيق: p013621 (p01 = رقم المعاملة، 3621 = الرقم المسلسل للعميل)\n */ \n// إعدادات نظام الأكواد\nconst CODE_CONFIG = {\n    PREFIX: \"p\",\n    TRANSACTION_DIGITS: 2,\n    CUSTOMER_DIGITS: 4,\n    STARTING_TRANSACTION: 1,\n    STARTING_CUSTOMER: 3621\n};\n/**\n * تحميل العدادات من التخزين الآمن\n */ const loadCounters = ()=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customerCounters\");\n    if (result.success && result.data) {\n        return result.data;\n    }\n    // القيم الافتراضية\n    return {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n};\n/**\n * حفظ العدادات في التخزين الآمن\n */ const saveCounters = (counters)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"customerCounters\", counters);\n    if (!result.success) {\n        console.error(\"فشل في حفظ عدادات العملاء:\", result.error);\n        throw new Error(\"فشل في حفظ عدادات العملاء\");\n    }\n};\n/**\n * تنسيق رقم بعدد أرقام محدد\n */ const formatNumber = (num, digits)=>{\n    return num.toString().padStart(digits, \"0\");\n};\n/**\n * إنشاء كود عميل جديد\n */ const generateCustomerCode = ()=>{\n    const counters = loadCounters();\n    // زيادة العدادات\n    counters.lastCustomerNumber += 1;\n    counters.totalCustomers += 1;\n    // إنشاء الكود\n    const transactionPart = formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS);\n    const customerPart = formatNumber(counters.lastCustomerNumber, CODE_CONFIG.CUSTOMER_DIGITS);\n    const code = \"\".concat(CODE_CONFIG.PREFIX).concat(transactionPart).concat(customerPart);\n    // حفظ العدادات المحدثة\n    saveCounters(counters);\n    return {\n        code,\n        transactionNumber: counters.lastTransactionNumber,\n        customerNumber: counters.lastCustomerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: false\n    };\n};\n/**\n * التحقق من صحة كود العميل\n */ const validateCustomerCode = (code)=>{\n    if (!code || typeof code !== \"string\") {\n        return {\n            isValid: false,\n            error: \"الكود مطلوب\"\n        };\n    }\n    // التحقق من البادئة\n    if (!code.startsWith(CODE_CONFIG.PREFIX)) {\n        return {\n            isValid: false,\n            error: 'يجب أن يبدأ الكود بـ \"'.concat(CODE_CONFIG.PREFIX, '\"')\n        };\n    }\n    // التحقق من الطول\n    const expectedLength = 1 + CODE_CONFIG.TRANSACTION_DIGITS + CODE_CONFIG.CUSTOMER_DIGITS;\n    if (code.length !== expectedLength) {\n        return {\n            isValid: false,\n            error: \"يجب أن يكون طول الكود \".concat(expectedLength, \" أحرف\")\n        };\n    }\n    // التحقق من الأرقام\n    const numberPart = code.slice(1);\n    if (!/^\\d+$/.test(numberPart)) {\n        return {\n            isValid: false,\n            error: \"يجب أن يحتوي الكود على أرقام فقط بعد البادئة\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * تحليل كود العميل\n */ const parseCustomerCode = (code)=>{\n    const validation = validateCustomerCode(code);\n    if (!validation.isValid) {\n        return null;\n    }\n    const numberPart = code.slice(1);\n    const transactionNumber = parseInt(numberPart.slice(0, CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    const customerNumber = parseInt(numberPart.slice(CODE_CONFIG.TRANSACTION_DIGITS), 10);\n    return {\n        code,\n        transactionNumber,\n        customerNumber,\n        generatedAt: new Date().toISOString(),\n        isCustom: true\n    };\n};\n/**\n * التحقق من تفرد كود العميل\n */ const isCustomerCodeUnique = (code, excludeClientId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return true; // إذا لم توجد عملاء، فالكود فريد\n    }\n    const customers = result.data;\n    return !customers.some((customer)=>customer.clientCode === code && customer.id !== excludeClientId);\n};\n/**\n * الحصول على إحصائيات أكواد العملاء\n */ const getCustomerCodeStats = ()=>{\n    const counters = loadCounters();\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    const customers = result.success && result.data ? result.data : [];\n    const customCodes = customers.filter((c)=>c.clientCode && !c.clientCode.match(/^p\\d+$/));\n    const generatedCodes = customers.filter((c)=>c.clientCode && c.clientCode.match(/^p\\d+$/));\n    return {\n        totalCustomers: customers.length,\n        generatedCodes: generatedCodes.length,\n        customCodes: customCodes.length,\n        nextCode: \"\".concat(CODE_CONFIG.PREFIX).concat(formatNumber(counters.lastTransactionNumber, CODE_CONFIG.TRANSACTION_DIGITS)).concat(formatNumber(counters.lastCustomerNumber + 1, CODE_CONFIG.CUSTOMER_DIGITS)),\n        lastTransactionNumber: counters.lastTransactionNumber,\n        lastCustomerNumber: counters.lastCustomerNumber\n    };\n};\n/**\n * إعادة تعيين العدادات (للاستخدام في حالات الطوارئ)\n */ const resetCustomerCounters = ()=>{\n    const defaultCounters = {\n        lastTransactionNumber: CODE_CONFIG.STARTING_TRANSACTION,\n        lastCustomerNumber: CODE_CONFIG.STARTING_CUSTOMER,\n        totalCustomers: 0\n    };\n    saveCounters(defaultCounters);\n};\n/**\n * تحديث رقم المعاملة (للانتقال لمعاملة جديدة)\n */ const incrementTransactionNumber = ()=>{\n    const counters = loadCounters();\n    counters.lastTransactionNumber += 1;\n    saveCounters(counters);\n};\n/**\n * البحث عن عميل بالكود\n */ const findCustomerByCode = (code)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return null;\n    }\n    return result.data.find((customer)=>customer.clientCode === code) || null;\n};\n/**\n * اقتراحات أكواد العملاء (للبحث التلقائي)\n */ const getCustomerCodeSuggestions = function(query) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return [];\n    }\n    const customers = result.data;\n    const lowerQuery = query.toLowerCase();\n    return customers.filter((customer)=>{\n        var _customer_clientCode;\n        return ((_customer_clientCode = customer.clientCode) === null || _customer_clientCode === void 0 ? void 0 : _customer_clientCode.toLowerCase().includes(lowerQuery)) || customer.name.toLowerCase().includes(lowerQuery) || customer.phone.includes(query);\n    }).slice(0, limit);\n};\n/**\n * التحقق من تكرار كود العميل\n */ const checkCodeDuplication = (code, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const existingCustomer = customers.find((customer)=>customer.clientCode === code && customer.id !== excludeId);\n    return {\n        isDuplicate: !!existingCustomer,\n        existingCustomer\n    };\n};\n/**\n * التحقق من تكرار بيانات العميل (الاسم + الهاتف)\n */ const checkCustomerDataDuplication = (name, phone, excludeId)=>{\n    const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureGetItem)(\"customers\");\n    if (!result.success || !result.data) {\n        return {\n            isDuplicate: false\n        };\n    }\n    const customers = result.data;\n    const trimmedName = name.trim().toLowerCase();\n    const trimmedPhone = phone.trim();\n    // البحث عن تطابق في الاسم والهاتف\n    const nameMatch = customers.find((customer)=>customer.name.trim().toLowerCase() === trimmedName && customer.id !== excludeId);\n    const phoneMatch = customers.find((customer)=>customer.phone.trim() === trimmedPhone && customer.id !== excludeId);\n    if (nameMatch && phoneMatch && nameMatch.id === phoneMatch.id) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"both\",\n            existingCustomer: nameMatch\n        };\n    } else if (nameMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"name\",\n            existingCustomer: nameMatch\n        };\n    } else if (phoneMatch) {\n        return {\n            isDuplicate: true,\n            duplicateType: \"phone\",\n            existingCustomer: phoneMatch\n        };\n    }\n    return {\n        isDuplicate: false\n    };\n};\n/**\n * التحقق الشامل من التكرار قبل إضافة/تحديث العميل\n */ const validateCustomerUniqueness = (customerData, excludeId)=>{\n    const errors = [];\n    const warnings = [];\n    const suggestions = [];\n    // التحقق من كود العميل إذا تم تمريره\n    if (customerData.clientCode) {\n        const codeValidation = validateCustomerCode(customerData.clientCode);\n        if (!codeValidation.isValid) {\n            errors.push(codeValidation.error);\n        } else {\n            const codeDuplication = checkCodeDuplication(customerData.clientCode, excludeId);\n            if (codeDuplication.isDuplicate) {\n                var _codeDuplication_existingCustomer;\n                errors.push('كود العميل \"'.concat(customerData.clientCode, '\" مستخدم بالفعل للعميل: ').concat((_codeDuplication_existingCustomer = codeDuplication.existingCustomer) === null || _codeDuplication_existingCustomer === void 0 ? void 0 : _codeDuplication_existingCustomer.name));\n                suggestions.push(\"يمكنك ترك حقل الكود فارغاً لإنشاء كود جديد تلقائياً\");\n            }\n        }\n    }\n    // التحقق من تكرار بيانات العميل\n    const dataDuplication = checkCustomerDataDuplication(customerData.name, customerData.phone, excludeId);\n    if (dataDuplication.isDuplicate) {\n        const existing = dataDuplication.existingCustomer;\n        switch(dataDuplication.duplicateType){\n            case \"both\":\n                errors.push(\"عميل بنفس الاسم ورقم الهاتف موجود بالفعل: \".concat(existing.name, \" (\").concat(existing.clientCode, \")\"));\n                break;\n            case \"name\":\n                warnings.push(\"يوجد عميل بنفس الاسم: \".concat(existing.name, \" (\").concat(existing.clientCode, \") - هاتف: \").concat(existing.phone));\n                suggestions.push(\"تأكد من أن هذا عميل مختلف أم نفس العميل\");\n                break;\n            case \"phone\":\n                warnings.push(\"يوجد عميل بنفس رقم الهاتف: \".concat(existing.name, \" (\").concat(existing.clientCode, \")\"));\n                suggestions.push(\"تأكد من صحة رقم الهاتف أو أن هذا عميل مختلف\");\n                break;\n        }\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings,\n        suggestions: suggestions.length > 0 ? suggestions : undefined\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/customerCodeGenerator.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: function() { return /* binding */ STORAGE_CONFIG; },\n/* harmony export */   checkStorageHealth: function() { return /* binding */ checkStorageHealth; },\n/* harmony export */   createBackup: function() { return /* binding */ createBackup; },\n/* harmony export */   restoreFromBackup: function() { return /* binding */ restoreFromBackup; },\n/* harmony export */   secureGetItem: function() { return /* binding */ secureGetItem; },\n/* harmony export */   secureSetItem: function() { return /* binding */ secureSetItem; }\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = \"backup-\".concat(Date.now());\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: \"تم استرداد البيانات بنجاح من النسخة: \".concat(backup.timestamp)\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/secureStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ZGZlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/navigation.js":
/*!*****************************************!*\
  !*** ./node_modules/next/navigation.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUEsK0pBQStEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanM/MzYwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/navigation.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Ccustomer-codes%5Cpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);