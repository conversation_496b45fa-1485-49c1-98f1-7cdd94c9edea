'use client';

import React, { useState, useEffect } from 'react';
import { Invoice } from '@/types';
import { useInvoiceStore } from '@/store/invoiceStore';
import { generateInvoicePDF } from '@/utils/pdfGenerator';
import InvoicePreview from './InvoicePreview';

const InvoicesList: React.FC = () => {
  const { invoices } = useInvoiceStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('date_desc');
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [previewInvoice, setPreviewInvoice] = useState<Invoice | null>(null);

  // إعادة تحميل البيانات من localStorage عند تحميل المكون
  useEffect(() => {
    const loadInvoices = () => {
      try {
        const stored = localStorage.getItem('invoices');
        if (stored) {
          const parsedInvoices = JSON.parse(stored);

          // التحقق من عدم وجود تكرار في البيانات
          const uniqueInvoices = parsedInvoices.filter((invoice: Invoice, index: number, self: Invoice[]) =>
            index === self.findIndex(inv => inv.id === invoice.id)
          );

          if (uniqueInvoices.length !== parsedInvoices.length) {
            localStorage.setItem('invoices', JSON.stringify(uniqueInvoices));
          }

          // تحديث المتجر بالبيانات المحملة
          useInvoiceStore.setState({ invoices: uniqueInvoices });

          // تحديث الحالة المحلية أيضاً
          setFilteredInvoices(uniqueInvoices);
        } else {
          useInvoiceStore.setState({ invoices: [] });
          setFilteredInvoices([]);
        }
      } catch (error) {
        useInvoiceStore.setState({ invoices: [] });
        setFilteredInvoices([]);
      }
    };

    // تحميل فوري
    loadInvoices();

    // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'invoices') {
        loadInvoices();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  useEffect(() => {
    let filtered = [...invoices];

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(invoice => 
        invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.client.phone.includes(searchTerm) ||
        invoice.id?.includes(searchTerm)
      );
    }

    // الفلترة
    if (filterBy !== 'all') {
      filtered = filtered.filter(invoice => {
        switch (filterBy) {
          case 'consultation':
            return invoice.services.some(s => s.serviceType === 'consultation');
          case 'pattern':
            return invoice.services.some(s => s.serviceType === 'pattern');
          case 'photography':
            return invoice.services.some(s => s.serviceType === 'photography');
          case 'shipping':
            return invoice.services.some(s => s.serviceType === 'shipping');
          case 'high_value':
            return invoice.total > 1000;
          case 'recent':
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            return new Date(invoice.createdAt) > oneWeekAgo;
          case 'paid_full':
            return invoice.paymentStatus === 'مدفوع بالكامل';
          case 'paid_partial':
            return invoice.paymentStatus === 'مدفوع جزئياً';
          case 'unpaid':
            return invoice.paymentStatus === 'غير مدفوع' || !invoice.paymentStatus;
          default:
            return true;
        }
      });
    }

    // الترتيب
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date_desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'date_asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'amount_desc':
          return b.total - a.total;
        case 'amount_asc':
          return a.total - b.total;
        case 'client_name':
          return a.client.name.localeCompare(b.client.name, 'ar');
        default:
          return 0;
      }
    });

    setFilteredInvoices(filtered);
  }, [invoices, searchTerm, filterBy, sortBy]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      calendar: 'gregory', // التقويم الميلادي
    });
  };

  const formatCurrency = (amount: number | string | undefined | null) => {
    // التحقق من صحة القيمة وتحويلها لرقم
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(String(amount || 0));

    // التحقق من أن النتيجة رقم صحيح
    if (isNaN(numericAmount)) {
      return '0.00 جنيه';
    }

    return `${numericAmount.toFixed(2)} جنيه`;
  };

  const getServiceNames = (services: any[]) => {
    const serviceNames: { [key: string]: string } = {
      consultation: 'استشارات',
      pattern: 'باترون',
      pattern_printing: 'طباعة باترون',
      manufacturing: 'تصنيع',
      samples: 'عينات',
      shipping: 'شحن',
      marketing: 'تسويق',
      photography: 'تصوير',
      products: 'منتجات',
      raw_purchases: 'مشتريات خام',
    };
    
    return services.map(s => serviceNames[s.serviceType] || s.serviceType).join('، ');
  };

  // دالة عرض رسائل التأكيد المحسنة
  const showSuccessMessage = (message: string) => {
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    toast.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(toast);
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 3000);
  };

  const showErrorMessage = (message: string) => {
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    toast.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(toast);
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 4000);
  };

  const handleExportPDF = async (invoice: Invoice) => {
    try {
      // فتح المعاينة أولاً للحصول على نفس التنسيق
      setPreviewInvoice(invoice);

      // انتظار قصير للتأكد من عرض المعاينة
      setTimeout(async () => {
        try {
          await generateInvoicePDF(invoice);
          showSuccessMessage(`تم تصدير PDF للفاتورة ${invoice.id} بنجاح! 🎉`);

          // إغلاق المعاينة بعد التصدير بثانية واحدة
          setTimeout(() => {
            setPreviewInvoice(null);
          }, 1000);
        } catch (error) {
          console.error('❌ Error exporting PDF:', error);
          showErrorMessage('فشل في تصدير PDF. يرجى المحاولة مرة أخرى.');
          setPreviewInvoice(null);
        }
      }, 500);
    } catch (error) {
      showErrorMessage('حدث خطأ أثناء تصدير PDF.');
    }
  };

  const handlePreviewInvoice = (invoice: Invoice) => {
    setPreviewInvoice(invoice);
  };

  const handleClosePreview = () => {
    setPreviewInvoice(null);
  };

  const handleEditFromPreview = () => {
    // For now, just close the preview
    // In a full implementation, this would navigate to edit mode
    setPreviewInvoice(null);
    alert('وظيفة التعديل ستكون متاحة قريباً');
  };



  const handleExportFromPreview = async () => {
    if (previewInvoice) {
      await handleExportPDF(previewInvoice);
    }
  };

  // دالة حذف الفاتورة مع تأكيد
  const handleDeleteInvoice = (invoice: Invoice) => {
    const confirmMessage = `هل أنت متأكد من حذف هذه الفاتورة؟\n\nالفاتورة: ${invoice.id}\nالعميل: ${invoice.client.name}\nالمبلغ: ${formatCurrency(invoice.total)}\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!`;

    if (window.confirm(confirmMessage)) {
      const store = useInvoiceStore.getState();
      store.deleteInvoice(invoice.id);

      if (previewInvoice && previewInvoice.id === invoice.id) {
        setPreviewInvoice(null);
      }

      setTimeout(() => {
        const updatedInvoices = JSON.parse(localStorage.getItem('invoices') || '[]');
        setFilteredInvoices(updatedInvoices);
      }, 100);

      showSuccessMessage(`تم حذف الفاتورة ${invoice.id} بنجاح! 🗑️`);
    }
  };

  const getTotalStats = () => {
    const total = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const count = filteredInvoices.length;
    const avgAmount = count > 0 ? total / count : 0;
    
    return { total, count, avgAmount };
  };

  const stats = getTotalStats();

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">إدارة الفواتير</h1>
        
        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500">
            <h3 className="text-lg font-semibold text-blue-700">إجمالي الفواتير</h3>
            <p className="text-2xl font-bold text-blue-800">{stats.count}</p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg border-r-4 border-green-500">
            <h3 className="text-lg font-semibold text-green-700">إجمالي المبلغ</h3>
            <p className="text-2xl font-bold text-green-800">{formatCurrency(stats.total)}</p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500">
            <h3 className="text-lg font-semibold text-purple-700">متوسط الفاتورة</h3>
            <p className="text-2xl font-bold text-purple-800">{formatCurrency(stats.avgAmount)}</p>
          </div>


        </div>

        {/* أدوات البحث والفلترة */}
        <div className="bg-white p-4 rounded-lg shadow-md">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="ابحث بالاسم، الهاتف، أو رقم الفاتورة"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* الفلترة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">فلترة حسب</label>
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الفواتير</option>
                <option value="consultation">استشارات</option>
                <option value="pattern">باترون</option>
                <option value="photography">تصوير</option>
                <option value="shipping">شحن</option>
                <option value="high_value">قيمة عالية (+1000 جنيه)</option>
                <option value="recent">الأسبوع الماضي</option>
                <option value="paid_full">مدفوع بالكامل</option>
                <option value="paid_partial">مدفوع جزئياً</option>
                <option value="unpaid">غير مدفوع</option>
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">ترتيب حسب</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                <option value="amount_desc">المبلغ (الأعلى أولاً)</option>
                <option value="amount_asc">المبلغ (الأقل أولاً)</option>
                <option value="client_name">اسم العميل</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة الفواتير */}
      {filteredInvoices.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-4">
            {invoices.length === 0 ? 'لا توجد فواتير محفوظة' : 'لا توجد نتائج للبحث'}
          </div>
          {invoices.length === 0 && (
            <p className="text-gray-400">قم بإنشاء فاتورة جديدة لتظهر هنا</p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredInvoices.map((invoice) => (
            <div key={invoice.id} className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
              {/* رأس البطاقة */}
              <div className="bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold">{invoice.client.name}</h3>
                    <p className="text-blue-100 text-sm">فاتورة #{invoice.id}</p>
                  </div>
                  <div className="text-left">
                    <p className="text-2xl font-bold">{formatCurrency(invoice.total)}</p>
                    <p className="text-blue-100 text-sm">{formatDate(invoice.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* محتوى البطاقة */}
              <div className="p-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">رقم الهاتف</p>
                    <p className="font-medium">{invoice.client.phone}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">السيلز</p>
                    <p className="font-medium">{invoice.client.salesperson}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">الخدمات</p>
                    <p className="font-medium text-sm">{getServiceNames(invoice.services)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">طريقة الدفع</p>
                    <p className="font-medium">{invoice.paymentMethod}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">حالة الدفع</p>
                    <div className={`inline-block px-2 py-1 rounded-full text-xs font-semibold ${
                      invoice.paymentStatus === 'مدفوع بالكامل' ? 'bg-green-100 text-green-800' :
                      invoice.paymentStatus === 'مدفوع جزئياً' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {invoice.paymentStatus || 'غير محدد'}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">المدفوع / المتبقي</p>
                    <p className="font-medium text-sm">
                      <span className="text-green-600">{formatCurrency(invoice.paidAmount || 0)}</span>
                      {' / '}
                      <span className={`${(invoice.remainingAmount || 0) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {formatCurrency(invoice.remainingAmount || 0)}
                      </span>
                    </p>
                  </div>

                  {invoice.requiresShipping && (
                    <div className="bg-yellow-50 p-2 rounded text-sm">
                      <span className="text-yellow-700 font-medium">🚚 يتطلب شحن</span>
                    </div>
                  )}
                </div>
              </div>

              {/* أزرار العمليات */}
              <div className="border-t border-gray-200 p-4">
                <div className="flex gap-2 mb-2">
                  <button
                    onClick={() => handleExportPDF(invoice)}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
                  >
                    📄 تصدير PDF
                  </button>

                  <button
                    onClick={() => handlePreviewInvoice(invoice)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors"
                  >
                    👁️ معاينة
                  </button>
                </div>

                {/* زر الحذف */}
                <button
                  onClick={() => handleDeleteInvoice(invoice)}
                  className="w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2"
                >
                  🗑️ حذف الفاتورة
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Preview Modal */}
      {previewInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center">
              <h2 className="text-xl font-bold">معاينة الفاتورة</h2>
              <button
                onClick={handleClosePreview}
                className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
              >
                ×
              </button>
            </div>
            <div className="p-4">
              <InvoicePreview
                invoice={previewInvoice}
                onEdit={handleEditFromPreview}
                onExportPDF={handleExportFromPreview}
              />

              {/* زر حذف في المعاينة */}
              <div className="border-t border-gray-200 pt-4 mt-4">
                <button
                  onClick={() => handleDeleteInvoice(previewInvoice)}
                  className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2"
                >
                  🗑️ حذف هذه الفاتورة
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoicesList;
