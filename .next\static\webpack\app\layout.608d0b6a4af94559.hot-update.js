/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\",\"latin\"],\"weight\":[\"300\",\"400\",\"600\",\"700\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\",\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNDQUQlMjBVU0VSJTIwWDY0JTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1QzIzMSU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQ0FEJTIwVVNFUiUyMFg2NCU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMyMzElNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkNhaXJvJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmFyYWJpYyUyMiUyQyUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyMzAwJTIyJTJDJTIyNDAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTVEJTJDJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtY2Fpcm8lMjIlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJjYWlybyUyMiU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFxSDtBQUNySCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzc4ZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxhcHBcXFxcZ2xvYmFscy5jc3NcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXENBRCBVU0VSIFg2NFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFwyMzFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZm9udFxcXFxnb29nbGVcXFxcdGFyZ2V0LmNzcz97XFxcInBhdGhcXFwiOlxcXCJzcmNcXFxcXFxcXGFwcFxcXFxcXFxcbGF5b3V0LnRzeFxcXCIsXFxcImltcG9ydFxcXCI6XFxcIkNhaXJvXFxcIixcXFwiYXJndW1lbnRzXFxcIjpbe1xcXCJzdWJzZXRzXFxcIjpbXFxcImFyYWJpY1xcXCIsXFxcImxhdGluXFxcIl0sXFxcIndlaWdodFxcXCI6W1xcXCIzMDBcXFwiLFxcXCI0MDBcXFwiLFxcXCI2MDBcXFwiLFxcXCI3MDBcXFwiXSxcXFwidmFyaWFibGVcXFwiOlxcXCItLWZvbnQtY2Fpcm9cXFwifV0sXFxcInZhcmlhYmxlTmFtZVxcXCI6XFxcImNhaXJvXFxcIn1cIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Cairo%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22arabic%22%2C%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22600%22%2C%22700%22%5D%2C%22variable%22%3A%22--font-cairo%22%7D%5D%2C%22variableName%22%3A%22cairo%22%7D&server=false!\n"));

/***/ })

});