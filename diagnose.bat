@echo off
chcp 65001 >nul 2>&1
title تشخيص مشاكل التطبيق - Diagnose Issues
cls

echo.
echo ==========================================
echo        تشخيص مشاكل التطبيق
echo        Application Diagnostics
echo ==========================================
echo.

echo جاري فحص النظام والتطبيق...
echo.

:: معلومات النظام
echo [معلومات النظام]
echo OS: %OS%
echo Architecture: %PROCESSOR_ARCHITECTURE%
echo User: %USERNAME%
echo Current Directory: %CD%
echo.

:: فحص Node.js
echo [فحص Node.js]
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js: غير مثبت
    echo    يرجى تثبيت Node.js من https://nodejs.org/
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo ✅ Node.js: %%i
)

:: فحص npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm: غير متوفر
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo ✅ npm: %%i
)

:: فحص Git (اختياري)
git --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Git: غير مثبت (اختياري)
) else (
    for /f "tokens=*" %%i in ('git --version 2^>nul') do echo ✅ Git: %%i
)

echo.

:: فحص ملفات المشروع
echo [فحص ملفات المشروع]
if exist "package.json" (
    echo ✅ package.json: موجود
) else (
    echo ❌ package.json: غير موجود
)

if exist "next.config.js" (
    echo ✅ next.config.js: موجود
) else (
    echo ⚠️ next.config.js: غير موجود
)

if exist "tsconfig.json" (
    echo ✅ tsconfig.json: موجود
) else (
    echo ⚠️ tsconfig.json: غير موجود
)

if exist "tailwind.config.js" (
    echo ✅ tailwind.config.js: موجود
) else (
    echo ⚠️ tailwind.config.js: غير موجود
)

if exist "src" (
    echo ✅ مجلد src: موجود
    if exist "src\app" (
        echo ✅ مجلد src\app: موجود
    ) else (
        echo ❌ مجلد src\app: غير موجود
    )
) else (
    echo ❌ مجلد src: غير موجود
)

if exist "node_modules" (
    echo ✅ node_modules: موجود
) else (
    echo ❌ node_modules: غير موجود - يحتاج npm install
)

if exist "package-lock.json" (
    echo ✅ package-lock.json: موجود
) else (
    echo ⚠️ package-lock.json: غير موجود
)

if exist ".next" (
    echo ✅ .next: موجود (ملفات البناء)
) else (
    echo ⚠️ .next: غير موجود (سيتم إنشاؤه عند التشغيل)
)

echo.

:: فحص المنافذ
echo [فحص المنافذ]
netstat -an | find ":3000" >nul 2>&1
if errorlevel 1 (
    echo ✅ المنفذ 3000: متاح
) else (
    echo ⚠️ المنفذ 3000: مستخدم
    echo    قد تحتاج لإيقاف العملية المستخدمة له
)

echo.

:: فحص التبعيات (إذا كانت مثبتة)
if exist "node_modules" (
    echo [فحص التبعيات]
    npm list --depth=0 >nul 2>&1
    if errorlevel 1 (
        echo ❌ التبعيات: توجد مشاكل
        echo    شغل fix-dependencies.bat لإصلاحها
    ) else (
        echo ✅ التبعيات: سليمة
    )
    echo.
)

:: فحص مساحة القرص
echo [فحص مساحة القرص]
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do (
    echo المساحة المتاحة: %%i bytes
)
echo.

:: اقتراحات الحلول
echo ==========================================
echo           اقتراحات الحلول
echo ==========================================
echo.

if not exist "node_modules" (
    echo 🔧 لتثبيت التبعيات:
    echo    شغل: fix-dependencies.bat
    echo.
)

netstat -an | find ":3000" >nul 2>&1
if not errorlevel 1 (
    echo 🔧 لحل مشكلة المنفذ 3000:
    echo    1. أغلق أي تطبيق يستخدم المنفذ 3000
    echo    2. أو شغل: netstat -ano ^| find "3000"
    echo    3. ثم: taskkill /PID [رقم العملية] /F
    echo.
)

npm list --depth=0 >nul 2>&1
if errorlevel 1 (
    if exist "node_modules" (
        echo 🔧 لإصلاح مشاكل التبعيات:
        echo    شغل: fix-dependencies.bat
        echo.
    )
)

echo 🔧 للتشغيل العادي:
echo    شغل: run-app.bat
echo.

echo 🔧 للتشغيل اليدوي:
echo    npm run dev
echo.

echo ==========================================
echo انتهى التشخيص
echo ==========================================
echo.

pause
