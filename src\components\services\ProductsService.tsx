'use client';

import React from 'react';
import { Field, ErrorMessage, FieldArray } from 'formik';
import { ProductsService, ProductItem } from '@/types';

interface ProductsServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
  isPreSelected?: boolean;
}

const ProductsServiceComponent: React.FC<ProductsServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
  isPreSelected = false,
}) => {
  const serviceData = values[namePrefix] as ProductsService;
  const isEnabled = serviceData?.enabled || false;

  // تفعيل الخدمة تلقائياً إذا كانت مختارة مسبقاً
  React.useEffect(() => {
    if (isPreSelected && !serviceData?.enabled) {
      setFieldValue(`${namePrefix}.enabled`, true);
      setFieldValue(`${namePrefix}.serviceType`, 'products');
    }
  }, [isPreSelected, namePrefix, setFieldValue, serviceData?.enabled]);

  const defaultProduct: ProductItem = {
    name: '',
    quantity: 1,
    unitPrice: 0,
    total: 0,
  };

  // حساب المجموع لكل منتج
  React.useEffect(() => {
    if (isEnabled && serviceData?.items) {
      serviceData.items.forEach((item, index) => {
        const quantity = item.quantity || 0;
        const unitPrice = item.unitPrice || 0;
        const total = quantity * unitPrice;
        setFieldValue(`${namePrefix}.items.${index}.total`, total);
      });
    }
  }, [serviceData?.items, isEnabled, namePrefix, setFieldValue]);

  // حساب المجموع الكلي
  const calculateGrandTotal = () => {
    if (!serviceData?.items) return 0;
    const subtotal = serviceData.items.reduce((total, item) => {
      return total + (item.total || 0);
    }, 0);

    // طرح الخصم
    const discountAmount = serviceData.discountAmount || 0;
    return Math.max(0, subtotal - discountAmount);
  };

  // تحديث المجموع الكلي
  React.useEffect(() => {
    if (isEnabled) {
      const total = calculateGrandTotal();
      setFieldValue(`${namePrefix}.grandTotal`, total);
    }
  }, [serviceData?.items, serviceData?.discountAmount, isEnabled]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'products');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              setFieldValue(`${namePrefix}.items`, [defaultProduct]);
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة المنتجات</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-pink-500">
          <FieldArray name={`${namePrefix}.items`}>
            {({ push, remove }) => (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-semibold">قائمة المنتجات</h3>
                  <button
                    type="button"
                    onClick={() => push(defaultProduct)}
                    className="btn-primary text-sm px-3 py-1"
                  >
                    إضافة منتج جديد
                  </button>
                </div>

                {serviceData?.items?.map((item, index) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4 relative">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">المنتج {index + 1}</h4>
                      {serviceData.items.length > 1 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          حذف
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {/* اسم المنتج */}
                      <div className="form-field">
                        <label className="form-label">اسم المنتج</label>
                        <Field
                          name={`${namePrefix}.items.${index}.name`}
                          type="text"
                          className="form-input"
                          placeholder="اسم المنتج"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.items.${index}.name`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* الكمية */}
                      <div className="form-field">
                        <label className="form-label">الكمية</label>
                        <Field
                          name={`${namePrefix}.items.${index}.quantity`}
                          type="number"
                          min="1"
                          className="form-input"
                          placeholder="الكمية"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.items.${index}.quantity`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* سعر الوحدة */}
                      <div className="form-field">
                        <label className="form-label">سعر الوحدة (جنيه)</label>
                        <Field
                          name={`${namePrefix}.items.${index}.unitPrice`}
                          type="number"
                          min="0"
                          step="0.01"
                          className="form-input"
                          placeholder="سعر الوحدة"
                        />
                        <ErrorMessage 
                          name={`${namePrefix}.items.${index}.unitPrice`} 
                          component="div" 
                          className="error-message" 
                        />
                      </div>

                      {/* المجموع */}
                      <div className="form-field">
                        <label className="form-label">المجموع (جنيه)</label>
                        <Field
                          name={`${namePrefix}.items.${index}.total`}
                          type="number"
                          className="form-input bg-green-50 font-semibold text-green-700"
                          readOnly
                          placeholder="المجموع"
                        />
                        <div className="text-xs text-gray-500 mt-1">
                          يتم الحساب تلقائياً
                        </div>
                      </div>
                    </div>

                    {/* تفاصيل إضافية */}
                    <div className="mt-4 space-y-3">
                      {/* وصف المنتج */}
                      <div className="form-field">
                        <label className="form-label">وصف المنتج</label>
                        <Field
                          name={`${namePrefix}.items.${index}.description`}
                          as="textarea"
                          rows="2"
                          className="form-input"
                          placeholder="وصف تفصيلي للمنتج"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* رمز المنتج */}
                        <div className="form-field">
                          <label className="form-label">رمز المنتج</label>
                          <Field
                            name={`${namePrefix}.items.${index}.productCode`}
                            type="text"
                            className="form-input"
                            placeholder="مثل: PRD-001"
                          />
                        </div>

                        {/* الفئة */}
                        <div className="form-field">
                          <label className="form-label">فئة المنتج</label>
                          <Field as="select" name={`${namePrefix}.items.${index}.category`} className="form-input">
                            <option value="">اختر الفئة</option>
                            <option value="clothing">ملابس</option>
                            <option value="accessories">إكسسوارات</option>
                            <option value="fabrics">أقمشة</option>
                            <option value="tools">أدوات</option>
                            <option value="patterns">باترونات</option>
                            <option value="other">أخرى</option>
                          </Field>
                        </div>

                        {/* الحالة */}
                        <div className="form-field">
                          <label className="form-label">حالة المنتج</label>
                          <Field as="select" name={`${namePrefix}.items.${index}.condition`} className="form-input">
                            <option value="">اختر الحالة</option>
                            <option value="new">جديد</option>
                            <option value="used">مستعمل</option>
                            <option value="refurbished">مجدد</option>
                            <option value="sample">عينة</option>
                          </Field>
                        </div>
                      </div>

                      {/* خيارات إضافية */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.items.${index}.warranty`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">ضمان</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.items.${index}.gift_wrap`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">تغليف هدايا</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.items.${index}.express_delivery`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">توصيل سريع</span>
                        </label>
                        
                        <label className="flex items-center">
                          <Field
                            type="checkbox"
                            name={`${namePrefix}.items.${index}.customization`}
                            className="form-checkbox"
                          />
                          <span className="text-sm">تخصيص</span>
                        </label>
                      </div>

                      {/* ملاحظات خاصة */}
                      <div className="form-field">
                        <label className="form-label">ملاحظات خاصة</label>
                        <Field
                          name={`${namePrefix}.items.${index}.notes`}
                          as="textarea"
                          rows="2"
                          className="form-input"
                          placeholder="أي ملاحظات خاصة بهذا المنتج"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </FieldArray>

          {/* المجموع الكلي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-md font-medium text-green-700">المجموع الفرعي:</span>
                <span className="text-lg font-semibold text-green-800">
                  {serviceData?.items?.reduce((total, item) => total + (item.total || 0), 0).toFixed(2)} جنيه
                </span>
              </div>
              {serviceData?.discountAmount && serviceData.discountAmount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-md font-medium text-red-700">الخصم:</span>
                  <span className="text-lg font-semibold text-red-800">
                    -{serviceData.discountAmount.toFixed(2)} جنيه
                  </span>
                </div>
              )}
              <hr className="border-green-300" />
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-green-700">المجموع الكلي:</span>
                <span className="text-2xl font-bold text-green-800">{calculateGrandTotal().toFixed(2)} جنيه</span>
              </div>
            </div>
          </div>

          {/* معلومات عامة */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات عامة</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* طريقة الدفع */}
              <div className="form-field">
                <label className="form-label">طريقة الدفع المفضلة</label>
                <Field as="select" name={`${namePrefix}.preferredPayment`} className="form-input">
                  <option value="">اختر طريقة الدفع</option>
                  <option value="cash">نقدي</option>
                  <option value="card">بطاقة</option>
                  <option value="transfer">تحويل</option>
                  <option value="installment">تقسيط</option>
                </Field>
              </div>

              {/* تاريخ التسليم */}
              <div className="form-field">
                <label className="form-label">تاريخ التسليم المطلوب</label>
                <Field
                  name={`${namePrefix}.deliveryDate`}
                  type="date"
                  className="form-input"
                />
              </div>
            </div>

            {/* خصم */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-field">
                <label className="form-label">نسبة الخصم (%)</label>
                <Field
                  name={`${namePrefix}.discountPercentage`}
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  className="form-input"
                  placeholder="نسبة الخصم"
                />
              </div>

              <div className="form-field">
                <label className="form-label">مبلغ الخصم (جنيه)</label>
                <Field
                  name={`${namePrefix}.discountAmount`}
                  type="number"
                  min="0"
                  step="0.01"
                  className="form-input"
                  placeholder="مبلغ الخصم الثابت"
                />
              </div>
            </div>

            {/* ملاحظات عامة */}
            <div className="form-field">
              <label className="form-label">ملاحظات عامة</label>
              <Field
                name={`${namePrefix}.generalNotes`}
                as="textarea"
                rows="3"
                className="form-input"
                placeholder="أي ملاحظات عامة حول الطلب"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductsServiceComponent;
