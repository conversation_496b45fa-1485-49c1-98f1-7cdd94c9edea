import type { Metadata } from 'next';
import { Cairo } from 'next/font/google';
import './globals.css';

const cairo = Cairo({ 
  subsets: ['arabic', 'latin'],
  weight: ['300', '400', '600', '700'],
  variable: '--font-cairo',
});

export const metadata: Metadata = {
  title: 'نظام إدارة الفواتير - Invoice Management System',
  description: 'نظام شامل لإدارة فواتير الخدمات مع دعم كامل للغة العربية',
  keywords: 'فواتير, إدارة, خدمات, استشارات, باترون, تصنيع',
  authors: [{ name: 'Invoice Management System' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl" className={cairo.variable}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${cairo.className} font-arabic`}>
        {children}
      </body>
    </html>
  );
}
