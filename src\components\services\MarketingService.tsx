'use client';

import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { MarketingService } from '@/types';

interface MarketingServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const MarketingServiceComponent: React.FC<MarketingServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as MarketingService;
  const isEnabled = serviceData?.enabled || false;

  const marketingTypes = [
    { value: 'social_media', label: 'وسائل التواصل الاجتماعي' },
    { value: 'google_ads', label: 'إعلانات جوجل' },
    { value: 'facebook_ads', label: 'إعلانات فيسبوك' },
    { value: 'instagram_ads', label: 'إعلانات انستغرام' },
    { value: 'snapchat_ads', label: 'إعلانات سناب شات' },
    { value: 'tiktok_ads', label: 'إعلانات تيك توك' },
    { value: 'influencer', label: 'تسويق المؤثرين' },
    { value: 'content_creation', label: 'إنشاء محتوى' },
    { value: 'seo', label: 'تحسين محركات البحث' },
    { value: 'email_marketing', label: 'التسويق عبر البريد الإلكتروني' },
    { value: 'print_media', label: 'الإعلانات المطبوعة' },
    { value: 'radio_tv', label: 'إعلانات الراديو والتلفزيون' },
  ];

  const campaignDurations = [
    { value: '1_week', label: 'أسبوع واحد' },
    { value: '2_weeks', label: 'أسبوعين' },
    { value: '1_month', label: 'شهر واحد' },
    { value: '3_months', label: '3 أشهر' },
    { value: '6_months', label: '6 أشهر' },
    { value: '1_year', label: 'سنة واحدة' },
    { value: 'ongoing', label: 'مستمر' },
    { value: 'custom', label: 'مدة مخصصة' },
  ];

  const targetAudiences = [
    { value: 'women_18_25', label: 'نساء 18-25 سنة' },
    { value: 'women_26_35', label: 'نساء 26-35 سنة' },
    { value: 'women_36_45', label: 'نساء 36-45 سنة' },
    { value: 'men_18_25', label: 'رجال 18-25 سنة' },
    { value: 'men_26_35', label: 'رجال 26-35 سنة' },
    { value: 'men_36_45', label: 'رجال 36-45 سنة' },
    { value: 'families', label: 'العائلات' },
    { value: 'professionals', label: 'المهنيين' },
    { value: 'students', label: 'الطلاب' },
    { value: 'entrepreneurs', label: 'رجال الأعمال' },
  ];

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'marketing');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة التسويق والإعلان</label>
      </div>

      {isEnabled && (
        <div className="space-y-6 pr-6 border-r-4 border-yellow-500">
          {/* نوع الحملة التسويقية */}
          <div className="form-field">
            <label className="form-label">نوع الحملة التسويقية</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {marketingTypes.map((type) => (
                <label key={type.value} className="flex items-center">
                  <Field
                    type="checkbox"
                    name={`${namePrefix}.marketingTypes`}
                    value={type.value}
                    className="form-checkbox"
                  />
                  <span className="text-sm">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* وصف الحملة */}
          <div className="form-field">
            <label className="form-label">وصف الحملة التسويقية</label>
            <Field
              name={`${namePrefix}.description`}
              as="textarea"
              rows="4"
              className="form-input"
              placeholder="اكتب وصفاً تفصيلياً للحملة التسويقية والأهداف المطلوبة"
            />
            <ErrorMessage name={`${namePrefix}.description`} component="div" className="error-message" />
          </div>

          {/* الجمهور المستهدف */}
          <div className="form-field">
            <label className="form-label">الجمهور المستهدف</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {targetAudiences.map((audience) => (
                <label key={audience.value} className="flex items-center">
                  <Field
                    type="checkbox"
                    name={`${namePrefix}.targetAudience`}
                    value={audience.value}
                    className="form-checkbox"
                  />
                  <span className="text-sm">{audience.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* تفاصيل الحملة */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">تفاصيل الحملة</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* مدة الحملة */}
              <div className="form-field">
                <label className="form-label">مدة الحملة</label>
                <Field as="select" name={`${namePrefix}.duration`} className="form-input">
                  <option value="">اختر مدة الحملة</option>
                  {campaignDurations.map((duration) => (
                    <option key={duration.value} value={duration.value}>
                      {duration.label}
                    </option>
                  ))}
                </Field>
                <ErrorMessage name={`${namePrefix}.duration`} component="div" className="error-message" />
              </div>

              {/* الميزانية */}
              <div className="form-field">
                <label className="form-label">الميزانية الإجمالية (جنيه)</label>
                <Field
                  name={`${namePrefix}.cost`}
                  type="number"
                  min="0"
                  step="0.01"
                  className="form-input"
                  placeholder="الميزانية المخصصة للحملة"
                />
                <ErrorMessage name={`${namePrefix}.cost`} component="div" className="error-message" />
              </div>

              {/* تاريخ البداية */}
              <div className="form-field">
                <label className="form-label">تاريخ بداية الحملة</label>
                <Field
                  name={`${namePrefix}.startDate`}
                  type="date"
                  className="form-input"
                />
              </div>

              {/* تاريخ النهاية */}
              <div className="form-field">
                <label className="form-label">تاريخ نهاية الحملة</label>
                <Field
                  name={`${namePrefix}.endDate`}
                  type="date"
                  className="form-input"
                />
              </div>
            </div>
          </div>

          {/* أهداف الحملة */}
          <div className="form-field">
            <label className="form-label">أهداف الحملة</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.brand_awareness`}
                  className="form-checkbox"
                />
                <span className="text-sm">زيادة الوعي بالعلامة التجارية</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.lead_generation`}
                  className="form-checkbox"
                />
                <span className="text-sm">جذب عملاء محتملين</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.sales_increase`}
                  className="form-checkbox"
                />
                <span className="text-sm">زيادة المبيعات</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.website_traffic`}
                  className="form-checkbox"
                />
                <span className="text-sm">زيادة زيارات الموقع</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.social_engagement`}
                  className="form-checkbox"
                />
                <span className="text-sm">زيادة التفاعل الاجتماعي</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.goals.customer_retention`}
                  className="form-checkbox"
                />
                <span className="text-sm">الاحتفاظ بالعملاء</span>
              </label>
            </div>
          </div>

          {/* المحتوى المطلوب */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">المحتوى المطلوب</h4>
            
            <div className="space-y-3">
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.content.graphics_design`}
                  className="form-checkbox"
                />
                <span className="text-sm">تصميم جرافيك</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.content.video_production`}
                  className="form-checkbox"
                />
                <span className="text-sm">إنتاج فيديو</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.content.copywriting`}
                  className="form-checkbox"
                />
                <span className="text-sm">كتابة إعلانية</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.content.photography`}
                  className="form-checkbox"
                />
                <span className="text-sm">تصوير فوتوغرافي</span>
              </label>
              
              <label className="flex items-center">
                <Field
                  type="checkbox"
                  name={`${namePrefix}.content.animation`}
                  className="form-checkbox"
                />
                <span className="text-sm">رسوم متحركة</span>
              </label>
            </div>
          </div>

          {/* مؤشرات الأداء */}
          <div className="form-field">
            <label className="form-label">مؤشرات الأداء المطلوب قياسها</label>
            <Field
              name={`${namePrefix}.kpis`}
              as="textarea"
              rows="3"
              className="form-input"
              placeholder="مثل: عدد المشاهدات، معدل التفاعل، عدد العملاء الجدد، معدل التحويل، إلخ"
            />
          </div>

          {/* ملاحظات خاصة */}
          <div className="form-field">
            <label className="form-label">ملاحظات خاصة</label>
            <Field
              name={`${namePrefix}.specialNotes`}
              as="textarea"
              rows="3"
              className="form-input"
              placeholder="أي ملاحظات أو متطلبات خاصة بالحملة التسويقية"
            />
          </div>

          {/* معلومات الاتصال */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">معلومات الاتصال</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-field">
                <label className="form-label">مدير التسويق</label>
                <Field
                  name={`${namePrefix}.marketingManager`}
                  type="text"
                  className="form-input"
                  placeholder="اسم مدير التسويق المسؤول"
                />
              </div>

              <div className="form-field">
                <label className="form-label">رقم الاتصال</label>
                <Field
                  name={`${namePrefix}.contactNumber`}
                  type="tel"
                  className="form-input"
                  placeholder="رقم الهاتف للتواصل"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketingServiceComponent;
