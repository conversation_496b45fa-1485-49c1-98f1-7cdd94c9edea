"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/store/invoiceStore.ts":
/*!***********************************!*\
  !*** ./src/store/invoiceStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInvoiceStore: function() { return /* binding */ useInvoiceStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* harmony import */ var _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/unifiedStorage */ \"(app-pages-browser)/./src/utils/unifiedStorage.ts\");\n/* harmony import */ var _utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storageKeys */ \"(app-pages-browser)/./src/utils/storageKeys.ts\");\n\n\n\n\n// تحميل البيانات من النظام الموحد\nconst loadInvoicesFromStorage = async ()=>{\n    if (true) {\n        try {\n            const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n            if (!result.success) {\n                console.error(\"❌ Error loading invoices from unified storage:\", result.message);\n                return [];\n            }\n            console.log(\"✅ Loaded invoices from unified storage:\", result.data.length);\n            return result.data;\n        } catch (error) {\n            console.error(\"❌ Critical error loading invoices:\", error);\n            return [];\n        }\n    }\n    return [];\n};\nconst useInvoiceStore = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)((set, get)=>({\n        currentClient: null,\n        currentInvoice: null,\n        invoices: loadInvoicesFromStorage(),\n        setCurrentClient: (client)=>{\n            set({\n                currentClient: client\n            });\n        },\n        clearCurrentClient: ()=>{\n            set({\n                currentClient: null\n            });\n        },\n        setCurrentInvoice: (invoice)=>{\n            set({\n                currentInvoice: invoice\n            });\n        },\n        addService: (service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = [\n                    ...currentInvoice.services,\n                    service\n                ];\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        removeService: (serviceId)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.filter((s)=>s.id !== serviceId);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        updateService: (serviceId, service)=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                const updatedServices = currentInvoice.services.map((s)=>s.id === serviceId ? service : s);\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    services: updatedServices\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n                get().calculateTotal();\n            }\n        },\n        calculateTotal: ()=>{\n            const { currentInvoice } = get();\n            if (currentInvoice) {\n                let total = 0;\n                currentInvoice.services.forEach((service)=>{\n                    switch(service.serviceType){\n                        case \"consultation\":\n                            total += service.cost || 0;\n                            break;\n                        case \"pattern\":\n                            var _service_models;\n                            (_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.forEach((model)=>{\n                                total += model.finalAmount || model.cost || 0;\n                            });\n                            break;\n                        case \"pattern_printing\":\n                            var _service_files;\n                            (_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.forEach((file)=>{\n                                total += file.cost || 0;\n                            });\n                            break;\n                        case \"manufacturing\":\n                            var _service_models1;\n                            (_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.forEach((model)=>{\n                                total += (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                            });\n                            break;\n                        case \"shipping\":\n                            total += service.total || 0;\n                            break;\n                        case \"marketing\":\n                            total += service.cost || 0;\n                            break;\n                        case \"photography\":\n                            total += service.cost || 0;\n                            break;\n                        case \"products\":\n                            var _service_items;\n                            let productsSubtotal = 0;\n                            (_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.forEach((item)=>{\n                                productsSubtotal += item.total || 0;\n                            });\n                            // طرح الخصم إن وجد\n                            if (service.discountAmount) {\n                                productsSubtotal -= service.discountAmount || 0;\n                            }\n                            total += Math.max(0, productsSubtotal);\n                            break;\n                        case \"raw_purchases\":\n                            var _service_materials;\n                            (_service_materials = service.materials) === null || _service_materials === void 0 ? void 0 : _service_materials.forEach((material)=>{\n                                total += material.total || 0;\n                            });\n                            break;\n                        case \"samples\":\n                            var _service_samples;\n                            (_service_samples = service.samples) === null || _service_samples === void 0 ? void 0 : _service_samples.forEach((sample)=>{\n                                total += sample.cost || 0;\n                            });\n                            break;\n                    }\n                });\n                const updatedInvoice = {\n                    ...currentInvoice,\n                    total\n                };\n                set({\n                    currentInvoice: updatedInvoice\n                });\n            }\n        },\n        saveInvoice: async ()=>{\n            const { currentInvoice, invoices } = get();\n            if (currentInvoice) {\n                console.log(\"\\uD83D\\uDD04 Attempting to save invoice:\", currentInvoice.id);\n                const invoiceToSave = {\n                    ...currentInvoice,\n                    id: currentInvoice.id || \"INV-\".concat(Date.now()),\n                    createdAt: currentInvoice.createdAt || new Date().toISOString(),\n                    // التأكد من وجود الحقول المطلوبة\n                    paidAmount: currentInvoice.paidAmount || 0,\n                    remainingAmount: currentInvoice.remainingAmount || currentInvoice.total,\n                    paymentStatus: currentInvoice.paymentStatus || \"غير مدفوع\",\n                    paymentMethod: currentInvoice.paymentMethod || \"\",\n                    requiresShipping: currentInvoice.requiresShipping || false,\n                    shippingAddress: currentInvoice.shippingAddress || \"\",\n                    notes: currentInvoice.notes || \"\"\n                };\n                // قراءة البيانات الحالية من النظام الموحد للتأكد من التزامن\n                let currentStoredInvoices = [];\n                try {\n                    const result = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.getItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, []);\n                    currentStoredInvoices = result.success ? result.data : invoices;\n                    if (!result.success) {\n                        console.error(\"❌ Error reading current invoices from unified storage:\", result.message);\n                    }\n                } catch (error) {\n                    console.error(\"❌ Error reading current invoices:\", error);\n                    currentStoredInvoices = invoices;\n                }\n                // التحقق من عدم وجود الفاتورة مسبقاً في localStorage\n                const existingIndex = currentStoredInvoices.findIndex((inv)=>inv.id === invoiceToSave.id);\n                let updatedInvoices;\n                if (existingIndex >= 0) {\n                    // تحديث فاتورة موجودة\n                    updatedInvoices = [\n                        ...currentStoredInvoices\n                    ];\n                    updatedInvoices[existingIndex] = invoiceToSave;\n                    console.log(\"\\uD83D\\uDCDD Updated existing invoice:\", invoiceToSave.id);\n                } else {\n                    // إضافة فاتورة جديدة\n                    updatedInvoices = [\n                        ...currentStoredInvoices,\n                        invoiceToSave\n                    ];\n                    console.log(\"➕ Added new invoice:\", invoiceToSave.id);\n                }\n                // إزالة أي تكرارات محتملة\n                const uniqueInvoices = updatedInvoices.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n                if (uniqueInvoices.length !== updatedInvoices.length) {\n                    console.warn(\"⚠️ Removed duplicate invoices during save\");\n                }\n                // تحديث المتجر\n                set({\n                    invoices: uniqueInvoices,\n                    currentInvoice: invoiceToSave\n                });\n                // حفظ في النظام الموحد\n                try {\n                    const saveResult = await _utils_unifiedStorage__WEBPACK_IMPORTED_MODULE_1__.UnifiedStorageManager.setItem(_utils_storageKeys__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.INVOICES, uniqueInvoices);\n                    if (saveResult.success) {\n                        console.log(\"✅ Invoice saved via unified storage. Total invoices:\", uniqueInvoices.length);\n                    } else {\n                        console.error(\"❌ Error saving invoice via unified storage:\", saveResult.message);\n                        // في حالة فشل الحفظ، عرض تنبيه للمستخدم\n                        if (true) {\n                            alert(\"تحذير: فشل في حفظ الفاتورة. \" + saveResult.message);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"❌ Critical error saving invoice:\", error);\n                    if (true) {\n                        alert(\"خطأ حرج: فشل في حفظ الفاتورة. يرجى المحاولة مرة أخرى.\");\n                    }\n                }\n            } else {\n                console.warn(\"⚠️ No current invoice to save\");\n            }\n        },\n        deleteInvoice: (invoiceId)=>{\n            const { invoices } = get();\n            console.log(\"\\uD83D\\uDDD1️ Attempting to delete invoice:\", invoiceId);\n            // البحث عن الفاتورة\n            const invoiceToDelete = invoices.find((inv)=>inv.id === invoiceId);\n            if (!invoiceToDelete) {\n                console.warn(\"⚠️ Invoice not found for deletion:\", invoiceId);\n                return;\n            }\n            // إنشاء قائمة جديدة بدون الفاتورة المحذوفة\n            const updatedInvoices = invoices.filter((inv)=>inv.id !== invoiceId);\n            console.log(\"\\uD83D\\uDCDD Removing invoice from list. Remaining invoices:\", updatedInvoices.length);\n            // تحديث المتجر\n            set({\n                invoices: updatedInvoices\n            });\n            // حفظ في التخزين الآمن\n            const saveResult = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_0__.secureSetItem)(\"invoices\", updatedInvoices);\n            if (saveResult.success) {\n                console.log(\"✅ Invoice deleted successfully\");\n                console.log(\"\\uD83D\\uDCCA Total invoices after deletion:\", updatedInvoices.length);\n            } else {\n                console.error(\"❌ Error deleting invoice:\", saveResult.message);\n                // في حالة فشل الحذف، عرض تنبيه للمستخدم\n                if (true) {\n                    alert(\"تحذير: فشل في حذف الفاتورة. \" + saveResult.message);\n                }\n            }\n        },\n        clearCurrentInvoice: ()=>{\n            set({\n                currentInvoice: null\n            });\n        },\n        getServicesByType: (type)=>{\n            const { currentInvoice } = get();\n            return (currentInvoice === null || currentInvoice === void 0 ? void 0 : currentInvoice.services.filter((s)=>s.serviceType === type)) || [];\n        }\n    }));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9pbnZvaWNlU3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFFb0M7QUFDTjtBQUNaO0FBeUJuRCxrQ0FBa0M7QUFDbEMsTUFBTUksMEJBQTBCO0lBQzlCLElBQUksSUFBa0IsRUFBYTtRQUNqQyxJQUFJO1lBQ0YsTUFBTUMsU0FBUyxNQUFNSCx3RUFBcUJBLENBQUNJLE9BQU8sQ0FBQ0gsNERBQVlBLENBQUNJLFFBQVEsRUFBRSxFQUFFO1lBQzVFLElBQUksQ0FBQ0YsT0FBT0csT0FBTyxFQUFFO2dCQUNuQkMsUUFBUUMsS0FBSyxDQUFDLGtEQUFrREwsT0FBT00sT0FBTztnQkFDOUUsT0FBTyxFQUFFO1lBQ1g7WUFDQUYsUUFBUUcsR0FBRyxDQUFDLDJDQUEyQ1AsT0FBT1EsSUFBSSxDQUFDQyxNQUFNO1lBQ3pFLE9BQU9ULE9BQU9RLElBQUk7UUFDcEIsRUFBRSxPQUFPSCxPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQyxzQ0FBc0NBO1lBQ3BELE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFDQSxPQUFPLEVBQUU7QUFDWDtBQUVPLE1BQU1LLGtCQUFrQmYsK0NBQU1BLENBQWUsQ0FBQ2dCLEtBQUtDLE1BQVM7UUFDakVDLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxVQUFVaEI7UUFFVmlCLGtCQUFrQixDQUFDQztZQUNqQk4sSUFBSTtnQkFBRUUsZUFBZUk7WUFBTztRQUM5QjtRQUVBQyxvQkFBb0I7WUFDbEJQLElBQUk7Z0JBQUVFLGVBQWU7WUFBSztRQUM1QjtRQUVBTSxtQkFBbUIsQ0FBQ0M7WUFDbEJULElBQUk7Z0JBQUVHLGdCQUFnQk07WUFBUTtRQUNoQztRQUVBQyxZQUFZLENBQUNDO1lBQ1gsTUFBTSxFQUFFUixjQUFjLEVBQUUsR0FBR0Y7WUFDM0IsSUFBSUUsZ0JBQWdCO2dCQUNsQixNQUFNUyxrQkFBa0I7dUJBQUlULGVBQWVVLFFBQVE7b0JBQUVGO2lCQUFRO2dCQUM3RCxNQUFNRyxpQkFBaUI7b0JBQUUsR0FBR1gsY0FBYztvQkFBRVUsVUFBVUQ7Z0JBQWdCO2dCQUN0RVosSUFBSTtvQkFBRUcsZ0JBQWdCVztnQkFBZTtnQkFDckNiLE1BQU1jLGNBQWM7WUFDdEI7UUFDRjtRQUVBQyxlQUFlLENBQUNDO1lBQ2QsTUFBTSxFQUFFZCxjQUFjLEVBQUUsR0FBR0Y7WUFDM0IsSUFBSUUsZ0JBQWdCO2dCQUNsQixNQUFNUyxrQkFBa0JULGVBQWVVLFFBQVEsQ0FBQ0ssTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtIO2dCQUNyRSxNQUFNSCxpQkFBaUI7b0JBQUUsR0FBR1gsY0FBYztvQkFBRVUsVUFBVUQ7Z0JBQWdCO2dCQUN0RVosSUFBSTtvQkFBRUcsZ0JBQWdCVztnQkFBZTtnQkFDckNiLE1BQU1jLGNBQWM7WUFDdEI7UUFDRjtRQUVBTSxlQUFlLENBQUNKLFdBQW1CTjtZQUNqQyxNQUFNLEVBQUVSLGNBQWMsRUFBRSxHQUFHRjtZQUMzQixJQUFJRSxnQkFBZ0I7Z0JBQ2xCLE1BQU1TLGtCQUFrQlQsZUFBZVUsUUFBUSxDQUFDUyxHQUFHLENBQUNILENBQUFBLElBQ2xEQSxFQUFFQyxFQUFFLEtBQUtILFlBQVlOLFVBQVVRO2dCQUVqQyxNQUFNTCxpQkFBaUI7b0JBQUUsR0FBR1gsY0FBYztvQkFBRVUsVUFBVUQ7Z0JBQWdCO2dCQUN0RVosSUFBSTtvQkFBRUcsZ0JBQWdCVztnQkFBZTtnQkFDckNiLE1BQU1jLGNBQWM7WUFDdEI7UUFDRjtRQUVBQSxnQkFBZ0I7WUFDZCxNQUFNLEVBQUVaLGNBQWMsRUFBRSxHQUFHRjtZQUMzQixJQUFJRSxnQkFBZ0I7Z0JBQ2xCLElBQUlvQixRQUFRO2dCQUVacEIsZUFBZVUsUUFBUSxDQUFDVyxPQUFPLENBQUNiLENBQUFBO29CQUM5QixPQUFRQSxRQUFRYyxXQUFXO3dCQUN6QixLQUFLOzRCQUNIRixTQUFTLFFBQWlCRyxJQUFJLElBQUk7NEJBQ2xDO3dCQUNGLEtBQUs7Z0NBQ0g7NkJBQUEsMEJBQWlCQyxNQUFNLGNBQXZCLHNEQUF5QkgsT0FBTyxDQUFDLENBQUNJO2dDQUNoQ0wsU0FBU0ssTUFBTUMsV0FBVyxJQUFJRCxNQUFNRixJQUFJLElBQUk7NEJBQzlDOzRCQUNBO3dCQUNGLEtBQUs7Z0NBQ0g7NkJBQUEseUJBQWlCSSxLQUFLLGNBQXRCLG9EQUF3Qk4sT0FBTyxDQUFDLENBQUNPO2dDQUMvQlIsU0FBU1EsS0FBS0wsSUFBSSxJQUFJOzRCQUN4Qjs0QkFDQTt3QkFDRixLQUFLO2dDQUNIOzZCQUFBLDJCQUFpQkMsTUFBTSxjQUF2Qix3REFBeUJILE9BQU8sQ0FBQyxDQUFDSTtnQ0FDaENMLFNBQVMsQ0FBQ0ssTUFBTUksWUFBWSxJQUFJLEtBQU1KLENBQUFBLE1BQU1LLFdBQVcsSUFBSSxLQUFNTCxDQUFBQSxNQUFNTSxrQkFBa0IsSUFBSTs0QkFDL0Y7NEJBQ0E7d0JBQ0YsS0FBSzs0QkFDSFgsU0FBUyxRQUFpQkEsS0FBSyxJQUFJOzRCQUNuQzt3QkFDRixLQUFLOzRCQUNIQSxTQUFTLFFBQWlCRyxJQUFJLElBQUk7NEJBQ2xDO3dCQUNGLEtBQUs7NEJBQ0hILFNBQVMsUUFBaUJHLElBQUksSUFBSTs0QkFDbEM7d0JBQ0YsS0FBSztnQ0FFSDs0QkFEQSxJQUFJUyxtQkFBbUI7NkJBQ3ZCLHlCQUFpQkMsS0FBSyxjQUF0QixvREFBd0JaLE9BQU8sQ0FBQyxDQUFDYTtnQ0FDL0JGLG9CQUFvQkUsS0FBS2QsS0FBSyxJQUFJOzRCQUNwQzs0QkFDQSxtQkFBbUI7NEJBQ25CLElBQUksUUFBaUJlLGNBQWMsRUFBRTtnQ0FDbkNILG9CQUFvQixRQUFpQkcsY0FBYyxJQUFJOzRCQUN6RDs0QkFDQWYsU0FBU2dCLEtBQUtDLEdBQUcsQ0FBQyxHQUFHTDs0QkFDckI7d0JBQ0YsS0FBSztnQ0FDSDs2QkFBQSw2QkFBaUJNLFNBQVMsY0FBMUIsNERBQTRCakIsT0FBTyxDQUFDLENBQUNrQjtnQ0FDbkNuQixTQUFTbUIsU0FBU25CLEtBQUssSUFBSTs0QkFDN0I7NEJBQ0E7d0JBQ0YsS0FBSztnQ0FDSDs2QkFBQSwyQkFBaUJvQixPQUFPLGNBQXhCLHdEQUEwQm5CLE9BQU8sQ0FBQyxDQUFDb0I7Z0NBQ2pDckIsU0FBU3FCLE9BQU9sQixJQUFJLElBQUk7NEJBQzFCOzRCQUNBO29CQUNKO2dCQUNGO2dCQUVBLE1BQU1aLGlCQUFpQjtvQkFBRSxHQUFHWCxjQUFjO29CQUFFb0I7Z0JBQU07Z0JBQ2xEdkIsSUFBSTtvQkFBRUcsZ0JBQWdCVztnQkFBZTtZQUN2QztRQUNGO1FBRUErQixhQUFhO1lBQ1gsTUFBTSxFQUFFMUMsY0FBYyxFQUFFQyxRQUFRLEVBQUUsR0FBR0g7WUFDckMsSUFBSUUsZ0JBQWdCO2dCQUNsQlYsUUFBUUcsR0FBRyxDQUFDLDRDQUFrQ08sZUFBZWlCLEVBQUU7Z0JBRS9ELE1BQU0wQixnQkFBZ0I7b0JBQ3BCLEdBQUczQyxjQUFjO29CQUNqQmlCLElBQUlqQixlQUFlaUIsRUFBRSxJQUFJLE9BQWtCLE9BQVgyQixLQUFLQyxHQUFHO29CQUN4Q0MsV0FBVzlDLGVBQWU4QyxTQUFTLElBQUksSUFBSUYsT0FBT0csV0FBVztvQkFDN0QsaUNBQWlDO29CQUNqQ0MsWUFBWWhELGVBQWVnRCxVQUFVLElBQUk7b0JBQ3pDQyxpQkFBaUJqRCxlQUFlaUQsZUFBZSxJQUFJakQsZUFBZW9CLEtBQUs7b0JBQ3ZFOEIsZUFBZWxELGVBQWVrRCxhQUFhLElBQUk7b0JBQy9DQyxlQUFlbkQsZUFBZW1ELGFBQWEsSUFBSTtvQkFDL0NDLGtCQUFrQnBELGVBQWVvRCxnQkFBZ0IsSUFBSTtvQkFDckRDLGlCQUFpQnJELGVBQWVxRCxlQUFlLElBQUk7b0JBQ25EQyxPQUFPdEQsZUFBZXNELEtBQUssSUFBSTtnQkFDakM7Z0JBRUEsNERBQTREO2dCQUM1RCxJQUFJQyx3QkFBbUMsRUFBRTtnQkFDekMsSUFBSTtvQkFDRixNQUFNckUsU0FBUyxNQUFNSCx3RUFBcUJBLENBQUNJLE9BQU8sQ0FBQ0gsNERBQVlBLENBQUNJLFFBQVEsRUFBRSxFQUFFO29CQUM1RW1FLHdCQUF3QnJFLE9BQU9HLE9BQU8sR0FBR0gsT0FBT1EsSUFBSSxHQUFHTztvQkFFdkQsSUFBSSxDQUFDZixPQUFPRyxPQUFPLEVBQUU7d0JBQ25CQyxRQUFRQyxLQUFLLENBQUMsMERBQTBETCxPQUFPTSxPQUFPO29CQUN4RjtnQkFDRixFQUFFLE9BQU9ELE9BQU87b0JBQ2RELFFBQVFDLEtBQUssQ0FBQyxxQ0FBcUNBO29CQUNuRGdFLHdCQUF3QnREO2dCQUMxQjtnQkFFQSxxREFBcUQ7Z0JBQ3JELE1BQU11RCxnQkFBZ0JELHNCQUFzQkUsU0FBUyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJekMsRUFBRSxLQUFLMEIsY0FBYzFCLEVBQUU7Z0JBQ3hGLElBQUkwQztnQkFFSixJQUFJSCxpQkFBaUIsR0FBRztvQkFDdEIsc0JBQXNCO29CQUN0Qkcsa0JBQWtCOzJCQUFJSjtxQkFBc0I7b0JBQzVDSSxlQUFlLENBQUNILGNBQWMsR0FBR2I7b0JBQ2pDckQsUUFBUUcsR0FBRyxDQUFDLDBDQUFnQ2tELGNBQWMxQixFQUFFO2dCQUM5RCxPQUFPO29CQUNMLHFCQUFxQjtvQkFDckIwQyxrQkFBa0I7MkJBQUlKO3dCQUF1Qlo7cUJBQWM7b0JBQzNEckQsUUFBUUcsR0FBRyxDQUFDLHdCQUF3QmtELGNBQWMxQixFQUFFO2dCQUN0RDtnQkFFQSwwQkFBMEI7Z0JBQzFCLE1BQU0yQyxpQkFBaUJELGdCQUFnQjVDLE1BQU0sQ0FBQyxDQUFDVCxTQUFTdUQsT0FBT0MsT0FDN0RELFVBQVVDLEtBQUtMLFNBQVMsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSXpDLEVBQUUsS0FBS1gsUUFBUVcsRUFBRTtnQkFHdkQsSUFBSTJDLGVBQWVqRSxNQUFNLEtBQUtnRSxnQkFBZ0JoRSxNQUFNLEVBQUU7b0JBQ3BETCxRQUFReUUsSUFBSSxDQUFDO2dCQUNmO2dCQUVBLGVBQWU7Z0JBQ2ZsRSxJQUFJO29CQUFFSSxVQUFVMkQ7b0JBQWdCNUQsZ0JBQWdCMkM7Z0JBQWM7Z0JBRTlELHVCQUF1QjtnQkFDdkIsSUFBSTtvQkFDRixNQUFNcUIsYUFBYSxNQUFNakYsd0VBQXFCQSxDQUFDa0YsT0FBTyxDQUFDakYsNERBQVlBLENBQUNJLFFBQVEsRUFBRXdFO29CQUM5RSxJQUFJSSxXQUFXM0UsT0FBTyxFQUFFO3dCQUN0QkMsUUFBUUcsR0FBRyxDQUFDLHdEQUF3RG1FLGVBQWVqRSxNQUFNO29CQUMzRixPQUFPO3dCQUNMTCxRQUFRQyxLQUFLLENBQUMsK0NBQStDeUUsV0FBV3hFLE9BQU87d0JBQy9FLHdDQUF3Qzt3QkFDeEMsSUFBSSxJQUFrQixFQUFhOzRCQUNqQzBFLE1BQU0saUNBQWlDRixXQUFXeEUsT0FBTzt3QkFDM0Q7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPRCxPQUFPO29CQUNkRCxRQUFRQyxLQUFLLENBQUMsb0NBQW9DQTtvQkFDbEQsSUFBSSxJQUFrQixFQUFhO3dCQUNqQzJFLE1BQU07b0JBQ1I7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMNUUsUUFBUXlFLElBQUksQ0FBQztZQUNmO1FBQ0Y7UUFFQUksZUFBZSxDQUFDQztZQUNkLE1BQU0sRUFBRW5FLFFBQVEsRUFBRSxHQUFHSDtZQUNyQlIsUUFBUUcsR0FBRyxDQUFDLCtDQUFxQzJFO1lBRWpELG9CQUFvQjtZQUNwQixNQUFNQyxrQkFBa0JwRSxTQUFTcUUsSUFBSSxDQUFDWixDQUFBQSxNQUFPQSxJQUFJekMsRUFBRSxLQUFLbUQ7WUFDeEQsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ3BCL0UsUUFBUXlFLElBQUksQ0FBQyxzQ0FBc0NLO2dCQUNuRDtZQUNGO1lBRUEsMkNBQTJDO1lBQzNDLE1BQU1ULGtCQUFrQjFELFNBQVNjLE1BQU0sQ0FBQzJDLENBQUFBLE1BQU9BLElBQUl6QyxFQUFFLEtBQUttRDtZQUMxRDlFLFFBQVFHLEdBQUcsQ0FBQyxnRUFBc0RrRSxnQkFBZ0JoRSxNQUFNO1lBRXhGLGVBQWU7WUFDZkUsSUFBSTtnQkFBRUksVUFBVTBEO1lBQWdCO1lBRWhDLHVCQUF1QjtZQUN2QixNQUFNSyxhQUFhbEYsbUVBQWFBLENBQUMsWUFBWTZFO1lBQzdDLElBQUlLLFdBQVczRSxPQUFPLEVBQUU7Z0JBQ3RCQyxRQUFRRyxHQUFHLENBQUM7Z0JBQ1pILFFBQVFHLEdBQUcsQ0FBQywrQ0FBcUNrRSxnQkFBZ0JoRSxNQUFNO1lBQ3pFLE9BQU87Z0JBQ0xMLFFBQVFDLEtBQUssQ0FBQyw2QkFBNkJ5RSxXQUFXeEUsT0FBTztnQkFDN0Qsd0NBQXdDO2dCQUN4QyxJQUFJLElBQWtCLEVBQWE7b0JBQ2pDMEUsTUFBTSxpQ0FBaUNGLFdBQVd4RSxPQUFPO2dCQUMzRDtZQUNGO1FBQ0Y7UUFFQStFLHFCQUFxQjtZQUNuQjFFLElBQUk7Z0JBQUVHLGdCQUFnQjtZQUFLO1FBQzdCO1FBRUF3RSxtQkFBbUIsQ0FBQ0M7WUFDbEIsTUFBTSxFQUFFekUsY0FBYyxFQUFFLEdBQUdGO1lBQzNCLE9BQU9FLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0JVLFFBQVEsQ0FBQ0ssTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFTSxXQUFXLEtBQUttRCxVQUFTLEVBQUU7UUFDM0U7SUFDRixJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdG9yZS9pbnZvaWNlU3RvcmUudHM/OGFlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IENsaWVudCwgSW52b2ljZSwgU2VydmljZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgc2VjdXJlR2V0SXRlbSwgc2VjdXJlU2V0SXRlbSB9IGZyb20gJ0AvdXRpbHMvc2VjdXJlU3RvcmFnZSc7XG5pbXBvcnQgeyBVbmlmaWVkU3RvcmFnZU1hbmFnZXIgfSBmcm9tICdAL3V0aWxzL3VuaWZpZWRTdG9yYWdlJztcbmltcG9ydCB7IFNUT1JBR0VfS0VZUyB9IGZyb20gJ0AvdXRpbHMvc3RvcmFnZUtleXMnO1xuXG5pbnRlcmZhY2UgSW52b2ljZVN0b3JlIHtcbiAgY3VycmVudENsaWVudDogQ2xpZW50IHwgbnVsbDtcbiAgY3VycmVudEludm9pY2U6IEludm9pY2UgfCBudWxsO1xuICBpbnZvaWNlczogSW52b2ljZVtdO1xuXG4gIC8vIENsaWVudCBhY3Rpb25zXG4gIHNldEN1cnJlbnRDbGllbnQ6IChjbGllbnQ6IENsaWVudCkgPT4gdm9pZDtcbiAgY2xlYXJDdXJyZW50Q2xpZW50OiAoKSA9PiB2b2lkO1xuXG4gIC8vIEludm9pY2UgYWN0aW9uc1xuICBzZXRDdXJyZW50SW52b2ljZTogKGludm9pY2U6IEludm9pY2UpID0+IHZvaWQ7XG4gIGFkZFNlcnZpY2U6IChzZXJ2aWNlOiBTZXJ2aWNlKSA9PiB2b2lkO1xuICByZW1vdmVTZXJ2aWNlOiAoc2VydmljZUlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIHVwZGF0ZVNlcnZpY2U6IChzZXJ2aWNlSWQ6IHN0cmluZywgc2VydmljZTogU2VydmljZSkgPT4gdm9pZDtcbiAgY2FsY3VsYXRlVG90YWw6ICgpID0+IHZvaWQ7XG4gIHNhdmVJbnZvaWNlOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBkZWxldGVJbnZvaWNlOiAoaW52b2ljZUlkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIGNsZWFyQ3VycmVudEludm9pY2U6ICgpID0+IHZvaWQ7XG5cbiAgLy8gVXRpbGl0eSBmdW5jdGlvbnNcbiAgZ2V0U2VydmljZXNCeVR5cGU6ICh0eXBlOiBzdHJpbmcpID0+IFNlcnZpY2VbXTtcbn1cblxuLy8g2KrYrdmF2YrZhCDYp9mE2KjZitin2YbYp9iqINmF2YYg2KfZhNmG2LjYp9mFINin2YTZhdmI2K3Yr1xuY29uc3QgbG9hZEludm9pY2VzRnJvbVN0b3JhZ2UgPSBhc3luYyAoKTogUHJvbWlzZTxJbnZvaWNlW10+ID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFVuaWZpZWRTdG9yYWdlTWFuYWdlci5nZXRJdGVtKFNUT1JBR0VfS0VZUy5JTlZPSUNFUywgW10pO1xuICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgbG9hZGluZyBpbnZvaWNlcyBmcm9tIHVuaWZpZWQgc3RvcmFnZTonLCByZXN1bHQubWVzc2FnZSk7XG4gICAgICAgIHJldHVybiBbXTtcbiAgICAgIH1cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTG9hZGVkIGludm9pY2VzIGZyb20gdW5pZmllZCBzdG9yYWdlOicsIHJlc3VsdC5kYXRhLmxlbmd0aCk7XG4gICAgICByZXR1cm4gcmVzdWx0LmRhdGE7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBDcml0aWNhbCBlcnJvciBsb2FkaW5nIGludm9pY2VzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIFtdO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZUludm9pY2VTdG9yZSA9IGNyZWF0ZTxJbnZvaWNlU3RvcmU+KChzZXQsIGdldCkgPT4gKHtcbiAgY3VycmVudENsaWVudDogbnVsbCxcbiAgY3VycmVudEludm9pY2U6IG51bGwsXG4gIGludm9pY2VzOiBsb2FkSW52b2ljZXNGcm9tU3RvcmFnZSgpLFxuXG4gIHNldEN1cnJlbnRDbGllbnQ6IChjbGllbnQ6IENsaWVudCkgPT4ge1xuICAgIHNldCh7IGN1cnJlbnRDbGllbnQ6IGNsaWVudCB9KTtcbiAgfSxcblxuICBjbGVhckN1cnJlbnRDbGllbnQ6ICgpID0+IHtcbiAgICBzZXQoeyBjdXJyZW50Q2xpZW50OiBudWxsIH0pO1xuICB9LFxuXG4gIHNldEN1cnJlbnRJbnZvaWNlOiAoaW52b2ljZTogSW52b2ljZSkgPT4ge1xuICAgIHNldCh7IGN1cnJlbnRJbnZvaWNlOiBpbnZvaWNlIH0pO1xuICB9LFxuXG4gIGFkZFNlcnZpY2U6IChzZXJ2aWNlOiBTZXJ2aWNlKSA9PiB7XG4gICAgY29uc3QgeyBjdXJyZW50SW52b2ljZSB9ID0gZ2V0KCk7XG4gICAgaWYgKGN1cnJlbnRJbnZvaWNlKSB7XG4gICAgICBjb25zdCB1cGRhdGVkU2VydmljZXMgPSBbLi4uY3VycmVudEludm9pY2Uuc2VydmljZXMsIHNlcnZpY2VdO1xuICAgICAgY29uc3QgdXBkYXRlZEludm9pY2UgPSB7IC4uLmN1cnJlbnRJbnZvaWNlLCBzZXJ2aWNlczogdXBkYXRlZFNlcnZpY2VzIH07XG4gICAgICBzZXQoeyBjdXJyZW50SW52b2ljZTogdXBkYXRlZEludm9pY2UgfSk7XG4gICAgICBnZXQoKS5jYWxjdWxhdGVUb3RhbCgpO1xuICAgIH1cbiAgfSxcblxuICByZW1vdmVTZXJ2aWNlOiAoc2VydmljZUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB7IGN1cnJlbnRJbnZvaWNlIH0gPSBnZXQoKTtcbiAgICBpZiAoY3VycmVudEludm9pY2UpIHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRTZXJ2aWNlcyA9IGN1cnJlbnRJbnZvaWNlLnNlcnZpY2VzLmZpbHRlcihzID0+IHMuaWQgIT09IHNlcnZpY2VJZCk7XG4gICAgICBjb25zdCB1cGRhdGVkSW52b2ljZSA9IHsgLi4uY3VycmVudEludm9pY2UsIHNlcnZpY2VzOiB1cGRhdGVkU2VydmljZXMgfTtcbiAgICAgIHNldCh7IGN1cnJlbnRJbnZvaWNlOiB1cGRhdGVkSW52b2ljZSB9KTtcbiAgICAgIGdldCgpLmNhbGN1bGF0ZVRvdGFsKCk7XG4gICAgfVxuICB9LFxuXG4gIHVwZGF0ZVNlcnZpY2U6IChzZXJ2aWNlSWQ6IHN0cmluZywgc2VydmljZTogU2VydmljZSkgPT4ge1xuICAgIGNvbnN0IHsgY3VycmVudEludm9pY2UgfSA9IGdldCgpO1xuICAgIGlmIChjdXJyZW50SW52b2ljZSkge1xuICAgICAgY29uc3QgdXBkYXRlZFNlcnZpY2VzID0gY3VycmVudEludm9pY2Uuc2VydmljZXMubWFwKHMgPT4gXG4gICAgICAgIHMuaWQgPT09IHNlcnZpY2VJZCA/IHNlcnZpY2UgOiBzXG4gICAgICApO1xuICAgICAgY29uc3QgdXBkYXRlZEludm9pY2UgPSB7IC4uLmN1cnJlbnRJbnZvaWNlLCBzZXJ2aWNlczogdXBkYXRlZFNlcnZpY2VzIH07XG4gICAgICBzZXQoeyBjdXJyZW50SW52b2ljZTogdXBkYXRlZEludm9pY2UgfSk7XG4gICAgICBnZXQoKS5jYWxjdWxhdGVUb3RhbCgpO1xuICAgIH1cbiAgfSxcblxuICBjYWxjdWxhdGVUb3RhbDogKCkgPT4ge1xuICAgIGNvbnN0IHsgY3VycmVudEludm9pY2UgfSA9IGdldCgpO1xuICAgIGlmIChjdXJyZW50SW52b2ljZSkge1xuICAgICAgbGV0IHRvdGFsID0gMDtcbiAgICAgIFxuICAgICAgY3VycmVudEludm9pY2Uuc2VydmljZXMuZm9yRWFjaChzZXJ2aWNlID0+IHtcbiAgICAgICAgc3dpdGNoIChzZXJ2aWNlLnNlcnZpY2VUeXBlKSB7XG4gICAgICAgICAgY2FzZSAnY29uc3VsdGF0aW9uJzpcbiAgICAgICAgICAgIHRvdGFsICs9IChzZXJ2aWNlIGFzIGFueSkuY29zdCB8fCAwO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAncGF0dGVybic6XG4gICAgICAgICAgICAoc2VydmljZSBhcyBhbnkpLm1vZGVscz8uZm9yRWFjaCgobW9kZWw6IGFueSkgPT4ge1xuICAgICAgICAgICAgICB0b3RhbCArPSBtb2RlbC5maW5hbEFtb3VudCB8fCBtb2RlbC5jb3N0IHx8IDA7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ3BhdHRlcm5fcHJpbnRpbmcnOlxuICAgICAgICAgICAgKHNlcnZpY2UgYXMgYW55KS5maWxlcz8uZm9yRWFjaCgoZmlsZTogYW55KSA9PiB7XG4gICAgICAgICAgICAgIHRvdGFsICs9IGZpbGUuY29zdCB8fCAwO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdtYW51ZmFjdHVyaW5nJzpcbiAgICAgICAgICAgIChzZXJ2aWNlIGFzIGFueSkubW9kZWxzPy5mb3JFYWNoKChtb2RlbDogYW55KSA9PiB7XG4gICAgICAgICAgICAgIHRvdGFsICs9IChtb2RlbC5wYXR0ZXJuUHJpY2UgfHwgMCkgKyAobW9kZWwuc2FtcGxlUHJpY2UgfHwgMCkgKyAobW9kZWwubWFudWZhY3R1cmluZ1ByaWNlIHx8IDApO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdzaGlwcGluZyc6XG4gICAgICAgICAgICB0b3RhbCArPSAoc2VydmljZSBhcyBhbnkpLnRvdGFsIHx8IDA7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdtYXJrZXRpbmcnOlxuICAgICAgICAgICAgdG90YWwgKz0gKHNlcnZpY2UgYXMgYW55KS5jb3N0IHx8IDA7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdwaG90b2dyYXBoeSc6XG4gICAgICAgICAgICB0b3RhbCArPSAoc2VydmljZSBhcyBhbnkpLmNvc3QgfHwgMDtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ3Byb2R1Y3RzJzpcbiAgICAgICAgICAgIGxldCBwcm9kdWN0c1N1YnRvdGFsID0gMDtcbiAgICAgICAgICAgIChzZXJ2aWNlIGFzIGFueSkuaXRlbXM/LmZvckVhY2goKGl0ZW06IGFueSkgPT4ge1xuICAgICAgICAgICAgICBwcm9kdWN0c1N1YnRvdGFsICs9IGl0ZW0udG90YWwgfHwgMDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgLy8g2LfYsditINin2YTYrti12YUg2KXZhiDZiNis2K9cbiAgICAgICAgICAgIGlmICgoc2VydmljZSBhcyBhbnkpLmRpc2NvdW50QW1vdW50KSB7XG4gICAgICAgICAgICAgIHByb2R1Y3RzU3VidG90YWwgLT0gKHNlcnZpY2UgYXMgYW55KS5kaXNjb3VudEFtb3VudCB8fCAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdG90YWwgKz0gTWF0aC5tYXgoMCwgcHJvZHVjdHNTdWJ0b3RhbCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlICdyYXdfcHVyY2hhc2VzJzpcbiAgICAgICAgICAgIChzZXJ2aWNlIGFzIGFueSkubWF0ZXJpYWxzPy5mb3JFYWNoKChtYXRlcmlhbDogYW55KSA9PiB7XG4gICAgICAgICAgICAgIHRvdGFsICs9IG1hdGVyaWFsLnRvdGFsIHx8IDA7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgJ3NhbXBsZXMnOlxuICAgICAgICAgICAgKHNlcnZpY2UgYXMgYW55KS5zYW1wbGVzPy5mb3JFYWNoKChzYW1wbGU6IGFueSkgPT4ge1xuICAgICAgICAgICAgICB0b3RhbCArPSBzYW1wbGUuY29zdCB8fCAwO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHVwZGF0ZWRJbnZvaWNlID0geyAuLi5jdXJyZW50SW52b2ljZSwgdG90YWwgfTtcbiAgICAgIHNldCh7IGN1cnJlbnRJbnZvaWNlOiB1cGRhdGVkSW52b2ljZSB9KTtcbiAgICB9XG4gIH0sXG5cbiAgc2F2ZUludm9pY2U6IGFzeW5jICgpID0+IHtcbiAgICBjb25zdCB7IGN1cnJlbnRJbnZvaWNlLCBpbnZvaWNlcyB9ID0gZ2V0KCk7XG4gICAgaWYgKGN1cnJlbnRJbnZvaWNlKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBBdHRlbXB0aW5nIHRvIHNhdmUgaW52b2ljZTonLCBjdXJyZW50SW52b2ljZS5pZCk7XG5cbiAgICAgIGNvbnN0IGludm9pY2VUb1NhdmUgPSB7XG4gICAgICAgIC4uLmN1cnJlbnRJbnZvaWNlLFxuICAgICAgICBpZDogY3VycmVudEludm9pY2UuaWQgfHwgYElOVi0ke0RhdGUubm93KCl9YCxcbiAgICAgICAgY3JlYXRlZEF0OiBjdXJyZW50SW52b2ljZS5jcmVhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAvLyDYp9mE2KrYo9mD2K8g2YXZhiDZiNis2YjYryDYp9mE2K3ZgtmI2YQg2KfZhNmF2LfZhNmI2KjYqVxuICAgICAgICBwYWlkQW1vdW50OiBjdXJyZW50SW52b2ljZS5wYWlkQW1vdW50IHx8IDAsXG4gICAgICAgIHJlbWFpbmluZ0Ftb3VudDogY3VycmVudEludm9pY2UucmVtYWluaW5nQW1vdW50IHx8IGN1cnJlbnRJbnZvaWNlLnRvdGFsLFxuICAgICAgICBwYXltZW50U3RhdHVzOiBjdXJyZW50SW52b2ljZS5wYXltZW50U3RhdHVzIHx8ICfYutmK2LEg2YXYr9mB2YjYuScsXG4gICAgICAgIHBheW1lbnRNZXRob2Q6IGN1cnJlbnRJbnZvaWNlLnBheW1lbnRNZXRob2QgfHwgJycsXG4gICAgICAgIHJlcXVpcmVzU2hpcHBpbmc6IGN1cnJlbnRJbnZvaWNlLnJlcXVpcmVzU2hpcHBpbmcgfHwgZmFsc2UsXG4gICAgICAgIHNoaXBwaW5nQWRkcmVzczogY3VycmVudEludm9pY2Uuc2hpcHBpbmdBZGRyZXNzIHx8ICcnLFxuICAgICAgICBub3RlczogY3VycmVudEludm9pY2Uubm90ZXMgfHwgJycsXG4gICAgICB9O1xuXG4gICAgICAvLyDZgtix2KfYodipINin2YTYqNmK2KfZhtin2Kog2KfZhNit2KfZhNmK2Kkg2YXZhiDYp9mE2YbYuNin2YUg2KfZhNmF2YjYrdivINmE2YTYqtij2YPYryDZhdmGINin2YTYqtiy2KfZhdmGXG4gICAgICBsZXQgY3VycmVudFN0b3JlZEludm9pY2VzOiBJbnZvaWNlW10gPSBbXTtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFVuaWZpZWRTdG9yYWdlTWFuYWdlci5nZXRJdGVtKFNUT1JBR0VfS0VZUy5JTlZPSUNFUywgW10pO1xuICAgICAgICBjdXJyZW50U3RvcmVkSW52b2ljZXMgPSByZXN1bHQuc3VjY2VzcyA/IHJlc3VsdC5kYXRhIDogaW52b2ljZXM7XG5cbiAgICAgICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciByZWFkaW5nIGN1cnJlbnQgaW52b2ljZXMgZnJvbSB1bmlmaWVkIHN0b3JhZ2U6JywgcmVzdWx0Lm1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgcmVhZGluZyBjdXJyZW50IGludm9pY2VzOicsIGVycm9yKTtcbiAgICAgICAgY3VycmVudFN0b3JlZEludm9pY2VzID0gaW52b2ljZXM7XG4gICAgICB9XG5cbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINi52K/ZhSDZiNis2YjYryDYp9mE2YHYp9iq2YjYsdipINmF2LPYqNmC2KfZiyDZgdmKIGxvY2FsU3RvcmFnZVxuICAgICAgY29uc3QgZXhpc3RpbmdJbmRleCA9IGN1cnJlbnRTdG9yZWRJbnZvaWNlcy5maW5kSW5kZXgoaW52ID0+IGludi5pZCA9PT0gaW52b2ljZVRvU2F2ZS5pZCk7XG4gICAgICBsZXQgdXBkYXRlZEludm9pY2VzO1xuXG4gICAgICBpZiAoZXhpc3RpbmdJbmRleCA+PSAwKSB7XG4gICAgICAgIC8vINiq2K3Yr9mK2Ksg2YHYp9iq2YjYsdipINmF2YjYrNmI2K/YqVxuICAgICAgICB1cGRhdGVkSW52b2ljZXMgPSBbLi4uY3VycmVudFN0b3JlZEludm9pY2VzXTtcbiAgICAgICAgdXBkYXRlZEludm9pY2VzW2V4aXN0aW5nSW5kZXhdID0gaW52b2ljZVRvU2F2ZTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk50gVXBkYXRlZCBleGlzdGluZyBpbnZvaWNlOicsIGludm9pY2VUb1NhdmUuaWQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8g2KXYttin2YHYqSDZgdin2KrZiNix2Kkg2KzYr9mK2K/YqVxuICAgICAgICB1cGRhdGVkSW52b2ljZXMgPSBbLi4uY3VycmVudFN0b3JlZEludm9pY2VzLCBpbnZvaWNlVG9TYXZlXTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KelSBBZGRlZCBuZXcgaW52b2ljZTonLCBpbnZvaWNlVG9TYXZlLmlkKTtcbiAgICAgIH1cblxuICAgICAgLy8g2KXYstin2YTYqSDYo9mKINiq2YPYsdin2LHYp9iqINmF2K3YqtmF2YTYqVxuICAgICAgY29uc3QgdW5pcXVlSW52b2ljZXMgPSB1cGRhdGVkSW52b2ljZXMuZmlsdGVyKChpbnZvaWNlLCBpbmRleCwgc2VsZikgPT5cbiAgICAgICAgaW5kZXggPT09IHNlbGYuZmluZEluZGV4KGludiA9PiBpbnYuaWQgPT09IGludm9pY2UuaWQpXG4gICAgICApO1xuXG4gICAgICBpZiAodW5pcXVlSW52b2ljZXMubGVuZ3RoICE9PSB1cGRhdGVkSW52b2ljZXMubGVuZ3RoKSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIFJlbW92ZWQgZHVwbGljYXRlIGludm9pY2VzIGR1cmluZyBzYXZlJyk7XG4gICAgICB9XG5cbiAgICAgIC8vINiq2K3Yr9mK2Ksg2KfZhNmF2KrYrNixXG4gICAgICBzZXQoeyBpbnZvaWNlczogdW5pcXVlSW52b2ljZXMsIGN1cnJlbnRJbnZvaWNlOiBpbnZvaWNlVG9TYXZlIH0pO1xuXG4gICAgICAvLyDYrdmB2Lgg2YHZiiDYp9mE2YbYuNin2YUg2KfZhNmF2YjYrdivXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBzYXZlUmVzdWx0ID0gYXdhaXQgVW5pZmllZFN0b3JhZ2VNYW5hZ2VyLnNldEl0ZW0oU1RPUkFHRV9LRVlTLklOVk9JQ0VTLCB1bmlxdWVJbnZvaWNlcyk7XG4gICAgICAgIGlmIChzYXZlUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEludm9pY2Ugc2F2ZWQgdmlhIHVuaWZpZWQgc3RvcmFnZS4gVG90YWwgaW52b2ljZXM6JywgdW5pcXVlSW52b2ljZXMubGVuZ3RoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3Igc2F2aW5nIGludm9pY2UgdmlhIHVuaWZpZWQgc3RvcmFnZTonLCBzYXZlUmVzdWx0Lm1lc3NhZ2UpO1xuICAgICAgICAgIC8vINmB2Yog2K3Yp9mE2Kkg2YHYtNmEINin2YTYrdmB2LjYjCDYudix2LYg2KrZhtio2YrZhyDZhNmE2YXYs9iq2K7Yr9mFXG4gICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICBhbGVydCgn2KrYrdiw2YrYsTog2YHYtNmEINmB2Yog2K3Zgdi4INin2YTZgdin2KrZiNix2KkuICcgKyBzYXZlUmVzdWx0Lm1lc3NhZ2UpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIENyaXRpY2FsIGVycm9yIHNhdmluZyBpbnZvaWNlOicsIGVycm9yKTtcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgYWxlcnQoJ9iu2LfYoyDYrdix2Kw6INmB2LTZhCDZgdmKINit2YHYuCDYp9mE2YHYp9iq2YjYsdipLiDZitix2KzZiSDYp9mE2YXYrdin2YjZhNipINmF2LHYqSDYo9iu2LHZiS4nKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBObyBjdXJyZW50IGludm9pY2UgdG8gc2F2ZScpO1xuICAgIH1cbiAgfSxcblxuICBkZWxldGVJbnZvaWNlOiAoaW52b2ljZUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB7IGludm9pY2VzIH0gPSBnZXQoKTtcbiAgICBjb25zb2xlLmxvZygn8J+Xke+4jyBBdHRlbXB0aW5nIHRvIGRlbGV0ZSBpbnZvaWNlOicsIGludm9pY2VJZCk7XG5cbiAgICAvLyDYp9mE2KjYrdirINi52YYg2KfZhNmB2KfYqtmI2LHYqVxuICAgIGNvbnN0IGludm9pY2VUb0RlbGV0ZSA9IGludm9pY2VzLmZpbmQoaW52ID0+IGludi5pZCA9PT0gaW52b2ljZUlkKTtcbiAgICBpZiAoIWludm9pY2VUb0RlbGV0ZSkge1xuICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gSW52b2ljZSBub3QgZm91bmQgZm9yIGRlbGV0aW9uOicsIGludm9pY2VJZCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g2KXZhti02KfYoSDZgtin2KbZhdipINis2K/Zitiv2Kkg2KjYr9mI2YYg2KfZhNmB2KfYqtmI2LHYqSDYp9mE2YXYrdiw2YjZgdipXG4gICAgY29uc3QgdXBkYXRlZEludm9pY2VzID0gaW52b2ljZXMuZmlsdGVyKGludiA9PiBpbnYuaWQgIT09IGludm9pY2VJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk50gUmVtb3ZpbmcgaW52b2ljZSBmcm9tIGxpc3QuIFJlbWFpbmluZyBpbnZvaWNlczonLCB1cGRhdGVkSW52b2ljZXMubGVuZ3RoKTtcblxuICAgIC8vINiq2K3Yr9mK2Ksg2KfZhNmF2KrYrNixXG4gICAgc2V0KHsgaW52b2ljZXM6IHVwZGF0ZWRJbnZvaWNlcyB9KTtcblxuICAgIC8vINit2YHYuCDZgdmKINin2YTYqtiu2LLZitmGINin2YTYotmF2YZcbiAgICBjb25zdCBzYXZlUmVzdWx0ID0gc2VjdXJlU2V0SXRlbSgnaW52b2ljZXMnLCB1cGRhdGVkSW52b2ljZXMpO1xuICAgIGlmIChzYXZlUmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgSW52b2ljZSBkZWxldGVkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4ogVG90YWwgaW52b2ljZXMgYWZ0ZXIgZGVsZXRpb246JywgdXBkYXRlZEludm9pY2VzLmxlbmd0aCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBkZWxldGluZyBpbnZvaWNlOicsIHNhdmVSZXN1bHQubWVzc2FnZSk7XG4gICAgICAvLyDZgdmKINit2KfZhNipINmB2LTZhCDYp9mE2K3YsNmB2Iwg2LnYsdi2INiq2YbYqNmK2Ycg2YTZhNmF2LPYqtiu2K/ZhVxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIGFsZXJ0KCfYqtit2LDZitixOiDZgdi02YQg2YHZiiDYrdiw2YEg2KfZhNmB2KfYqtmI2LHYqS4gJyArIHNhdmVSZXN1bHQubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfVxuICB9LFxuXG4gIGNsZWFyQ3VycmVudEludm9pY2U6ICgpID0+IHtcbiAgICBzZXQoeyBjdXJyZW50SW52b2ljZTogbnVsbCB9KTtcbiAgfSxcblxuICBnZXRTZXJ2aWNlc0J5VHlwZTogKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHsgY3VycmVudEludm9pY2UgfSA9IGdldCgpO1xuICAgIHJldHVybiBjdXJyZW50SW52b2ljZT8uc2VydmljZXMuZmlsdGVyKHMgPT4gcy5zZXJ2aWNlVHlwZSA9PT0gdHlwZSkgfHwgW107XG4gIH0sXG59KSk7XG4iXSwibmFtZXMiOlsiY3JlYXRlIiwic2VjdXJlU2V0SXRlbSIsIlVuaWZpZWRTdG9yYWdlTWFuYWdlciIsIlNUT1JBR0VfS0VZUyIsImxvYWRJbnZvaWNlc0Zyb21TdG9yYWdlIiwicmVzdWx0IiwiZ2V0SXRlbSIsIklOVk9JQ0VTIiwic3VjY2VzcyIsImNvbnNvbGUiLCJlcnJvciIsIm1lc3NhZ2UiLCJsb2ciLCJkYXRhIiwibGVuZ3RoIiwidXNlSW52b2ljZVN0b3JlIiwic2V0IiwiZ2V0IiwiY3VycmVudENsaWVudCIsImN1cnJlbnRJbnZvaWNlIiwiaW52b2ljZXMiLCJzZXRDdXJyZW50Q2xpZW50IiwiY2xpZW50IiwiY2xlYXJDdXJyZW50Q2xpZW50Iiwic2V0Q3VycmVudEludm9pY2UiLCJpbnZvaWNlIiwiYWRkU2VydmljZSIsInNlcnZpY2UiLCJ1cGRhdGVkU2VydmljZXMiLCJzZXJ2aWNlcyIsInVwZGF0ZWRJbnZvaWNlIiwiY2FsY3VsYXRlVG90YWwiLCJyZW1vdmVTZXJ2aWNlIiwic2VydmljZUlkIiwiZmlsdGVyIiwicyIsImlkIiwidXBkYXRlU2VydmljZSIsIm1hcCIsInRvdGFsIiwiZm9yRWFjaCIsInNlcnZpY2VUeXBlIiwiY29zdCIsIm1vZGVscyIsIm1vZGVsIiwiZmluYWxBbW91bnQiLCJmaWxlcyIsImZpbGUiLCJwYXR0ZXJuUHJpY2UiLCJzYW1wbGVQcmljZSIsIm1hbnVmYWN0dXJpbmdQcmljZSIsInByb2R1Y3RzU3VidG90YWwiLCJpdGVtcyIsIml0ZW0iLCJkaXNjb3VudEFtb3VudCIsIk1hdGgiLCJtYXgiLCJtYXRlcmlhbHMiLCJtYXRlcmlhbCIsInNhbXBsZXMiLCJzYW1wbGUiLCJzYXZlSW52b2ljZSIsImludm9pY2VUb1NhdmUiLCJEYXRlIiwibm93IiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJwYWlkQW1vdW50IiwicmVtYWluaW5nQW1vdW50IiwicGF5bWVudFN0YXR1cyIsInBheW1lbnRNZXRob2QiLCJyZXF1aXJlc1NoaXBwaW5nIiwic2hpcHBpbmdBZGRyZXNzIiwibm90ZXMiLCJjdXJyZW50U3RvcmVkSW52b2ljZXMiLCJleGlzdGluZ0luZGV4IiwiZmluZEluZGV4IiwiaW52IiwidXBkYXRlZEludm9pY2VzIiwidW5pcXVlSW52b2ljZXMiLCJpbmRleCIsInNlbGYiLCJ3YXJuIiwic2F2ZVJlc3VsdCIsInNldEl0ZW0iLCJhbGVydCIsImRlbGV0ZUludm9pY2UiLCJpbnZvaWNlSWQiLCJpbnZvaWNlVG9EZWxldGUiLCJmaW5kIiwiY2xlYXJDdXJyZW50SW52b2ljZSIsImdldFNlcnZpY2VzQnlUeXBlIiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/invoiceStore.ts\n"));

/***/ })

});