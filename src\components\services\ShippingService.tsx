'use client';

import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { ShippingService } from '@/types';

interface ShippingServiceProps {
  namePrefix: string;
  values: any;
  setFieldValue: (field: string, value: any) => void;
}

const ShippingServiceComponent: React.FC<ShippingServiceProps> = ({
  namePrefix,
  values,
  setFieldValue,
}) => {
  const serviceData = values[namePrefix] as ShippingService;
  const isEnabled = serviceData?.enabled || false;

  const contactMethods = [
    { value: 'whatsapp', label: 'واتساب' },
    { value: 'meta', label: 'ميتا' },
    { value: 'meeting', label: 'مقابلة' },
    { value: 'phone', label: 'هاتف' },
  ];

  // حساب المجموع تلقائياً
  React.useEffect(() => {
    if (isEnabled && serviceData) {
      const quantity = serviceData.quantity || 0;
      const unitPrice = serviceData.unitPrice || 0;
      const total = quantity * unitPrice;
      setFieldValue(`${namePrefix}.total`, total);
    }
  }, [serviceData?.quantity, serviceData?.unitPrice, isEnabled, namePrefix, setFieldValue]);

  return (
    <div className="form-section">
      <div className="flex items-center mb-4">
        <Field
          type="checkbox"
          name={`${namePrefix}.enabled`}
          className="form-checkbox"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setFieldValue(`${namePrefix}.enabled`, e.target.checked);
            if (e.target.checked) {
              setFieldValue(`${namePrefix}.serviceType`, 'shipping');
              setFieldValue(`${namePrefix}.id`, Date.now().toString());
              // تعبئة البيانات الأساسية من العميل
              setFieldValue(`${namePrefix}.clientName`, values.client?.name || '');
              setFieldValue(`${namePrefix}.phone`, values.client?.phone || '');
              setFieldValue(`${namePrefix}.contactMethod`, values.client?.contactMethod || '');
              setFieldValue(`${namePrefix}.salesperson`, values.client?.salesperson || '');
            }
          }}
        />
        <label className="text-lg font-semibold">خدمة الشحن والتعبئة</label>
      </div>

      {isEnabled && (
        <div className="space-y-4 pr-6 border-r-4 border-orange-500">
          {/* بيانات العميل */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">بيانات العميل</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-field">
                <label className="form-label">اسم العميل</label>
                <Field
                  name={`${namePrefix}.clientName`}
                  type="text"
                  className="form-input"
                  placeholder="اسم العميل"
                />
                <ErrorMessage name={`${namePrefix}.clientName`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">رقم الهاتف</label>
                <Field
                  name={`${namePrefix}.phone`}
                  type="tel"
                  className="form-input"
                  placeholder="رقم الهاتف"
                />
                <ErrorMessage name={`${namePrefix}.phone`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">وسيلة التواصل</label>
                <Field as="select" name={`${namePrefix}.contactMethod`} className="form-input">
                  <option value="">اختر وسيلة التواصل</option>
                  {contactMethods.map((method) => (
                    <option key={method.value} value={method.value}>
                      {method.label}
                    </option>
                  ))}
                </Field>
                <ErrorMessage name={`${namePrefix}.contactMethod`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">اسم السيلز</label>
                <Field
                  name={`${namePrefix}.salesperson`}
                  type="text"
                  className="form-input bg-gray-100"
                  placeholder="اسم السيلز"
                  readOnly
                />
                <div className="text-xs text-gray-500 mt-1">
                  يتم تعبئة هذا الحقل تلقائياً من بيانات العميل
                </div>
              </div>
            </div>
          </div>

          {/* تفاصيل الشحنة */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-3">تفاصيل الشحنة</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="form-field">
                <label className="form-label">الصنف/المنتج</label>
                <Field
                  name={`${namePrefix}.item`}
                  type="text"
                  className="form-input"
                  placeholder="اسم الصنف أو المنتج"
                />
                <ErrorMessage name={`${namePrefix}.item`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">الكمية</label>
                <Field
                  name={`${namePrefix}.quantity`}
                  type="number"
                  min="1"
                  className="form-input"
                  placeholder="عدد القطع"
                />
                <ErrorMessage name={`${namePrefix}.quantity`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">سعر الوحدة (جنيه)</label>
                <Field
                  name={`${namePrefix}.unitPrice`}
                  type="number"
                  min="0"
                  step="0.01"
                  className="form-input"
                  placeholder="سعر القطعة الواحدة"
                />
                <ErrorMessage name={`${namePrefix}.unitPrice`} component="div" className="error-message" />
              </div>

              <div className="form-field">
                <label className="form-label">المجموع (جنيه)</label>
                <Field
                  name={`${namePrefix}.total`}
                  type="number"
                  className="form-input bg-gray-100"
                  readOnly
                  placeholder="المجموع الكلي"
                />
                <div className="text-xs text-gray-500 mt-1">
                  يتم حساب المجموع تلقائياً (الكمية × سعر الوحدة)
                </div>
              </div>
            </div>
          </div>

          {/* عنوان الشحن */}
          <div className="form-field">
            <label className="form-label">عنوان الشحن</label>
            <Field
              name={`${namePrefix}.shippingAddress`}
              as="textarea"
              rows="3"
              className="form-input"
              placeholder="العنوان الكامل للشحن"
            />
            <ErrorMessage name={`${namePrefix}.shippingAddress`} component="div" className="error-message" />
          </div>

          {/* ملاحظات الشحن */}
          <div className="form-field">
            <label className="form-label">ملاحظات الشحن</label>
            <Field
              name={`${namePrefix}.shippingNotes`}
              as="textarea"
              rows="2"
              className="form-input"
              placeholder="أي ملاحظات خاصة بالشحن"
            />
          </div>

          {/* تاريخ الشحن المتوقع */}
          <div className="form-field">
            <label className="form-label">تاريخ الشحن المتوقع</label>
            <Field
              name={`${namePrefix}.expectedShippingDate`}
              type="date"
              className="form-input"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ShippingServiceComponent;
