'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import CustomerCodeManager from '@/components/CustomerCodeManager';

const CustomerCodesPage: React.FC = () => {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة أكواد العملاء</h1>
            <p className="text-gray-600 mt-2">
              إدارة وتتبع أكواد العملاء والعدادات الخاصة بالنظام
            </p>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={() => router.push('/customers')}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clipRule="evenodd"/>
              </svg>
              إدارة العملاء
            </button>
            
            <button
              onClick={() => router.push('/')}
              className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
              </svg>
              الصفحة الرئيسية
            </button>
          </div>
        </div>

        {/* Main Content */}
        <CustomerCodeManager />

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">معلومات مهمة</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-blue-700 mb-2">تنسيق أكواد العملاء:</h3>
              <ul className="text-sm text-blue-600 space-y-1">
                <li>• التنسيق: p + رقم المعاملة (2 رقم) + رقم العميل (4 أرقام)</li>
                <li>• مثال: p013621 (معاملة 01، عميل 3621)</li>
                <li>• الأكواد تتزايد تلقائياً مع كل عميل جديد</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-blue-700 mb-2">إدارة العدادات:</h3>
              <ul className="text-sm text-blue-600 space-y-1">
                <li>• يمكن تعديل العدادات يدوياً عند الحاجة</li>
                <li>• رقم المعاملة يزيد عند بدء معاملة جديدة</li>
                <li>• إعادة التعيين تحذف جميع العدادات</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-yellow-100 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>تنبيه:</strong> تأكد من عمل نسخة احتياطية قبل إجراء أي تعديلات على العدادات
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerCodesPage;
