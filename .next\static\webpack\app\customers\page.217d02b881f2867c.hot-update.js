"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customers/page",{

/***/ "(app-pages-browser)/./src/components/CustomerList.tsx":
/*!*****************************************!*\
  !*** ./src/components/CustomerList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_customerStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/customerStore */ \"(app-pages-browser)/./src/store/customerStore.ts\");\n/**\n * مكون قائمة العملاء مع البحث والتصفية\n */ \nvar _s = $RefreshSig$();\n\n\nconst CustomerList = (param)=>{\n    let { onSelectCustomer, onEditCustomer, onDeleteCustomer, onCreateInvoice, onViewDetails, selectable = false, showActions = true } = param;\n    _s();\n    const { customers, searchQuery, setSearchQuery, searchCustomers, isLoading, error, loadCustomers, clearError } = (0,_store_customerStore__WEBPACK_IMPORTED_MODULE_2__.useCustomerStore)();\n    const [filteredCustomers, setFilteredCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"name\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    const [selectedCustomer, setSelectedCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadCustomers();\n    }, [\n        loadCustomers\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = searchQuery ? searchCustomers(searchQuery) : customers;\n        // ترتيب النتائج\n        filtered = [\n            ...filtered\n        ].sort((a, b)=>{\n            let aValue;\n            let bValue;\n            switch(sortBy){\n                case \"code\":\n                    aValue = a.clientCode || \"\";\n                    bValue = b.clientCode || \"\";\n                    break;\n                case \"date\":\n                    aValue = a.id || \"\";\n                    bValue = b.id || \"\";\n                    break;\n                default:\n                    aValue = a.name;\n                    bValue = b.name;\n            }\n            const comparison = aValue.localeCompare(bValue, \"ar\");\n            return sortOrder === \"asc\" ? comparison : -comparison;\n        });\n        setFilteredCustomers(filtered);\n    }, [\n        customers,\n        searchQuery,\n        sortBy,\n        sortOrder,\n        searchCustomers\n    ]);\n    const handleSort = (field)=>{\n        if (sortBy === field) {\n            setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortBy(field);\n            setSortOrder(\"asc\");\n        }\n    };\n    const handleSelectCustomer = (customer)=>{\n        if (selectable) {\n            setSelectedCustomer(customer.id || null);\n            onSelectCustomer === null || onSelectCustomer === void 0 ? void 0 : onSelectCustomer(customer);\n        }\n    };\n    const getContactMethodIcon = (method)=>{\n        switch(method){\n            case \"whatsapp\":\n                return \"\\uD83D\\uDCF1\";\n            case \"meta\":\n                return \"\\uD83D\\uDCAC\";\n            case \"meeting\":\n                return \"\\uD83E\\uDD1D\";\n            case \"phone\":\n                return \"\\uD83D\\uDCDE\";\n            default:\n                return \"\\uD83D\\uDCDE\";\n        }\n    };\n    const getCategoryColor = (category)=>{\n        switch(category){\n            case \"A\":\n                return \"bg-green-100 text-green-800\";\n            case \"B\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"C\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-gray-600\",\n                    children: \"جاري تحميل العملاء...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-500 ml-2\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-800\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearError,\n                        className: \"text-red-600 hover:text-red-800\",\n                        children: \"✕\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-gray-400\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            placeholder: \"البحث بالاسم، الهاتف، أو كود العميل...\",\n                                            className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: sortBy,\n                                        onChange: (e)=>setSortBy(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"name\",\n                                                children: \"ترتيب بالاسم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"code\",\n                                                children: \"ترتيب بالكود\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"date\",\n                                                children: \"ترتيب بالتاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\"),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex items-center gap-4 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"إجمالي العملاء: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-blue-600\",\n                                        children: customers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"النتائج المعروضة: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-green-600\",\n                                        children: filteredCustomers.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 35\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            filteredCustomers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"لا توجد عملاء\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: searchQuery ? \"لم يتم العثور على عملاء مطابقين لبحثك\" : \"لم يتم إضافة أي عملاء بعد\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                            onClick: ()=>handleSort(\"name\"),\n                                            children: [\n                                                \"اسم العميل\",\n                                                sortBy === \"name\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                                            onClick: ()=>handleSort(\"code\"),\n                                            children: [\n                                                \"كود العميل\",\n                                                sortBy === \"code\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: sortOrder === \"asc\" ? \"↑\" : \"↓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الهاتف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"التصنيف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"التواصل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الخدمات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: filteredCustomers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50 transition-colors \".concat(selectable && selectedCustomer === customer.id ? \"bg-blue-50\" : \"\", \" \").concat(selectable ? \"cursor-pointer\" : \"\"),\n                                        onClick: ()=>handleSelectCustomer(customer),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        customer.businessName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: customer.businessName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                    children: customer.clientCode\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                children: customer.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getCategoryColor(customer.initialCategory)),\n                                                    children: [\n                                                        \"فئة \",\n                                                        customer.initialCategory\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1\",\n                                                            children: getContactMethodIcon(customer.contactMethod)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        customer.contactMethod\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        customer.services.slice(0, 2).map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                children: service\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 27\n                                                            }, undefined)),\n                                                        customer.services.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                            children: [\n                                                                \"+\",\n                                                                customer.services.length - 2\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        onViewDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onViewDetails(customer);\n                                                            },\n                                                            className: \"bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-1 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                            title: \"عرض تفاصيل العميل الشاملة\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                \"تفاصيل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        onCreateInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onCreateInvoice(customer);\n                                                            },\n                                                            className: \"bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-md text-xs font-medium transition-colors flex items-center gap-1\",\n                                                            title: \"إنشاء فاتورة جديدة لهذا العميل\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                \"فاتورة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        onEditCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                onEditCustomer(customer);\n                                                            },\n                                                            className: \"text-blue-600 hover:text-blue-900\",\n                                                            children: \"تعديل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        onDeleteCustomer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                if (confirm(\"هل أنت متأكد من حذف هذا العميل؟\")) {\n                                                                    onDeleteCustomer(customer.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 hover:text-red-900\",\n                                                            children: \"حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, customer.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\CustomerList.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerList, \"KJZN3zosjEpNeIyk5VNwrHoEA8w=\", false, function() {\n    return [\n        _store_customerStore__WEBPACK_IMPORTED_MODULE_2__.useCustomerStore\n    ];\n});\n_c = CustomerList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CustomerList);\nvar _c;\n$RefreshReg$(_c, \"CustomerList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0N1c3RvbWVyTGlzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7Q0FFQzs7QUFFa0Q7QUFFTTtBQVl6RCxNQUFNSSxlQUE0QztRQUFDLEVBQ2pEQyxnQkFBZ0IsRUFDaEJDLGNBQWMsRUFDZEMsZ0JBQWdCLEVBQ2hCQyxlQUFlLEVBQ2ZDLGFBQWEsRUFDYkMsYUFBYSxLQUFLLEVBQ2xCQyxjQUFjLElBQUksRUFDbkI7O0lBQ0MsTUFBTSxFQUNKQyxTQUFTLEVBQ1RDLFdBQVcsRUFDWEMsY0FBYyxFQUNkQyxlQUFlLEVBQ2ZDLFNBQVMsRUFDVEMsS0FBSyxFQUNMQyxhQUFhLEVBQ2JDLFVBQVUsRUFDWCxHQUFHaEIsc0VBQWdCQTtJQUVwQixNQUFNLENBQUNpQixtQkFBbUJDLHFCQUFxQixHQUFHcEIsK0NBQVFBLENBQVcsRUFBRTtJQUN2RSxNQUFNLENBQUNxQixRQUFRQyxVQUFVLEdBQUd0QiwrQ0FBUUEsQ0FBMkI7SUFDL0QsTUFBTSxDQUFDdUIsV0FBV0MsYUFBYSxHQUFHeEIsK0NBQVFBLENBQWlCO0lBQzNELE1BQU0sQ0FBQ3lCLGtCQUFrQkMsb0JBQW9CLEdBQUcxQiwrQ0FBUUEsQ0FBZ0I7SUFFeEVDLGdEQUFTQSxDQUFDO1FBQ1JnQjtJQUNGLEdBQUc7UUFBQ0E7S0FBYztJQUVsQmhCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTBCLFdBQVdmLGNBQWNFLGdCQUFnQkYsZUFBZUQ7UUFFNUQsZ0JBQWdCO1FBQ2hCZ0IsV0FBVztlQUFJQTtTQUFTLENBQUNDLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUNoQyxJQUFJQztZQUNKLElBQUlDO1lBRUosT0FBUVg7Z0JBQ04sS0FBSztvQkFDSFUsU0FBU0YsRUFBRUksVUFBVSxJQUFJO29CQUN6QkQsU0FBU0YsRUFBRUcsVUFBVSxJQUFJO29CQUN6QjtnQkFDRixLQUFLO29CQUNIRixTQUFTRixFQUFFSyxFQUFFLElBQUk7b0JBQ2pCRixTQUFTRixFQUFFSSxFQUFFLElBQUk7b0JBQ2pCO2dCQUNGO29CQUNFSCxTQUFTRixFQUFFTSxJQUFJO29CQUNmSCxTQUFTRixFQUFFSyxJQUFJO1lBQ25CO1lBRUEsTUFBTUMsYUFBYUwsT0FBT00sYUFBYSxDQUFDTCxRQUFRO1lBQ2hELE9BQU9ULGNBQWMsUUFBUWEsYUFBYSxDQUFDQTtRQUM3QztRQUVBaEIscUJBQXFCTztJQUN2QixHQUFHO1FBQUNoQjtRQUFXQztRQUFhUztRQUFRRTtRQUFXVDtLQUFnQjtJQUUvRCxNQUFNd0IsYUFBYSxDQUFDQztRQUNsQixJQUFJbEIsV0FBV2tCLE9BQU87WUFDcEJmLGFBQWFELGNBQWMsUUFBUSxTQUFTO1FBQzlDLE9BQU87WUFDTEQsVUFBVWlCO1lBQ1ZmLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTWdCLHVCQUF1QixDQUFDQztRQUM1QixJQUFJaEMsWUFBWTtZQUNkaUIsb0JBQW9CZSxTQUFTUCxFQUFFLElBQUk7WUFDbkM5Qiw2QkFBQUEsdUNBQUFBLGlCQUFtQnFDO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNQyx1QkFBdUIsQ0FBQ0M7UUFDNUIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7Z0JBQ0gsT0FBTztZQUNUO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsSUFBSTlCLFdBQVc7UUFDYixxQkFDRSw4REFBQytCO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDQztvQkFBS0QsV0FBVTs4QkFBcUI7Ozs7Ozs7Ozs7OztJQUczQztJQUVBLElBQUkvQixPQUFPO1FBQ1QscUJBQ0UsOERBQUM4QjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUlGLFdBQVU7Z0NBQTRCRyxNQUFLO2dDQUFlQyxTQUFROzBDQUNyRSw0RUFBQ0M7b0NBQUtDLFVBQVM7b0NBQVVDLEdBQUU7b0NBQTBOQyxVQUFTOzs7Ozs7Ozs7OzswQ0FFaFEsOERBQUNQO2dDQUFLRCxXQUFVOzBDQUFnQi9COzs7Ozs7Ozs7Ozs7a0NBRWxDLDhEQUFDd0M7d0JBQ0NDLFNBQVN2Qzt3QkFDVDZCLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0U7Z0RBQUlGLFdBQVU7Z0RBQXdCRyxNQUFLO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLFVBQVM7b0RBQVVDLEdBQUU7b0RBQW1IQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUczSiw4REFBQ0c7NENBQ0NDLE1BQUs7NENBQ0xDLE9BQU9oRDs0Q0FDUGlELFVBQVUsQ0FBQ0MsSUFBTWpELGVBQWVpRCxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQzlDSSxhQUFZOzRDQUNaakIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTWhCLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNrQjt3Q0FDQ0wsT0FBT3ZDO3dDQUNQd0MsVUFBVSxDQUFDQyxJQUFNeEMsVUFBVXdDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDekNiLFdBQVU7OzBEQUVWLDhEQUFDbUI7Z0RBQU9OLE9BQU07MERBQU87Ozs7OzswREFDckIsOERBQUNNO2dEQUFPTixPQUFNOzBEQUFPOzs7Ozs7MERBQ3JCLDhEQUFDTTtnREFBT04sT0FBTTswREFBTzs7Ozs7Ozs7Ozs7O2tEQUd2Qiw4REFBQ0o7d0NBQ0NDLFNBQVMsSUFBTWpDLGFBQWFELGNBQWMsUUFBUSxTQUFTO3dDQUMzRHdCLFdBQVU7a0RBRVR4QixjQUFjLFFBQVEsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1uQyw4REFBQ3VCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0M7O29DQUFLO2tEQUFnQiw4REFBQ21CO3dDQUFPcEIsV0FBVTtrREFBaUJwQyxVQUFVeUQsTUFBTTs7Ozs7Ozs7Ozs7OzBDQUN6RSw4REFBQ3BCOztvQ0FBSztrREFBa0IsOERBQUNtQjt3Q0FBT3BCLFdBQVU7a0RBQWtCNUIsa0JBQWtCaUQsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBS3ZGakQsa0JBQWtCaUQsTUFBTSxLQUFLLGtCQUM1Qiw4REFBQ3RCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUlGLFdBQVU7d0JBQXVDRyxNQUFLO3dCQUFPbUIsUUFBTzt3QkFBZWxCLFNBQVE7a0NBQzlGLDRFQUFDQzs0QkFBS2tCLGVBQWM7NEJBQVFDLGdCQUFlOzRCQUFRQyxhQUFhOzRCQUFHbEIsR0FBRTs7Ozs7Ozs7Ozs7a0NBRXZFLDhEQUFDbUI7d0JBQUcxQixXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQzJCO3dCQUFFM0IsV0FBVTtrQ0FDVm5DLGNBQWMsMENBQTBDOzs7Ozs7Ozs7OzswQ0FJN0QsOERBQUNrQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUM0Qjt3QkFBTTVCLFdBQVU7OzBDQUNmLDhEQUFDNkI7Z0NBQU03QixXQUFVOzBDQUNmLDRFQUFDOEI7O3NEQUNDLDhEQUFDQzs0Q0FDQy9CLFdBQVU7NENBQ1ZVLFNBQVMsSUFBTW5CLFdBQVc7O2dEQUMzQjtnREFFRWpCLFdBQVcsd0JBQ1YsOERBQUMyQjtvREFBS0QsV0FBVTs4REFBUXhCLGNBQWMsUUFBUSxNQUFNOzs7Ozs7Ozs7Ozs7c0RBR3hELDhEQUFDdUQ7NENBQ0MvQixXQUFVOzRDQUNWVSxTQUFTLElBQU1uQixXQUFXOztnREFDM0I7Z0RBRUVqQixXQUFXLHdCQUNWLDhEQUFDMkI7b0RBQUtELFdBQVU7OERBQVF4QixjQUFjLFFBQVEsTUFBTTs7Ozs7Ozs7Ozs7O3NEQUd4RCw4REFBQ3VEOzRDQUFHL0IsV0FBVTtzREFBa0Y7Ozs7OztzREFHaEcsOERBQUMrQjs0Q0FBRy9CLFdBQVU7c0RBQWtGOzs7Ozs7c0RBR2hHLDhEQUFDK0I7NENBQUcvQixXQUFVO3NEQUFrRjs7Ozs7O3NEQUdoRyw4REFBQytCOzRDQUFHL0IsV0FBVTtzREFBa0Y7Ozs7Ozt3Q0FHL0ZyQyw2QkFDQyw4REFBQ29FOzRDQUFHL0IsV0FBVTtzREFBa0Y7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU10Ryw4REFBQ2dDO2dDQUFNaEMsV0FBVTswQ0FDZDVCLGtCQUFrQjZELEdBQUcsQ0FBQyxDQUFDdkMseUJBQ3RCLDhEQUFDb0M7d0NBRUM5QixXQUFXLHNDQUVQdEMsT0FERkEsY0FBY2dCLHFCQUFxQmdCLFNBQVNQLEVBQUUsR0FBRyxlQUFlLElBQ2pFLEtBQXNDLE9BQW5DekIsYUFBYSxtQkFBbUI7d0NBQ3BDZ0QsU0FBUyxJQUFNakIscUJBQXFCQzs7MERBRXBDLDhEQUFDd0M7Z0RBQUdsQyxXQUFVOzBEQUNaLDRFQUFDRDs7c0VBQ0MsOERBQUNBOzREQUFJQyxXQUFVO3NFQUFxQ04sU0FBU04sSUFBSTs7Ozs7O3dEQUNoRU0sU0FBU3lDLFlBQVksa0JBQ3BCLDhEQUFDcEM7NERBQUlDLFdBQVU7c0VBQXlCTixTQUFTeUMsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSW5FLDhEQUFDRDtnREFBR2xDLFdBQVU7MERBQ1osNEVBQUNDO29EQUFLRCxXQUFVOzhEQUNiTixTQUFTUixVQUFVOzs7Ozs7Ozs7OzswREFHeEIsOERBQUNnRDtnREFBR2xDLFdBQVU7MERBQ1hOLFNBQVMwQyxLQUFLOzs7Ozs7MERBRWpCLDhEQUFDRjtnREFBR2xDLFdBQVU7MERBQ1osNEVBQUNDO29EQUFLRCxXQUFXLDJFQUFzSCxPQUEzQ0gsaUJBQWlCSCxTQUFTMkMsZUFBZTs7d0RBQUs7d0RBQ25JM0MsU0FBUzJDLGVBQWU7Ozs7Ozs7Ozs7OzswREFHakMsOERBQUNIO2dEQUFHbEMsV0FBVTswREFDWiw0RUFBQ0M7b0RBQUtELFdBQVU7O3NFQUNkLDhEQUFDQzs0REFBS0QsV0FBVTtzRUFBUUwscUJBQXFCRCxTQUFTNEMsYUFBYTs7Ozs7O3dEQUNsRTVDLFNBQVM0QyxhQUFhOzs7Ozs7Ozs7Ozs7MERBRzNCLDhEQUFDSjtnREFBR2xDLFdBQVU7MERBQ1osNEVBQUNEO29EQUFJQyxXQUFVOzt3REFDWk4sU0FBUzZDLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsR0FBR1AsR0FBRyxDQUFDLENBQUNRLFNBQVNDLHNCQUMzQyw4REFBQ3pDO2dFQUVDRCxXQUFVOzBFQUVUeUM7K0RBSElDOzs7Ozt3REFNUmhELFNBQVM2QyxRQUFRLENBQUNsQixNQUFNLEdBQUcsbUJBQzFCLDhEQUFDcEI7NERBQUtELFdBQVU7O2dFQUFnRztnRUFDNUdOLFNBQVM2QyxRQUFRLENBQUNsQixNQUFNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FLcEMxRCw2QkFDQyw4REFBQ3VFO2dEQUFHbEMsV0FBVTswREFDWiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3dEQUNadkMsK0JBQ0MsOERBQUNnRDs0REFDQ0MsU0FBUyxDQUFDSztnRUFDUkEsRUFBRTRCLGVBQWU7Z0VBQ2pCbEYsY0FBY2lDOzREQUNoQjs0REFDQU0sV0FBVTs0REFDVjRDLE9BQU07OzhFQUVOLDhEQUFDMUM7b0VBQUlGLFdBQVU7b0VBQVVHLE1BQUs7b0VBQWVDLFNBQVE7O3NGQUNuRCw4REFBQ0M7NEVBQUtFLEdBQUU7Ozs7OztzRkFDUiw4REFBQ0Y7NEVBQUtDLFVBQVM7NEVBQVVDLEdBQUU7NEVBQTBJQyxVQUFTOzs7Ozs7Ozs7Ozs7Z0VBQzFLOzs7Ozs7O3dEQUlUaEQsaUNBQ0MsOERBQUNpRDs0REFDQ0MsU0FBUyxDQUFDSztnRUFDUkEsRUFBRTRCLGVBQWU7Z0VBQ2pCbkYsZ0JBQWdCa0M7NERBQ2xCOzREQUNBTSxXQUFVOzREQUNWNEMsT0FBTTs7OEVBRU4sOERBQUMxQztvRUFBSUYsV0FBVTtvRUFBVUcsTUFBSztvRUFBZUMsU0FBUTs4RUFDbkQsNEVBQUNDO3dFQUFLRSxHQUFFOzs7Ozs7Ozs7OztnRUFDSjs7Ozs7Ozt3REFJVGpELGdDQUNDLDhEQUFDbUQ7NERBQ0NDLFNBQVMsQ0FBQ0s7Z0VBQ1JBLEVBQUU0QixlQUFlO2dFQUNqQnJGLGVBQWVvQzs0REFDakI7NERBQ0FNLFdBQVU7c0VBQ1g7Ozs7Ozt3REFJRnpDLGtDQUNDLDhEQUFDa0Q7NERBQ0NDLFNBQVMsQ0FBQ0s7Z0VBQ1JBLEVBQUU0QixlQUFlO2dFQUNqQixJQUFJRSxRQUFRLG9DQUFvQztvRUFDOUN0RixpQkFBaUJtQyxTQUFTUCxFQUFFO2dFQUM5Qjs0REFDRjs0REFDQWEsV0FBVTtzRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQXhHSk4sU0FBU1AsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF3SHBDO0dBeFdNL0I7O1FBa0JBRCxrRUFBZ0JBOzs7S0FsQmhCQztBQTBXTiwrREFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9DdXN0b21lckxpc3QudHN4P2RiMzMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiDZhdmD2YjZhiDZgtin2KbZhdipINin2YTYudmF2YTYp9ihINmF2Lkg2KfZhNio2K3YqyDZiNin2YTYqti12YHZitipXG4gKi9cblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDbGllbnQgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB7IHVzZUN1c3RvbWVyU3RvcmUgfSBmcm9tICdAL3N0b3JlL2N1c3RvbWVyU3RvcmUnO1xuXG5pbnRlcmZhY2UgQ3VzdG9tZXJMaXN0UHJvcHMge1xuICBvblNlbGVjdEN1c3RvbWVyPzogKGN1c3RvbWVyOiBDbGllbnQpID0+IHZvaWQ7XG4gIG9uRWRpdEN1c3RvbWVyPzogKGN1c3RvbWVyOiBDbGllbnQpID0+IHZvaWQ7XG4gIG9uRGVsZXRlQ3VzdG9tZXI/OiAoY3VzdG9tZXJJZDogc3RyaW5nKSA9PiB2b2lkO1xuICBvbkNyZWF0ZUludm9pY2U/OiAoY3VzdG9tZXI6IENsaWVudCkgPT4gdm9pZDtcbiAgb25WaWV3RGV0YWlscz86IChjdXN0b21lcjogQ2xpZW50KSA9PiB2b2lkO1xuICBzZWxlY3RhYmxlPzogYm9vbGVhbjtcbiAgc2hvd0FjdGlvbnM/OiBib29sZWFuO1xufVxuXG5jb25zdCBDdXN0b21lckxpc3Q6IFJlYWN0LkZDPEN1c3RvbWVyTGlzdFByb3BzPiA9ICh7XG4gIG9uU2VsZWN0Q3VzdG9tZXIsXG4gIG9uRWRpdEN1c3RvbWVyLFxuICBvbkRlbGV0ZUN1c3RvbWVyLFxuICBvbkNyZWF0ZUludm9pY2UsXG4gIG9uVmlld0RldGFpbHMsXG4gIHNlbGVjdGFibGUgPSBmYWxzZSxcbiAgc2hvd0FjdGlvbnMgPSB0cnVlLFxufSkgPT4ge1xuICBjb25zdCB7XG4gICAgY3VzdG9tZXJzLFxuICAgIHNlYXJjaFF1ZXJ5LFxuICAgIHNldFNlYXJjaFF1ZXJ5LFxuICAgIHNlYXJjaEN1c3RvbWVycyxcbiAgICBpc0xvYWRpbmcsXG4gICAgZXJyb3IsXG4gICAgbG9hZEN1c3RvbWVycyxcbiAgICBjbGVhckVycm9yLFxuICB9ID0gdXNlQ3VzdG9tZXJTdG9yZSgpO1xuXG4gIGNvbnN0IFtmaWx0ZXJlZEN1c3RvbWVycywgc2V0RmlsdGVyZWRDdXN0b21lcnNdID0gdXNlU3RhdGU8Q2xpZW50W10+KFtdKTtcbiAgY29uc3QgW3NvcnRCeSwgc2V0U29ydEJ5XSA9IHVzZVN0YXRlPCduYW1lJyB8ICdjb2RlJyB8ICdkYXRlJz4oJ25hbWUnKTtcbiAgY29uc3QgW3NvcnRPcmRlciwgc2V0U29ydE9yZGVyXSA9IHVzZVN0YXRlPCdhc2MnIHwgJ2Rlc2MnPignYXNjJyk7XG4gIGNvbnN0IFtzZWxlY3RlZEN1c3RvbWVyLCBzZXRTZWxlY3RlZEN1c3RvbWVyXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZEN1c3RvbWVycygpO1xuICB9LCBbbG9hZEN1c3RvbWVyc10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gc2VhcmNoUXVlcnkgPyBzZWFyY2hDdXN0b21lcnMoc2VhcmNoUXVlcnkpIDogY3VzdG9tZXJzO1xuICAgIFxuICAgIC8vINiq2LHYqtmK2Kgg2KfZhNmG2KrYp9im2KxcbiAgICBmaWx0ZXJlZCA9IFsuLi5maWx0ZXJlZF0uc29ydCgoYSwgYikgPT4ge1xuICAgICAgbGV0IGFWYWx1ZTogc3RyaW5nO1xuICAgICAgbGV0IGJWYWx1ZTogc3RyaW5nO1xuICAgICAgXG4gICAgICBzd2l0Y2ggKHNvcnRCeSkge1xuICAgICAgICBjYXNlICdjb2RlJzpcbiAgICAgICAgICBhVmFsdWUgPSBhLmNsaWVudENvZGUgfHwgJyc7XG4gICAgICAgICAgYlZhbHVlID0gYi5jbGllbnRDb2RlIHx8ICcnO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdkYXRlJzpcbiAgICAgICAgICBhVmFsdWUgPSBhLmlkIHx8ICcnO1xuICAgICAgICAgIGJWYWx1ZSA9IGIuaWQgfHwgJyc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgYVZhbHVlID0gYS5uYW1lO1xuICAgICAgICAgIGJWYWx1ZSA9IGIubmFtZTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgY29uc3QgY29tcGFyaXNvbiA9IGFWYWx1ZS5sb2NhbGVDb21wYXJlKGJWYWx1ZSwgJ2FyJyk7XG4gICAgICByZXR1cm4gc29ydE9yZGVyID09PSAnYXNjJyA/IGNvbXBhcmlzb24gOiAtY29tcGFyaXNvbjtcbiAgICB9KTtcbiAgICBcbiAgICBzZXRGaWx0ZXJlZEN1c3RvbWVycyhmaWx0ZXJlZCk7XG4gIH0sIFtjdXN0b21lcnMsIHNlYXJjaFF1ZXJ5LCBzb3J0QnksIHNvcnRPcmRlciwgc2VhcmNoQ3VzdG9tZXJzXSk7XG5cbiAgY29uc3QgaGFuZGxlU29ydCA9IChmaWVsZDogJ25hbWUnIHwgJ2NvZGUnIHwgJ2RhdGUnKSA9PiB7XG4gICAgaWYgKHNvcnRCeSA9PT0gZmllbGQpIHtcbiAgICAgIHNldFNvcnRPcmRlcihzb3J0T3JkZXIgPT09ICdhc2MnID8gJ2Rlc2MnIDogJ2FzYycpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTb3J0QnkoZmllbGQpO1xuICAgICAgc2V0U29ydE9yZGVyKCdhc2MnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0Q3VzdG9tZXIgPSAoY3VzdG9tZXI6IENsaWVudCkgPT4ge1xuICAgIGlmIChzZWxlY3RhYmxlKSB7XG4gICAgICBzZXRTZWxlY3RlZEN1c3RvbWVyKGN1c3RvbWVyLmlkIHx8IG51bGwpO1xuICAgICAgb25TZWxlY3RDdXN0b21lcj8uKGN1c3RvbWVyKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0Q29udGFjdE1ldGhvZEljb24gPSAobWV0aG9kOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKG1ldGhvZCkge1xuICAgICAgY2FzZSAnd2hhdHNhcHAnOlxuICAgICAgICByZXR1cm4gJ/Cfk7EnO1xuICAgICAgY2FzZSAnbWV0YSc6XG4gICAgICAgIHJldHVybiAn8J+SrCc7XG4gICAgICBjYXNlICdtZWV0aW5nJzpcbiAgICAgICAgcmV0dXJuICfwn6SdJztcbiAgICAgIGNhc2UgJ3Bob25lJzpcbiAgICAgICAgcmV0dXJuICfwn5OeJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAn8J+Tnic7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldENhdGVnb3J5Q29sb3IgPSAoY2F0ZWdvcnk6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoY2F0ZWdvcnkpIHtcbiAgICAgIGNhc2UgJ0EnOlxuICAgICAgICByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCc7XG4gICAgICBjYXNlICdCJzpcbiAgICAgICAgcmV0dXJuICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCc7XG4gICAgICBjYXNlICdDJzpcbiAgICAgICAgcmV0dXJuICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xuICAgIH1cbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMyB0ZXh0LWdyYXktNjAwXCI+2KzYp9ix2Yog2KrYrdmF2YrZhCDYp9mE2LnZhdmE2KfYoS4uLjwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcmVkLTUwMCBtbC0yXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2ek04LjcwNyA3LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNEw4LjU4NiAxMGwtMS4yOTMgMS4yOTNhMSAxIDAgMTAxLjQxNCAxLjQxNEwxMCAxMS40MTRsMS4yOTMgMS4yOTNhMSAxIDAgMDAxLjQxNC0xLjQxNEwxMS40MTQgMTBsMS4yOTMtMS4yOTNhMSAxIDAgMDAtMS40MTQtMS40MTRMMTAgOC41ODYgOC43MDcgNy4yOTN6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDBcIj57ZXJyb3J9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2NsZWFyRXJyb3J9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtODAwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDinJVcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgey8qINi02LHZiti3INin2YTYqNit2Ksg2YjYp9mE2KrYtdmB2YrYqSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00XCI+XG4gICAgICAgICAgey8qINin2YTYqNit2KsgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC15LTAgcmlnaHQtMCBwci0zIGZsZXggaXRlbXMtY2VudGVyIHBvaW50ZXItZXZlbnRzLW5vbmVcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTQwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTggNGE0IDQgMCAxMDAgOCA0IDQgMCAwMDAtOHpNMiA4YTYgNiAwIDExMTAuODkgMy40NzZsNC44MTcgNC44MTdhMSAxIDAgMDEtMS40MTQgMS40MTRsLTQuODE2LTQuODE2QTYgNiAwIDAxMiA4elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9mE2KjYrdirINio2KfZhNin2LPZhdiMINin2YTZh9in2KrZgdiMINij2Ygg2YPZiNivINin2YTYudmF2YrZhC4uLlwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHByLTEwIHBsLTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiDYrtmK2KfYsdin2Kog2KfZhNiq2LHYqtmK2KggKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtzb3J0Qnl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U29ydEJ5KGUudGFyZ2V0LnZhbHVlIGFzICduYW1lJyB8ICdjb2RlJyB8ICdkYXRlJyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5hbWVcIj7Yqtix2KrZitioINio2KfZhNin2LPZhTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY29kZVwiPtiq2LHYqtmK2Kgg2KjYp9mE2YPZiNivPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJkYXRlXCI+2KrYsdiq2YrYqCDYqNin2YTYqtin2LHZitiuPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTb3J0T3JkZXIoc29ydE9yZGVyID09PSAnYXNjJyA/ICdkZXNjJyA6ICdhc2MnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtzb3J0T3JkZXIgPT09ICdhc2MnID8gJ+KGkScgOiAn4oaTJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiDYpdit2LXYp9im2YrYp9iqINiz2LHZiti52KkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICA8c3Bhbj7Ypdis2YXYp9mE2Yog2KfZhNi52YXZhNin2KE6IDxzdHJvbmcgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPntjdXN0b21lcnMubGVuZ3RofTwvc3Ryb25nPjwvc3Bhbj5cbiAgICAgICAgICA8c3Bhbj7Yp9mE2YbYqtin2KbYrCDYp9mE2YXYudix2YjYttipOiA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwXCI+e2ZpbHRlcmVkQ3VzdG9tZXJzLmxlbmd0aH08L3N0cm9uZz48L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDZgtin2KbZhdipINin2YTYudmF2YTYp9ihICovfVxuICAgICAge2ZpbHRlcmVkQ3VzdG9tZXJzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNyAyMGg1di0yYTMgMyAwIDAwLTUuMzU2LTEuODU3TTE3IDIwSDdtMTAgMHYtMmMwLS42NTYtLjEyNi0xLjI4My0uMzU2LTEuODU3TTcgMjBIMnYtMmEzIDMgMCAwMTUuMzU2LTEuODU3TTcgMjB2LTJjMC0uNjU2LjEyNi0xLjI4My4zNTYtMS44NTdtMCAwYTUuMDAyIDUuMDAyIDAgMDE5LjI4OCAwTTE1IDdhMyAzIDAgMTEtNiAwIDMgMyAwIDAxNiAwem02IDNhMiAyIDAgMTEtNCAwIDIgMiAwIDAxNCAwek03IDEwYTIgMiAwIDExLTQgMCAyIDIgMCAwMTQgMHpcIi8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+2YTYpyDYqtmI2KzYryDYudmF2YTYp9ihPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICB7c2VhcmNoUXVlcnkgPyAn2YTZhSDZitiq2YUg2KfZhNi52KvZiNixINi52YTZiSDYudmF2YTYp9ihINmF2LfYp9io2YLZitmGINmE2KjYrdir2YMnIDogJ9mE2YUg2YrYqtmFINil2LbYp9mB2Kkg2KPZiiDYudmF2YTYp9ihINio2LnYryd9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkgOiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgIDx0aCBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ25hbWUnKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg2KfYs9mFINin2YTYudmF2YrZhFxuICAgICAgICAgICAgICAgICAgICB7c29ydEJ5ID09PSAnbmFtZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTFcIj57c29ydE9yZGVyID09PSAnYXNjJyA/ICfihpEnIDogJ+KGkyd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ2NvZGUnKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg2YPZiNivINin2YTYudmF2YrZhFxuICAgICAgICAgICAgICAgICAgICB7c29ydEJ5ID09PSAnY29kZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTFcIj57c29ydE9yZGVyID09PSAnYXNjJyA/ICfihpEnIDogJ+KGkyd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgINix2YLZhSDYp9mE2YfYp9iq2YFcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICDYp9mE2KrYtdmG2YrZgVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgINin2YTYqtmI2KfYtdmEXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LXJpZ2h0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAg2KfZhNiu2K/Zhdin2KpcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICB7c2hvd0FjdGlvbnMgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgINin2YTYpdis2LHYp9ih2KfYqlxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAge2ZpbHRlcmVkQ3VzdG9tZXJzLm1hcCgoY3VzdG9tZXIpID0+IChcbiAgICAgICAgICAgICAgICAgIDx0clxuICAgICAgICAgICAgICAgICAgICBrZXk9e2N1c3RvbWVyLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgc2VsZWN0YWJsZSAmJiBzZWxlY3RlZEN1c3RvbWVyID09PSBjdXN0b21lci5pZCA/ICdiZy1ibHVlLTUwJyA6ICcnXG4gICAgICAgICAgICAgICAgICAgIH0gJHtzZWxlY3RhYmxlID8gJ2N1cnNvci1wb2ludGVyJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNlbGVjdEN1c3RvbWVyKGN1c3RvbWVyKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntjdXN0b21lci5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWVyLmJ1c2luZXNzTmFtZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2N1c3RvbWVyLmJ1c2luZXNzTmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMi41IHB5LTAuNSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y3VzdG9tZXIuY2xpZW50Q29kZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWVyLnBob25lfVxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRDYXRlZ29yeUNvbG9yKGN1c3RvbWVyLmluaXRpYWxDYXRlZ29yeSl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICDZgdim2Kkge2N1c3RvbWVyLmluaXRpYWxDYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTFcIj57Z2V0Q29udGFjdE1ldGhvZEljb24oY3VzdG9tZXIuY29udGFjdE1ldGhvZCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWVyLmNvbnRhY3RNZXRob2R9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2N1c3RvbWVyLnNlcnZpY2VzLnNsaWNlKDAsIDIpLm1hcCgoc2VydmljZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZXJ2aWNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjdXN0b21lci5zZXJ2aWNlcy5sZW5ndGggPiAyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgK3tjdXN0b21lci5zZXJ2aWNlcy5sZW5ndGggLSAyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICB7c2hvd0FjdGlvbnMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7b25WaWV3RGV0YWlscyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25WaWV3RGV0YWlscyhjdXN0b21lcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCBob3ZlcjpiZy1ibHVlLTIwMCBweC0zIHB5LTEgcm91bmRlZC1tZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi2LnYsdi2INiq2YHYp9i12YrZhCDYp9mE2LnZhdmK2YQg2KfZhNi02KfZhdmE2KlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtM1wiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTAgMTJhMiAyIDAgMTAwLTQgMiAyIDAgMDAwIDR6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTS40NTggMTBDMS43MzIgNS45NDMgNS41MjIgMyAxMCAzczguMjY4IDIuOTQzIDkuNTQyIDdjLTEuMjc0IDQuMDU3LTUuMDY0IDctOS41NDIgN1MxLjczMiAxNC4wNTcuNDU4IDEwek0xNCAxMGE0IDQgMCAxMS04IDAgNCA0IDAgMDE4IDB6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDYqtmB2KfYtdmK2YRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge29uQ3JlYXRlSW52b2ljZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DcmVhdGVJbnZvaWNlKGN1c3RvbWVyKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi03MDAgaG92ZXI6YmctZ3JlZW4tMjAwIHB4LTMgcHktMSByb3VuZGVkLW1kIHRleHQteHMgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLYpdmG2LTYp9ihINmB2KfYqtmI2LHYqSDYrNiv2YrYr9ipINmE2YfYsNinINin2YTYudmF2YrZhFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk00IDRhMiAyIDAgMDAtMiAydjhhMiAyIDAgMDAyIDJoMTJhMiAyIDAgMDAyLTJWNmEyIDIgMCAwMC0yLTJINHptMiA2YTEgMSAwIDAxMS0xaDZhMSAxIDAgMTEwIDJIN2ExIDEgMCAwMS0xLTF6bTEgM2ExIDEgMCAxMDAgMmg2YTEgMSAwIDEwMC0ySDd6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDZgdin2KrZiNix2KlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge29uRWRpdEN1c3RvbWVyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkVkaXRDdXN0b21lcihjdXN0b21lcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDYqti52K/ZitmEXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtvbkRlbGV0ZUN1c3RvbWVyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoY29uZmlybSgn2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINit2LDZgSDZh9iw2Kcg2KfZhNi52YXZitmE2J8nKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVsZXRlQ3VzdG9tZXIoY3VzdG9tZXIuaWQhKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgINit2LDZgVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDdXN0b21lckxpc3Q7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUN1c3RvbWVyU3RvcmUiLCJDdXN0b21lckxpc3QiLCJvblNlbGVjdEN1c3RvbWVyIiwib25FZGl0Q3VzdG9tZXIiLCJvbkRlbGV0ZUN1c3RvbWVyIiwib25DcmVhdGVJbnZvaWNlIiwib25WaWV3RGV0YWlscyIsInNlbGVjdGFibGUiLCJzaG93QWN0aW9ucyIsImN1c3RvbWVycyIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJzZWFyY2hDdXN0b21lcnMiLCJpc0xvYWRpbmciLCJlcnJvciIsImxvYWRDdXN0b21lcnMiLCJjbGVhckVycm9yIiwiZmlsdGVyZWRDdXN0b21lcnMiLCJzZXRGaWx0ZXJlZEN1c3RvbWVycyIsInNvcnRCeSIsInNldFNvcnRCeSIsInNvcnRPcmRlciIsInNldFNvcnRPcmRlciIsInNlbGVjdGVkQ3VzdG9tZXIiLCJzZXRTZWxlY3RlZEN1c3RvbWVyIiwiZmlsdGVyZWQiLCJzb3J0IiwiYSIsImIiLCJhVmFsdWUiLCJiVmFsdWUiLCJjbGllbnRDb2RlIiwiaWQiLCJuYW1lIiwiY29tcGFyaXNvbiIsImxvY2FsZUNvbXBhcmUiLCJoYW5kbGVTb3J0IiwiZmllbGQiLCJoYW5kbGVTZWxlY3RDdXN0b21lciIsImN1c3RvbWVyIiwiZ2V0Q29udGFjdE1ldGhvZEljb24iLCJtZXRob2QiLCJnZXRDYXRlZ29yeUNvbG9yIiwiY2F0ZWdvcnkiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJidXR0b24iLCJvbkNsaWNrIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInNlbGVjdCIsIm9wdGlvbiIsInN0cm9uZyIsImxlbmd0aCIsInN0cm9rZSIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiaDMiLCJwIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsIm1hcCIsInRkIiwiYnVzaW5lc3NOYW1lIiwicGhvbmUiLCJpbml0aWFsQ2F0ZWdvcnkiLCJjb250YWN0TWV0aG9kIiwic2VydmljZXMiLCJzbGljZSIsInNlcnZpY2UiLCJpbmRleCIsInN0b3BQcm9wYWdhdGlvbiIsInRpdGxlIiwiY29uZmlybSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CustomerList.tsx\n"));

/***/ })

});