"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoiceForm.tsx":
/*!****************************************!*\
  !*** ./src/components/InvoiceForm.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _services_ConsultationService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./services/ConsultationService */ \"(app-pages-browser)/./src/components/services/ConsultationService.tsx\");\n/* harmony import */ var _services_PatternService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./services/PatternService */ \"(app-pages-browser)/./src/components/services/PatternService.tsx\");\n/* harmony import */ var _services_PhotographyService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./services/PhotographyService */ \"(app-pages-browser)/./src/components/services/PhotographyService.tsx\");\n/* harmony import */ var _services_ShippingService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./services/ShippingService */ \"(app-pages-browser)/./src/components/services/ShippingService.tsx\");\n/* harmony import */ var _services_PatternPrintingService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./services/PatternPrintingService */ \"(app-pages-browser)/./src/components/services/PatternPrintingService.tsx\");\n/* harmony import */ var _services_ManufacturingService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./services/ManufacturingService */ \"(app-pages-browser)/./src/components/services/ManufacturingService.tsx\");\n/* harmony import */ var _services_ProductsService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./services/ProductsService */ \"(app-pages-browser)/./src/components/services/ProductsService.tsx\");\n/* harmony import */ var _services_MarketingService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./services/MarketingService */ \"(app-pages-browser)/./src/components/services/MarketingService.tsx\");\n/* harmony import */ var _services_SamplesService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./services/SamplesService */ \"(app-pages-browser)/./src/components/services/SamplesService.tsx\");\n/* harmony import */ var _services_RawPurchasesService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./services/RawPurchasesService */ \"(app-pages-browser)/./src/components/services/RawPurchasesService.tsx\");\n/* harmony import */ var _PaymentCalculator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./PaymentCalculator */ \"(app-pages-browser)/./src/components/PaymentCalculator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst InvoiceForm = (param)=>{\n    let { client, onSubmit } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { setCurrentInvoice, calculateTotal } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore)();\n    const [totalAmount, setTotalAmount] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [paidAmount, setPaidAmount] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const initialValues = {\n        client,\n        services: {},\n        total: 0,\n        paymentMethod: \"\",\n        requiresShipping: false,\n        shippingAddress: \"\",\n        notes: \"\",\n        createdAt: new Date().toISOString(),\n        // Initialize service objects\n        consultation: {\n            id: \"\",\n            serviceType: \"consultation\",\n            enabled: false,\n            hours: 0,\n            topic: \"\",\n            dateTime: \"\",\n            cost: 0,\n            paymentMethod: \"\",\n            notes: \"\"\n        },\n        pattern: {\n            id: \"\",\n            serviceType: \"pattern\",\n            enabled: false,\n            models: []\n        },\n        pattern_printing: {\n            id: \"\",\n            serviceType: \"pattern_printing\",\n            enabled: false,\n            files: []\n        },\n        manufacturing: {\n            id: \"\",\n            serviceType: \"manufacturing\",\n            enabled: false,\n            models: []\n        },\n        shipping: {\n            id: \"\",\n            serviceType: \"shipping\",\n            enabled: false,\n            clientName: client.name,\n            phone: client.phone,\n            contactMethod: client.contactMethod,\n            salesperson: client.salesperson,\n            item: \"\",\n            quantity: 0,\n            unitPrice: 0,\n            total: 0\n        },\n        marketing: {\n            id: \"\",\n            serviceType: \"marketing\",\n            enabled: false,\n            description: \"\",\n            cost: 0,\n            duration: \"\"\n        },\n        photography: {\n            id: \"\",\n            serviceType: \"photography\",\n            enabled: false,\n            location: \"indoor\",\n            withModel: false,\n            withEditing: false,\n            cost: 0\n        },\n        products: {\n            id: \"\",\n            serviceType: \"products\",\n            enabled: false,\n            items: []\n        },\n        raw_purchases: {\n            id: \"\",\n            serviceType: \"raw_purchases\",\n            enabled: false,\n            materials: []\n        }\n    };\n    // تم تبسيط التحقق لتجنب المراجع الدائرية\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_2__.object({\n        paymentMethod: yup__WEBPACK_IMPORTED_MODULE_2__.string().required(\"طريقة الدفع مطلوبة\"),\n        notes: yup__WEBPACK_IMPORTED_MODULE_2__.string(),\n        requiresShipping: yup__WEBPACK_IMPORTED_MODULE_2__.boolean(),\n        shippingAddress: yup__WEBPACK_IMPORTED_MODULE_2__.string()\n    });\n    const handleSubmit = (values)=>{\n        // Collect enabled services\n        const enabledServices = [];\n        // قائمة الخدمات المحتملة\n        const serviceKeys = [\n            \"consultation\",\n            \"pattern\",\n            \"pattern_printing\",\n            \"manufacturing\",\n            \"samples\",\n            \"shipping\",\n            \"marketing\",\n            \"photography\",\n            \"products\",\n            \"raw_purchases\"\n        ];\n        serviceKeys.forEach((key)=>{\n            var _values_key, _values_key1;\n            if (((_values_key = values[key]) === null || _values_key === void 0 ? void 0 : _values_key.enabled) && ((_values_key1 = values[key]) === null || _values_key1 === void 0 ? void 0 : _values_key1.serviceType)) {\n                enabledServices.push(values[key]);\n            }\n        });\n        // إنشاء معرف فريد مبسط للفاتورة\n        const generateUniqueInvoiceId = ()=>{\n            const now = new Date();\n            const month = now.getMonth() + 1;\n            const year = now.getFullYear();\n            const counter = Date.now() % 1000; // آخر 3 أرقام من timestamp\n            return \"INV-\".concat(month, \"-\").concat(year, \"-\").concat(String(counter).padStart(3, \"0\"));\n        };\n        const invoice = {\n            id: generateUniqueInvoiceId(),\n            client: client,\n            services: enabledServices,\n            total: totalAmount,\n            paidAmount: paidAmount,\n            remainingAmount: totalAmount - paidAmount,\n            paymentStatus: paidAmount === 0 ? \"غير مدفوع\" : paidAmount >= totalAmount ? \"مدفوع بالكامل\" : \"مدفوع جزئياً\",\n            paymentMethod: values.paymentMethod,\n            requiresShipping: values.requiresShipping || false,\n            shippingAddress: values.shippingAddress || \"\",\n            createdAt: new Date().toISOString(),\n            notes: values.notes || \"\"\n        };\n        setCurrentInvoice(invoice);\n        onSubmit(invoice);\n    };\n    const paymentMethods = [\n        {\n            value: \"cash\",\n            label: \"نقدي\"\n        },\n        {\n            value: \"card\",\n            label: \"بطاقة\"\n        },\n        {\n            value: \"transfer\",\n            label: \"تحويل\"\n        },\n        {\n            value: \"installment\",\n            label: \"تقسيط\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-6xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-l from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-100 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 p-3 rounded-full shadow-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"إنشاء فاتورة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: \"اختر الخدمات وأدخل التفاصيل المطلوبة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-blue-600\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-800\",\n                                        children: \"معلومات العميل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-4 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"العميل:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: client.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"كود العميل:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-800 font-mono bg-gray-100 px-2 py-1 rounded text-xs\",\n                                                children: client.clientCode || \"غير محدد\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"الهاتف:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: client.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"السيلز:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-800\",\n                                                children: client.salesperson\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-blue-600 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"الخطوة 2 من 3 - تفاصيل الخدمات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_15__.Formik, {\n                initialValues: initialValues,\n                validationSchema: validationSchema,\n                onSubmit: handleSubmit,\n                children: _s1((param)=>{\n                    let { values, setFieldValue } = param;\n                    _s1();\n                    // تعبئة اسم السيلز تلقائياً في جميع الخدمات\n                    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n                        const clientSalesperson = client.salesperson;\n                        if (clientSalesperson) {\n                            // تحديث اسم السيلز في جميع الخدمات المفعلة\n                            const serviceKeys = [\n                                \"consultation\",\n                                \"pattern\",\n                                \"pattern_printing\",\n                                \"manufacturing\",\n                                \"samples\",\n                                \"shipping\",\n                                \"marketing\",\n                                \"photography\",\n                                \"products\",\n                                \"raw_purchases\"\n                            ];\n                            serviceKeys.forEach((key)=>{\n                                const service = values[key];\n                                if (service === null || service === void 0 ? void 0 : service.enabled) {\n                                    // للخدمات التي تحتوي على salesperson مباشرة\n                                    if (key === \"shipping\" || key === \"consultation\") {\n                                        setFieldValue(\"\".concat(key, \".salesperson\"), clientSalesperson);\n                                    }\n                                    // للخدمات التي تحتوي على models مع salesperson\n                                    if ((key === \"pattern\" || key === \"manufacturing\") && service.models) {\n                                        service.models.forEach((model, index)=>{\n                                            setFieldValue(\"\".concat(key, \".models.\").concat(index, \".salesperson\"), clientSalesperson);\n                                        });\n                                    }\n                                    // للخدمات التي تحتوي على files مع salesperson\n                                    if (key === \"pattern_printing\" && service.files) {\n                                        service.files.forEach((file, index)=>{\n                                            setFieldValue(\"\".concat(key, \".files.\").concat(index, \".salesperson\"), clientSalesperson);\n                                        });\n                                    }\n                                }\n                            });\n                        }\n                    }, [\n                        values,\n                        client.salesperson,\n                        setFieldValue\n                    ]);\n                    // حساب المجموع تلقائياً\n                    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n                        let calculatedTotal = 0;\n                        const serviceKeys = [\n                            \"consultation\",\n                            \"pattern\",\n                            \"pattern_printing\",\n                            \"manufacturing\",\n                            \"samples\",\n                            \"shipping\",\n                            \"marketing\",\n                            \"photography\",\n                            \"products\",\n                            \"raw_purchases\"\n                        ];\n                        serviceKeys.forEach((key)=>{\n                            const service = values[key];\n                            if (service === null || service === void 0 ? void 0 : service.enabled) {\n                                // خدمة الاستشارات\n                                if (key === \"consultation\") {\n                                    calculatedTotal += Number(service.cost) || 0;\n                                }\n                                // خدمة الباترون\n                                if (key === \"pattern\" && service.models) {\n                                    service.models.forEach((model)=>{\n                                        calculatedTotal += Number(model.finalAmount) || Number(model.cost) || 0;\n                                    });\n                                }\n                                // خدمة طباعة الباترون\n                                if (key === \"pattern_printing\" && service.files) {\n                                    service.files.forEach((file)=>{\n                                        calculatedTotal += Number(file.cost) || 0;\n                                    });\n                                }\n                                // خدمة التصنيع\n                                if (key === \"manufacturing\" && service.models) {\n                                    service.models.forEach((model)=>{\n                                        const patternPrice = Number(model.patternPrice) || 0;\n                                        const samplePrice = Number(model.samplePrice) || 0;\n                                        const manufacturingPrice = Number(model.manufacturingPrice) || 0;\n                                        calculatedTotal += patternPrice + samplePrice + manufacturingPrice;\n                                    });\n                                }\n                                // خدمة العينات\n                                if (key === \"samples\" && service.samples) {\n                                    service.samples.forEach((sample)=>{\n                                        calculatedTotal += Number(sample.cost) || 0;\n                                    });\n                                }\n                                // خدمة الشحن\n                                if (key === \"shipping\") {\n                                    calculatedTotal += Number(service.total) || 0;\n                                }\n                                // خدمة التسويق\n                                if (key === \"marketing\") {\n                                    calculatedTotal += Number(service.cost) || 0;\n                                }\n                                // خدمة التصوير\n                                if (key === \"photography\") {\n                                    calculatedTotal += Number(service.cost) || 0;\n                                }\n                                // خدمة المنتجات\n                                if (key === \"products\" && service.items) {\n                                    let productsTotal = 0;\n                                    service.items.forEach((item)=>{\n                                        productsTotal += Number(item.total) || 0;\n                                    });\n                                    // طرح الخصم إن وجد\n                                    if (service.discountAmount) {\n                                        productsTotal -= Number(service.discountAmount) || 0;\n                                    }\n                                    calculatedTotal += Math.max(0, productsTotal);\n                                }\n                                // خدمة المشتريات الخام\n                                if (key === \"raw_purchases\" && service.materials) {\n                                    service.materials.forEach((material)=>{\n                                        calculatedTotal += Number(material.total) || 0;\n                                    });\n                                }\n                            }\n                        });\n                        setTotalAmount(Math.max(0, calculatedTotal));\n                    }, [\n                        values\n                    ]);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_15__.Form, {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-green-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"تفاصيل الخدمات المطلوبة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"أدخل تفاصيل كل خدمة مطلوبة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-8\",\n                                        children: [\n                                            client.services.includes(\"consultation\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_ConsultationService__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    namePrefix: \"consultation\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"pattern\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_PatternService__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    namePrefix: \"pattern\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"photography\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_PhotographyService__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    namePrefix: \"photography\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"shipping\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_ShippingService__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    namePrefix: \"shipping\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"pattern_printing\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_PatternPrintingService__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    namePrefix: \"pattern_printing\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"manufacturing\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_ManufacturingService__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    namePrefix: \"manufacturing\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"marketing\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_MarketingService__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    namePrefix: \"marketing\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"products\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_ProductsService__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    namePrefix: \"products\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"samples\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_SamplesService__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    namePrefix: \"samples\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            client.services.includes(\"raw_purchases\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"service-section animate-fadeIn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_services_RawPurchasesService__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    namePrefix: \"raw_purchases\",\n                                                    values: values,\n                                                    setFieldValue: setFieldValue\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-yellow-50 to-amber-50 px-6 py-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-yellow-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"حاسبة المدفوعات\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"احسب المبالغ المدفوعة والمتبقية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaymentCalculator__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            totalAmount: totalAmount,\n                                            paidAmount: paidAmount,\n                                            onPaidAmountChange: setPaidAmount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-l from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5 text-purple-600\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"معلومات الدفع والشحن\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \" طريقة الدفع الرئيسية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    fillRule: \"evenodd\",\n                                                                                    d: \"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z\",\n                                                                                    clipRule: \"evenodd\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        name: \"paymentMethod\",\n                                                                        value: values.paymentMethod,\n                                                                        onChange: (e)=>setFieldValue(\"paymentMethod\", e.target.value),\n                                                                        className: \"form-input-enhanced pr-10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"اختر طريقة الدفع\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            paymentMethods.map((method)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: method.value,\n                                                                                    children: method.label\n                                                                                }, method.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, undefined))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"form-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label-enhanced\",\n                                                                children: \"خيارات الشحن\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center gap-3 p-4 border-2 border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: values.requiresShipping,\n                                                                        onChange: (e)=>setFieldValue(\"requiresShipping\", e.target.checked),\n                                                                        className: \"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-5 h-5 text-gray-500\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 20 20\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                        lineNumber: 557,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                        lineNumber: 558,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                lineNumber: 556,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                                children: \"يتطلب شحن\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                                lineNumber: 560,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 555,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            values.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group animate-fadeIn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"عنوان الشحن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-3 right-3 pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"shippingAddress\",\n                                                                value: values.shippingAddress,\n                                                                onChange: (e)=>setFieldValue(\"shippingAddress\", e.target.value),\n                                                                rows: 3,\n                                                                className: \"form-input-enhanced pr-10\",\n                                                                placeholder: \"أدخل عنوان الشحن التفصيلي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"form-field group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label-enhanced\",\n                                                        children: \"ملاحظات إضافية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-3 right-3 pointer-events-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-400 group-focus-within:text-purple-500 transition-colors\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"notes\",\n                                                                value: values.notes,\n                                                                onChange: (e)=>setFieldValue(\"notes\", e.target.value),\n                                                                rows: 3,\n                                                                className: \"form-input-enhanced pr-10\",\n                                                                placeholder: \"أي ملاحظات أو تعليمات خاصة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center items-center gap-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary-enhanced group w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"إنشاء الفاتورة ومعاينتها\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"btn-secondary px-6 py-3 w-full sm:w-auto\",\n                                        onClick: ()=>window.history.back(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 ml-2\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"العودة للخطوة السابقة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, undefined);\n                }, \"3ubReDTFssvu4DHeldAg55cW/CI=\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoiceForm.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoiceForm, \"lTO4W4jh6JUZLmt4jUFvVtCZqQ8=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_3__.useInvoiceStore\n    ];\n});\n_c = InvoiceForm;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoiceForm);\nvar _c;\n$RefreshReg$(_c, \"InvoiceForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoiceForm.tsx\n"));

/***/ })

});