/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/data-protection/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/DataProtectionDashboard.tsx */ \"(app-pages-browser)/./src/components/DataProtectionDashboard.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNDQUQlMjBVU0VSJTIwWDY0JTVDRG9jdW1lbnRzJTVDYXVnbWVudC1wcm9qZWN0cyU1QzIzMSU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNEYXRhUHJvdGVjdGlvbkRhc2hib2FyZC50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2MyYTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxDQUQgVVNFUiBYNjRcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcMjMxXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXERhdGFQcm90ZWN0aW9uRGFzaGJvYXJkLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DataProtectionDashboard.tsx":
/*!****************************************************!*\
  !*** ./src/components/DataProtectionDashboard.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/dataProtection */ \"(app-pages-browser)/./src/utils/dataProtection.ts\");\n/* harmony import */ var _utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst DataProtectionDashboard = ()=>{\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [storageHealth, setStorageHealth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث البيانات\n    const refreshData = ()=>{\n        const currentStatus = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.getStatus();\n        const health = (0,_utils_secureStorage__WEBPACK_IMPORTED_MODULE_3__.checkStorageHealth)();\n        setStatus(currentStatus);\n        setStorageHealth(health);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        refreshData();\n        // تحديث كل 30 ثانية\n        const interval = setInterval(refreshData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // إنشاء نسخة احتياطية يدوية\n    const handleManualBackup = async ()=>{\n        setIsLoading(true);\n        try {\n            const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.createManualBackup();\n            if (result.success) {\n                alert(\"✅ تم إنشاء النسخة الاحتياطية بنجاح\");\n            } else {\n                alert(\"❌ فشل في إنشاء النسخة الاحتياطية: \" + result.message);\n            }\n            refreshData();\n        } catch (error) {\n            alert(\"❌ خطأ غير متوقع\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // استرداد البيانات\n    const handleRestore = async ()=>{\n        if (!confirm(\"هل أنت متأكد من استرداد البيانات من النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.\")) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.restoreData();\n            if (result.success) {\n                alert(\"✅ تم استرداد البيانات بنجاح. سيتم إعادة تحميل الصفحة.\");\n            } else {\n                alert(\"❌ فشل في استرداد البيانات: \" + result.message);\n            }\n        } catch (error) {\n            alert(\"❌ خطأ غير متوقع\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // تصدير البيانات\n    const handleExport = ()=>{\n        const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.exportData();\n        if (result.success && result.data) {\n            const blob = new Blob([\n                result.data\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"infapapp-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n            alert(\"✅ تم تصدير البيانات بنجاح\");\n        } else {\n            alert(\"❌ فشل في تصدير البيانات: \" + result.message);\n        }\n    };\n    // استيراد البيانات\n    const handleImport = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".json\";\n        input.onchange = (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (file) {\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    try {\n                        var _e_target;\n                        const data = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                        const result = _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.importData(data);\n                        if (result.success) {\n                            alert(\"✅ تم استيراد البيانات بنجاح\");\n                            refreshData();\n                        } else {\n                            alert(\"❌ فشل في استيراد البيانات: \" + result.message);\n                        }\n                    } catch (error) {\n                        alert(\"❌ ملف غير صحيح\");\n                    }\n                };\n                reader.readAsText(file);\n            }\n        };\n        input.click();\n    };\n    // تبديل النسخ الاحتياطي التلقائي\n    const toggleAutoBackup = ()=>{\n        const newState = !(status === null || status === void 0 ? void 0 : status.autoBackupEnabled);\n        _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.toggleAutoBackup(newState);\n        refreshData();\n    };\n    // حل التنبيه\n    const resolveAlert = (alertId)=>{\n        _utils_dataProtection__WEBPACK_IMPORTED_MODULE_2__.dataProtection.resolveAlert(alertId);\n        refreshData();\n    };\n    if (!status || !storageHealth) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 bg-white rounded-lg shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"جاري تحميل بيانات النظام...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: \"\\uD83D\\uDEE1️ لوحة تحكم حماية البيانات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"opacity-90\",\n                        children: \"مراقبة وحماية بيانات التطبيق مع نسخ احتياطية تلقائية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-green-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"حالة المراقبة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-green-600\",\n                                        children: status.isMonitoring ? \"\\uD83D\\uDFE2 نشط\" : \"\\uD83D\\uDD34 متوقف\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-blue-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"النسخ الاحتياطي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-blue-600\",\n                                        children: status.autoBackupEnabled ? \"\\uD83D\\uDFE2 تلقائي\" : \"\\uD83D\\uDFE1 يدوي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-purple-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"التنبيهات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-purple-600\",\n                                        children: [\n                                            status.alertsCount,\n                                            \" تنبيه\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow border-r-4 border-orange-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"حالة التخزين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold text-orange-600\",\n                                        children: storageHealth.isHealthy ? \"\\uD83D\\uDFE2 سليم\" : \"\\uD83D\\uDFE1 يحتاج انتباه\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"\\uD83D\\uDCCA معلومات التخزين\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"المساحة المستخدمة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            Math.round(storageHealth.storageUsed / 1024),\n                                            \" KB\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2 mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-600 h-2 rounded-full\",\n                                            style: {\n                                                width: \"\".concat(storageHealth.storageUsed / storageHealth.storageLimit * 100, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"النسخ الاحتياطية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            storageHealth.backupsCount,\n                                            \" نسخة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"آخر نسخة احتياطية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: status.lastBackup ? new Date(status.lastBackup).toLocaleString(\"ar-EG\") : \"لا توجد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"⚙️ أدوات التحكم\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleManualBackup,\n                                disabled: isLoading,\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n                                children: \"\\uD83D\\uDCBE نسخة احتياطية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRestore,\n                                disabled: isLoading,\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50\",\n                                children: \"\\uD83D\\uDD04 استرداد البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleExport,\n                                className: \"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\",\n                                children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleImport,\n                                className: \"bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700\",\n                                children: \"\\uD83D\\uDCE5 استيراد البيانات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: status.autoBackupEnabled,\n                                    onChange: toggleAutoBackup,\n                                    className: \"rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"تفعيل النسخ الاحتياطي التلقائي (كل 5 دقائق)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined),\n            status.alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"\\uD83D\\uDEA8 التنبيهات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: status.alerts.map((alert1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 rounded-lg border-r-4 \".concat(alert1.type === \"error\" ? \"bg-red-50 border-red-500\" : alert1.type === \"warning\" ? \"bg-yellow-50 border-yellow-500\" : \"bg-blue-50 border-blue-500\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: alert1.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: new Date(alert1.timestamp).toLocaleString(\"ar-EG\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>resolveAlert(alert1.id),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, alert1.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined),\n            storageHealth.issues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: \"⚠️ مشاكل التخزين\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: storageHealth.issues.map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-yellow-50 border-r-4 border-yellow-500 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-yellow-800\",\n                                    children: issue\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, undefined),\n                    storageHealth.recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold mb-2\",\n                                children: \"\\uD83D\\uDCA1 التوصيات:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside space-y-1\",\n                                children: storageHealth.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-gray-700\",\n                                        children: rec\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\DataProtectionDashboard.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataProtectionDashboard, \"wGkhhqAjXBZhaLr7Cu5go+Nmr6Y=\");\n_c = DataProtectionDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DataProtectionDashboard);\nvar _c;\n$RefreshReg$(_c, \"DataProtectionDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DataProtectionDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/dataProtection.ts":
/*!*************************************!*\
  !*** ./src/utils/dataProtection.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataProtection: function() { return /* binding */ dataProtection; }\n/* harmony export */ });\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secureStorage */ \"(app-pages-browser)/./src/utils/secureStorage.ts\");\n/**\n * نظام حماية البيانات والمراقبة التلقائية\n * يوفر مراقبة مستمرة ونسخ احتياطية تلقائية\n */ \nclass DataProtectionSystem {\n    /**\n   * بدء نظام الحماية\n   */ start() {\n        if (this.status.isMonitoring) {\n            console.log(\"\\uD83D\\uDEE1️ Data protection system already running\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDE80 Starting data protection system...\");\n        // بدء المراقبة\n        this.startMonitoring();\n        // بدء النسخ الاحتياطي التلقائي\n        if (this.status.autoBackupEnabled) {\n            this.startAutoBackup();\n        }\n        // فحص فوري للحالة\n        this.performHealthCheck();\n        this.status.isMonitoring = true;\n        console.log(\"✅ Data protection system started successfully\");\n    }\n    /**\n   * إيقاف نظام الحماية\n   */ stop() {\n        if (!this.status.isMonitoring) {\n            return;\n        }\n        console.log(\"\\uD83D\\uDED1 Stopping data protection system...\");\n        if (this.monitoringInterval) {\n            clearInterval(this.monitoringInterval);\n            this.monitoringInterval = null;\n        }\n        if (this.backupInterval) {\n            clearInterval(this.backupInterval);\n            this.backupInterval = null;\n        }\n        this.status.isMonitoring = false;\n        console.log(\"✅ Data protection system stopped\");\n    }\n    /**\n   * بدء المراقبة المستمرة\n   */ startMonitoring() {\n        // فحص كل دقيقة\n        this.monitoringInterval = setInterval(()=>{\n            this.performHealthCheck();\n        }, 60 * 1000);\n    }\n    /**\n   * بدء النسخ الاحتياطي التلقائي\n   */ startAutoBackup() {\n        // نسخ احتياطي كل 5 دقائق\n        this.backupInterval = setInterval(()=>{\n            this.performAutoBackup();\n        }, _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.BACKUP_INTERVAL);\n    }\n    /**\n   * تنفيذ فحص الحالة\n   */ performHealthCheck() {\n        try {\n            const health = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.checkStorageHealth)();\n            this.status.lastHealthCheck = new Date().toISOString();\n            // معالجة المشاكل المكتشفة\n            if (!health.isHealthy) {\n                health.issues.forEach((issue)=>{\n                    this.addAlert(\"warning\", \"مشكلة في النظام: \".concat(issue));\n                });\n                // إجراءات تلقائية للمشاكل الحرجة\n                if (health.storageUsed > _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n                    this.handleStorageOverflow();\n                }\n            }\n            // تنبيه إذا لم تكن هناك نسخ احتياطية\n            if (health.backupsCount === 0) {\n                this.addAlert(\"error\", \"لا توجد نسخ احتياطية - سيتم إنشاء نسخة فورية\");\n                this.performAutoBackup();\n            }\n            console.log(\"\\uD83D\\uDD0D Health check completed:\", {\n                healthy: health.isHealthy,\n                issues: health.issues.length,\n                storageUsed: Math.round(health.storageUsed / 1024) + \"KB\",\n                backups: health.backupsCount\n            });\n        } catch (error) {\n            this.addAlert(\"error\", \"فشل في فحص حالة النظام\");\n            console.error(\"❌ Health check failed:\", error);\n        }\n    }\n    /**\n   * تنفيذ نسخ احتياطي تلقائي\n   */ performAutoBackup() {\n        try {\n            // فحص إذا كانت هناك تغييرات تستدعي النسخ الاحتياطي\n            if (this.shouldCreateBackup()) {\n                const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n                if (result.success) {\n                    this.status.lastBackup = new Date().toISOString();\n                    console.log(\"\\uD83D\\uDCBE Auto backup completed successfully\");\n                } else {\n                    this.addAlert(\"error\", \"فشل النسخ الاحتياطي التلقائي: \".concat(result.message));\n                }\n            }\n        } catch (error) {\n            this.addAlert(\"error\", \"خطأ في النسخ الاحتياطي التلقائي\");\n            console.error(\"❌ Auto backup failed:\", error);\n        }\n    }\n    /**\n   * فحص إذا كان النسخ الاحتياطي مطلوب\n   */ shouldCreateBackup() {\n        // إنشاء نسخة احتياطية إذا:\n        // 1. لا توجد نسخة سابقة\n        // 2. مر أكثر من 5 دقائق على آخر نسخة\n        // 3. تم تعديل البيانات مؤخراً\n        if (!this.status.lastBackup) {\n            return true;\n        }\n        const lastBackupTime = new Date(this.status.lastBackup).getTime();\n        const now = Date.now();\n        const timeDiff = now - lastBackupTime;\n        return timeDiff > _secureStorage__WEBPACK_IMPORTED_MODULE_0__.STORAGE_CONFIG.BACKUP_INTERVAL;\n    }\n    /**\n   * معالجة امتلاء التخزين\n   */ handleStorageOverflow() {\n        try {\n            console.log(\"⚠️ Storage overflow detected, attempting cleanup...\");\n            // تنظيف النسخ الاحتياطية القديمة أولاً\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeA - timeB; // ترتيب تصاعدي (الأقدم أولاً)\n            });\n            // حذف النصف الأقدم من النسخ الاحتياطية\n            const toDelete = backupKeys.slice(0, Math.floor(backupKeys.length / 2));\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup due to storage overflow:\", key);\n            });\n            // تنظيف البيانات المؤقتة\n            const tempKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"temp-\") || key.startsWith(\"cache-\") || key.startsWith(\"draft-\"));\n            tempKeys.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed temporary data:\", key);\n            });\n            this.addAlert(\"info\", \"تم تنظيف التخزين تلقائياً لتوفير مساحة\");\n        } catch (error) {\n            this.addAlert(\"error\", \"فشل في تنظيف التخزين\");\n            console.error(\"❌ Storage cleanup failed:\", error);\n        }\n    }\n    /**\n   * إضافة تنبيه\n   */ addAlert(type, message) {\n        const alert = {\n            id: Date.now().toString(),\n            type,\n            message,\n            timestamp: new Date().toISOString(),\n            resolved: false\n        };\n        this.alerts.unshift(alert);\n        this.status.alertsCount++;\n        // الاحتفاظ بآخر 50 تنبيه فقط\n        if (this.alerts.length > 50) {\n            this.alerts = this.alerts.slice(0, 50);\n        }\n        console.log(\"\\uD83D\\uDEA8 Alert [\".concat(type.toUpperCase(), \"]: \").concat(message));\n    }\n    /**\n   * الحصول على حالة النظام\n   */ getStatus() {\n        return {\n            ...this.status,\n            alerts: this.alerts.filter((alert)=>!alert.resolved)\n        };\n    }\n    /**\n   * حل تنبيه\n   */ resolveAlert(alertId) {\n        const alert = this.alerts.find((a)=>a.id === alertId);\n        if (alert) {\n            alert.resolved = true;\n            this.status.alertsCount = Math.max(0, this.status.alertsCount - 1);\n        }\n    }\n    /**\n   * تفعيل/إلغاء النسخ الاحتياطي التلقائي\n   */ toggleAutoBackup(enabled) {\n        this.status.autoBackupEnabled = enabled;\n        if (enabled && this.status.isMonitoring) {\n            this.startAutoBackup();\n        } else if (this.backupInterval) {\n            clearInterval(this.backupInterval);\n            this.backupInterval = null;\n        }\n    }\n    /**\n   * إنشاء نسخة احتياطية يدوية\n   */ createManualBackup() {\n        const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n        if (result.success) {\n            this.status.lastBackup = new Date().toISOString();\n            this.addAlert(\"info\", \"تم إنشاء نسخة احتياطية يدوية بنجاح\");\n        }\n        return result;\n    }\n    /**\n   * استرداد من النسخة الاحتياطية\n   */ restoreData(backupId) {\n        const result = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.restoreFromBackup)(backupId);\n        if (result.success) {\n            this.addAlert(\"info\", \"تم استرداد البيانات بنجاح\");\n            // إعادة تحميل الصفحة لتطبيق البيانات المستردة\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1000);\n        }\n        return result;\n    }\n    /**\n   * تصدير البيانات\n   */ exportData() {\n        try {\n            const data = {\n                invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n                settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n                counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\"),\n                exportDate: new Date().toISOString(),\n                version: \"1.0.0\"\n            };\n            const exportData = JSON.stringify(data, null, 2);\n            return {\n                success: true,\n                data: exportData,\n                message: \"تم تصدير البيانات بنجاح\"\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: \"فشل في تصدير البيانات: \" + error.message\n            };\n        }\n    }\n    /**\n   * استيراد البيانات\n   */ importData(importData) {\n        try {\n            const data = JSON.parse(importData);\n            // التحقق من صحة البيانات\n            if (!data.invoices || !Array.isArray(data.invoices)) {\n                throw new Error(\"بيانات الفواتير غير صحيحة\");\n            }\n            // إنشاء نسخة احتياطية قبل الاستيراد\n            const backupResult = (0,_secureStorage__WEBPACK_IMPORTED_MODULE_0__.createBackup)();\n            if (!backupResult.success) {\n                throw new Error(\"فشل في إنشاء نسخة احتياطية قبل الاستيراد\");\n            }\n            // استيراد البيانات\n            localStorage.setItem(\"invoices\", JSON.stringify(data.invoices));\n            if (data.settings) {\n                localStorage.setItem(\"companySettings\", JSON.stringify(data.settings));\n            }\n            if (data.counters) {\n                localStorage.setItem(\"invoiceCounters\", JSON.stringify(data.counters));\n            }\n            this.addAlert(\"info\", \"تم استيراد البيانات بنجاح\");\n            return {\n                success: true,\n                message: \"تم استيراد البيانات بنجاح\"\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: \"فشل في استيراد البيانات: \" + error.message\n            };\n        }\n    }\n    constructor(){\n        this.status = {\n            isMonitoring: false,\n            lastBackup: null,\n            lastHealthCheck: null,\n            alertsCount: 0,\n            autoBackupEnabled: true\n        };\n        this.alerts = [];\n        this.monitoringInterval = null;\n        this.backupInterval = null;\n    }\n}\n// إنشاء مثيل واحد من النظام\nconst dataProtection = new DataProtectionSystem();\n// بدء النظام تلقائياً عند تحميل الصفحة\nif (true) {\n    // انتظار تحميل الصفحة كاملة\n    if (document.readyState === \"loading\") {\n        document.addEventListener(\"DOMContentLoaded\", ()=>{\n            setTimeout(()=>dataProtection.start(), 1000);\n        });\n    } else {\n        setTimeout(()=>dataProtection.start(), 1000);\n    }\n    // إيقاف النظام عند إغلاق الصفحة\n    window.addEventListener(\"beforeunload\", ()=>{\n        dataProtection.stop();\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/dataProtection.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/secureStorage.ts":
/*!************************************!*\
  !*** ./src/utils/secureStorage.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STORAGE_CONFIG: function() { return /* binding */ STORAGE_CONFIG; },\n/* harmony export */   checkStorageHealth: function() { return /* binding */ checkStorageHealth; },\n/* harmony export */   createBackup: function() { return /* binding */ createBackup; },\n/* harmony export */   restoreFromBackup: function() { return /* binding */ restoreFromBackup; },\n/* harmony export */   secureGetItem: function() { return /* binding */ secureGetItem; },\n/* harmony export */   secureSetItem: function() { return /* binding */ secureSetItem; }\n/* harmony export */ });\n/**\n * نظام إدارة التخزين الآمن والمحسن\n * يوفر حماية شاملة للبيانات مع نسخ احتياطية تلقائية\n */ // إعدادات النظام\nconst STORAGE_CONFIG = {\n    MAX_STORAGE_SIZE: 4 * 1024 * 1024,\n    BACKUP_INTERVAL: 5 * 60 * 1000,\n    MAX_BACKUPS: 10,\n    ENCRYPTION_KEY: \"infapapp-secure-key-2024\"\n};\n/**\n * تشفير أساسي للبيانات الحساسة\n */ const encryptData = (data)=>{\n    try {\n        // تشفير بسيط باستخدام Base64 مع تشويش\n        const encoded = btoa(data);\n        const scrambled = encoded.split(\"\").reverse().join(\"\");\n        return btoa(scrambled + STORAGE_CONFIG.ENCRYPTION_KEY);\n    } catch (error) {\n        console.error(\"❌ Encryption failed:\", error);\n        return data; // إرجاع البيانات بدون تشفير في حالة الفشل\n    }\n};\n/**\n * فك تشفير البيانات\n */ const decryptData = (encryptedData)=>{\n    try {\n        const decoded = atob(encryptedData);\n        const withoutKey = decoded.replace(STORAGE_CONFIG.ENCRYPTION_KEY, \"\");\n        const unscrambled = withoutKey.split(\"\").reverse().join(\"\");\n        return atob(unscrambled);\n    } catch (error) {\n        console.error(\"❌ Decryption failed:\", error);\n        return encryptedData; // إرجاع البيانات كما هي في حالة الفشل\n    }\n};\n/**\n * حساب checksum للبيانات\n */ const calculateChecksum = (data)=>{\n    let hash = 0;\n    for(let i = 0; i < data.length; i++){\n        const char = data.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // تحويل إلى 32bit integer\n    }\n    return Math.abs(hash).toString(16);\n};\n/**\n * فحص حالة التخزين\n */ const checkStorageHealth = ()=>{\n    const health = {\n        isHealthy: true,\n        issues: [],\n        recommendations: [],\n        storageUsed: 0,\n        storageLimit: STORAGE_CONFIG.MAX_STORAGE_SIZE,\n        backupsCount: 0\n    };\n    try {\n        // حساب حجم التخزين المستخدم\n        let totalSize = 0;\n        for(let key in localStorage){\n            if (localStorage.hasOwnProperty(key)) {\n                totalSize += localStorage[key].length;\n            }\n        }\n        health.storageUsed = totalSize;\n        // فحص الحد الأقصى\n        if (totalSize > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.8) {\n            health.isHealthy = false;\n            health.issues.push(\"التخزين يقترب من الحد الأقصى\");\n            health.recommendations.push(\"تنظيف البيانات القديمة\");\n        }\n        // فحص النسخ الاحتياطية\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\"));\n        health.backupsCount = backupKeys.length;\n        if (health.backupsCount === 0) {\n            health.issues.push(\"لا توجد نسخ احتياطية\");\n            health.recommendations.push(\"إنشاء نسخة احتياطية فورية\");\n        }\n        // فحص البيانات الأساسية\n        const invoicesData = localStorage.getItem(\"invoices\");\n        if (!invoicesData) {\n            health.issues.push(\"لا توجد بيانات فواتير\");\n        } else {\n            try {\n                const invoices = JSON.parse(invoicesData);\n                if (!Array.isArray(invoices)) {\n                    health.isHealthy = false;\n                    health.issues.push(\"بيانات الفواتير تالفة\");\n                    health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n                }\n            } catch (error) {\n                health.isHealthy = false;\n                health.issues.push(\"فشل في قراءة بيانات الفواتير\");\n                health.recommendations.push(\"استرداد من النسخة الاحتياطية\");\n            }\n        }\n    } catch (error) {\n        health.isHealthy = false;\n        health.issues.push(\"فشل في فحص حالة التخزين\");\n        health.recommendations.push(\"إعادة تشغيل التطبيق\");\n    }\n    return health;\n};\n/**\n * إنشاء نسخة احتياطية\n */ const createBackup = ()=>{\n    try {\n        const timestamp = new Date().toISOString();\n        const backupId = \"backup-\".concat(Date.now());\n        // جمع البيانات\n        const data = {\n            invoices: JSON.parse(localStorage.getItem(\"invoices\") || \"[]\"),\n            settings: JSON.parse(localStorage.getItem(\"companySettings\") || \"{}\"),\n            counters: JSON.parse(localStorage.getItem(\"invoiceCounters\") || \"{}\")\n        };\n        // إنشاء النسخة الاحتياطية\n        const backup = {\n            data,\n            timestamp,\n            version: \"1.0.0\",\n            checksum: calculateChecksum(JSON.stringify(data))\n        };\n        // تشفير النسخة الاحتياطية\n        const encryptedBackup = encryptData(JSON.stringify(backup));\n        // حفظ النسخة الاحتياطية\n        localStorage.setItem(backupId, encryptedBackup);\n        // تنظيف النسخ القديمة\n        cleanupOldBackups();\n        console.log(\"✅ Backup created successfully:\", backupId);\n        return {\n            success: true,\n            message: \"تم إنشاء النسخة الاحتياطية بنجاح\",\n            backupId\n        };\n    } catch (error) {\n        console.error(\"❌ Backup creation failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في إنشاء النسخة الاحتياطية: \" + error.message\n        };\n    }\n};\n/**\n * تنظيف النسخ الاحتياطية القديمة\n */ const cleanupOldBackups = ()=>{\n    try {\n        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n            const timeA = parseInt(a.replace(\"backup-\", \"\"));\n            const timeB = parseInt(b.replace(\"backup-\", \"\"));\n            return timeB - timeA; // ترتيب تنازلي (الأحدث أولاً)\n        });\n        // حذف النسخ الزائدة\n        if (backupKeys.length > STORAGE_CONFIG.MAX_BACKUPS) {\n            const toDelete = backupKeys.slice(STORAGE_CONFIG.MAX_BACKUPS);\n            toDelete.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed old backup:\", key);\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Failed to cleanup old backups:\", error);\n    }\n};\n/**\n * استرداد من النسخة الاحتياطية\n */ const restoreFromBackup = (backupId)=>{\n    try {\n        let targetBackupId = backupId;\n        // إذا لم يتم تحديد النسخة، استخدم الأحدث\n        if (!targetBackupId) {\n            const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(\"backup-\")).sort((a, b)=>{\n                const timeA = parseInt(a.replace(\"backup-\", \"\"));\n                const timeB = parseInt(b.replace(\"backup-\", \"\"));\n                return timeB - timeA;\n            });\n            if (backupKeys.length === 0) {\n                return {\n                    success: false,\n                    message: \"لا توجد نسخ احتياطية متاحة\"\n                };\n            }\n            targetBackupId = backupKeys[0];\n        }\n        // قراءة النسخة الاحتياطية\n        const encryptedBackup = localStorage.getItem(targetBackupId);\n        if (!encryptedBackup) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية غير موجودة\"\n            };\n        }\n        // فك التشفير\n        const decryptedData = decryptData(encryptedBackup);\n        const backup = JSON.parse(decryptedData);\n        // التحقق من سلامة البيانات\n        const currentChecksum = calculateChecksum(JSON.stringify(backup.data));\n        if (currentChecksum !== backup.checksum) {\n            return {\n                success: false,\n                message: \"النسخة الاحتياطية تالفة (checksum mismatch)\"\n            };\n        }\n        // استرداد البيانات\n        localStorage.setItem(\"invoices\", JSON.stringify(backup.data.invoices));\n        localStorage.setItem(\"companySettings\", JSON.stringify(backup.data.settings));\n        localStorage.setItem(\"invoiceCounters\", JSON.stringify(backup.data.counters));\n        console.log(\"✅ Data restored successfully from:\", targetBackupId);\n        return {\n            success: true,\n            message: \"تم استرداد البيانات بنجاح من النسخة: \".concat(backup.timestamp)\n        };\n    } catch (error) {\n        console.error(\"❌ Restore failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في استرداد البيانات: \" + error.message\n        };\n    }\n};\n/**\n * الحفظ الآمن للبيانات\n */ const secureSetItem = (key, data)=>{\n    try {\n        // فحص حالة التخزين أولاً\n        const health = checkStorageHealth();\n        if (!health.isHealthy && health.storageUsed > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.9) {\n            return {\n                success: false,\n                message: \"التخزين ممتلئ - يرجى تنظيف البيانات\"\n            };\n        }\n        // تحويل البيانات إلى JSON\n        const jsonData = JSON.stringify(data);\n        // فحص حجم البيانات\n        if (jsonData.length > STORAGE_CONFIG.MAX_STORAGE_SIZE * 0.1) {\n            console.warn(\"⚠️ Large data being saved:\", Math.round(jsonData.length / 1024), \"KB\");\n        }\n        // الحفظ\n        localStorage.setItem(key, jsonData);\n        // التحقق من الحفظ\n        const saved = localStorage.getItem(key);\n        if (saved !== jsonData) {\n            throw new Error(\"فشل في التحقق من الحفظ\");\n        }\n        // إنشاء نسخة احتياطية تلقائية للبيانات المهمة\n        if (key === \"invoices\" || key === \"companySettings\") {\n            setTimeout(()=>createBackup(), 1000);\n        }\n        return {\n            success: true,\n            message: \"تم الحفظ بنجاح\"\n        };\n    } catch (error) {\n        console.error(\"❌ Secure save failed:\", error);\n        return {\n            success: false,\n            message: \"فشل في الحفظ: \" + error.message\n        };\n    }\n};\n/**\n * القراءة الآمنة للبيانات\n */ const secureGetItem = (key, defaultValue)=>{\n    try {\n        const stored = localStorage.getItem(key);\n        if (!stored) {\n            return {\n                success: true,\n                data: defaultValue,\n                message: \"لا توجد بيانات محفوظة، استخدام القيم الافتراضية\"\n            };\n        }\n        const parsed = JSON.parse(stored);\n        // التحقق من نوع البيانات\n        if (typeof parsed !== typeof defaultValue) {\n            console.warn(\"⚠️ Data type mismatch for key:\", key);\n            return {\n                success: false,\n                data: defaultValue,\n                message: \"نوع البيانات غير متطابق\"\n            };\n        }\n        return {\n            success: true,\n            data: parsed\n        };\n    } catch (error) {\n        console.error(\"❌ Secure read failed:\", error);\n        // محاولة الاسترداد من النسخة الاحتياطية\n        if (key === \"invoices\") {\n            const restoreResult = restoreFromBackup();\n            if (restoreResult.success) {\n                // إعادة المحاولة بعد الاسترداد\n                try {\n                    const stored = localStorage.getItem(key);\n                    if (stored) {\n                        return {\n                            success: true,\n                            data: JSON.parse(stored),\n                            message: \"تم الاسترداد من النسخة الاحتياطية\"\n                        };\n                    }\n                } catch (retryError) {\n                    console.error(\"❌ Retry after restore failed:\", retryError);\n                }\n            }\n        }\n        return {\n            success: false,\n            data: defaultValue,\n            message: \"فشل في قراءة البيانات: \" + error.message\n        };\n    }\n};\n// تصدير الإعدادات للاستخدام الخارجي\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/secureStorage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ZGZlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CCAD%20USER%20X64%5CDocuments%5Caugment-projects%5C231%5Csrc%5Ccomponents%5CDataProtectionDashboard.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);