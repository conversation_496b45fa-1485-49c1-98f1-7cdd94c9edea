"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/POSInvoice.tsx":
/*!***************************************!*\
  !*** ./src/components/POSInvoice.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/imageExporter */ \"(app-pages-browser)/./src/utils/imageExporter.ts\");\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/settingsStore */ \"(app-pages-browser)/./src/store/settingsStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst POSInvoice = (param)=>{\n    let { invoice, onClose } = param;\n    _s();\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSharing, setIsSharing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { settings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_4__.useSettingsStore)();\n    // دالة تصدير الفاتورة كصورة\n    const handleExportAsImage = async ()=>{\n        try {\n            setIsExporting(true);\n            await (0,_utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__.exportPOSInvoiceAsImage)(invoice.id || \"unknown\", invoice.id);\n            alert(\"تم تصدير الفاتورة كصورة بنجاح!\");\n        } catch (error) {\n            console.error(\"خطأ في تصدير الصورة:\", error);\n            alert(\"حدث خطأ أثناء تصدير الصورة. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // دالة مشاركة الفاتورة كصورة\n    const handleShareAsImage = async ()=>{\n        try {\n            setIsSharing(true);\n            await (0,_utils_imageExporter__WEBPACK_IMPORTED_MODULE_3__.sharePOSInvoiceAsImage)(\"pos-invoice-content\", \"فاتورة-\".concat(invoice.id || \"unknown\"));\n        } catch (error) {\n            console.error(\"خطأ في مشاركة الصورة:\", error);\n            alert(\"حدث خطأ أثناء مشاركة الصورة. يرجى المحاولة مرة أخرى.\");\n        } finally{\n            setIsSharing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"ar-EG\", {\n            style: \"currency\",\n            currency: \"EGP\",\n            minimumFractionDigits: 2\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const getServiceName = (serviceType)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\",\n            samples: \"عينات\"\n        };\n        return serviceNames[serviceType] || serviceType;\n    };\n    const getServiceDetails = (service)=>{\n        let details = \"\";\n        let quantity = \"\";\n        let unitPrice = 0;\n        let serviceTotal = 0;\n        switch(service.serviceType){\n            case \"consultation\":\n                serviceTotal = service.cost || 0;\n                details = \"\".concat(service.topic || \"استشارة\", \" - \").concat(service.hours || 0, \" ساعة\");\n                quantity = \"\".concat(service.hours || 0, \" ساعة\");\n                unitPrice = service.cost || 0;\n                break;\n            case \"pattern\":\n                var _service_models, _service_models1, _service_models2, _service_models3;\n                serviceTotal = ((_service_models = service.models) === null || _service_models === void 0 ? void 0 : _service_models.reduce((sum, model)=>sum + (model.finalAmount || model.cost || 0), 0)) || 0;\n                details = \"\".concat(((_service_models1 = service.models) === null || _service_models1 === void 0 ? void 0 : _service_models1.length) || 0, \" نموذج باترون\");\n                quantity = \"\".concat(((_service_models2 = service.models) === null || _service_models2 === void 0 ? void 0 : _service_models2.length) || 0, \" نموذج\");\n                unitPrice = serviceTotal / (((_service_models3 = service.models) === null || _service_models3 === void 0 ? void 0 : _service_models3.length) || 1);\n                break;\n            case \"pattern_printing\":\n                var _service_files, _service_files1, _service_files2;\n                serviceTotal = ((_service_files = service.files) === null || _service_files === void 0 ? void 0 : _service_files.reduce((sum, file)=>sum + (file.cost || 0), 0)) || 0;\n                details = \"طباعة \".concat(((_service_files1 = service.files) === null || _service_files1 === void 0 ? void 0 : _service_files1.length) || 0, \" ملف\");\n                quantity = \"\".concat(((_service_files2 = service.files) === null || _service_files2 === void 0 ? void 0 : _service_files2.reduce((sum, file)=>sum + (file.meters || 0), 0)) || 0, \" متر\");\n                unitPrice = serviceTotal;\n                break;\n            case \"manufacturing\":\n                var _service_models4, _service_models5, _service_models6, _service_models7;\n                serviceTotal = ((_service_models4 = service.models) === null || _service_models4 === void 0 ? void 0 : _service_models4.reduce((sum, model)=>{\n                    return sum + (model.patternPrice || 0) + (model.samplePrice || 0) + (model.manufacturingPrice || 0);\n                }, 0)) || 0;\n                details = \"تصنيع \".concat(((_service_models5 = service.models) === null || _service_models5 === void 0 ? void 0 : _service_models5.length) || 0, \" نموذج\");\n                quantity = \"\".concat(((_service_models6 = service.models) === null || _service_models6 === void 0 ? void 0 : _service_models6.reduce((sum, model)=>sum + (model.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_models7 = service.models) === null || _service_models7 === void 0 ? void 0 : _service_models7.length) || 1);\n                break;\n            case \"shipping\":\n                serviceTotal = service.total || 0;\n                details = \"شحن \".concat(service.item || \"منتج\");\n                quantity = \"\".concat(service.quantity || 1);\n                unitPrice = service.unitPrice || 0;\n                break;\n            case \"products\":\n                var _service_items, _service_items1, _service_items2, _service_items3;\n                let productsSubtotal = ((_service_items = service.items) === null || _service_items === void 0 ? void 0 : _service_items.reduce((sum, item)=>sum + (item.total || 0), 0)) || 0;\n                if (service.discountAmount) {\n                    productsSubtotal -= service.discountAmount || 0;\n                }\n                serviceTotal = Math.max(0, productsSubtotal);\n                details = \"\".concat(((_service_items1 = service.items) === null || _service_items1 === void 0 ? void 0 : _service_items1.length) || 0, \" منتج\");\n                quantity = \"\".concat(((_service_items2 = service.items) === null || _service_items2 === void 0 ? void 0 : _service_items2.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 0);\n                unitPrice = serviceTotal / (((_service_items3 = service.items) === null || _service_items3 === void 0 ? void 0 : _service_items3.reduce((sum, item)=>sum + (item.quantity || 0), 0)) || 1);\n                break;\n            default:\n                serviceTotal = service.cost || service.total || 0;\n                details = getServiceName(service.serviceType);\n                quantity = \"1\";\n                unitPrice = serviceTotal;\n        }\n        return {\n            details,\n            quantity,\n            unitPrice,\n            serviceTotal\n        };\n    };\n    const handlePrint = ()=>{\n        // إنشاء نافذة طباعة منفصلة\n        const printWindow = window.open(\"\", \"_blank\", \"width=300,height=600\");\n        if (printWindow) {\n            var _document_querySelector;\n            // الحصول على محتوى الفاتورة مع تحديث الشعار\n            const posContent = (_document_querySelector = document.querySelector(\".pos-invoice-content\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.innerHTML;\n            if (posContent) {\n                printWindow.document.write('\\n          <!DOCTYPE html>\\n          <html dir=\"rtl\" lang=\"ar\">\\n          <head>\\n            <meta charset=\"UTF-8\">\\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\\n            <title>فاتورة POS - '.concat(invoice.id, \"</title>\\n            <style>\\n              * {\\n                margin: 0;\\n                padding: 0;\\n                box-sizing: border-box;\\n              }\\n\\n              body {\\n                font-family: 'Courier New', monospace;\\n                font-size: 10px;\\n                line-height: 1.2;\\n                color: black;\\n                background: white;\\n                width: 80mm;\\n                margin: 0 auto;\\n                padding: 2mm;\\n              }\\n\\n              @page {\\n                size: 80mm auto;\\n                margin: 2mm;\\n              }\\n\\n              @media print {\\n                body {\\n                  width: 80mm !important;\\n                  font-size: 9px !important;\\n                }\\n              }\\n\\n              .text-center { text-align: center; }\\n              .text-sm { font-size: 9px; }\\n              .text-xs { font-size: 8px; }\\n              .text-lg { font-size: 12px; }\\n              .font-bold { font-weight: bold; }\\n              .font-semibold { font-weight: 600; }\\n              .mb-1 { margin-bottom: 2px; }\\n              .mb-2 { margin-bottom: 4px; }\\n              .mb-3 { margin-bottom: 6px; }\\n              .pb-3 { padding-bottom: 6px; }\\n              .pt-3 { padding-top: 6px; }\\n              .mr-3 { margin-right: 6px; }\\n              .mt-2 { margin-top: 4px; }\\n\\n              .border-b { border-bottom: 1px solid #ccc; }\\n              .border-dashed { border-style: dashed; }\\n              .border-gray-400 { border-color: #999; }\\n\\n              .flex { display: flex; }\\n              .justify-between { justify-content: space-between; }\\n\\n              /* Thermal Printer Colors - Black and White Only */\\n              .text-green-600 { color: #000; font-weight: bold; }\\n              .text-orange-600 { color: #000; font-weight: bold; }\\n              .text-red-600 { color: #000; font-weight: bold; }\\n              .text-gray-600 { color: #666; }\\n              .text-gray-700 { color: #333; }\\n\\n              /* Enhanced Logo Styles for Print - Thermal Printer Optimized */\\n              .inline-block { display: inline-block; }\\n              .bg-gradient-to-br { background: #000; } /* Black background for thermal printing */\\n              .rounded-xl { border-radius: 8px; }\\n              .p-3 { padding: 6px; }\\n              .p-1 { padding: 2px; }\\n              .w-20 { width: 40px; }\\n              .h-20 { height: 40px; }\\n              .w-full { width: 100%; }\\n              .h-full { height: 100%; }\\n              .mx-auto { margin-left: auto; margin-right: auto; }\\n              .flex { display: flex; }\\n              .items-center { align-items: center; }\\n              .justify-center { justify-content: center; }\\n              .text-white { color: white; }\\n              .bg-white { background-color: white; }\\n              .font-extrabold { font-weight: 800; }\\n              .font-semibold { font-weight: 600; }\\n              .leading-tight { line-height: 1.1; }\\n              .tracking-wide { letter-spacing: 0.025em; }\\n              .tracking-widest { letter-spacing: 0.1em; }\\n              .opacity-90 { opacity: 0.9; }\\n              .space-y-1 > * + * { margin-top: 2px; }\\n              .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }\\n              .border { border: 1px solid #e5e7eb; }\\n              .border-gray-200 { border-color: #e5e7eb; }\\n              .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }\\n              .relative { position: relative; }\\n              .absolute { position: absolute; }\\n              .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n              .top-1 { top: 2px; }\\n              .right-1 { right: 2px; }\\n              .bottom-2 { bottom: 4px; }\\n              .left-2 { left: 4px; }\\n              .w-3 { width: 6px; }\\n              .h-3 { height: 6px; }\\n              .w-2 { width: 4px; }\\n              .h-2 { height: 4px; }\\n              .w-8 { width: 16px; }\\n              .h-0.5 { height: 1px; }\\n              .mt-1 { margin-top: 2px; }\\n              .rounded-full { border-radius: 50%; }\\n              .z-10 { z-index: 10; }\\n              .overflow-hidden { overflow: hidden; }\\n\\n              /* Image Styles for Uploaded Logo */\\n              .object-contain {\\n                object-fit: contain;\\n                image-rendering: -webkit-optimize-contrast;\\n                image-rendering: crisp-edges;\\n              }\\n\\n              /* Enhanced Layout Styles for Thermal Print - Black and White */\\n              .bg-gray-50 { background-color: #f8f8f8; }\\n              .bg-blue-50 { background-color: #f0f0f0; }\\n              .text-blue-600 { color: #000; font-weight: bold; }\\n              .text-blue-800 { color: #000; font-weight: bold; }\\n              .text-green-600 { color: #000; font-weight: bold; }\\n              .text-green-800 { color: #000; font-weight: bold; }\\n              .text-orange-800 { color: #000; font-weight: bold; }\\n              .text-red-800 { color: #000; font-weight: bold; }\\n              .bg-green-100 { background-color: #f0f0f0; }\\n              .bg-orange-100 { background-color: #f0f0f0; }\\n              .bg-red-100 { background-color: #f0f0f0; }\\n              .grid { display: grid; }\\n              .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\\n              .gap-2 { gap: 8px; }\\n              .items-center { align-items: center; }\\n              .border-b { border-bottom: 1px solid #e5e7eb; }\\n              .pb-1 { padding-bottom: 2px; }\\n              .font-medium { font-weight: 500; }\\n              .space-y-2 > * + * { margin-top: 8px; }\\n              .px-2 { padding-left: 4px; padding-right: 4px; }\\n              .py-1 { padding-top: 2px; padding-bottom: 2px; }\\n              .px-4 { padding-left: 8px; padding-right: 8px; }\\n              .py-2 { padding-top: 4px; padding-bottom: 4px; }\\n              .rounded-full { border-radius: 9999px; }\\n              .border-2 { border-width: 2px; }\\n              .border-t-2 { border-top-width: 2px; }\\n              .pt-4 { padding-top: 8px; }\\n              .mb-4 { margin-bottom: 8px; }\\n              .mb-3 { margin-bottom: 6px; }\\n              .mb-2 { margin-bottom: 4px; }\\n              .mb-1 { margin-bottom: 2px; }\\n              .mt-1 { margin-top: 2px; }\\n              .mt-2 { margin-top: 4px; }\\n              .mr-2 { margin-right: 4px; }\\n              .mr-3 { margin-right: 6px; }\\n              .tracking-wide { letter-spacing: 0.025em; }\\n              .leading-relaxed { line-height: 1.625; }\\n            </style>\\n          </head>\\n          <body>\\n            \").concat(posContent, \"\\n          </body>\\n          </html>\\n        \"));\n                printWindow.document.close();\n                // انتظار تحميل المحتوى ثم الطباعة\n                printWindow.onload = ()=>{\n                    setTimeout(()=>{\n                        printWindow.print();\n                        printWindow.close();\n                    }, 250);\n                };\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-34d022bd924bccec\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-34d022bd924bccec\" + \" \" + \"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"p-4 border-b border-gray-200 print:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xl font-bold\",\n                                        children: \"فاتورة POS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex gap-2 flex-wrap\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePrint,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"طباعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExportAsImage,\n                                        disabled: isExporting,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isExporting ? \"جاري التصدير...\" : \"تصدير كصورة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShareAsImage,\n                                        disabled: isSharing,\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 transition-colors text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-4 h-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\",\n                                                    className: \"jsx-34d022bd924bccec\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            isSharing ? \"جاري المشاركة...\" : \"مشاركة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"pos-invoice-content\",\n                        style: {\n                            fontFamily: \"Arial, sans-serif\",\n                            fontSize: \"13px\",\n                            lineHeight: \"1.5\",\n                            maxWidth: \"320px\",\n                            margin: \"0 auto\",\n                            color: \"#000\"\n                        },\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"pos-invoice-content p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-b border-dashed border-gray-400 pb-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"inline-block bg-white rounded-xl p-2 mb-2 border border-gray-400\",\n                                            children: settings.companyLogo ? /* Uploaded Company Logo */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-16 h-16 mx-auto rounded-lg overflow-hidden border border-gray-400 bg-white flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: settings.companyLogo,\n                                                    alt: \"شعار الشركة\",\n                                                    style: {\n                                                        maxWidth: \"100%\",\n                                                        maxHeight: \"100%\",\n                                                        imageRendering: \"crisp-edges\",\n                                                        filter: \"contrast(1.2) brightness(1.1)\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"w-full h-full object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined) : /* Fallback CSS Logo - Thermal Optimized */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"w-16 h-16 mx-auto bg-black rounded-lg flex items-center justify-center border border-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-white font-bold text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-lg leading-tight font-extrabold tracking-wide\",\n                                                            children: \"OKA\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xs leading-tight font-semibold tracking-widest\",\n                                                            children: \"GROUP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"w-6 h-0.5 bg-white mx-auto mt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            color: \"#000\"\n                                        },\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-xl font-bold mb-1 tracking-wide\",\n                                        children: settings.companyName || \"OKA GROUP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#333\"\n                                        },\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-semibold mb-2\",\n                                        children: settings.description || \"مجموعة أوكا للخدمات المتكاملة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"mt-3 space-y-1 text-xs leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCE7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.email || \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCDE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.complaintsPhone || \"0114954118\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"flex items-center justify-center gap-1 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\",\n                                                        children: \"\\uD83D\\uDCCD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                        children: settings.address || \"73 ش 6 اكتوبر الجراش جسر السويس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-4 text-sm bg-white p-3 rounded-lg border border-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"grid grid-cols-1 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"رقم الفاتورة:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                    children: [\n                                                        \"#\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"التاريخ:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center border-b border-gray-400 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"العميل:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"الهاتف:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#000\"\n                                                    },\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t-2 border-dashed border-gray-400 pt-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center font-bold mb-3 text-sm bg-white py-2 rounded-lg border border-gray-400\",\n                                        children: \"\\uD83D\\uDCCB تفاصيل الطلب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    invoice.services.map((service, index)=>{\n                                        const serviceInfo = getServiceDetails(service);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"mb-3 p-2 bg-gray-50 rounded-lg border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between items-center font-bold text-sm mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-blue-600\",\n                                                            children: [\n                                                                index + 1,\n                                                                \". \",\n                                                                getServiceName(service.serviceType)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-green-600\",\n                                                            children: formatCurrency(serviceInfo.serviceTotal)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-600 text-xs mr-3 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-medium\",\n                                                            children: serviceInfo.details\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-34d022bd924bccec\",\n                                                                    children: [\n                                                                        \"الكمية: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-semibold\",\n                                                                            children: serviceInfo.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 37\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-34d022bd924bccec\",\n                                                                    children: [\n                                                                        \"السعر: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-semibold\",\n                                                                            children: formatCurrency(Number(serviceInfo.quantity) > 0 ? serviceInfo.serviceTotal / Number(serviceInfo.quantity) : serviceInfo.serviceTotal)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 36\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, service.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t-2 border-dashed border-gray-400 pt-4 mb-4 bg-gray-50 p-3 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold border-b border-gray-200 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المجموع الكلي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-blue-600\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm border-b border-gray-200 pb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المبلغ المدفوع:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-green-600\",\n                                                    children: formatCurrency(invoice.paidAmount || 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex justify-between text-sm font-bold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\",\n                                                    children: \"المبلغ المتبقي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-red-600\",\n                                                    children: formatCurrency(invoice.total - (invoice.paidAmount || 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t border-dashed border-gray-400 pt-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"inline-block bg-white border-2 rounded-lg px-4 py-2 shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34d022bd924bccec\" + \" \" + \"text-sm font-bold\",\n                                        children: [\n                                            \"حالة الدفع:\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34d022bd924bccec\" + \" \" + \"mr-2 px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-orange-100 text-orange-800\" : \"bg-red-100 text-red-800\"),\n                                                children: invoice.paymentStatus\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34d022bd924bccec\" + \" \" + \"text-center border-t-2 border-dashed border-gray-400 pt-4 text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"bg-blue-50 p-3 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"font-bold text-blue-800 mb-1\",\n                                            children: \"\\uD83D\\uDE4F شكراً لتعاملكم معنا\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"text-blue-600 mb-2\",\n                                            children: \"Thank you for your business\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34d022bd924bccec\" + \" \" + \"border-t border-blue-200 pt-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-600\",\n                                                    children: [\n                                                        \"تاريخ الطباعة: \",\n                                                        new Date().toLocaleDateString(\"ar-EG\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"text-gray-500 text-xs mt-1\",\n                                                    children: [\n                                                        \"Printed: \",\n                                                        new Date().toLocaleString(\"en-US\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34d022bd924bccec\" + \" \" + \"p-4 border-t border-gray-200 print:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-34d022bd924bccec\" + \" \" + \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePrint,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"\\uD83D\\uDDA8️ طباعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"jsx-34d022bd924bccec\" + \" \" + \"flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded transition-colors\",\n                                    children: \"إغلاق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"34d022bd924bccec\",\n                children: '@media print{*.jsx-34d022bd924bccec{-webkit-print-color-adjust:exact!important;color-adjust:exact!important}body.jsx-34d022bd924bccec{margin:0!important;padding:0!important;background:white!important}.pos-print-content.jsx-34d022bd924bccec{width:80mm!important;max-width:80mm!important;font-family:\"Courier New\",monospace!important;font-size:10px!important;line-height:1.2!important;color:black!important;background:white!important;margin:0!important;padding:2mm!important}.pos-invoice-content.jsx-34d022bd924bccec{width:100%!important;max-width:100%!important;font-size:10px!important;line-height:1.2!important;padding:0!important;margin:0!important}.pos-invoice-content.jsx-34d022bd924bccec .inline-block.jsx-34d022bd924bccec{display:inline-block!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gray-100.jsx-34d022bd924bccec{background-color:#f3f4f6!important}.pos-invoice-content.jsx-34d022bd924bccec .rounded-lg.jsx-34d022bd924bccec{-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important}.pos-invoice-content.jsx-34d022bd924bccec .w-16.jsx-34d022bd924bccec{width:32px!important;height:32px!important}.pos-invoice-content.jsx-34d022bd924bccec .bg-gradient-to-br.jsx-34d022bd924bccec{background:-webkit-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-moz-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:-o-linear-gradient(315deg,#2563eb,#1d4ed8)!important;background:linear-gradient(135deg,#2563eb,#1d4ed8)!important}.pos-invoice-content.jsx-34d022bd924bccec .text-white.jsx-34d022bd924bccec{color:white!important}.pos-invoice-content.jsx-34d022bd924bccec .font-semibold.jsx-34d022bd924bccec{font-weight:600!important}@page{size:80mm auto;margin:2mm}.print\\\\\\\\.jsx-34d022bd924bccec:hidden{display:none!important}}.pos-invoice-content.jsx-34d022bd924bccec{font-family:\"Courier New\",monospace;font-size:12px;line-height:1.4;color:#333}'\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\POSInvoice.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, undefined);\n};\n_s(POSInvoice, \"M+qfwJl7bPrrTj6eCKG63WmFpYU=\", false, function() {\n    return [\n        _store_settingsStore__WEBPACK_IMPORTED_MODULE_4__.useSettingsStore\n    ];\n});\n_c = POSInvoice;\n/* harmony default export */ __webpack_exports__[\"default\"] = (POSInvoice);\nvar _c;\n$RefreshReg$(_c, \"POSInvoice\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/POSInvoice.tsx\n"));

/***/ })

});