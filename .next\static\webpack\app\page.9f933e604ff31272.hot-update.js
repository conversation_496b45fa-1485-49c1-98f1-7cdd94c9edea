"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/InvoicesList.tsx":
/*!*****************************************!*\
  !*** ./src/components/InvoicesList.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/invoiceStore */ \"(app-pages-browser)/./src/store/invoiceStore.ts\");\n/* harmony import */ var _utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\");\n/* harmony import */ var _InvoicePreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvoicePreview */ \"(app-pages-browser)/./src/components/InvoicePreview.tsx\");\n/* harmony import */ var _POSInvoice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./POSInvoice */ \"(app-pages-browser)/./src/components/POSInvoice.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst InvoicesList = ()=>{\n    _s();\n    const { invoices } = (0,_store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"date_desc\");\n    const [filteredInvoices, setFilteredInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previewInvoice, setPreviewInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [posInvoice, setPosInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // إعادة تحميل البيانات من التخزين الآمن عند تحميل المكون\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInvoices = ()=>{\n            // استخدام النظام الآمن لتحميل البيانات\n            const result = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState().invoices;\n            // التحقق من عدم وجود تكرار في البيانات\n            const uniqueInvoices = result.filter((invoice, index, self)=>index === self.findIndex((inv)=>inv.id === invoice.id));\n            // تحديث الحالة المحلية\n            setFilteredInvoices(uniqueInvoices);\n        };\n        // تحميل فوري\n        loadInvoices();\n        // الاستماع لتغييرات localStorage بدلاً من التحديث الدوري\n        const handleStorageChange = (e)=>{\n            if (e.key === \"invoices\") {\n                loadInvoices();\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = [\n            ...invoices\n        ];\n        // البحث\n        if (searchTerm) {\n            filtered = filtered.filter((invoice)=>{\n                var _invoice_id;\n                return invoice.client.name.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.client.phone.includes(searchTerm) || ((_invoice_id = invoice.id) === null || _invoice_id === void 0 ? void 0 : _invoice_id.includes(searchTerm));\n            });\n        }\n        // الفلترة\n        if (filterBy !== \"all\") {\n            filtered = filtered.filter((invoice)=>{\n                switch(filterBy){\n                    case \"consultation\":\n                        return invoice.services.some((s)=>s.serviceType === \"consultation\");\n                    case \"pattern\":\n                        return invoice.services.some((s)=>s.serviceType === \"pattern\");\n                    case \"photography\":\n                        return invoice.services.some((s)=>s.serviceType === \"photography\");\n                    case \"shipping\":\n                        return invoice.services.some((s)=>s.serviceType === \"shipping\");\n                    case \"high_value\":\n                        return invoice.total > 1000;\n                    case \"recent\":\n                        const oneWeekAgo = new Date();\n                        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n                        return new Date(invoice.createdAt) > oneWeekAgo;\n                    case \"paid_full\":\n                        return invoice.paymentStatus === \"مدفوع بالكامل\";\n                    case \"paid_partial\":\n                        return invoice.paymentStatus === \"مدفوع جزئياً\";\n                    case \"unpaid\":\n                        return invoice.paymentStatus === \"غير مدفوع\" || !invoice.paymentStatus;\n                    default:\n                        return true;\n                }\n            });\n        }\n        // الترتيب\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"date_desc\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"date_asc\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"amount_desc\":\n                    return b.total - a.total;\n                case \"amount_asc\":\n                    return a.total - b.total;\n                case \"client_name\":\n                    return a.client.name.localeCompare(b.client.name, \"ar\");\n                default:\n                    return 0;\n            }\n        });\n        setFilteredInvoices(filtered);\n    }, [\n        invoices,\n        searchTerm,\n        filterBy,\n        sortBy\n    ]);\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            calendar: \"gregory\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        // التحقق من صحة القيمة وتحويلها لرقم\n        const numericAmount = typeof amount === \"number\" ? amount : parseFloat(String(amount || 0));\n        // التحقق من أن النتيجة رقم صحيح\n        if (isNaN(numericAmount)) {\n            return \"0.00 جنيه\";\n        }\n        return \"\".concat(numericAmount.toFixed(2), \" جنيه\");\n    };\n    const getServiceNames = (services)=>{\n        const serviceNames = {\n            consultation: \"استشارات\",\n            pattern: \"باترون\",\n            pattern_printing: \"طباعة باترون\",\n            manufacturing: \"تصنيع\",\n            samples: \"عينات\",\n            shipping: \"شحن\",\n            marketing: \"تسويق\",\n            photography: \"تصوير\",\n            products: \"منتجات\",\n            raw_purchases: \"مشتريات خام\"\n        };\n        return services.map((s)=>serviceNames[s.serviceType] || s.serviceType).join(\"، \");\n    };\n    // دالة عرض رسائل التأكيد المحسنة\n    const showSuccessMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 3000);\n    };\n    const showErrorMessage = (message)=>{\n        const toast = document.createElement(\"div\");\n        toast.className = \"fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300\";\n        toast.innerHTML = '\\n      <div class=\"flex items-center gap-2\">\\n        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"/>\\n        </svg>\\n        <span>'.concat(message, \"</span>\\n      </div>\\n    \");\n        document.body.appendChild(toast);\n        setTimeout(()=>{\n            toast.style.transform = \"translateX(100%)\";\n            setTimeout(()=>{\n                if (document.body.contains(toast)) {\n                    document.body.removeChild(toast);\n                }\n            }, 300);\n        }, 4000);\n    };\n    const handleExportPDF = async (invoice)=>{\n        try {\n            // فتح المعاينة أولاً للحصول على نفس التنسيق\n            setPreviewInvoice(invoice);\n            // انتظار قصير للتأكد من عرض المعاينة\n            setTimeout(async ()=>{\n                try {\n                    await (0,_utils_pdfGenerator__WEBPACK_IMPORTED_MODULE_3__.generateInvoicePDF)(invoice);\n                    showSuccessMessage(\"تم تصدير PDF للفاتورة \".concat(invoice.id, \" بنجاح! \\uD83C\\uDF89\"));\n                    // إغلاق المعاينة بعد التصدير بثانية واحدة\n                    setTimeout(()=>{\n                        setPreviewInvoice(null);\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"❌ Error exporting PDF:\", error);\n                    showErrorMessage(\"فشل في تصدير PDF. يرجى المحاولة مرة أخرى.\");\n                    setPreviewInvoice(null);\n                }\n            }, 500);\n        } catch (error) {\n            showErrorMessage(\"حدث خطأ أثناء تصدير PDF.\");\n        }\n    };\n    const handlePreviewInvoice = (invoice)=>{\n        setPreviewInvoice(invoice);\n    };\n    const handlePOSInvoice = (invoice)=>{\n        setPosInvoice(invoice);\n    };\n    const handleClosePreview = ()=>{\n        setPreviewInvoice(null);\n    };\n    const handleEditFromPreview = ()=>{\n        // For now, just close the preview\n        // In a full implementation, this would navigate to edit mode\n        setPreviewInvoice(null);\n        alert(\"وظيفة التعديل ستكون متاحة قريباً\");\n    };\n    const handleExportFromPreview = async ()=>{\n        if (previewInvoice) {\n            await handleExportPDF(previewInvoice);\n        }\n    };\n    // دالة حذف الفاتورة مع تأكيد\n    const handleDeleteInvoice = (invoice)=>{\n        const confirmMessage = \"هل أنت متأكد من حذف هذه الفاتورة؟\\n\\nالفاتورة: \".concat(invoice.id, \"\\nالعميل: \").concat(invoice.client.name, \"\\nالمبلغ: \").concat(formatCurrency(invoice.total), \"\\n\\n⚠️ هذا الإجراء لا يمكن التراجع عنه!\");\n        if (window.confirm(confirmMessage)) {\n            const store = _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore.getState();\n            store.deleteInvoice(invoice.id);\n            if (previewInvoice && previewInvoice.id === invoice.id) {\n                setPreviewInvoice(null);\n            }\n            setTimeout(()=>{\n                const updatedInvoices = JSON.parse(localStorage.getItem(\"invoices\") || \"[]\");\n                setFilteredInvoices(updatedInvoices);\n            }, 100);\n            showSuccessMessage(\"تم حذف الفاتورة \".concat(invoice.id, \" بنجاح! \\uD83D\\uDDD1️\"));\n        }\n    };\n    const getTotalStats = ()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const count = filteredInvoices.length;\n        const avgAmount = count > 0 ? total / count : 0;\n        // حساب المبالغ المدفوعة والمتبقية\n        const totalPaid = filteredInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const totalRemaining = total - totalPaid;\n        // حساب عدد الفواتير حسب حالة الدفع\n        const paidFullCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const paidPartialCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const unpaidCount = filteredInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        // حساب نسبة التحصيل\n        const collectionRate = total > 0 ? totalPaid / total * 100 : 0;\n        return {\n            total,\n            count,\n            avgAmount,\n            totalPaid,\n            totalRemaining,\n            paidFullCount,\n            paidPartialCount,\n            unpaidCount,\n            collectionRate\n        };\n    };\n    const stats = getTotalStats();\n    // حساب إحصائيات اليوم الحالي\n    const getTodayStats = ()=>{\n        const today = new Date().toDateString();\n        const todayInvoices = invoices.filter((invoice)=>new Date(invoice.createdAt).toDateString() === today);\n        const todayTotal = todayInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const todayPaid = todayInvoices.reduce((sum, inv)=>sum + (inv.paidAmount || 0), 0);\n        const todayRemaining = todayTotal - todayPaid;\n        const todayPaidFullCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع بالكامل\").length;\n        const todayPaidPartialCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"مدفوع جزئياً\").length;\n        const todayUnpaidCount = todayInvoices.filter((inv)=>inv.paymentStatus === \"غير مدفوع\").length;\n        const todayCollectionRate = todayTotal > 0 ? todayPaid / todayTotal * 100 : 0;\n        return {\n            count: todayInvoices.length,\n            total: todayTotal,\n            paid: todayPaid,\n            remaining: todayRemaining,\n            paidFullCount: todayPaidFullCount,\n            paidPartialCount: todayPaidPartialCount,\n            unpaidCount: todayUnpaidCount,\n            collectionRate: todayCollectionRate\n        };\n    };\n    const todayStats = getTodayStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-4\",\n                        children: \"إدارة الفواتير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-l from-indigo-500 to-purple-600 text-white rounded-xl p-6 mb-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"\\uD83D\\uDCCA إحصائيات اليوم الحالي\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100 text-sm\",\n                                            children: new Date().toLocaleDateString(\"ar-EG\", {\n                                                weekday: \"long\",\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: todayStats.count\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"فواتير اليوم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"إجمالي المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(todayStats.paid)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"المبلغ المحصل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white bg-opacity-20 rounded-lg p-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    todayStats.collectionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-indigo-100 text-sm\",\n                                                children: \"نسبة التحصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 pt-4 border-t border-indigo-400 border-opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-green-200\",\n                                                    children: todayStats.paidFullCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-yellow-200\",\n                                                    children: todayStats.paidPartialCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold text-red-200\",\n                                                    children: todayStats.unpaidCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-indigo-100\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-700\",\n                                        children: \"إجمالي الفواتير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-800\",\n                                        children: stats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-4 rounded-lg border-r-4 border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-green-700\",\n                                        children: \"إجمالي المبلغ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-800\",\n                                        children: formatCurrency(stats.total)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 p-4 rounded-lg border-r-4 border-emerald-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-emerald-700\",\n                                        children: \"المبلغ المدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-emerald-800\",\n                                        children: formatCurrency(stats.totalPaid)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-lg border-r-4 border-red-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-red-700\",\n                                        children: \"المبلغ المتبقي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-red-800\",\n                                        children: formatCurrency(stats.totalRemaining)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-teal-50 p-4 rounded-lg border-r-4 border-teal-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-teal-700\",\n                                        children: \"مدفوع بالكامل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-teal-800\",\n                                        children: stats.paidFullCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-teal-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-orange-700\",\n                                        children: \"مدفوع جزئياً\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-800\",\n                                        children: stats.paidPartialCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg border-r-4 border-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-700\",\n                                        children: \"غير مدفوع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: stats.unpaidCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 p-4 rounded-lg border-r-4 border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-700\",\n                                        children: \"نسبة التحصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-800\",\n                                        children: [\n                                            stats.collectionRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: [\n                                            \"متوسط الفاتورة: \",\n                                            formatCurrency(stats.avgAmount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 rounded-lg shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"البحث\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"ابحث بالاسم، الهاتف، أو رقم الفاتورة\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"فلترة حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterBy,\n                                            onChange: (e)=>setFilterBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"جميع الفواتير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"consultation\",\n                                                    children: \"استشارات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pattern\",\n                                                    children: \"باترون\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"photography\",\n                                                    children: \"تصوير\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shipping\",\n                                                    children: \"شحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high_value\",\n                                                    children: \"قيمة عالية (+1000 جنيه)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"recent\",\n                                                    children: \"الأسبوع الماضي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_full\",\n                                                    children: \"مدفوع بالكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid_partial\",\n                                                    children: \"مدفوع جزئياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"unpaid\",\n                                                    children: \"غير مدفوع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"ترتيب حسب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_desc\",\n                                                    children: \"التاريخ (الأحدث أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"date_asc\",\n                                                    children: \"التاريخ (الأقدم أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_desc\",\n                                                    children: \"المبلغ (الأعلى أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"amount_asc\",\n                                                    children: \"المبلغ (الأقل أولاً)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"client_name\",\n                                                    children: \"اسم العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            filteredInvoices.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-lg mb-4\",\n                        children: invoices.length === 0 ? \"لا توجد فواتير محفوظة\" : \"لا توجد نتائج للبحث\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 11\n                    }, undefined),\n                    invoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"قم بإنشاء فاتورة جديدة لتظهر هنا\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-l from-blue-500 to-blue-600 text-white p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: invoice.client.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: [\n                                                        \"فاتورة #\",\n                                                        invoice.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: formatCurrency(invoice.total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100 text-sm\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"السيلز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.client.salesperson\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الخدمات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: getServiceNames(invoice.services)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"طريقة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: invoice.paymentMethod\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"حالة الدفع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-block px-2 py-1 rounded-full text-xs font-semibold \".concat(invoice.paymentStatus === \"مدفوع بالكامل\" ? \"bg-green-100 text-green-800\" : invoice.paymentStatus === \"مدفوع جزئياً\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: invoice.paymentStatus || \"غير محدد\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المدفوع / المتبقي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatCurrency(invoice.paidAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" / \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((invoice.remainingAmount || 0) > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                                            children: formatCurrency(invoice.remainingAmount || 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        invoice.requiresShipping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-2 rounded text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-yellow-700 font-medium\",\n                                                children: \"\\uD83D\\uDE9A يتطلب شحن\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleExportPDF(invoice),\n                                                className: \"bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDCC4 PDF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePreviewInvoice(invoice),\n                                                className: \"bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83D\\uDC41️ معاينة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(invoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-2 rounded transition-colors\",\n                                                children: \"\\uD83E\\uDDFE POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDeleteInvoice(invoice),\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-3 rounded transition-colors flex items-center justify-center gap-2\",\n                                        children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, invoice.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 521,\n                columnNumber: 9\n            }, undefined),\n            previewInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 bg-white border-b border-gray-200 p-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"معاينة الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClosePreview,\n                                    className: \"text-gray-500 hover:text-gray-700 text-2xl font-bold\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    invoice: previewInvoice,\n                                    onEdit: handleEditFromPreview,\n                                    onExportPDF: handleExportFromPreview\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePOSInvoice(previewInvoice),\n                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83E\\uDDFE فاتورة POS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteInvoice(previewInvoice),\n                                                className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded transition-colors flex items-center justify-center gap-2\",\n                                                children: \"\\uD83D\\uDDD1️ حذف الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 631,\n                columnNumber: 9\n            }, undefined),\n            posInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_POSInvoice__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                invoice: posInvoice,\n                onClose: ()=>setPosInvoice(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n                lineNumber: 673,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\231\\\\src\\\\components\\\\InvoicesList.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InvoicesList, \"vNSHrZ6bGj05wXDBkzjUPPk++oo=\", false, function() {\n    return [\n        _store_invoiceStore__WEBPACK_IMPORTED_MODULE_2__.useInvoiceStore\n    ];\n});\n_c = InvoicesList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicesList);\nvar _c;\n$RefreshReg$(_c, \"InvoicesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0ludm9pY2VzTGlzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVtRDtBQUVJO0FBQ0c7QUFDWjtBQUNSO0FBRXRDLE1BQU1PLGVBQXlCOztJQUM3QixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHTCxvRUFBZUE7SUFDcEMsTUFBTSxDQUFDTSxZQUFZQyxjQUFjLEdBQUdULCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ1UsVUFBVUMsWUFBWSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNZLFFBQVFDLFVBQVUsR0FBR2IsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDYyxrQkFBa0JDLG9CQUFvQixHQUFHZiwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RFLE1BQU0sQ0FBQ2dCLGdCQUFnQkMsa0JBQWtCLEdBQUdqQiwrQ0FBUUEsQ0FBaUI7SUFDckUsTUFBTSxDQUFDa0IsWUFBWUMsY0FBYyxHQUFHbkIsK0NBQVFBLENBQWlCO0lBRTdELHlEQUF5RDtJQUN6REMsZ0RBQVNBLENBQUM7UUFDUixNQUFNbUIsZUFBZTtZQUNuQix1Q0FBdUM7WUFDdkMsTUFBTUMsU0FBU25CLGdFQUFlQSxDQUFDb0IsUUFBUSxHQUFHZixRQUFRO1lBRWxELHVDQUF1QztZQUN2QyxNQUFNZ0IsaUJBQWlCRixPQUFPRyxNQUFNLENBQUMsQ0FBQ0MsU0FBa0JDLE9BQWVDLE9BQ3JFRCxVQUFVQyxLQUFLQyxTQUFTLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsS0FBS0wsUUFBUUssRUFBRTtZQUd2RCx1QkFBdUI7WUFDdkJmLG9CQUFvQlE7UUFDdEI7UUFFQSxhQUFhO1FBQ2JIO1FBRUEseURBQXlEO1FBQ3pELE1BQU1XLHNCQUFzQixDQUFDQztZQUMzQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssWUFBWTtnQkFDeEJiO1lBQ0Y7UUFDRjtRQUVBYyxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXSjtRQUVuQyxPQUFPO1lBQ0xHLE9BQU9FLG1CQUFtQixDQUFDLFdBQVdMO1FBQ3hDO0lBQ0YsR0FBRyxFQUFFO0lBRUw5QixnREFBU0EsQ0FBQztRQUNSLElBQUlvQyxXQUFXO2VBQUk5QjtTQUFTO1FBRTVCLFFBQVE7UUFDUixJQUFJQyxZQUFZO1lBQ2Q2QixXQUFXQSxTQUFTYixNQUFNLENBQUNDLENBQUFBO29CQUd6QkE7dUJBRkFBLFFBQVFhLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ2pDLFdBQVdnQyxXQUFXLE9BQ2pFZixRQUFRYSxNQUFNLENBQUNJLEtBQUssQ0FBQ0QsUUFBUSxDQUFDakMsaUJBQzlCaUIsY0FBQUEsUUFBUUssRUFBRSxjQUFWTCxrQ0FBQUEsWUFBWWdCLFFBQVEsQ0FBQ2pDOztRQUV6QjtRQUVBLFVBQVU7UUFDVixJQUFJRSxhQUFhLE9BQU87WUFDdEIyQixXQUFXQSxTQUFTYixNQUFNLENBQUNDLENBQUFBO2dCQUN6QixPQUFRZjtvQkFDTixLQUFLO3dCQUNILE9BQU9lLFFBQVFrQixRQUFRLENBQUNDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVyxLQUFLO29CQUN0RCxLQUFLO3dCQUNILE9BQU9yQixRQUFRa0IsUUFBUSxDQUFDQyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFdBQVcsS0FBSztvQkFDdEQsS0FBSzt3QkFDSCxPQUFPckIsUUFBUWtCLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxXQUFXLEtBQUs7b0JBQ3RELEtBQUs7d0JBQ0gsT0FBT3JCLFFBQVFrQixRQUFRLENBQUNDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVyxLQUFLO29CQUN0RCxLQUFLO3dCQUNILE9BQU9yQixRQUFRc0IsS0FBSyxHQUFHO29CQUN6QixLQUFLO3dCQUNILE1BQU1DLGFBQWEsSUFBSUM7d0JBQ3ZCRCxXQUFXRSxPQUFPLENBQUNGLFdBQVdHLE9BQU8sS0FBSzt3QkFDMUMsT0FBTyxJQUFJRixLQUFLeEIsUUFBUTJCLFNBQVMsSUFBSUo7b0JBQ3ZDLEtBQUs7d0JBQ0gsT0FBT3ZCLFFBQVE0QixhQUFhLEtBQUs7b0JBQ25DLEtBQUs7d0JBQ0gsT0FBTzVCLFFBQVE0QixhQUFhLEtBQUs7b0JBQ25DLEtBQUs7d0JBQ0gsT0FBTzVCLFFBQVE0QixhQUFhLEtBQUssZUFBZSxDQUFDNUIsUUFBUTRCLGFBQWE7b0JBQ3hFO3dCQUNFLE9BQU87Z0JBQ1g7WUFDRjtRQUNGO1FBRUEsVUFBVTtRQUNWaEIsU0FBU2lCLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUNoQixPQUFRNUM7Z0JBQ04sS0FBSztvQkFDSCxPQUFPLElBQUlxQyxLQUFLTyxFQUFFSixTQUFTLEVBQUVLLE9BQU8sS0FBSyxJQUFJUixLQUFLTSxFQUFFSCxTQUFTLEVBQUVLLE9BQU87Z0JBQ3hFLEtBQUs7b0JBQ0gsT0FBTyxJQUFJUixLQUFLTSxFQUFFSCxTQUFTLEVBQUVLLE9BQU8sS0FBSyxJQUFJUixLQUFLTyxFQUFFSixTQUFTLEVBQUVLLE9BQU87Z0JBQ3hFLEtBQUs7b0JBQ0gsT0FBT0QsRUFBRVQsS0FBSyxHQUFHUSxFQUFFUixLQUFLO2dCQUMxQixLQUFLO29CQUNILE9BQU9RLEVBQUVSLEtBQUssR0FBR1MsRUFBRVQsS0FBSztnQkFDMUIsS0FBSztvQkFDSCxPQUFPUSxFQUFFakIsTUFBTSxDQUFDQyxJQUFJLENBQUNtQixhQUFhLENBQUNGLEVBQUVsQixNQUFNLENBQUNDLElBQUksRUFBRTtnQkFDcEQ7b0JBQ0UsT0FBTztZQUNYO1FBQ0Y7UUFFQXhCLG9CQUFvQnNCO0lBQ3RCLEdBQUc7UUFBQzlCO1FBQVVDO1FBQVlFO1FBQVVFO0tBQU87SUFFM0MsTUFBTStDLGFBQWEsQ0FBQ0M7UUFDbEIsT0FBTyxJQUFJWCxLQUFLVyxZQUFZQyxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3REQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsS0FBSztZQUNMQyxVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDQztRQUN0QixxQ0FBcUM7UUFDckMsTUFBTUMsZ0JBQWdCLE9BQU9ELFdBQVcsV0FBV0EsU0FBU0UsV0FBV0MsT0FBT0gsVUFBVTtRQUV4RixnQ0FBZ0M7UUFDaEMsSUFBSUksTUFBTUgsZ0JBQWdCO1lBQ3hCLE9BQU87UUFDVDtRQUVBLE9BQU8sR0FBNEIsT0FBekJBLGNBQWNJLE9BQU8sQ0FBQyxJQUFHO0lBQ3JDO0lBRUEsTUFBTUMsa0JBQWtCLENBQUM5QjtRQUN2QixNQUFNK0IsZUFBMEM7WUFDOUNDLGNBQWM7WUFDZEMsU0FBUztZQUNUQyxrQkFBa0I7WUFDbEJDLGVBQWU7WUFDZkMsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLGVBQWU7UUFDakI7UUFFQSxPQUFPekMsU0FBUzBDLEdBQUcsQ0FBQ3hDLENBQUFBLElBQUs2QixZQUFZLENBQUM3QixFQUFFQyxXQUFXLENBQUMsSUFBSUQsRUFBRUMsV0FBVyxFQUFFd0MsSUFBSSxDQUFDO0lBQzlFO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxRQUFRQyxTQUFTQyxhQUFhLENBQUM7UUFDckNGLE1BQU1HLFNBQVMsR0FBRztRQUNsQkgsTUFBTUksU0FBUyxHQUFHLDRWQUtFLE9BQVJMLFNBQVE7UUFJcEJFLFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDTjtRQUMxQk8sV0FBVztZQUNUUCxNQUFNUSxLQUFLLENBQUNDLFNBQVMsR0FBRztZQUN4QkYsV0FBVztnQkFDVCxJQUFJTixTQUFTSSxJQUFJLENBQUNLLFFBQVEsQ0FBQ1YsUUFBUTtvQkFDakNDLFNBQVNJLElBQUksQ0FBQ00sV0FBVyxDQUFDWDtnQkFDNUI7WUFDRixHQUFHO1FBQ0wsR0FBRztJQUNMO0lBRUEsTUFBTVksbUJBQW1CLENBQUNiO1FBQ3hCLE1BQU1DLFFBQVFDLFNBQVNDLGFBQWEsQ0FBQztRQUNyQ0YsTUFBTUcsU0FBUyxHQUFHO1FBQ2xCSCxNQUFNSSxTQUFTLEdBQUcsOGFBS0UsT0FBUkwsU0FBUTtRQUlwQkUsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO1FBQzFCTyxXQUFXO1lBQ1RQLE1BQU1RLEtBQUssQ0FBQ0MsU0FBUyxHQUFHO1lBQ3hCRixXQUFXO2dCQUNULElBQUlOLFNBQVNJLElBQUksQ0FBQ0ssUUFBUSxDQUFDVixRQUFRO29CQUNqQ0MsU0FBU0ksSUFBSSxDQUFDTSxXQUFXLENBQUNYO2dCQUM1QjtZQUNGLEdBQUc7UUFDTCxHQUFHO0lBQ0w7SUFFQSxNQUFNYSxrQkFBa0IsT0FBTzdFO1FBQzdCLElBQUk7WUFDRiw0Q0FBNEM7WUFDNUNSLGtCQUFrQlE7WUFFbEIscUNBQXFDO1lBQ3JDdUUsV0FBVztnQkFDVCxJQUFJO29CQUNGLE1BQU03Rix1RUFBa0JBLENBQUNzQjtvQkFDekI4RCxtQkFBbUIseUJBQW9DLE9BQVg5RCxRQUFRSyxFQUFFLEVBQUM7b0JBRXZELDBDQUEwQztvQkFDMUNrRSxXQUFXO3dCQUNUL0Usa0JBQWtCO29CQUNwQixHQUFHO2dCQUNMLEVBQUUsT0FBT3NGLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO29CQUN4Q0YsaUJBQWlCO29CQUNqQnBGLGtCQUFrQjtnQkFDcEI7WUFDRixHQUFHO1FBQ0wsRUFBRSxPQUFPc0YsT0FBTztZQUNkRixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLE1BQU1JLHVCQUF1QixDQUFDaEY7UUFDNUJSLGtCQUFrQlE7SUFDcEI7SUFFQSxNQUFNaUYsbUJBQW1CLENBQUNqRjtRQUN4Qk4sY0FBY007SUFDaEI7SUFFQSxNQUFNa0YscUJBQXFCO1FBQ3pCMUYsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTTJGLHdCQUF3QjtRQUM1QixrQ0FBa0M7UUFDbEMsNkRBQTZEO1FBQzdEM0Ysa0JBQWtCO1FBQ2xCNEYsTUFBTTtJQUNSO0lBSUEsTUFBTUMsMEJBQTBCO1FBQzlCLElBQUk5RixnQkFBZ0I7WUFDbEIsTUFBTXNGLGdCQUFnQnRGO1FBQ3hCO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTStGLHNCQUFzQixDQUFDdEY7UUFDM0IsTUFBTXVGLGlCQUFpQixrREFBeUV2RixPQUF2QkEsUUFBUUssRUFBRSxFQUFDLGNBQTRDb0MsT0FBaEN6QyxRQUFRYSxNQUFNLENBQUNDLElBQUksRUFBQyxjQUEwQyxPQUE5QjJCLGVBQWV6QyxRQUFRc0IsS0FBSyxHQUFFO1FBRTlKLElBQUliLE9BQU8rRSxPQUFPLENBQUNELGlCQUFpQjtZQUNsQyxNQUFNRSxRQUFRaEgsZ0VBQWVBLENBQUNvQixRQUFRO1lBQ3RDNEYsTUFBTUMsYUFBYSxDQUFDMUYsUUFBUUssRUFBRTtZQUU5QixJQUFJZCxrQkFBa0JBLGVBQWVjLEVBQUUsS0FBS0wsUUFBUUssRUFBRSxFQUFFO2dCQUN0RGIsa0JBQWtCO1lBQ3BCO1lBRUErRSxXQUFXO2dCQUNULE1BQU1vQixrQkFBa0JDLEtBQUtDLEtBQUssQ0FBQ0MsYUFBYUMsT0FBTyxDQUFDLGVBQWU7Z0JBQ3ZFekcsb0JBQW9CcUc7WUFDdEIsR0FBRztZQUVIN0IsbUJBQW1CLG1CQUE4QixPQUFYOUQsUUFBUUssRUFBRSxFQUFDO1FBQ25EO0lBQ0Y7SUFFQSxNQUFNMkYsZ0JBQWdCO1FBQ3BCLE1BQU0xRSxRQUFRakMsaUJBQWlCNEcsTUFBTSxDQUFDLENBQUNDLEtBQUs5RixNQUFROEYsTUFBTTlGLElBQUlrQixLQUFLLEVBQUU7UUFDckUsTUFBTTZFLFFBQVE5RyxpQkFBaUIrRyxNQUFNO1FBQ3JDLE1BQU1DLFlBQVlGLFFBQVEsSUFBSTdFLFFBQVE2RSxRQUFRO1FBRTlDLGtDQUFrQztRQUNsQyxNQUFNRyxZQUFZakgsaUJBQWlCNEcsTUFBTSxDQUFDLENBQUNDLEtBQUs5RixNQUFROEYsTUFBTzlGLENBQUFBLElBQUltRyxVQUFVLElBQUksSUFBSTtRQUNyRixNQUFNQyxpQkFBaUJsRixRQUFRZ0Y7UUFFL0IsbUNBQW1DO1FBQ25DLE1BQU1HLGdCQUFnQnBILGlCQUFpQlUsTUFBTSxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJd0IsYUFBYSxLQUFLLGlCQUFpQndFLE1BQU07UUFDbEcsTUFBTU0sbUJBQW1CckgsaUJBQWlCVSxNQUFNLENBQUNLLENBQUFBLE1BQU9BLElBQUl3QixhQUFhLEtBQUssZ0JBQWdCd0UsTUFBTTtRQUNwRyxNQUFNTyxjQUFjdEgsaUJBQWlCVSxNQUFNLENBQUNLLENBQUFBLE1BQU9BLElBQUl3QixhQUFhLEtBQUssYUFBYXdFLE1BQU07UUFFNUYsb0JBQW9CO1FBQ3BCLE1BQU1RLGlCQUFpQnRGLFFBQVEsSUFBSSxZQUFhQSxRQUFTLE1BQU07UUFFL0QsT0FBTztZQUNMQTtZQUNBNkU7WUFDQUU7WUFDQUM7WUFDQUU7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7UUFDRjtJQUNGO0lBRUEsTUFBTUMsUUFBUWI7SUFFZCw2QkFBNkI7SUFDN0IsTUFBTWMsZ0JBQWdCO1FBQ3BCLE1BQU1DLFFBQVEsSUFBSXZGLE9BQU93RixZQUFZO1FBQ3JDLE1BQU1DLGdCQUFnQm5JLFNBQVNpQixNQUFNLENBQUNDLENBQUFBLFVBQ3BDLElBQUl3QixLQUFLeEIsUUFBUTJCLFNBQVMsRUFBRXFGLFlBQVksT0FBT0Q7UUFHakQsTUFBTUcsYUFBYUQsY0FBY2hCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLOUYsTUFBUThGLE1BQU05RixJQUFJa0IsS0FBSyxFQUFFO1FBQ3ZFLE1BQU02RixZQUFZRixjQUFjaEIsTUFBTSxDQUFDLENBQUNDLEtBQUs5RixNQUFROEYsTUFBTzlGLENBQUFBLElBQUltRyxVQUFVLElBQUksSUFBSTtRQUNsRixNQUFNYSxpQkFBaUJGLGFBQWFDO1FBRXBDLE1BQU1FLHFCQUFxQkosY0FBY2xILE1BQU0sQ0FBQ0ssQ0FBQUEsTUFBT0EsSUFBSXdCLGFBQWEsS0FBSyxpQkFBaUJ3RSxNQUFNO1FBQ3BHLE1BQU1rQix3QkFBd0JMLGNBQWNsSCxNQUFNLENBQUNLLENBQUFBLE1BQU9BLElBQUl3QixhQUFhLEtBQUssZ0JBQWdCd0UsTUFBTTtRQUN0RyxNQUFNbUIsbUJBQW1CTixjQUFjbEgsTUFBTSxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJd0IsYUFBYSxLQUFLLGFBQWF3RSxNQUFNO1FBRTlGLE1BQU1vQixzQkFBc0JOLGFBQWEsSUFBSSxZQUFhQSxhQUFjLE1BQU07UUFFOUUsT0FBTztZQUNMZixPQUFPYyxjQUFjYixNQUFNO1lBQzNCOUUsT0FBTzRGO1lBQ1BPLE1BQU1OO1lBQ05PLFdBQVdOO1lBQ1hYLGVBQWVZO1lBQ2ZYLGtCQUFrQlk7WUFDbEJYLGFBQWFZO1lBQ2JYLGdCQUFnQlk7UUFDbEI7SUFDRjtJQUVBLE1BQU1HLGFBQWFiO0lBRW5CLHFCQUNFLDhEQUFDYztRQUFJekQsV0FBVTs7MEJBRWIsOERBQUN5RDtnQkFBSXpELFdBQVU7O2tDQUNiLDhEQUFDMEQ7d0JBQUcxRCxXQUFVO2tDQUEwQjs7Ozs7O2tDQUd4Qyw4REFBQ3lEO3dCQUFJekQsV0FBVTs7MENBQ2IsOERBQUN5RDtnQ0FBSXpELFdBQVU7O2tEQUNiLDhEQUFDMkQ7d0NBQUczRCxXQUFVOzswREFDWiw4REFBQzREO2dEQUFJNUQsV0FBVTtnREFBVTZELE1BQUs7Z0RBQWVDLFNBQVE7MERBQ25ELDRFQUFDQztvREFBS0MsVUFBUztvREFBVUMsR0FBRTtvREFBeUpDLFVBQVM7Ozs7Ozs7Ozs7OzRDQUN6TDs7Ozs7OztrREFHUiw4REFBQ1Q7d0NBQUl6RCxXQUFVO2tEQUNiLDRFQUFDbUU7NENBQUVuRSxXQUFVO3NEQUNWLElBQUkzQyxPQUFPWSxrQkFBa0IsQ0FBQyxTQUFTO2dEQUN0Q21HLFNBQVM7Z0RBQ1RsRyxNQUFNO2dEQUNOQyxPQUFPO2dEQUNQQyxLQUFLOzRDQUNQOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLTiw4REFBQ3FGO2dDQUFJekQsV0FBVTs7a0RBQ2IsOERBQUN5RDt3Q0FBSXpELFdBQVU7OzBEQUNiLDhEQUFDeUQ7Z0RBQUl6RCxXQUFVOzBEQUFzQndELFdBQVd4QixLQUFLOzs7Ozs7MERBQ3JELDhEQUFDeUI7Z0RBQUl6RCxXQUFVOzBEQUEwQjs7Ozs7Ozs7Ozs7O2tEQUczQyw4REFBQ3lEO3dDQUFJekQsV0FBVTs7MERBQ2IsOERBQUN5RDtnREFBSXpELFdBQVU7MERBQXNCMUIsZUFBZWtGLFdBQVdyRyxLQUFLOzs7Ozs7MERBQ3BFLDhEQUFDc0c7Z0RBQUl6RCxXQUFVOzBEQUEwQjs7Ozs7Ozs7Ozs7O2tEQUczQyw4REFBQ3lEO3dDQUFJekQsV0FBVTs7MERBQ2IsOERBQUN5RDtnREFBSXpELFdBQVU7MERBQXNCMUIsZUFBZWtGLFdBQVdGLElBQUk7Ozs7OzswREFDbkUsOERBQUNHO2dEQUFJekQsV0FBVTswREFBMEI7Ozs7Ozs7Ozs7OztrREFHM0MsOERBQUN5RDt3Q0FBSXpELFdBQVU7OzBEQUNiLDhEQUFDeUQ7Z0RBQUl6RCxXQUFVOztvREFBc0J3RCxXQUFXZixjQUFjLENBQUM3RCxPQUFPLENBQUM7b0RBQUc7Ozs7Ozs7MERBQzFFLDhEQUFDNkU7Z0RBQUl6RCxXQUFVOzBEQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUs3Qyw4REFBQ3lEO2dDQUFJekQsV0FBVTswQ0FDYiw0RUFBQ3lEO29DQUFJekQsV0FBVTs7c0RBQ2IsOERBQUN5RDs7OERBQ0MsOERBQUNBO29EQUFJekQsV0FBVTs4REFBd0N3RCxXQUFXbEIsYUFBYTs7Ozs7OzhEQUMvRSw4REFBQ21CO29EQUFJekQsV0FBVTs4REFBMEI7Ozs7Ozs7Ozs7OztzREFFM0MsOERBQUN5RDs7OERBQ0MsOERBQUNBO29EQUFJekQsV0FBVTs4REFBeUN3RCxXQUFXakIsZ0JBQWdCOzs7Ozs7OERBQ25GLDhEQUFDa0I7b0RBQUl6RCxXQUFVOzhEQUEwQjs7Ozs7Ozs7Ozs7O3NEQUUzQyw4REFBQ3lEOzs4REFDQyw4REFBQ0E7b0RBQUl6RCxXQUFVOzhEQUFzQ3dELFdBQVdoQixXQUFXOzs7Ozs7OERBQzNFLDhEQUFDaUI7b0RBQUl6RCxXQUFVOzhEQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT2pELDhEQUFDeUQ7d0JBQUl6RCxXQUFVOzswQ0FFYiw4REFBQ3lEO2dDQUFJekQsV0FBVTs7a0RBQ2IsOERBQUNxRTt3Q0FBR3JFLFdBQVU7a0RBQXNDOzs7Ozs7a0RBQ3BELDhEQUFDbUU7d0NBQUVuRSxXQUFVO2tEQUFvQzBDLE1BQU1WLEtBQUs7Ozs7Ozs7Ozs7OzswQ0FHOUQsOERBQUN5QjtnQ0FBSXpELFdBQVU7O2tEQUNiLDhEQUFDcUU7d0NBQUdyRSxXQUFVO2tEQUF1Qzs7Ozs7O2tEQUNyRCw4REFBQ21FO3dDQUFFbkUsV0FBVTtrREFBcUMxQixlQUFlb0UsTUFBTXZGLEtBQUs7Ozs7Ozs7Ozs7OzswQ0FHOUUsOERBQUNzRztnQ0FBSXpELFdBQVU7O2tEQUNiLDhEQUFDcUU7d0NBQUdyRSxXQUFVO2tEQUF5Qzs7Ozs7O2tEQUN2RCw4REFBQ21FO3dDQUFFbkUsV0FBVTtrREFBdUMxQixlQUFlb0UsTUFBTVAsU0FBUzs7Ozs7Ozs7Ozs7OzBDQUdwRiw4REFBQ3NCO2dDQUFJekQsV0FBVTs7a0RBQ2IsOERBQUNxRTt3Q0FBR3JFLFdBQVU7a0RBQXFDOzs7Ozs7a0RBQ25ELDhEQUFDbUU7d0NBQUVuRSxXQUFVO2tEQUFtQzFCLGVBQWVvRSxNQUFNTCxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3ZGLDhEQUFDb0I7d0JBQUl6RCxXQUFVOzswQ0FDYiw4REFBQ3lEO2dDQUFJekQsV0FBVTs7a0RBQ2IsOERBQUNxRTt3Q0FBR3JFLFdBQVU7a0RBQXNDOzs7Ozs7a0RBQ3BELDhEQUFDbUU7d0NBQUVuRSxXQUFVO2tEQUFvQzBDLE1BQU1KLGFBQWE7Ozs7OztrREFDcEUsOERBQUM2Qjt3Q0FBRW5FLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBR3ZDLDhEQUFDeUQ7Z0NBQUl6RCxXQUFVOztrREFDYiw4REFBQ3FFO3dDQUFHckUsV0FBVTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUNtRTt3Q0FBRW5FLFdBQVU7a0RBQXNDMEMsTUFBTUgsZ0JBQWdCOzs7Ozs7a0RBQ3pFLDhEQUFDNEI7d0NBQUVuRSxXQUFVO2tEQUEwQjs7Ozs7Ozs7Ozs7OzBDQUd6Qyw4REFBQ3lEO2dDQUFJekQsV0FBVTs7a0RBQ2IsOERBQUNxRTt3Q0FBR3JFLFdBQVU7a0RBQXNDOzs7Ozs7a0RBQ3BELDhEQUFDbUU7d0NBQUVuRSxXQUFVO2tEQUFvQzBDLE1BQU1GLFdBQVc7Ozs7OztrREFDbEUsOERBQUMyQjt3Q0FBRW5FLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBR3ZDLDhEQUFDeUQ7Z0NBQUl6RCxXQUFVOztrREFDYiw4REFBQ3FFO3dDQUFHckUsV0FBVTtrREFBd0M7Ozs7OztrREFDdEQsOERBQUNtRTt3Q0FBRW5FLFdBQVU7OzRDQUFzQzBDLE1BQU1ELGNBQWMsQ0FBQzdELE9BQU8sQ0FBQzs0Q0FBRzs7Ozs7OztrREFDbkYsOERBQUN1Rjt3Q0FBRW5FLFdBQVU7OzRDQUEwQjs0Q0FBaUIxQixlQUFlb0UsTUFBTVIsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLMUYsOERBQUN1Qjt3QkFBSXpELFdBQVU7a0NBQ2IsNEVBQUN5RDs0QkFBSXpELFdBQVU7OzhDQUViLDhEQUFDeUQ7O3NEQUNDLDhEQUFDYTs0Q0FBTXRFLFdBQVU7c0RBQStDOzs7Ozs7c0RBQ2hFLDhEQUFDdUU7NENBQ0NDLE1BQUs7NENBQ0xDLE9BQU83Sjs0Q0FDUDhKLFVBQVUsQ0FBQ3RJLElBQU12QixjQUFjdUIsRUFBRXVJLE1BQU0sQ0FBQ0YsS0FBSzs0Q0FDN0NHLGFBQVk7NENBQ1o1RSxXQUFVOzs7Ozs7Ozs7Ozs7OENBS2QsOERBQUN5RDs7c0RBQ0MsOERBQUNhOzRDQUFNdEUsV0FBVTtzREFBK0M7Ozs7OztzREFDaEUsOERBQUM2RTs0Q0FDQ0osT0FBTzNKOzRDQUNQNEosVUFBVSxDQUFDdEksSUFBTXJCLFlBQVlxQixFQUFFdUksTUFBTSxDQUFDRixLQUFLOzRDQUMzQ3pFLFdBQVU7OzhEQUVWLDhEQUFDOEU7b0RBQU9MLE9BQU07OERBQU07Ozs7Ozs4REFDcEIsOERBQUNLO29EQUFPTCxPQUFNOzhEQUFlOzs7Ozs7OERBQzdCLDhEQUFDSztvREFBT0wsT0FBTTs4REFBVTs7Ozs7OzhEQUN4Qiw4REFBQ0s7b0RBQU9MLE9BQU07OERBQWM7Ozs7Ozs4REFDNUIsOERBQUNLO29EQUFPTCxPQUFNOzhEQUFXOzs7Ozs7OERBQ3pCLDhEQUFDSztvREFBT0wsT0FBTTs4REFBYTs7Ozs7OzhEQUMzQiw4REFBQ0s7b0RBQU9MLE9BQU07OERBQVM7Ozs7Ozs4REFDdkIsOERBQUNLO29EQUFPTCxPQUFNOzhEQUFZOzs7Ozs7OERBQzFCLDhEQUFDSztvREFBT0wsT0FBTTs4REFBZTs7Ozs7OzhEQUM3Qiw4REFBQ0s7b0RBQU9MLE9BQU07OERBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLM0IsOERBQUNoQjs7c0RBQ0MsOERBQUNhOzRDQUFNdEUsV0FBVTtzREFBK0M7Ozs7OztzREFDaEUsOERBQUM2RTs0Q0FDQ0osT0FBT3pKOzRDQUNQMEosVUFBVSxDQUFDdEksSUFBTW5CLFVBQVVtQixFQUFFdUksTUFBTSxDQUFDRixLQUFLOzRDQUN6Q3pFLFdBQVU7OzhEQUVWLDhEQUFDOEU7b0RBQU9MLE9BQU07OERBQVk7Ozs7Ozs4REFDMUIsOERBQUNLO29EQUFPTCxPQUFNOzhEQUFXOzs7Ozs7OERBQ3pCLDhEQUFDSztvREFBT0wsT0FBTTs4REFBYzs7Ozs7OzhEQUM1Qiw4REFBQ0s7b0RBQU9MLE9BQU07OERBQWE7Ozs7Ozs4REFDM0IsOERBQUNLO29EQUFPTCxPQUFNOzhEQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVFyQ3ZKLGlCQUFpQitHLE1BQU0sS0FBSyxrQkFDM0IsOERBQUN3QjtnQkFBSXpELFdBQVU7O2tDQUNiLDhEQUFDeUQ7d0JBQUl6RCxXQUFVO2tDQUNackYsU0FBU3NILE1BQU0sS0FBSyxJQUFJLDBCQUEwQjs7Ozs7O29CQUVwRHRILFNBQVNzSCxNQUFNLEtBQUssbUJBQ25CLDhEQUFDa0M7d0JBQUVuRSxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7MENBSWpDLDhEQUFDeUQ7Z0JBQUl6RCxXQUFVOzBCQUNaOUUsaUJBQWlCdUUsR0FBRyxDQUFDLENBQUM1RCx3QkFDckIsOERBQUM0SDt3QkFBcUJ6RCxXQUFVOzswQ0FFOUIsOERBQUN5RDtnQ0FBSXpELFdBQVU7MENBQ2IsNEVBQUN5RDtvQ0FBSXpELFdBQVU7O3NEQUNiLDhEQUFDeUQ7OzhEQUNDLDhEQUFDWTtvREFBR3JFLFdBQVU7OERBQXlCbkUsUUFBUWEsTUFBTSxDQUFDQyxJQUFJOzs7Ozs7OERBQzFELDhEQUFDd0g7b0RBQUVuRSxXQUFVOzt3REFBd0I7d0RBQVNuRSxRQUFRSyxFQUFFOzs7Ozs7Ozs7Ozs7O3NEQUUxRCw4REFBQ3VIOzRDQUFJekQsV0FBVTs7OERBQ2IsOERBQUNtRTtvREFBRW5FLFdBQVU7OERBQXNCMUIsZUFBZXpDLFFBQVFzQixLQUFLOzs7Ozs7OERBQy9ELDhEQUFDZ0g7b0RBQUVuRSxXQUFVOzhEQUF5QmpDLFdBQVdsQyxRQUFRMkIsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXhFLDhEQUFDaUc7Z0NBQUl6RCxXQUFVOzBDQUNiLDRFQUFDeUQ7b0NBQUl6RCxXQUFVOztzREFDYiw4REFBQ3lEOzs4REFDQyw4REFBQ1U7b0RBQUVuRSxXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ21FO29EQUFFbkUsV0FBVTs4REFBZW5FLFFBQVFhLE1BQU0sQ0FBQ0ksS0FBSzs7Ozs7Ozs7Ozs7O3NEQUdsRCw4REFBQzJHOzs4REFDQyw4REFBQ1U7b0RBQUVuRSxXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ21FO29EQUFFbkUsV0FBVTs4REFBZW5FLFFBQVFhLE1BQU0sQ0FBQ3FJLFdBQVc7Ozs7Ozs7Ozs7OztzREFHeEQsOERBQUN0Qjs7OERBQ0MsOERBQUNVO29EQUFFbkUsV0FBVTs4REFBd0I7Ozs7Ozs4REFDckMsOERBQUNtRTtvREFBRW5FLFdBQVU7OERBQXVCbkIsZ0JBQWdCaEQsUUFBUWtCLFFBQVE7Ozs7Ozs7Ozs7OztzREFHdEUsOERBQUMwRzs7OERBQ0MsOERBQUNVO29EQUFFbkUsV0FBVTs4REFBd0I7Ozs7Ozs4REFDckMsOERBQUNtRTtvREFBRW5FLFdBQVU7OERBQWVuRSxRQUFRbUosYUFBYTs7Ozs7Ozs7Ozs7O3NEQUduRCw4REFBQ3ZCOzs4REFDQyw4REFBQ1U7b0RBQUVuRSxXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ3lEO29EQUFJekQsV0FBVyw2REFJZixPQUhDbkUsUUFBUTRCLGFBQWEsS0FBSyxrQkFBa0IsZ0NBQzVDNUIsUUFBUTRCLGFBQWEsS0FBSyxpQkFBaUIsa0NBQzNDOzhEQUVDNUIsUUFBUTRCLGFBQWEsSUFBSTs7Ozs7Ozs7Ozs7O3NEQUk5Qiw4REFBQ2dHOzs4REFDQyw4REFBQ1U7b0RBQUVuRSxXQUFVOzhEQUF3Qjs7Ozs7OzhEQUNyQyw4REFBQ21FO29EQUFFbkUsV0FBVTs7c0VBQ1gsOERBQUNpRjs0REFBS2pGLFdBQVU7c0VBQWtCMUIsZUFBZXpDLFFBQVF1RyxVQUFVLElBQUk7Ozs7Ozt3REFDdEU7c0VBQ0QsOERBQUM2Qzs0REFBS2pGLFdBQVcsR0FBMEUsT0FBdkUsQ0FBQ25FLFFBQVFxSixlQUFlLElBQUksS0FBSyxJQUFJLGlCQUFpQjtzRUFDdkU1RyxlQUFlekMsUUFBUXFKLGVBQWUsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQUtoRHJKLFFBQVFzSixnQkFBZ0Isa0JBQ3ZCLDhEQUFDMUI7NENBQUl6RCxXQUFVO3NEQUNiLDRFQUFDaUY7Z0RBQUtqRixXQUFVOzBEQUE4Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPdEQsOERBQUN5RDtnQ0FBSXpELFdBQVU7O2tEQUNiLDhEQUFDeUQ7d0NBQUl6RCxXQUFVOzswREFDYiw4REFBQ29GO2dEQUNDQyxTQUFTLElBQU0zRSxnQkFBZ0I3RTtnREFDL0JtRSxXQUFVOzBEQUNYOzs7Ozs7MERBSUQsOERBQUNvRjtnREFDQ0MsU0FBUyxJQUFNeEUscUJBQXFCaEY7Z0RBQ3BDbUUsV0FBVTswREFDWDs7Ozs7OzBEQUlELDhEQUFDb0Y7Z0RBQ0NDLFNBQVMsSUFBTXZFLGlCQUFpQmpGO2dEQUNoQ21FLFdBQVU7MERBQ1g7Ozs7Ozs7Ozs7OztrREFNSCw4REFBQ29GO3dDQUNDQyxTQUFTLElBQU1sRSxvQkFBb0J0Rjt3Q0FDbkNtRSxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7O3VCQWpHS25FLFFBQVFLLEVBQUU7Ozs7Ozs7Ozs7WUEyR3pCZCxnQ0FDQyw4REFBQ3FJO2dCQUFJekQsV0FBVTswQkFDYiw0RUFBQ3lEO29CQUFJekQsV0FBVTs7c0NBQ2IsOERBQUN5RDs0QkFBSXpELFdBQVU7OzhDQUNiLDhEQUFDMkQ7b0NBQUczRCxXQUFVOzhDQUFvQjs7Ozs7OzhDQUNsQyw4REFBQ29GO29DQUNDQyxTQUFTdEU7b0NBQ1RmLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7OztzQ0FJSCw4REFBQ3lEOzRCQUFJekQsV0FBVTs7OENBQ2IsOERBQUN4Rix1REFBY0E7b0NBQ2JxQixTQUFTVDtvQ0FDVGtLLFFBQVF0RTtvQ0FDUnVFLGFBQWFyRTs7Ozs7OzhDQUlmLDhEQUFDdUM7b0NBQUl6RCxXQUFVOzhDQUNiLDRFQUFDeUQ7d0NBQUl6RCxXQUFVOzswREFDYiw4REFBQ29GO2dEQUNDQyxTQUFTLElBQU12RSxpQkFBaUIxRjtnREFDaEM0RSxXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUNvRjtnREFDQ0MsU0FBUyxJQUFNbEUsb0JBQW9CL0Y7Z0RBQ25DNEUsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVdaMUUsNEJBQ0MsOERBQUNiLG1EQUFVQTtnQkFDVG9CLFNBQVNQO2dCQUNUa0ssU0FBUyxJQUFNakssY0FBYzs7Ozs7Ozs7Ozs7O0FBS3ZDO0dBOXBCTWI7O1FBQ2lCSixnRUFBZUE7OztLQURoQ0k7QUFncUJOLCtEQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0ludm9pY2VzTGlzdC50c3g/ZmJkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSW52b2ljZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgdXNlSW52b2ljZVN0b3JlIH0gZnJvbSAnQC9zdG9yZS9pbnZvaWNlU3RvcmUnO1xuaW1wb3J0IHsgZ2VuZXJhdGVJbnZvaWNlUERGIH0gZnJvbSAnQC91dGlscy9wZGZHZW5lcmF0b3InO1xuaW1wb3J0IEludm9pY2VQcmV2aWV3IGZyb20gJy4vSW52b2ljZVByZXZpZXcnO1xuaW1wb3J0IFBPU0ludm9pY2UgZnJvbSAnLi9QT1NJbnZvaWNlJztcblxuY29uc3QgSW52b2ljZXNMaXN0OiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgeyBpbnZvaWNlcyB9ID0gdXNlSW52b2ljZVN0b3JlKCk7XG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2ZpbHRlckJ5LCBzZXRGaWx0ZXJCeV0gPSB1c2VTdGF0ZSgnYWxsJyk7XG4gIGNvbnN0IFtzb3J0QnksIHNldFNvcnRCeV0gPSB1c2VTdGF0ZSgnZGF0ZV9kZXNjJyk7XG4gIGNvbnN0IFtmaWx0ZXJlZEludm9pY2VzLCBzZXRGaWx0ZXJlZEludm9pY2VzXSA9IHVzZVN0YXRlPEludm9pY2VbXT4oW10pO1xuICBjb25zdCBbcHJldmlld0ludm9pY2UsIHNldFByZXZpZXdJbnZvaWNlXSA9IHVzZVN0YXRlPEludm9pY2UgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Bvc0ludm9pY2UsIHNldFBvc0ludm9pY2VdID0gdXNlU3RhdGU8SW52b2ljZSB8IG51bGw+KG51bGwpO1xuXG4gIC8vINil2LnYp9iv2Kkg2KrYrdmF2YrZhCDYp9mE2KjZitin2YbYp9iqINmF2YYg2KfZhNiq2K7YstmK2YYg2KfZhNii2YXZhiDYudmG2K8g2KrYrdmF2YrZhCDYp9mE2YXZg9mI2YZcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkSW52b2ljZXMgPSAoKSA9PiB7XG4gICAgICAvLyDYp9iz2KrYrtiv2KfZhSDYp9mE2YbYuNin2YUg2KfZhNii2YXZhiDZhNiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqlxuICAgICAgY29uc3QgcmVzdWx0ID0gdXNlSW52b2ljZVN0b3JlLmdldFN0YXRlKCkuaW52b2ljZXM7XG5cbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINi52K/ZhSDZiNis2YjYryDYqtmD2LHYp9ixINmB2Yog2KfZhNio2YrYp9mG2KfYqlxuICAgICAgY29uc3QgdW5pcXVlSW52b2ljZXMgPSByZXN1bHQuZmlsdGVyKChpbnZvaWNlOiBJbnZvaWNlLCBpbmRleDogbnVtYmVyLCBzZWxmOiBJbnZvaWNlW10pID0+XG4gICAgICAgIGluZGV4ID09PSBzZWxmLmZpbmRJbmRleChpbnYgPT4gaW52LmlkID09PSBpbnZvaWNlLmlkKVxuICAgICAgKTtcblxuICAgICAgLy8g2KrYrdiv2YrYqyDYp9mE2K3Yp9mE2Kkg2KfZhNmF2K3ZhNmK2KlcbiAgICAgIHNldEZpbHRlcmVkSW52b2ljZXModW5pcXVlSW52b2ljZXMpO1xuICAgIH07XG5cbiAgICAvLyDYqtit2YXZitmEINmB2YjYsdmKXG4gICAgbG9hZEludm9pY2VzKCk7XG5cbiAgICAvLyDYp9mE2KfYs9iq2YXYp9i5INmE2KrYutmK2YrYsdin2KogbG9jYWxTdG9yYWdlINio2K/ZhNin2Ysg2YXZhiDYp9mE2KrYrdiv2YrYqyDYp9mE2K/ZiNix2YpcbiAgICBjb25zdCBoYW5kbGVTdG9yYWdlQ2hhbmdlID0gKGU6IFN0b3JhZ2VFdmVudCkgPT4ge1xuICAgICAgaWYgKGUua2V5ID09PSAnaW52b2ljZXMnKSB7XG4gICAgICAgIGxvYWRJbnZvaWNlcygpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzdG9yYWdlJywgaGFuZGxlU3RvcmFnZUNoYW5nZSk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gWy4uLmludm9pY2VzXTtcblxuICAgIC8vINin2YTYqNit2KtcbiAgICBpZiAoc2VhcmNoVGVybSkge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoaW52b2ljZSA9PiBcbiAgICAgICAgaW52b2ljZS5jbGllbnQubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgaW52b2ljZS5jbGllbnQucGhvbmUuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgICAgaW52b2ljZS5pZD8uaW5jbHVkZXMoc2VhcmNoVGVybSlcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8g2KfZhNmB2YTYqtix2KlcbiAgICBpZiAoZmlsdGVyQnkgIT09ICdhbGwnKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihpbnZvaWNlID0+IHtcbiAgICAgICAgc3dpdGNoIChmaWx0ZXJCeSkge1xuICAgICAgICAgIGNhc2UgJ2NvbnN1bHRhdGlvbic6XG4gICAgICAgICAgICByZXR1cm4gaW52b2ljZS5zZXJ2aWNlcy5zb21lKHMgPT4gcy5zZXJ2aWNlVHlwZSA9PT0gJ2NvbnN1bHRhdGlvbicpO1xuICAgICAgICAgIGNhc2UgJ3BhdHRlcm4nOlxuICAgICAgICAgICAgcmV0dXJuIGludm9pY2Uuc2VydmljZXMuc29tZShzID0+IHMuc2VydmljZVR5cGUgPT09ICdwYXR0ZXJuJyk7XG4gICAgICAgICAgY2FzZSAncGhvdG9ncmFwaHknOlxuICAgICAgICAgICAgcmV0dXJuIGludm9pY2Uuc2VydmljZXMuc29tZShzID0+IHMuc2VydmljZVR5cGUgPT09ICdwaG90b2dyYXBoeScpO1xuICAgICAgICAgIGNhc2UgJ3NoaXBwaW5nJzpcbiAgICAgICAgICAgIHJldHVybiBpbnZvaWNlLnNlcnZpY2VzLnNvbWUocyA9PiBzLnNlcnZpY2VUeXBlID09PSAnc2hpcHBpbmcnKTtcbiAgICAgICAgICBjYXNlICdoaWdoX3ZhbHVlJzpcbiAgICAgICAgICAgIHJldHVybiBpbnZvaWNlLnRvdGFsID4gMTAwMDtcbiAgICAgICAgICBjYXNlICdyZWNlbnQnOlxuICAgICAgICAgICAgY29uc3Qgb25lV2Vla0FnbyA9IG5ldyBEYXRlKCk7XG4gICAgICAgICAgICBvbmVXZWVrQWdvLnNldERhdGUob25lV2Vla0Fnby5nZXREYXRlKCkgLSA3KTtcbiAgICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShpbnZvaWNlLmNyZWF0ZWRBdCkgPiBvbmVXZWVrQWdvO1xuICAgICAgICAgIGNhc2UgJ3BhaWRfZnVsbCc6XG4gICAgICAgICAgICByZXR1cm4gaW52b2ljZS5wYXltZW50U3RhdHVzID09PSAn2YXYr9mB2YjYuSDYqNin2YTZg9in2YXZhCc7XG4gICAgICAgICAgY2FzZSAncGFpZF9wYXJ0aWFsJzpcbiAgICAgICAgICAgIHJldHVybiBpbnZvaWNlLnBheW1lbnRTdGF0dXMgPT09ICfZhdiv2YHZiNi5INis2LLYptmK2KfZiyc7XG4gICAgICAgICAgY2FzZSAndW5wYWlkJzpcbiAgICAgICAgICAgIHJldHVybiBpbnZvaWNlLnBheW1lbnRTdGF0dXMgPT09ICfYutmK2LEg2YXYr9mB2YjYuScgfHwgIWludm9pY2UucGF5bWVudFN0YXR1cztcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vINin2YTYqtix2KrZitioXG4gICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4ge1xuICAgICAgc3dpdGNoIChzb3J0QnkpIHtcbiAgICAgICAgY2FzZSAnZGF0ZV9kZXNjJzpcbiAgICAgICAgICByZXR1cm4gbmV3IERhdGUoYi5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGEuY3JlYXRlZEF0KS5nZXRUaW1lKCk7XG4gICAgICAgIGNhc2UgJ2RhdGVfYXNjJzpcbiAgICAgICAgICByZXR1cm4gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCk7XG4gICAgICAgIGNhc2UgJ2Ftb3VudF9kZXNjJzpcbiAgICAgICAgICByZXR1cm4gYi50b3RhbCAtIGEudG90YWw7XG4gICAgICAgIGNhc2UgJ2Ftb3VudF9hc2MnOlxuICAgICAgICAgIHJldHVybiBhLnRvdGFsIC0gYi50b3RhbDtcbiAgICAgICAgY2FzZSAnY2xpZW50X25hbWUnOlxuICAgICAgICAgIHJldHVybiBhLmNsaWVudC5uYW1lLmxvY2FsZUNvbXBhcmUoYi5jbGllbnQubmFtZSwgJ2FyJyk7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgcmV0dXJuIDA7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBzZXRGaWx0ZXJlZEludm9pY2VzKGZpbHRlcmVkKTtcbiAgfSwgW2ludm9pY2VzLCBzZWFyY2hUZXJtLCBmaWx0ZXJCeSwgc29ydEJ5XSk7XG5cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1FRycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBjYWxlbmRhcjogJ2dyZWdvcnknLCAvLyDYp9mE2KrZgtmI2YrZhSDYp9mE2YXZitmE2KfYr9mKXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoYW1vdW50OiBudW1iZXIgfCBzdHJpbmcgfCB1bmRlZmluZWQgfCBudWxsKSA9PiB7XG4gICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2LXYrdipINin2YTZgtmK2YXYqSDZiNiq2K3ZiNmK2YTZh9inINmE2LHZgtmFXG4gICAgY29uc3QgbnVtZXJpY0Ftb3VudCA9IHR5cGVvZiBhbW91bnQgPT09ICdudW1iZXInID8gYW1vdW50IDogcGFyc2VGbG9hdChTdHJpbmcoYW1vdW50IHx8IDApKTtcblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINij2YYg2KfZhNmG2KrZitis2Kkg2LHZgtmFINi12K3ZititXG4gICAgaWYgKGlzTmFOKG51bWVyaWNBbW91bnQpKSB7XG4gICAgICByZXR1cm4gJzAuMDAg2KzZhtmK2YcnO1xuICAgIH1cblxuICAgIHJldHVybiBgJHtudW1lcmljQW1vdW50LnRvRml4ZWQoMil9INis2YbZitmHYDtcbiAgfTtcblxuICBjb25zdCBnZXRTZXJ2aWNlTmFtZXMgPSAoc2VydmljZXM6IGFueVtdKSA9PiB7XG4gICAgY29uc3Qgc2VydmljZU5hbWVzOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICAgY29uc3VsdGF0aW9uOiAn2KfYs9iq2LTYp9ix2KfYqicsXG4gICAgICBwYXR0ZXJuOiAn2KjYp9iq2LHZiNmGJyxcbiAgICAgIHBhdHRlcm5fcHJpbnRpbmc6ICfYt9io2KfYudipINio2KfYqtix2YjZhicsXG4gICAgICBtYW51ZmFjdHVyaW5nOiAn2KrYtdmG2YrYuScsXG4gICAgICBzYW1wbGVzOiAn2LnZitmG2KfYqicsXG4gICAgICBzaGlwcGluZzogJ9i02K3ZhicsXG4gICAgICBtYXJrZXRpbmc6ICfYqtiz2YjZitmCJyxcbiAgICAgIHBob3RvZ3JhcGh5OiAn2KrYtdmI2YrYsScsXG4gICAgICBwcm9kdWN0czogJ9mF2YbYqtis2KfYqicsXG4gICAgICByYXdfcHVyY2hhc2VzOiAn2YXYtNiq2LHZitin2Kog2K7Yp9mFJyxcbiAgICB9O1xuICAgIFxuICAgIHJldHVybiBzZXJ2aWNlcy5tYXAocyA9PiBzZXJ2aWNlTmFtZXNbcy5zZXJ2aWNlVHlwZV0gfHwgcy5zZXJ2aWNlVHlwZSkuam9pbign2IwgJyk7XG4gIH07XG5cbiAgLy8g2K/Yp9mE2Kkg2LnYsdi2INix2LPYp9im2YQg2KfZhNiq2KPZg9mK2K8g2KfZhNmF2K3Ys9mG2KlcbiAgY29uc3Qgc2hvd1N1Y2Nlc3NNZXNzYWdlID0gKG1lc3NhZ2U6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHRvYXN0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gICAgdG9hc3QuY2xhc3NOYW1lID0gJ2ZpeGVkIHRvcC00IHJpZ2h0LTQgYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgcHgtNiBweS0zIHJvdW5kZWQtbGcgc2hhZG93LWxnIHotNTAgdHJhbnNmb3JtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCc7XG4gICAgdG9hc3QuaW5uZXJIVE1MID0gYFxuICAgICAgPGRpdiBjbGFzcz1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgIDxzdmcgY2xhc3M9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2em0zLjcwNy05LjI5M2ExIDEgMCAwMC0xLjQxNC0xLjQxNEw5IDEwLjU4NiA3LjcwNyA5LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNGwyIDJhMSAxIDAgMDAxLjQxNCAwbDQtNHpcIiBjbGlwLXJ1bGU9XCJldmVub2RkXCIvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICAgPHNwYW4+JHttZXNzYWdlfTwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgIGA7XG5cbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHRvYXN0KTtcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHRvYXN0LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVYKDEwMCUpJztcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBpZiAoZG9jdW1lbnQuYm9keS5jb250YWlucyh0b2FzdCkpIHtcbiAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKHRvYXN0KTtcbiAgICAgICAgfVxuICAgICAgfSwgMzAwKTtcbiAgICB9LCAzMDAwKTtcbiAgfTtcblxuICBjb25zdCBzaG93RXJyb3JNZXNzYWdlID0gKG1lc3NhZ2U6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHRvYXN0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gICAgdG9hc3QuY2xhc3NOYW1lID0gJ2ZpeGVkIHRvcC00IHJpZ2h0LTQgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIHNoYWRvdy1sZyB6LTUwIHRyYW5zZm9ybSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnO1xuICAgIHRvYXN0LmlubmVySFRNTCA9IGBcbiAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICA8c3ZnIGNsYXNzPVwidy01IGgtNVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgPHBhdGggZmlsbC1ydWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTAgMThhOCA4IDAgMTAwLTE2IDggOCAwIDAwMCAxNnpNOC43MDcgNy4yOTNhMSAxIDAgMDAtMS40MTQgMS40MTRMOC41ODYgMTBsLTEuMjkzIDEuMjkzYTEgMSAwIDEwMS40MTQgMS40MTRMMTAgMTEuNDE0bDEuMjkzIDEuMjkzYTEgMSAwIDAwMS40MTQtMS40MTRMMTEuNDE0IDEwbDEuMjkzLTEuMjkzYTEgMSAwIDAwLTEuNDE0LTEuNDE0TDEwIDguNTg2IDguNzA3IDcuMjkzelwiIGNsaXAtcnVsZT1cImV2ZW5vZGRcIi8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgICA8c3Bhbj4ke21lc3NhZ2V9PC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgYDtcblxuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodG9hc3QpO1xuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgdG9hc3Quc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVgoMTAwJSknO1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChkb2N1bWVudC5ib2R5LmNvbnRhaW5zKHRvYXN0KSkge1xuICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodG9hc3QpO1xuICAgICAgICB9XG4gICAgICB9LCAzMDApO1xuICAgIH0sIDQwMDApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydFBERiA9IGFzeW5jIChpbnZvaWNlOiBJbnZvaWNlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vINmB2KrYrSDYp9mE2YXYudin2YrZhtipINij2YjZhNin2Ysg2YTZhNit2LXZiNmEINi52YTZiSDZhtmB2LMg2KfZhNiq2YbYs9mK2YJcbiAgICAgIHNldFByZXZpZXdJbnZvaWNlKGludm9pY2UpO1xuXG4gICAgICAvLyDYp9mG2KrYuNin2LEg2YLYtdmK2LEg2YTZhNiq2KPZg9ivINmF2YYg2LnYsdi2INin2YTZhdi52KfZitmG2KlcbiAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGdlbmVyYXRlSW52b2ljZVBERihpbnZvaWNlKTtcbiAgICAgICAgICBzaG93U3VjY2Vzc01lc3NhZ2UoYNiq2YUg2KrYtdiv2YrYsSBQREYg2YTZhNmB2KfYqtmI2LHYqSAke2ludm9pY2UuaWR9INio2YbYrNin2K0hIPCfjolgKTtcblxuICAgICAgICAgIC8vINil2LrZhNin2YIg2KfZhNmF2LnYp9mK2YbYqSDYqNi52K8g2KfZhNiq2LXYr9mK2LEg2KjYq9in2YbZitipINmI2KfYrdiv2KlcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIHNldFByZXZpZXdJbnZvaWNlKG51bGwpO1xuICAgICAgICAgIH0sIDEwMDApO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBleHBvcnRpbmcgUERGOicsIGVycm9yKTtcbiAgICAgICAgICBzaG93RXJyb3JNZXNzYWdlKCfZgdi02YQg2YHZiiDYqti12K/ZitixIFBERi4g2YrYsdis2Ykg2KfZhNmF2K3Yp9mI2YTYqSDZhdix2Kkg2KPYrtix2YkuJyk7XG4gICAgICAgICAgc2V0UHJldmlld0ludm9pY2UobnVsbCk7XG4gICAgICAgIH1cbiAgICAgIH0sIDUwMCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNob3dFcnJvck1lc3NhZ2UoJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYqti12K/ZitixIFBERi4nKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUHJldmlld0ludm9pY2UgPSAoaW52b2ljZTogSW52b2ljZSkgPT4ge1xuICAgIHNldFByZXZpZXdJbnZvaWNlKGludm9pY2UpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBPU0ludm9pY2UgPSAoaW52b2ljZTogSW52b2ljZSkgPT4ge1xuICAgIHNldFBvc0ludm9pY2UoaW52b2ljZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2xvc2VQcmV2aWV3ID0gKCkgPT4ge1xuICAgIHNldFByZXZpZXdJbnZvaWNlKG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXRGcm9tUHJldmlldyA9ICgpID0+IHtcbiAgICAvLyBGb3Igbm93LCBqdXN0IGNsb3NlIHRoZSBwcmV2aWV3XG4gICAgLy8gSW4gYSBmdWxsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIG5hdmlnYXRlIHRvIGVkaXQgbW9kZVxuICAgIHNldFByZXZpZXdJbnZvaWNlKG51bGwpO1xuICAgIGFsZXJ0KCfZiNi42YrZgdipINin2YTYqti52K/ZitmEINiz2KrZg9mI2YYg2YXYqtin2K3YqSDZgtix2YrYqNin2YsnKTtcbiAgfTtcblxuXG5cbiAgY29uc3QgaGFuZGxlRXhwb3J0RnJvbVByZXZpZXcgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHByZXZpZXdJbnZvaWNlKSB7XG4gICAgICBhd2FpdCBoYW5kbGVFeHBvcnRQREYocHJldmlld0ludm9pY2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDYr9in2YTYqSDYrdiw2YEg2KfZhNmB2KfYqtmI2LHYqSDZhdi5INiq2KPZg9mK2K9cbiAgY29uc3QgaGFuZGxlRGVsZXRlSW52b2ljZSA9IChpbnZvaWNlOiBJbnZvaWNlKSA9PiB7XG4gICAgY29uc3QgY29uZmlybU1lc3NhZ2UgPSBg2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINit2LDZgSDZh9iw2Ycg2KfZhNmB2KfYqtmI2LHYqdifXFxuXFxu2KfZhNmB2KfYqtmI2LHYqTogJHtpbnZvaWNlLmlkfVxcbtin2YTYudmF2YrZhDogJHtpbnZvaWNlLmNsaWVudC5uYW1lfVxcbtin2YTZhdio2YTYujogJHtmb3JtYXRDdXJyZW5jeShpbnZvaWNlLnRvdGFsKX1cXG5cXG7imqDvuI8g2YfYsNinINin2YTYpdis2LHYp9ihINmE2Kcg2YrZhdmD2YYg2KfZhNiq2LHYp9is2Lkg2LnZhtmHIWA7XG5cbiAgICBpZiAod2luZG93LmNvbmZpcm0oY29uZmlybU1lc3NhZ2UpKSB7XG4gICAgICBjb25zdCBzdG9yZSA9IHVzZUludm9pY2VTdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgc3RvcmUuZGVsZXRlSW52b2ljZShpbnZvaWNlLmlkKTtcblxuICAgICAgaWYgKHByZXZpZXdJbnZvaWNlICYmIHByZXZpZXdJbnZvaWNlLmlkID09PSBpbnZvaWNlLmlkKSB7XG4gICAgICAgIHNldFByZXZpZXdJbnZvaWNlKG51bGwpO1xuICAgICAgfVxuXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEludm9pY2VzID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnaW52b2ljZXMnKSB8fCAnW10nKTtcbiAgICAgICAgc2V0RmlsdGVyZWRJbnZvaWNlcyh1cGRhdGVkSW52b2ljZXMpO1xuICAgICAgfSwgMTAwKTtcblxuICAgICAgc2hvd1N1Y2Nlc3NNZXNzYWdlKGDYqtmFINit2LDZgSDYp9mE2YHYp9iq2YjYsdipICR7aW52b2ljZS5pZH0g2KjZhtis2KfYrSEg8J+Xke+4j2ApO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRUb3RhbFN0YXRzID0gKCkgPT4ge1xuICAgIGNvbnN0IHRvdGFsID0gZmlsdGVyZWRJbnZvaWNlcy5yZWR1Y2UoKHN1bSwgaW52KSA9PiBzdW0gKyBpbnYudG90YWwsIDApO1xuICAgIGNvbnN0IGNvdW50ID0gZmlsdGVyZWRJbnZvaWNlcy5sZW5ndGg7XG4gICAgY29uc3QgYXZnQW1vdW50ID0gY291bnQgPiAwID8gdG90YWwgLyBjb3VudCA6IDA7XG5cbiAgICAvLyDYrdiz2KfYqCDYp9mE2YXYqNin2YTYuiDYp9mE2YXYr9mB2YjYudipINmI2KfZhNmF2KrYqNmC2YrYqVxuICAgIGNvbnN0IHRvdGFsUGFpZCA9IGZpbHRlcmVkSW52b2ljZXMucmVkdWNlKChzdW0sIGludikgPT4gc3VtICsgKGludi5wYWlkQW1vdW50IHx8IDApLCAwKTtcbiAgICBjb25zdCB0b3RhbFJlbWFpbmluZyA9IHRvdGFsIC0gdG90YWxQYWlkO1xuXG4gICAgLy8g2K3Ys9in2Kgg2LnYr9ivINin2YTZgdmI2KfYqtmK2LEg2K3Ys9ioINit2KfZhNipINin2YTYr9mB2LlcbiAgICBjb25zdCBwYWlkRnVsbENvdW50ID0gZmlsdGVyZWRJbnZvaWNlcy5maWx0ZXIoaW52ID0+IGludi5wYXltZW50U3RhdHVzID09PSAn2YXYr9mB2YjYuSDYqNin2YTZg9in2YXZhCcpLmxlbmd0aDtcbiAgICBjb25zdCBwYWlkUGFydGlhbENvdW50ID0gZmlsdGVyZWRJbnZvaWNlcy5maWx0ZXIoaW52ID0+IGludi5wYXltZW50U3RhdHVzID09PSAn2YXYr9mB2YjYuSDYrNiy2KbZitin2YsnKS5sZW5ndGg7XG4gICAgY29uc3QgdW5wYWlkQ291bnQgPSBmaWx0ZXJlZEludm9pY2VzLmZpbHRlcihpbnYgPT4gaW52LnBheW1lbnRTdGF0dXMgPT09ICfYutmK2LEg2YXYr9mB2YjYuScpLmxlbmd0aDtcblxuICAgIC8vINit2LPYp9ioINmG2LPYqNipINin2YTYqtit2LXZitmEXG4gICAgY29uc3QgY29sbGVjdGlvblJhdGUgPSB0b3RhbCA+IDAgPyAodG90YWxQYWlkIC8gdG90YWwpICogMTAwIDogMDtcblxuICAgIHJldHVybiB7XG4gICAgICB0b3RhbCxcbiAgICAgIGNvdW50LFxuICAgICAgYXZnQW1vdW50LFxuICAgICAgdG90YWxQYWlkLFxuICAgICAgdG90YWxSZW1haW5pbmcsXG4gICAgICBwYWlkRnVsbENvdW50LFxuICAgICAgcGFpZFBhcnRpYWxDb3VudCxcbiAgICAgIHVucGFpZENvdW50LFxuICAgICAgY29sbGVjdGlvblJhdGVcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IHN0YXRzID0gZ2V0VG90YWxTdGF0cygpO1xuXG4gIC8vINit2LPYp9ioINil2K3Ytdin2KbZitin2Kog2KfZhNmK2YjZhSDYp9mE2K3Yp9mE2YpcbiAgY29uc3QgZ2V0VG9kYXlTdGF0cyA9ICgpID0+IHtcbiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCk7XG4gICAgY29uc3QgdG9kYXlJbnZvaWNlcyA9IGludm9pY2VzLmZpbHRlcihpbnZvaWNlID0+XG4gICAgICBuZXcgRGF0ZShpbnZvaWNlLmNyZWF0ZWRBdCkudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5XG4gICAgKTtcblxuICAgIGNvbnN0IHRvZGF5VG90YWwgPSB0b2RheUludm9pY2VzLnJlZHVjZSgoc3VtLCBpbnYpID0+IHN1bSArIGludi50b3RhbCwgMCk7XG4gICAgY29uc3QgdG9kYXlQYWlkID0gdG9kYXlJbnZvaWNlcy5yZWR1Y2UoKHN1bSwgaW52KSA9PiBzdW0gKyAoaW52LnBhaWRBbW91bnQgfHwgMCksIDApO1xuICAgIGNvbnN0IHRvZGF5UmVtYWluaW5nID0gdG9kYXlUb3RhbCAtIHRvZGF5UGFpZDtcblxuICAgIGNvbnN0IHRvZGF5UGFpZEZ1bGxDb3VudCA9IHRvZGF5SW52b2ljZXMuZmlsdGVyKGludiA9PiBpbnYucGF5bWVudFN0YXR1cyA9PT0gJ9mF2K/ZgdmI2Lkg2KjYp9mE2YPYp9mF2YQnKS5sZW5ndGg7XG4gICAgY29uc3QgdG9kYXlQYWlkUGFydGlhbENvdW50ID0gdG9kYXlJbnZvaWNlcy5maWx0ZXIoaW52ID0+IGludi5wYXltZW50U3RhdHVzID09PSAn2YXYr9mB2YjYuSDYrNiy2KbZitin2YsnKS5sZW5ndGg7XG4gICAgY29uc3QgdG9kYXlVbnBhaWRDb3VudCA9IHRvZGF5SW52b2ljZXMuZmlsdGVyKGludiA9PiBpbnYucGF5bWVudFN0YXR1cyA9PT0gJ9i62YrYsSDZhdiv2YHZiNi5JykubGVuZ3RoO1xuXG4gICAgY29uc3QgdG9kYXlDb2xsZWN0aW9uUmF0ZSA9IHRvZGF5VG90YWwgPiAwID8gKHRvZGF5UGFpZCAvIHRvZGF5VG90YWwpICogMTAwIDogMDtcblxuICAgIHJldHVybiB7XG4gICAgICBjb3VudDogdG9kYXlJbnZvaWNlcy5sZW5ndGgsXG4gICAgICB0b3RhbDogdG9kYXlUb3RhbCxcbiAgICAgIHBhaWQ6IHRvZGF5UGFpZCxcbiAgICAgIHJlbWFpbmluZzogdG9kYXlSZW1haW5pbmcsXG4gICAgICBwYWlkRnVsbENvdW50OiB0b2RheVBhaWRGdWxsQ291bnQsXG4gICAgICBwYWlkUGFydGlhbENvdW50OiB0b2RheVBhaWRQYXJ0aWFsQ291bnQsXG4gICAgICB1bnBhaWRDb3VudDogdG9kYXlVbnBhaWRDb3VudCxcbiAgICAgIGNvbGxlY3Rpb25SYXRlOiB0b2RheUNvbGxlY3Rpb25SYXRlXG4gICAgfTtcbiAgfTtcblxuICBjb25zdCB0b2RheVN0YXRzID0gZ2V0VG9kYXlTdGF0cygpO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBwLTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCBtYi00XCI+2KXYr9in2LHYqSDYp9mE2YHZiNin2KrZitixPC9oMT5cblxuICAgICAgICB7Lyog2KXYrdi12KfYptmK2KfYqiDYp9mE2YrZiNmFINin2YTYrdin2YTZiiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1sIGZyb20taW5kaWdvLTUwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC14bCBwLTYgbWItNiBzaGFkb3ctbGdcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTYgMmExIDEgMCAwMC0xIDF2MUg0YTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY2YTIgMiAwIDAwLTItMmgtMVYzYTEgMSAwIDEwLTIgMHYxSDdWM2ExIDEgMCAwMC0xLTF6bTAgNWExIDEgMCAwMDAgMmg4YTEgMSAwIDEwMC0ySDZ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAg8J+TiiDYpdit2LXYp9im2YrYp9iqINin2YTZitmI2YUg2KfZhNit2KfZhNmKXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTEwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1FRycsIHtcbiAgICAgICAgICAgICAgICAgIHdlZWtkYXk6ICdsb25nJyxcbiAgICAgICAgICAgICAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgICAgICAgICBkYXk6ICdudW1lcmljJ1xuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBiZy1vcGFjaXR5LTIwIHJvdW5kZWQtbGcgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3RvZGF5U3RhdHMuY291bnR9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1pbmRpZ28tMTAwIHRleHQtc21cIj7ZgdmI2KfYqtmK2LEg2KfZhNmK2YjZhTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYmctb3BhY2l0eS0yMCByb3VuZGVkLWxnIHAtNCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRDdXJyZW5jeSh0b2RheVN0YXRzLnRvdGFsKX08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWluZGlnby0xMDAgdGV4dC1zbVwiPtil2KzZhdin2YTZiiDYp9mE2YXYqNmK2LnYp9iqPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBiZy1vcGFjaXR5LTIwIHJvdW5kZWQtbGcgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e2Zvcm1hdEN1cnJlbmN5KHRvZGF5U3RhdHMucGFpZCl9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1pbmRpZ28tMTAwIHRleHQtc21cIj7Yp9mE2YXYqNmE2Log2KfZhNmF2K3YtdmEPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBiZy1vcGFjaXR5LTIwIHJvdW5kZWQtbGcgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+e3RvZGF5U3RhdHMuY29sbGVjdGlvblJhdGUudG9GaXhlZCgxKX0lPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1pbmRpZ28tMTAwIHRleHQtc21cIj7Zhtiz2KjYqSDYp9mE2KrYrdi12YrZhDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog2KrZgdin2LXZitmEINil2LbYp9mB2YrYqSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcHQtNCBib3JkZXItdCBib3JkZXItaW5kaWdvLTQwMCBib3JkZXItb3BhY2l0eS0zMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi0yMDBcIj57dG9kYXlTdGF0cy5wYWlkRnVsbENvdW50fTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWluZGlnby0xMDBcIj7Zhdiv2YHZiNi5INio2KfZhNmD2KfZhdmEPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQteWVsbG93LTIwMFwiPnt0b2RheVN0YXRzLnBhaWRQYXJ0aWFsQ291bnR9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtaW5kaWdvLTEwMFwiPtmF2K/ZgdmI2Lkg2KzYstim2YrYp9mLPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtcmVkLTIwMFwiPnt0b2RheVN0YXRzLnVucGFpZENvdW50fTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWluZGlnby0xMDBcIj7YutmK2LEg2YXYr9mB2YjYuTwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiDYpdit2LXYp9im2YrYp9iqINiz2LHZiti52KkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgey8qINin2YTYtdmBINin2YTYo9mI2YQgLSDYp9mE2KXYrdi12KfYptmK2KfYqiDYp9mE2KPYs9in2LPZitipICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBwLTQgcm91bmRlZC1sZyBib3JkZXItci00IGJvcmRlci1ibHVlLTUwMFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtNzAwXCI+2KXYrNmF2KfZhNmKINin2YTZgdmI2KfYqtmK2LE8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS04MDBcIj57c3RhdHMuY291bnR9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBwLTQgcm91bmRlZC1sZyBib3JkZXItci00IGJvcmRlci1ncmVlbi01MDBcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi03MDBcIj7Ypdis2YXYp9mE2Yog2KfZhNmF2KjZhNi6PC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTgwMFwiPntmb3JtYXRDdXJyZW5jeShzdGF0cy50b3RhbCl9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1lbWVyYWxkLTUwIHAtNCByb3VuZGVkLWxnIGJvcmRlci1yLTQgYm9yZGVyLWVtZXJhbGQtNTAwXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZW1lcmFsZC03MDBcIj7Yp9mE2YXYqNmE2Log2KfZhNmF2K/ZgdmI2Lk8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZW1lcmFsZC04MDBcIj57Zm9ybWF0Q3VycmVuY3koc3RhdHMudG90YWxQYWlkKX08L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBwLTQgcm91bmRlZC1sZyBib3JkZXItci00IGJvcmRlci1yZWQtNTAwXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtcmVkLTcwMFwiPtin2YTZhdio2YTYuiDYp9mE2YXYqtio2YLZijwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtODAwXCI+e2Zvcm1hdEN1cnJlbmN5KHN0YXRzLnRvdGFsUmVtYWluaW5nKX08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYp9mE2LXZgSDYp9mE2KvYp9mG2YogLSDYpdit2LXYp9im2YrYp9iqINit2KfZhNipINin2YTYr9mB2LkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy10ZWFsLTUwIHAtNCByb3VuZGVkLWxnIGJvcmRlci1yLTQgYm9yZGVyLXRlYWwtNTAwXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtdGVhbC03MDBcIj7Zhdiv2YHZiNi5INio2KfZhNmD2KfZhdmEPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXRlYWwtODAwXCI+e3N0YXRzLnBhaWRGdWxsQ291bnR9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRlYWwtNjAwXCI+2YHYp9iq2YjYsdipPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAgcC00IHJvdW5kZWQtbGcgYm9yZGVyLXItNCBib3JkZXItb3JhbmdlLTUwMFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LW9yYW5nZS03MDBcIj7Zhdiv2YHZiNi5INis2LLYptmK2KfZizwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtODAwXCI+e3N0YXRzLnBhaWRQYXJ0aWFsQ291bnR9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW9yYW5nZS02MDBcIj7Zgdin2KrZiNix2Kk8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGcgYm9yZGVyLXItNCBib3JkZXItZ3JheS01MDBcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMFwiPti62YrYsSDZhdiv2YHZiNi5PC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+e3N0YXRzLnVucGFpZENvdW50fTwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtmB2KfYqtmI2LHYqTwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHVycGxlLTUwIHAtNCByb3VuZGVkLWxnIGJvcmRlci1yLTQgYm9yZGVyLXB1cnBsZS01MDBcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1wdXJwbGUtNzAwXCI+2YbYs9io2Kkg2KfZhNiq2K3YtdmK2YQ8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTgwMFwiPntzdGF0cy5jb2xsZWN0aW9uUmF0ZS50b0ZpeGVkKDEpfSU8L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHVycGxlLTYwMFwiPtmF2KrZiNiz2Lcg2KfZhNmB2KfYqtmI2LHYqToge2Zvcm1hdEN1cnJlbmN5KHN0YXRzLmF2Z0Ftb3VudCl9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog2KPYr9mI2KfYqiDYp9mE2KjYrdirINmI2KfZhNmB2YTYqtix2KkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC00IHJvdW5kZWQtbGcgc2hhZG93LW1kXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICB7Lyog2KfZhNio2K3YqyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPtin2YTYqNit2Ks8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItin2KjYrdirINio2KfZhNin2LPZhdiMINin2YTZh9in2KrZgdiMINij2Ygg2LHZgtmFINin2YTZgdin2KrZiNix2KlcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINin2YTZgdmE2KrYsdipICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2YHZhNiq2LHYqSDYrdiz2Kg8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlckJ5fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyQnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWxsXCI+2KzZhdmK2Lkg2KfZhNmB2YjYp9iq2YrYsTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb25zdWx0YXRpb25cIj7Yp9iz2KrYtNin2LHYp9iqPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInBhdHRlcm5cIj7YqNin2KrYsdmI2YY8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicGhvdG9ncmFwaHlcIj7Yqti12YjZitixPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInNoaXBwaW5nXCI+2LTYrdmGPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImhpZ2hfdmFsdWVcIj7ZgtmK2YXYqSDYudin2YTZitipICgrMTAwMCDYrNmG2YrZhyk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicmVjZW50XCI+2KfZhNij2LPYqNmI2Lkg2KfZhNmF2KfYttmKPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInBhaWRfZnVsbFwiPtmF2K/ZgdmI2Lkg2KjYp9mE2YPYp9mF2YQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicGFpZF9wYXJ0aWFsXCI+2YXYr9mB2YjYuSDYrNiy2KbZitin2Ys8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwidW5wYWlkXCI+2LrZitixINmF2K/ZgdmI2Lk8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINin2YTYqtix2KrZitioICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2KrYsdiq2YrYqCDYrdiz2Kg8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NvcnRCeX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNvcnRCeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJkYXRlX2Rlc2NcIj7Yp9mE2KrYp9ix2YrYriAo2KfZhNij2K3Yr9irINij2YjZhNin2YspPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImRhdGVfYXNjXCI+2KfZhNiq2KfYsdmK2K4gKNin2YTYo9mC2K/ZhSDYo9mI2YTYp9mLKTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbW91bnRfZGVzY1wiPtin2YTZhdio2YTYuiAo2KfZhNij2LnZhNmJINij2YjZhNin2YspPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFtb3VudF9hc2NcIj7Yp9mE2YXYqNmE2LogKNin2YTYo9mC2YQg2KPZiNmE2KfZiyk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY2xpZW50X25hbWVcIj7Yp9iz2YUg2KfZhNi52YXZitmEPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDZgtin2KbZhdipINin2YTZgdmI2KfYqtmK2LEgKi99XG4gICAgICB7ZmlsdGVyZWRJbnZvaWNlcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1sZyBtYi00XCI+XG4gICAgICAgICAgICB7aW52b2ljZXMubGVuZ3RoID09PSAwID8gJ9mE2Kcg2KrZiNis2K8g2YHZiNin2KrZitixINmF2K3ZgdmI2LjYqScgOiAn2YTYpyDYqtmI2KzYryDZhtiq2KfYptisINmE2YTYqNit2KsnfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHtpbnZvaWNlcy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPtmC2YUg2KjYpdmG2LTYp9ihINmB2KfYqtmI2LHYqSDYrNiv2YrYr9ipINmE2KrYuNmH2LEg2YfZhtinPC9wPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSA6IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIHhsOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAge2ZpbHRlcmVkSW52b2ljZXMubWFwKChpbnZvaWNlKSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aW52b2ljZS5pZH0gY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgey8qINix2KPYsyDYp9mE2KjYt9in2YLYqSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1sIGZyb20tYmx1ZS01MDAgdG8tYmx1ZS02MDAgdGV4dC13aGl0ZSBwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+e2ludm9pY2UuY2xpZW50Lm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTEwMCB0ZXh0LXNtXCI+2YHYp9iq2YjYsdipICN7aW52b2ljZS5pZH08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRDdXJyZW5jeShpbnZvaWNlLnRvdGFsKX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDAgdGV4dC1zbVwiPntmb3JtYXREYXRlKGludm9pY2UuY3JlYXRlZEF0KX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qINmF2K3YqtmI2Ykg2KfZhNio2LfYp9mC2KkgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtix2YLZhSDYp9mE2YfYp9iq2YE8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2ludm9pY2UuY2xpZW50LnBob25lfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yp9mE2LPZitmE2LI8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2ludm9pY2UuY2xpZW50LnNhbGVzcGVyc29ufTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yp9mE2K7Yr9mF2KfYqjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntnZXRTZXJ2aWNlTmFtZXMoaW52b2ljZS5zZXJ2aWNlcyl9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPti32LHZitmC2Kkg2KfZhNiv2YHYuTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aW52b2ljZS5wYXltZW50TWV0aG9kfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yrdin2YTYqSDYp9mE2K/Zgdi5PC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGlubGluZS1ibG9jayBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1zZW1pYm9sZCAke1xuICAgICAgICAgICAgICAgICAgICAgIGludm9pY2UucGF5bWVudFN0YXR1cyA9PT0gJ9mF2K/ZgdmI2Lkg2KjYp9mE2YPYp9mF2YQnID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIGludm9pY2UucGF5bWVudFN0YXR1cyA9PT0gJ9mF2K/ZgdmI2Lkg2KzYstim2YrYp9mLJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtpbnZvaWNlLnBheW1lbnRTdGF0dXMgfHwgJ9i62YrYsSDZhdit2K/Yryd9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPtin2YTZhdiv2YHZiNi5IC8g2KfZhNmF2KrYqNmC2Yo8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPntmb3JtYXRDdXJyZW5jeShpbnZvaWNlLnBhaWRBbW91bnQgfHwgMCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIHsnIC8gJ31cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2AkeyhpbnZvaWNlLnJlbWFpbmluZ0Ftb3VudCB8fCAwKSA+IDAgPyAndGV4dC1yZWQtNjAwJyA6ICd0ZXh0LWdyZWVuLTYwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3VycmVuY3koaW52b2ljZS5yZW1haW5pbmdBbW91bnQgfHwgMCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAge2ludm9pY2UucmVxdWlyZXNTaGlwcGluZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwIHAtMiByb3VuZGVkIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy03MDAgZm9udC1tZWRpdW1cIj7wn5qaINmK2KrYt9mE2Kgg2LTYrdmGPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDYo9iy2LHYp9ixINin2YTYudmF2YTZitin2KogKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtMiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUV4cG9ydFBERihpbnZvaWNlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW0gcHktMiBweC0yIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDwn5OEIFBERlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUHJldmlld0ludm9pY2UoaW52b2ljZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bSBweS0yIHB4LTIgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIPCfkYHvuI8g2YXYudin2YrZhtipXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQT1NJbnZvaWNlKGludm9pY2UpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAgdGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHB5LTIgcHgtMiByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg8J+nviBQT1NcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qINiy2LEg2KfZhNit2LDZgSAqL31cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVJbnZvaWNlKGludm9pY2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW0gcHktMiBweC0zIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTJcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIPCfl5HvuI8g2K3YsNmBINin2YTZgdin2KrZiNix2KlcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogUHJldmlldyBNb2RhbCAqL31cbiAgICAgIHtwcmV2aWV3SW52b2ljZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIG1heC13LTZ4bCB3LWZ1bGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LWF1dG9cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC0wIGJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBwLTQgZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPtmF2LnYp9mK2YbYqSDYp9mE2YHYp9iq2YjYsdipPC9oMj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsb3NlUHJldmlld31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgdGV4dC0yeGwgZm9udC1ib2xkXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICA8SW52b2ljZVByZXZpZXdcbiAgICAgICAgICAgICAgICBpbnZvaWNlPXtwcmV2aWV3SW52b2ljZX1cbiAgICAgICAgICAgICAgICBvbkVkaXQ9e2hhbmRsZUVkaXRGcm9tUHJldmlld31cbiAgICAgICAgICAgICAgICBvbkV4cG9ydFBERj17aGFuZGxlRXhwb3J0RnJvbVByZXZpZXd9XG4gICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgey8qINij2LLYsdin2LEg2KXYttin2YHZitipINmB2Yog2KfZhNmF2LnYp9mK2YbYqSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS0yMDAgcHQtNCBtdC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00IG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUE9TSW52b2ljZShwcmV2aWV3SW52b2ljZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHB5LTMgcHgtNCByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg8J+nviDZgdin2KrZiNix2KkgUE9TXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlSW52b2ljZShwcmV2aWV3SW52b2ljZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHB5LTMgcHgtNCByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg8J+Xke+4jyDYrdiw2YEg2KfZhNmB2KfYqtmI2LHYqVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFBPUyBJbnZvaWNlIE1vZGFsICovfVxuICAgICAge3Bvc0ludm9pY2UgJiYgKFxuICAgICAgICA8UE9TSW52b2ljZVxuICAgICAgICAgIGludm9pY2U9e3Bvc0ludm9pY2V9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0UG9zSW52b2ljZShudWxsKX1cbiAgICAgICAgLz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBJbnZvaWNlc0xpc3Q7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUludm9pY2VTdG9yZSIsImdlbmVyYXRlSW52b2ljZVBERiIsIkludm9pY2VQcmV2aWV3IiwiUE9TSW52b2ljZSIsIkludm9pY2VzTGlzdCIsImludm9pY2VzIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJmaWx0ZXJCeSIsInNldEZpbHRlckJ5Iiwic29ydEJ5Iiwic2V0U29ydEJ5IiwiZmlsdGVyZWRJbnZvaWNlcyIsInNldEZpbHRlcmVkSW52b2ljZXMiLCJwcmV2aWV3SW52b2ljZSIsInNldFByZXZpZXdJbnZvaWNlIiwicG9zSW52b2ljZSIsInNldFBvc0ludm9pY2UiLCJsb2FkSW52b2ljZXMiLCJyZXN1bHQiLCJnZXRTdGF0ZSIsInVuaXF1ZUludm9pY2VzIiwiZmlsdGVyIiwiaW52b2ljZSIsImluZGV4Iiwic2VsZiIsImZpbmRJbmRleCIsImludiIsImlkIiwiaGFuZGxlU3RvcmFnZUNoYW5nZSIsImUiLCJrZXkiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImZpbHRlcmVkIiwiY2xpZW50IiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJwaG9uZSIsInNlcnZpY2VzIiwic29tZSIsInMiLCJzZXJ2aWNlVHlwZSIsInRvdGFsIiwib25lV2Vla0FnbyIsIkRhdGUiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImNyZWF0ZWRBdCIsInBheW1lbnRTdGF0dXMiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwibG9jYWxlQ29tcGFyZSIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiY2FsZW5kYXIiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIm51bWVyaWNBbW91bnQiLCJwYXJzZUZsb2F0IiwiU3RyaW5nIiwiaXNOYU4iLCJ0b0ZpeGVkIiwiZ2V0U2VydmljZU5hbWVzIiwic2VydmljZU5hbWVzIiwiY29uc3VsdGF0aW9uIiwicGF0dGVybiIsInBhdHRlcm5fcHJpbnRpbmciLCJtYW51ZmFjdHVyaW5nIiwic2FtcGxlcyIsInNoaXBwaW5nIiwibWFya2V0aW5nIiwicGhvdG9ncmFwaHkiLCJwcm9kdWN0cyIsInJhd19wdXJjaGFzZXMiLCJtYXAiLCJqb2luIiwic2hvd1N1Y2Nlc3NNZXNzYWdlIiwibWVzc2FnZSIsInRvYXN0IiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiY2xhc3NOYW1lIiwiaW5uZXJIVE1MIiwiYm9keSIsImFwcGVuZENoaWxkIiwic2V0VGltZW91dCIsInN0eWxlIiwidHJhbnNmb3JtIiwiY29udGFpbnMiLCJyZW1vdmVDaGlsZCIsInNob3dFcnJvck1lc3NhZ2UiLCJoYW5kbGVFeHBvcnRQREYiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVQcmV2aWV3SW52b2ljZSIsImhhbmRsZVBPU0ludm9pY2UiLCJoYW5kbGVDbG9zZVByZXZpZXciLCJoYW5kbGVFZGl0RnJvbVByZXZpZXciLCJhbGVydCIsImhhbmRsZUV4cG9ydEZyb21QcmV2aWV3IiwiaGFuZGxlRGVsZXRlSW52b2ljZSIsImNvbmZpcm1NZXNzYWdlIiwiY29uZmlybSIsInN0b3JlIiwiZGVsZXRlSW52b2ljZSIsInVwZGF0ZWRJbnZvaWNlcyIsIkpTT04iLCJwYXJzZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJnZXRUb3RhbFN0YXRzIiwicmVkdWNlIiwic3VtIiwiY291bnQiLCJsZW5ndGgiLCJhdmdBbW91bnQiLCJ0b3RhbFBhaWQiLCJwYWlkQW1vdW50IiwidG90YWxSZW1haW5pbmciLCJwYWlkRnVsbENvdW50IiwicGFpZFBhcnRpYWxDb3VudCIsInVucGFpZENvdW50IiwiY29sbGVjdGlvblJhdGUiLCJzdGF0cyIsImdldFRvZGF5U3RhdHMiLCJ0b2RheSIsInRvRGF0ZVN0cmluZyIsInRvZGF5SW52b2ljZXMiLCJ0b2RheVRvdGFsIiwidG9kYXlQYWlkIiwidG9kYXlSZW1haW5pbmciLCJ0b2RheVBhaWRGdWxsQ291bnQiLCJ0b2RheVBhaWRQYXJ0aWFsQ291bnQiLCJ0b2RheVVucGFpZENvdW50IiwidG9kYXlDb2xsZWN0aW9uUmF0ZSIsInBhaWQiLCJyZW1haW5pbmciLCJ0b2RheVN0YXRzIiwiZGl2IiwiaDEiLCJoMiIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwicCIsIndlZWtkYXkiLCJoMyIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwic2VsZWN0Iiwib3B0aW9uIiwic2FsZXNwZXJzb24iLCJwYXltZW50TWV0aG9kIiwic3BhbiIsInJlbWFpbmluZ0Ftb3VudCIsInJlcXVpcmVzU2hpcHBpbmciLCJidXR0b24iLCJvbkNsaWNrIiwib25FZGl0Iiwib25FeHBvcnRQREYiLCJvbkNsb3NlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InvoicesList.tsx\n"));

/***/ })

});